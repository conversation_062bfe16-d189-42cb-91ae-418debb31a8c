using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using FlowCustom.PluginHost;
using FlowCustom.Engine;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustom.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WorkflowController : ControllerBase
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<WorkflowController> _logger;

    public WorkflowController(
        IWorkflowService workflowService,
        IWorkflowEngine workflowEngine,
        IPluginManager pluginManager,
        ILogger<WorkflowController> logger)
    {
        _workflowService = workflowService;
        _workflowEngine = workflowEngine;
        _pluginManager = pluginManager;
        _logger = logger;
    }

    /// <summary>
    /// 获取用户的工作流列表
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Workflow>>> GetWorkflows([FromQuery] string? userId = "default")
    {
        var workflows = await _workflowService.GetUserWorkflowsAsync(userId ?? "default");
        return Ok(workflows);
    }

    /// <summary>
    /// 获取指定工作流
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<Workflow>> GetWorkflow(Guid id)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }
        return Ok(workflow);
    }

    /// <summary>
    /// 创建新工作流
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<Workflow>> CreateWorkflow([FromBody] CreateWorkflowRequest request)
    {
        _logger.LogInformation("Creating workflow with name: '{Name}', description: '{Description}'", request.Name, request.Description);

        // 创建完整的工作流对象
        var workflow = new Workflow
        {
            Id = Guid.NewGuid(),
            Name = request.Name ?? "New Workflow",
            Description = request.Description ?? "",
            IsActive = request.IsActive ?? false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = "default", // TODO: 从认证上下文获取用户信息
            Nodes = request.Nodes ?? new List<WorkflowNode>(),
            Connections = request.Connections ?? new List<NodeConnection>(),
            Settings = request.Settings ?? new WorkflowSettings()
        };

        // 使用插件系统验证工作流
        var validation = await ValidateWorkflowWithPlugins(workflow);
        if (!validation.IsValid)
        {
            return BadRequest(validation.Errors);
        }

        var createdWorkflow = await _workflowService.CreateWorkflowAsync(workflow);

        return CreatedAtAction(nameof(GetWorkflow), new { id = createdWorkflow.Id }, createdWorkflow);
    }

    /// <summary>
    /// 更新工作流
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<Workflow>> UpdateWorkflow(Guid id, [FromBody] Workflow workflow)
    {
        try
        {
            _logger.LogInformation("Updating workflow {WorkflowId} with name: {WorkflowName}", id, workflow.Name);

            if (id != workflow.Id)
            {
                _logger.LogWarning("ID mismatch: URL ID {UrlId} != Body ID {BodyId}", id, workflow.Id);
                return BadRequest("ID mismatch");
            }

            var existingWorkflow = await _workflowService.GetWorkflowAsync(id);
            if (existingWorkflow == null)
            {
                _logger.LogWarning("Workflow not found: {WorkflowId}", id);
                return NotFound();
            }

            // 使用插件系统验证工作流
            var validation = await ValidateWorkflowWithPlugins(workflow);
            if (!validation.IsValid)
            {
                _logger.LogWarning("Workflow validation failed for {WorkflowId}: {Errors}", id, string.Join(", ", validation.Errors));
                return BadRequest(validation.Errors);
            }

            var updatedWorkflow = await _workflowService.UpdateWorkflowAsync(workflow);
            _logger.LogInformation("Successfully updated workflow: {WorkflowId}", id);
            return Ok(updatedWorkflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating workflow {WorkflowId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 删除工作流
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteWorkflow(Guid id)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }

        await _workflowService.DeleteWorkflowAsync(id);
        return NoContent();
    }

    /// <summary>
    /// 激活/停用工作流
    /// </summary>
    [HttpPost("{id}/toggle")]
    public async Task<IActionResult> ToggleWorkflow(Guid id, [FromBody] bool isActive)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }

        await _workflowService.SetWorkflowActiveAsync(id, isActive);
        return Ok();
    }

    /// <summary>
    /// 手动执行工作流
    /// </summary>
    [HttpPost("{id}/execute")]
    public async Task<ActionResult<object>> ExecuteWorkflow(
        Guid id, 
        [FromBody] Dictionary<string, object>? inputData = null)
    {
        try
        {
            var workflow = await _workflowService.GetWorkflowAsync(id);
            if (workflow == null)
            {
                return NotFound();
            }

            // 使用新的插件化执行引擎
            var request = new WorkflowExecutionRequest
            {
                WorkflowId = id.ToString(),
                Workflow = ConvertToEngineWorkflow(workflow),
                Variables = inputData ?? new(),
                TriggerData = new Dictionary<string, object>
                {
                    ["triggeredBy"] = "manual",
                    ["triggeredAt"] = DateTime.UtcNow
                }
            };

            var result = await _workflowEngine.ExecuteWorkflowAsync(request);
            
            return Ok(new
            {
                executionId = result.ExecutionId,
                workflowId = result.WorkflowId,
                status = result.Status.ToString(),
                startTime = result.StartTime,
                endTime = result.EndTime,
                duration = result.Duration?.TotalMilliseconds,
                nodeResults = result.NodeResults.Select(nr => new
                {
                    nodeId = nr.NodeId,
                    nodeName = nr.NodeName,
                    nodeType = nr.NodeType,
                    success = nr.Success,
                    outputData = nr.OutputData,
                    errorMessage = nr.ErrorMessage,
                    startTime = nr.StartTime,
                    endTime = nr.EndTime
                }).ToList()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行工作流失败: {WorkflowId}", id);
            return StatusCode(500, new { error = ex.Message });
        }
    }

    /// <summary>
    /// 使用插件系统验证工作流
    /// </summary>
    private async Task<WorkflowValidationResult> ValidateWorkflowWithPlugins(Workflow workflow)
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        // 验证节点
        foreach (var node in workflow.Nodes)
        {
            var plugin = _pluginManager.GetPlugin(node.Type);
            if (plugin == null)
            {
                errors.Add($"节点 '{node.Name}' 的类型 '{node.Type}' 没有对应的插件");
                continue;
            }

            try
            {
                var nodeConfig = new FlowCustom.SDK.NodeConfiguration
                {
                    Parameters = node.Parameters?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new(),
                    IsDisabled = node.IsDisabled,
                    Notes = node.Notes ?? ""
                };

                var validationResult = await plugin.ValidateAsync(nodeConfig);
                if (!validationResult.IsValid)
                {
                    errors.AddRange(validationResult.Errors.Select(e => $"节点 '{node.Name}': {e.Message}"));
                }
                warnings.AddRange(validationResult.Warnings.Select(w => $"节点 '{node.Name}': {w.Message}"));
            }
            catch (Exception ex)
            {
                errors.Add($"验证节点 '{node.Name}' 时发生错误: {ex.Message}");
            }
        }

        return new WorkflowValidationResult
        {
            IsValid = errors.Count == 0,
            Errors = errors,
            Warnings = warnings
        };
    }

    /// <summary>
    /// 转换为执行引擎的工作流格式
    /// </summary>
    private FlowCustom.Engine.Workflow ConvertToEngineWorkflow(Workflow workflow)
    {
        return new FlowCustom.Engine.Workflow
        {
            Id = workflow.Id.ToString(),
            Name = workflow.Name,
            Nodes = workflow.Nodes.Select(n => new FlowCustom.Engine.WorkflowNode
            {
                Id = n.Id.ToString(),
                Name = n.Name,
                Type = n.Type,
                Parameters = n.Parameters,
                IsDisabled = n.IsDisabled,
                Notes = n.Notes,
                CustomInputs = n.CustomInputs?.Select(ci => (dynamic)new
                {
                    Id = ci.Id,
                    Name = ci.Name,
                    Description = ci.Description,
                    Type = ci.Type,
                    DataType = ci.DataType,
                    Required = ci.Required,
                    Position = new { Side = ci.Position.Side, Offset = ci.Position.Offset }
                }).ToList(),
                CustomOutputs = n.CustomOutputs?.Select(co => (dynamic)new
                {
                    Id = co.Id,
                    Name = co.Name,
                    Description = co.Description,
                    Type = co.Type,
                    DataType = co.DataType,
                    Required = co.Required,
                    Position = new { Side = co.Position.Side, Offset = co.Position.Offset }
                }).ToList()
            }).ToList(),
            Connections = workflow.Connections.Select(c => new FlowCustom.Engine.WorkflowConnection
            {
                Id = c.Id.ToString(),
                SourceNodeId = c.SourceNodeId.ToString(),
                TargetNodeId = c.TargetNodeId.ToString(),
                SourceOutput = c.SourceOutput,
                TargetInput = c.TargetInput
            }).ToList()
        };
    }
}

/// <summary>
/// 创建工作流请求模型
/// </summary>
public class CreateWorkflowRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public List<WorkflowNode>? Nodes { get; set; }
    public List<NodeConnection>? Connections { get; set; }
    public WorkflowSettings? Settings { get; set; }
}

/// <summary>
/// 工作流验证结果
/// </summary>
public class WorkflowValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

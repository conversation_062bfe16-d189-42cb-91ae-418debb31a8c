<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\FlowCustom.Core\FlowCustom.Core.csproj" />
    <ProjectReference Include="..\FlowCustom.Engine\FlowCustom.Engine.csproj" />
    <ProjectReference Include="..\FlowCustom.Data\FlowCustom.Data.csproj" />
    <ProjectReference Include="..\FlowCustom.Nodes\FlowCustom.Nodes.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.3" />
  </ItemGroup>

</Project>

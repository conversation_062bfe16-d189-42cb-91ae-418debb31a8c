<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <PropertyGroup>
    <AssemblyTitle>FlowCustomV1 Workflow Engine</AssemblyTitle>
    <AssemblyDescription>Workflow execution engine for FlowCustomV1</AssemblyDescription>
    <AssemblyCompany>FlowCustom Team</AssemblyCompany>
    <AssemblyProduct>FlowCustomV1</AssemblyProduct>
    <AssemblyCopyright>Copyright © FlowCustom Team 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>1.0.0</InformationalVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
    <PackageReference Include="System.Threading.Channels" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FlowCustomV1.Core\FlowCustomV1.Core.csproj" />
    <ProjectReference Include="..\FlowCustomV1.PluginHost\FlowCustomV1.PluginHost.csproj" />
  </ItemGroup>

</Project>

using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Nodes;

/// <summary>
/// Webhook 触发器节点
/// </summary>
public class WebhookTriggerNode : ITriggerNode
{
    private readonly ILogger<WebhookTriggerNode> _logger;
    private static readonly ConcurrentDictionary<string, WebhookTriggerNode> _activeWebhooks = new();

    public WebhookTriggerNode(ILogger<WebhookTriggerNode> logger)
    {
        _logger = logger;
    }

    public string NodeType => "webhook-trigger";
    public string DisplayName => "Webhook Trigger";
    public string Description => "Trigger workflow via HTTP webhook";
    public string Category => "Trigger";
    public string Icon => "webhook";

    public event EventHandler<TriggerEventArgs>? Triggered;

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "path",
                DisplayName = "Webhook Path",
                Description = "The path for the webhook endpoint (e.g., /webhook/my-workflow)",
                Type = ParameterType.String,
                Required = true
            },
            new NodeParameterDefinition
            {
                Name = "httpMethod",
                DisplayName = "HTTP Method",
                Description = "Allowed HTTP methods",
                Type = ParameterType.MultiSelect,
                Required = false,
                DefaultValue = new[] { "POST" },
                Options = new[] { "GET", "POST", "PUT", "DELETE", "PATCH" }
            },
            new NodeParameterDefinition
            {
                Name = "authentication",
                DisplayName = "Authentication",
                Description = "Authentication method",
                Type = ParameterType.Select,
                Required = false,
                DefaultValue = "none",
                Options = new[] { "none", "basic", "header" }
            },
            new NodeParameterDefinition
            {
                Name = "authValue",
                DisplayName = "Authentication Value",
                Description = "Authentication token or credentials",
                Type = ParameterType.String,
                Required = false
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node,
        Models.ExecutionContext context,
        CancellationToken cancellationToken = default)
    {
        // 触发器节点不直接执行，而是在触发时提供数据
        await Task.CompletedTask;
        return NodeExecutionResult.CreateSuccess(new Dictionary<string, object>
        {
            ["message"] = "Webhook trigger is active"
        });
    }

    public async Task StartAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        var path = GetParameter<string>(node, "path");
        if (string.IsNullOrEmpty(path))
        {
            throw new ArgumentException("Webhook path is required");
        }

        // 确保路径以 / 开头
        if (!path.StartsWith("/"))
        {
            path = "/" + path;
        }

        var webhookKey = $"{node.WorkflowId}:{path}";
        
        if (_activeWebhooks.TryAdd(webhookKey, this))
        {
            _logger.LogInformation("Started webhook trigger: {Path} for workflow: {WorkflowId}", 
                path, node.WorkflowId);
        }
        else
        {
            _logger.LogWarning("Webhook path already in use: {Path}", path);
        }

        await Task.CompletedTask;
    }

    public async Task StopAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        var path = GetParameter<string>(node, "path");
        if (string.IsNullOrEmpty(path))
        {
            return;
        }

        if (!path.StartsWith("/"))
        {
            path = "/" + path;
        }

        var webhookKey = $"{node.WorkflowId}:{path}";
        
        if (_activeWebhooks.TryRemove(webhookKey, out _))
        {
            _logger.LogInformation("Stopped webhook trigger: {Path} for workflow: {WorkflowId}", 
                path, node.WorkflowId);
        }

        await Task.CompletedTask;
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.ContainsKey("path") || string.IsNullOrEmpty(parameters["path"]?.ToString()))
        {
            errors.Add("Webhook path is required");
        }

        return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
    }

    /// <summary>
    /// 处理传入的 Webhook 请求
    /// </summary>
    public static async Task<bool> HandleWebhookRequest(
        string path, 
        string method, 
        Dictionary<string, object> data,
        Dictionary<string, string> headers)
    {
        var matchingWebhooks = _activeWebhooks.Where(kvp => 
        {
            var parts = kvp.Key.Split(':');
            return parts.Length > 1 && parts[1] == path;
        }).ToList();

        if (!matchingWebhooks.Any())
        {
            return false;
        }

        foreach (var webhook in matchingWebhooks)
        {
            try
            {
                var parts = webhook.Key.Split(':');
                var workflowId = Guid.Parse(parts[0]);

                var triggerArgs = new TriggerEventArgs
                {
                    WorkflowId = workflowId,
                    NodeId = Guid.NewGuid(), // TODO: 获取实际的节点ID
                    Data = new Dictionary<string, object>
                    {
                        ["method"] = method,
                        ["path"] = path,
                        ["headers"] = headers,
                        ["body"] = data
                    },
                    Source = "webhook"
                };

                webhook.Value.Triggered?.Invoke(webhook.Value, triggerArgs);
            }
            catch (Exception ex)
            {
                // 记录错误但继续处理其他 webhook
                Console.WriteLine($"Error processing webhook: {ex.Message}");
            }
        }

        return true;
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

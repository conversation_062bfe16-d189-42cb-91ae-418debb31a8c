/**
 * 端点管理组件
 * 
 * 用于在节点配置面板中管理节点的输入输出端点
 * 支持动态添加、删除和编辑端点
 */

import React, { useState } from 'react';
import type { NodeEndpoint } from '../types/workflow';

interface EndpointManagerProps {
  /** 端点类型：输入或输出 */
  type: 'input' | 'output';
  /** 当前端点列表 */
  endpoints: NodeEndpoint[];
  /** 端点变化回调 */
  onChange: (endpoints: NodeEndpoint[]) => void;
  /** 是否只读 */
  readonly?: boolean;
}

/**
 * 端点管理组件
 */
const EndpointManager: React.FC<EndpointManagerProps> = ({
  type,
  endpoints,
  onChange,
  readonly = false
}) => {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  /**
   * 端点模板
   */
  const endpointTemplates = [
    {
      name: '数据端点',
      template: {
        type: 'data',
        dataType: 'object',
        description: '通用数据传输端点'
      }
    },
    {
      name: '文本端点',
      template: {
        type: 'text',
        dataType: 'string',
        description: '文本字符串端点'
      }
    },
    {
      name: '数字端点',
      template: {
        type: 'number',
        dataType: 'number',
        description: '数值类型端点'
      }
    },
    {
      name: '文件端点',
      template: {
        type: 'file',
        dataType: 'file',
        description: '文件数据端点'
      }
    },
    {
      name: '成功端点',
      template: {
        type: 'success',
        dataType: 'object',
        description: '成功结果输出端点'
      }
    },
    {
      name: '错误端点',
      template: {
        type: 'error',
        dataType: 'error',
        description: '错误信息输出端点'
      }
    }
  ];

  /**
   * 添加新端点
   */
  const handleAddEndpoint = (template?: any) => {
    const baseEndpoint = {
      id: `${type}_${Date.now()}`,
      name: `${type === 'input' ? '输入' : '输出'}${endpoints.length + 1}`,
      description: '',
      type: 'data',
      dataType: 'any',
      required: false,
      position: {
        side: type === 'input' ? 'top' : 'bottom',
        offset: 50 + (endpoints.length * 20) % 60
      }
    };

    const newEndpoint: NodeEndpoint = template
      ? { ...baseEndpoint, ...template }
      : baseEndpoint;

    onChange([...endpoints, newEndpoint]);
    setEditingIndex(endpoints.length);
  };

  /**
   * 删除端点
   */
  const handleDeleteEndpoint = (index: number) => {
    const newEndpoints = endpoints.filter((_, i) => i !== index);
    onChange(newEndpoints);
    setEditingIndex(null);
  };

  /**
   * 更新端点
   */
  const handleUpdateEndpoint = (index: number, updatedEndpoint: NodeEndpoint) => {
    const newEndpoints = [...endpoints];
    newEndpoints[index] = updatedEndpoint;
    onChange(newEndpoints);
  };

  /**
   * 端点类型选项
   */
  const endpointTypes = [
    { value: 'data', label: '数据', color: '#3b82f6', description: '通用数据传输' },
    { value: 'trigger', label: '触发', color: '#a855f7', description: '触发器信号' },
    { value: 'error', label: '错误', color: '#ef4444', description: '错误信息' },
    { value: 'success', label: '成功', color: '#10b981', description: '成功结果' },
    { value: 'condition', label: '条件', color: '#f59e0b', description: '条件分支' },
    { value: 'file', label: '文件', color: '#8b5cf6', description: '文件数据' },
    { value: 'json', label: 'JSON', color: '#06b6d4', description: 'JSON对象' },
    { value: 'text', label: '文本', color: '#84cc16', description: '文本字符串' },
    { value: 'number', label: '数字', color: '#f97316', description: '数值类型' },
    { value: 'boolean', label: '布尔', color: '#ec4899', description: '布尔值' }
  ];

  /**
   * 位置选项
   */
  const positionOptions = [
    { value: 'top', label: '顶部' },
    { value: 'bottom', label: '底部' },
    { value: 'left', label: '左侧' },
    { value: 'right', label: '右侧' }
  ];

  return (
    <div className="space-y-3">
      {/* 标题和添加按钮 */}
      <div className="flex items-center justify-between mb-2">
        <h5 className="text-sm font-medium text-gray-700">
          {type === 'input' ? '输入端点' : '输出端点'}
          <span className="ml-2 text-xs text-gray-500">({endpoints.length})</span>
        </h5>
        {!readonly && (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleAddEndpoint()}
              className="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
              title={`添加${type === 'input' ? '输入' : '输出'}端点`}
            >
              + 添加
            </button>
            <div className="relative group">
              <button
                className="text-xs px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                title="从模板添加"
              >
                📋 模板
              </button>
              <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                <div className="p-2">
                  <div className="text-xs text-gray-600 mb-2 font-medium">选择端点模板</div>
                  {endpointTemplates.map((template, idx) => (
                    <button
                      key={idx}
                      onClick={() => handleAddEndpoint(template.template)}
                      className="w-full text-left text-xs px-2 py-1 hover:bg-gray-100 rounded block"
                      title={template.template.description}
                    >
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{
                            backgroundColor: endpointTypes.find(t => t.value === template.template.type)?.color || '#6b7280'
                          }}
                        />
                        <span>{template.name}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 端点列表 */}
      <div className="space-y-2 max-h-48 overflow-y-auto">
        {endpoints.map((endpoint, index) => (
          <div
            key={endpoint.id}
            className="border border-gray-200 rounded-lg p-3 bg-gray-50"
          >
            {editingIndex === index ? (
              /* 编辑模式 */
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">名称</label>
                    <input
                      type="text"
                      value={endpoint.name}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        name: e.target.value
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                      placeholder="端点名称"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">ID</label>
                    <input
                      type="text"
                      value={endpoint.id}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        id: e.target.value
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                      placeholder="端点ID"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-xs text-gray-600 mb-1">描述</label>
                  <input
                    type="text"
                    value={endpoint.description || ''}
                    onChange={(e) => handleUpdateEndpoint(index, {
                      ...endpoint,
                      description: e.target.value
                    })}
                    className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                    placeholder="端点描述"
                  />
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">端点类型</label>
                    <select
                      value={endpoint.type}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        type: e.target.value
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                      title={endpointTypes.find(t => t.value === endpoint.type)?.description}
                    >
                      {endpointTypes.map(type => (
                        <option key={type.value} value={type.value} title={type.description}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">数据类型</label>
                    <input
                      type="text"
                      value={endpoint.dataType || 'any'}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        dataType: e.target.value
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                      placeholder="数据类型"
                      title="指定端点接受的数据类型，如：string, number, object, array等"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">位置</label>
                    <select
                      value={endpoint.position.side}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        position: {
                          ...endpoint.position,
                          side: e.target.value as 'top' | 'bottom' | 'left' | 'right'
                        }
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                    >
                      {positionOptions.map(pos => (
                        <option key={pos.value} value={pos.value}>
                          {pos.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">必需</label>
                    <select
                      value={endpoint.required ? 'true' : 'false'}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        required: e.target.value === 'true'
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                    >
                      <option value="false">可选</option>
                      <option value="true">必需</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-xs text-gray-600 mb-1">
                    偏移位置 ({endpoint.position.offset}%)
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="90"
                    value={endpoint.position.offset}
                    onChange={(e) => handleUpdateEndpoint(index, {
                      ...endpoint,
                      position: {
                        ...endpoint.position,
                        offset: parseInt(e.target.value)
                      }
                    })}
                    className="w-full"
                  />
                </div>

                <div className="flex justify-end space-x-2">
                  <button
                    onClick={() => setEditingIndex(null)}
                    className="text-xs px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    完成
                  </button>
                  <button
                    onClick={() => handleDeleteEndpoint(index)}
                    className="text-xs px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                  >
                    删除
                  </button>
                </div>
              </div>
            ) : (
              /* 显示模式 */
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <div
                      className="w-3 h-3 rounded-full border border-white shadow-sm"
                      style={{
                        backgroundColor: endpointTypes.find(t => t.value === endpoint.type)?.color || '#6b7280'
                      }}
                      title={endpointTypes.find(t => t.value === endpoint.type)?.description}
                    />
                    <span className="text-sm font-medium">{endpoint.name}</span>
                    <span className="text-xs text-gray-500">({endpoint.id})</span>
                    {endpoint.required && (
                      <span className="text-xs bg-red-100 text-red-600 px-1 rounded">必需</span>
                    )}
                  </div>

                  {endpoint.description && (
                    <p className="text-xs text-gray-600 mb-1">{endpoint.description}</p>
                  )}

                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>
                      类型: <span className="font-medium">{endpointTypes.find(t => t.value === endpoint.type)?.label}</span>
                    </span>
                    <span>
                      数据: <span className="font-medium">{endpoint.dataType || 'any'}</span>
                    </span>
                    <span>
                      位置: <span className="font-medium">
                        {positionOptions.find(p => p.value === endpoint.position.side)?.label} {endpoint.position.offset}%
                      </span>
                    </span>
                  </div>
                </div>
                {!readonly && (
                  <button
                    onClick={() => setEditingIndex(index)}
                    className="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 ml-2"
                    title="编辑端点配置"
                  >
                    编辑
                  </button>
                )}
              </div>
            )}
          </div>
        ))}

        {endpoints.length === 0 && (
          <div className="text-center py-4 text-gray-500 text-sm">
            暂无{type === 'input' ? '输入' : '输出'}端点
          </div>
        )}
      </div>
    </div>
  );
};

export default EndpointManager;

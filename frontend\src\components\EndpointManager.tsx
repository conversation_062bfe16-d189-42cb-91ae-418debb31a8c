/**
 * 端点管理组件
 * 
 * 用于在节点配置面板中管理节点的输入输出端点
 * 支持动态添加、删除和编辑端点
 */

import React, { useState } from 'react';
import type { NodeEndpoint } from '../types/workflow';

interface EndpointManagerProps {
  /** 端点类型：输入或输出 */
  type: 'input' | 'output';
  /** 当前端点列表 */
  endpoints: NodeEndpoint[];
  /** 端点变化回调 */
  onChange: (endpoints: NodeEndpoint[]) => void;
  /** 是否只读 */
  readonly?: boolean;
}

/**
 * 端点管理组件
 */
const EndpointManager: React.FC<EndpointManagerProps> = ({
  type,
  endpoints,
  onChange,
  readonly = false
}) => {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  /**
   * 添加新端点
   */
  const handleAddEndpoint = () => {
    const newEndpoint: NodeEndpoint = {
      id: `${type}_${Date.now()}`,
      name: `${type === 'input' ? '输入' : '输出'}${endpoints.length + 1}`,
      description: '',
      type: 'data',
      position: {
        side: type === 'input' ? 'top' : 'bottom',
        offset: 50 + (endpoints.length * 20) % 60
      }
    };

    onChange([...endpoints, newEndpoint]);
    setEditingIndex(endpoints.length);
  };

  /**
   * 删除端点
   */
  const handleDeleteEndpoint = (index: number) => {
    const newEndpoints = endpoints.filter((_, i) => i !== index);
    onChange(newEndpoints);
    setEditingIndex(null);
  };

  /**
   * 更新端点
   */
  const handleUpdateEndpoint = (index: number, updatedEndpoint: NodeEndpoint) => {
    const newEndpoints = [...endpoints];
    newEndpoints[index] = updatedEndpoint;
    onChange(newEndpoints);
  };

  /**
   * 端点类型选项
   */
  const endpointTypes = [
    { value: 'data', label: '数据', color: '#3b82f6' },
    { value: 'trigger', label: '触发', color: '#a855f7' },
    { value: 'error', label: '错误', color: '#ef4444' },
    { value: 'success', label: '成功', color: '#10b981' },
    { value: 'condition', label: '条件', color: '#f59e0b' }
  ];

  /**
   * 位置选项
   */
  const positionOptions = [
    { value: 'top', label: '顶部' },
    { value: 'bottom', label: '底部' },
    { value: 'left', label: '左侧' },
    { value: 'right', label: '右侧' }
  ];

  return (
    <div className="space-y-3">
      {/* 标题和添加按钮 */}
      <div className="flex items-center justify-between">
        <h5 className="text-sm font-medium text-gray-700">
          {type === 'input' ? '输入端点' : '输出端点'}
          <span className="ml-2 text-xs text-gray-500">({endpoints.length})</span>
        </h5>
        {!readonly && (
          <button
            onClick={handleAddEndpoint}
            className="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
            title={`添加${type === 'input' ? '输入' : '输出'}端点`}
          >
            + 添加
          </button>
        )}
      </div>

      {/* 端点列表 */}
      <div className="space-y-2 max-h-48 overflow-y-auto">
        {endpoints.map((endpoint, index) => (
          <div
            key={endpoint.id}
            className="border border-gray-200 rounded-lg p-3 bg-gray-50"
          >
            {editingIndex === index ? (
              /* 编辑模式 */
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">名称</label>
                    <input
                      type="text"
                      value={endpoint.name}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        name: e.target.value
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                      placeholder="端点名称"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">ID</label>
                    <input
                      type="text"
                      value={endpoint.id}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        id: e.target.value
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                      placeholder="端点ID"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-xs text-gray-600 mb-1">描述</label>
                  <input
                    type="text"
                    value={endpoint.description || ''}
                    onChange={(e) => handleUpdateEndpoint(index, {
                      ...endpoint,
                      description: e.target.value
                    })}
                    className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                    placeholder="端点描述"
                  />
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">类型</label>
                    <select
                      value={endpoint.type}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        type: e.target.value
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                    >
                      {endpointTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">位置</label>
                    <select
                      value={endpoint.position.side}
                      onChange={(e) => handleUpdateEndpoint(index, {
                        ...endpoint,
                        position: {
                          ...endpoint.position,
                          side: e.target.value as 'top' | 'bottom' | 'left' | 'right'
                        }
                      })}
                      className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
                    >
                      {positionOptions.map(pos => (
                        <option key={pos.value} value={pos.value}>
                          {pos.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-xs text-gray-600 mb-1">
                    偏移位置 ({endpoint.position.offset}%)
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="90"
                    value={endpoint.position.offset}
                    onChange={(e) => handleUpdateEndpoint(index, {
                      ...endpoint,
                      position: {
                        ...endpoint.position,
                        offset: parseInt(e.target.value)
                      }
                    })}
                    className="w-full"
                  />
                </div>

                <div className="flex justify-end space-x-2">
                  <button
                    onClick={() => setEditingIndex(null)}
                    className="text-xs px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    完成
                  </button>
                  <button
                    onClick={() => handleDeleteEndpoint(index)}
                    className="text-xs px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                  >
                    删除
                  </button>
                </div>
              </div>
            ) : (
              /* 显示模式 */
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{
                        backgroundColor: endpointTypes.find(t => t.value === endpoint.type)?.color || '#6b7280'
                      }}
                    />
                    <span className="text-sm font-medium">{endpoint.name}</span>
                    <span className="text-xs text-gray-500">({endpoint.id})</span>
                  </div>
                  {endpoint.description && (
                    <p className="text-xs text-gray-600 mt-1">{endpoint.description}</p>
                  )}
                  <div className="text-xs text-gray-500 mt-1">
                    {positionOptions.find(p => p.value === endpoint.position.side)?.label} - {endpoint.position.offset}%
                  </div>
                </div>
                {!readonly && (
                  <button
                    onClick={() => setEditingIndex(index)}
                    className="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    编辑
                  </button>
                )}
              </div>
            )}
          </div>
        ))}

        {endpoints.length === 0 && (
          <div className="text-center py-4 text-gray-500 text-sm">
            暂无{type === 'input' ? '输入' : '输出'}端点
          </div>
        )}
      </div>
    </div>
  );
};

export default EndpointManager;

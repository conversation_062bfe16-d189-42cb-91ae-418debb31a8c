using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text;

namespace FlowCustom.Data;

/// <summary>
/// 用户服务实现
/// </summary>
public class UserService : IUserService
{
    private readonly ILogger<UserService> _logger;
    private readonly ConcurrentDictionary<Guid, User> _users = new();
    private readonly ConcurrentDictionary<string, Guid> _usernameIndex = new();
    private readonly ConcurrentDictionary<string, Guid> _emailIndex = new();

    public UserService(ILogger<UserService> logger)
    {
        _logger = logger;
        
        // 创建默认管理员用户
        CreateDefaultAdminUser();
    }

    public async Task<User> CreateUserAsync(User user, string password)
    {
        // 检查用户名和邮箱是否已存在
        if (_usernameIndex.ContainsKey(user.Username.ToLower()))
        {
            throw new InvalidOperationException("Username already exists");
        }

        if (_emailIndex.ContainsKey(user.Email.ToLower()))
        {
            throw new InvalidOperationException("Email already exists");
        }

        user.Id = Guid.NewGuid();
        user.PasswordHash = HashPassword(password);
        user.CreatedAt = DateTime.UtcNow;
        user.UpdatedAt = DateTime.UtcNow;

        _users[user.Id] = user;
        _usernameIndex[user.Username.ToLower()] = user.Id;
        _emailIndex[user.Email.ToLower()] = user.Id;

        _logger.LogInformation("Created user: {Username} ({UserId})", user.Username, user.Id);
        
        await Task.CompletedTask;
        return user;
    }

    public async Task<User?> GetUserAsync(Guid userId)
    {
        await Task.CompletedTask;
        return _users.TryGetValue(userId, out var user) ? user : null;
    }

    public async Task<User?> GetUserByUsernameAsync(string username)
    {
        await Task.CompletedTask;
        if (_usernameIndex.TryGetValue(username.ToLower(), out var userId))
        {
            return _users.TryGetValue(userId, out var user) ? user : null;
        }
        return null;
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        await Task.CompletedTask;
        if (_emailIndex.TryGetValue(email.ToLower(), out var userId))
        {
            return _users.TryGetValue(userId, out var user) ? user : null;
        }
        return null;
    }

    public async Task<User> UpdateUserAsync(User user)
    {
        if (!_users.ContainsKey(user.Id))
        {
            throw new ArgumentException("User not found");
        }

        var existingUser = _users[user.Id];
        
        // 如果用户名或邮箱发生变化，需要更新索引
        if (existingUser.Username != user.Username)
        {
            _usernameIndex.TryRemove(existingUser.Username.ToLower(), out _);
            _usernameIndex[user.Username.ToLower()] = user.Id;
        }

        if (existingUser.Email != user.Email)
        {
            _emailIndex.TryRemove(existingUser.Email.ToLower(), out _);
            _emailIndex[user.Email.ToLower()] = user.Id;
        }

        user.UpdatedAt = DateTime.UtcNow;
        _users[user.Id] = user;

        _logger.LogInformation("Updated user: {Username} ({UserId})", user.Username, user.Id);
        
        await Task.CompletedTask;
        return user;
    }

    public async Task DeleteUserAsync(Guid userId)
    {
        if (_users.TryRemove(userId, out var user))
        {
            _usernameIndex.TryRemove(user.Username.ToLower(), out _);
            _emailIndex.TryRemove(user.Email.ToLower(), out _);
            
            _logger.LogInformation("Deleted user: {Username} ({UserId})", user.Username, user.Id);
        }
        
        await Task.CompletedTask;
    }

    public async Task<bool> ValidatePasswordAsync(Guid userId, string password)
    {
        await Task.CompletedTask;
        
        if (_users.TryGetValue(userId, out var user))
        {
            return VerifyPassword(password, user.PasswordHash);
        }
        
        return false;
    }

    public async Task UpdatePasswordAsync(Guid userId, string newPassword)
    {
        if (_users.TryGetValue(userId, out var user))
        {
            user.PasswordHash = HashPassword(newPassword);
            user.UpdatedAt = DateTime.UtcNow;
            
            _logger.LogInformation("Updated password for user: {Username} ({UserId})", user.Username, user.Id);
        }
        
        await Task.CompletedTask;
    }

    public async Task<IEnumerable<User>> GetUsersAsync(int page = 1, int pageSize = 50)
    {
        await Task.CompletedTask;
        
        return _users.Values
            .OrderBy(u => u.Username)
            .Skip((page - 1) * pageSize)
            .Take(pageSize);
    }

    private void CreateDefaultAdminUser()
    {
        var adminUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "admin",
            Email = "<EMAIL>",
            FirstName = "System",
            LastName = "Administrator",
            PasswordHash = HashPassword("admin123"),
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        adminUser.Roles.Add(new UserRole
        {
            UserId = adminUser.Id,
            RoleName = "Administrator",
            AssignedBy = "System"
        });

        _users[adminUser.Id] = adminUser;
        _usernameIndex[adminUser.Username.ToLower()] = adminUser.Id;
        _emailIndex[adminUser.Email.ToLower()] = adminUser.Id;

        _logger.LogInformation("Created default admin user: {Username}", adminUser.Username);
    }

    private string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var salt = "FlowCustomSalt"; // 在实际项目中应该使用随机盐值
        var saltedPassword = password + salt;
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
        return Convert.ToBase64String(hashedBytes);
    }

    private bool VerifyPassword(string password, string hash)
    {
        var computedHash = HashPassword(password);
        return computedHash == hash;
    }
}

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using FlowCustom.SDK;

namespace FlowCustom.Plugins.Core
{
    /// <summary>
    /// 脚本执行器插件
    /// 支持执行Python、JavaScript、PowerShell等脚本
    /// </summary>
    public class ScriptExecutorPlugin : NodePluginBase
    {
        public override string NodeType => "script-executor";
        public override string DisplayName => "脚本执行器";
        public override string Description => "执行Python、JavaScript、PowerShell等脚本";
        public override string Category => "执行器";
        public override string Author => "FlowCustom Team";
        public override string Version => "1.0.0";
        public override bool IsTrigger => false;
        public override string Icon => "💻";

        /// <summary>
        /// 获取参数定义
        /// </summary>
        protected override List<ParameterDefinition> GetParameterDefinitions()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "scriptType",
                    DisplayName = "脚本类型",
                    Description = "选择要执行的脚本类型",
                    Type = ParameterType.Select,
                    Required = true,
                    DefaultValue = "python",
                    Options = new List<ParameterOption>
                    {
                        new ParameterOption { Value = "python", Label = "Python", Description = "Python 脚本" },
                        new ParameterOption { Value = "javascript", Label = "JavaScript", Description = "Node.js JavaScript 脚本" },
                        new ParameterOption { Value = "powershell", Label = "PowerShell", Description = "PowerShell 脚本" },
                        new ParameterOption { Value = "bash", Label = "Bash", Description = "Bash 脚本 (Linux/Mac)" },
                        new ParameterOption { Value = "cmd", Label = "CMD", Description = "Windows 命令行" }
                    }
                },
                new ParameterDefinition
                {
                    Name = "script",
                    DisplayName = "脚本内容",
                    Description = "要执行的脚本代码",
                    Type = ParameterType.Code,
                    Required = true,
                    DefaultValue = "print('Hello, World!')"
                },
                new ParameterDefinition
                {
                    Name = "timeout",
                    DisplayName = "超时时间(秒)",
                    Description = "脚本执行的超时时间，0表示无限制",
                    Type = ParameterType.Number,
                    Required = false,
                    DefaultValue = 30,
                    Validation = new Dictionary<string, object>
                    {
                        ["min"] = 0,
                        ["max"] = 3600
                    }
                },
                new ParameterDefinition
                {
                    Name = "workingDirectory",
                    DisplayName = "工作目录",
                    Description = "脚本执行的工作目录，留空使用默认目录",
                    Type = ParameterType.String,
                    Required = false,
                    DefaultValue = ""
                },
                new ParameterDefinition
                {
                    Name = "environmentVariables",
                    DisplayName = "环境变量",
                    Description = "脚本执行时的环境变量 (JSON格式)",
                    Type = ParameterType.Json,
                    Required = false,
                    DefaultValue = "{}"
                }
            };
        }

        /// <summary>
        /// 获取输出端点定义
        /// </summary>
        protected override List<EndpointDefinition> GetOutputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "main",
                    Name = "成功输出",
                    Description = "脚本执行成功时的输出",
                    Type = EndpointType.Success,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 30 },
                    DataType = "object"
                },
                new EndpointDefinition
                {
                    Id = "error",
                    Name = "错误输出",
                    Description = "脚本执行失败时的输出",
                    Type = EndpointType.Error,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 70 },
                    DataType = "error"
                }
            };
        }

        /// <summary>
        /// 执行脚本逻辑
        /// </summary>
        protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
        {
            var scriptType = GetParameter<string>(context, "scriptType", "python");
            var script = GetParameter<string>(context, "script", "");
            var timeout = GetParameter<int>(context, "timeout", 30);
            var workingDirectory = GetParameter<string>(context, "workingDirectory", "");
            var envVarsJson = GetParameter<string>(context, "environmentVariables", "{}");

            Log($"开始执行 {scriptType} 脚本，超时时间: {timeout}秒");

            if (string.IsNullOrWhiteSpace(script))
            {
                return NodeExecutionResult.Failure("脚本内容不能为空");
            }

            try
            {
                // 创建临时脚本文件
                var tempFile = await CreateTempScriptFile(scriptType, script);
                
                try
                {
                    // 执行脚本
                    var result = await ExecuteScriptAsync(scriptType, tempFile, timeout, workingDirectory, envVarsJson, context);
                    return result;
                }
                finally
                {
                    // 清理临时文件
                    try
                    {
                        File.Delete(tempFile);
                    }
                    catch (Exception ex)
                    {
                        Log($"清理临时文件失败: {ex.Message}", LogLevel.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                Log($"脚本执行异常: {ex.Message}", LogLevel.Error);
                return NodeExecutionResult.Failure(ex.Message, ex);
            }
        }

        /// <summary>
        /// 创建临时脚本文件
        /// </summary>
        private async Task<string> CreateTempScriptFile(string scriptType, string script)
        {
            var extension = GetScriptExtension(scriptType);
            var tempFile = Path.Combine(Path.GetTempPath(), $"flowcustom_script_{Guid.NewGuid()}{extension}");
            
            await File.WriteAllTextAsync(tempFile, script);
            Log($"创建临时脚本文件: {tempFile}");
            
            return tempFile;
        }

        /// <summary>
        /// 获取脚本文件扩展名
        /// </summary>
        private string GetScriptExtension(string scriptType)
        {
            return scriptType.ToLower() switch
            {
                "python" => ".py",
                "javascript" => ".js",
                "powershell" => ".ps1",
                "bash" => ".sh",
                "cmd" => ".bat",
                _ => ".txt"
            };
        }

        /// <summary>
        /// 执行脚本
        /// </summary>
        private async Task<NodeExecutionResult> ExecuteScriptAsync(string scriptType, string scriptFile, 
            int timeout, string workingDirectory, string envVarsJson, NodeExecutionContext context)
        {
            var (fileName, arguments) = GetExecutorInfo(scriptType, scriptFile);
            
            var processStartInfo = new ProcessStartInfo
            {
                FileName = fileName,
                Arguments = arguments,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            // 设置工作目录
            if (!string.IsNullOrWhiteSpace(workingDirectory) && Directory.Exists(workingDirectory))
            {
                processStartInfo.WorkingDirectory = workingDirectory;
            }

            // 设置环境变量
            try
            {
                var envVars = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(envVarsJson);
                if (envVars != null)
                {
                    foreach (var kvp in envVars)
                    {
                        processStartInfo.Environment[kvp.Key] = kvp.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                Log($"解析环境变量失败: {ex.Message}", LogLevel.Warning);
            }

            using var process = new Process { StartInfo = processStartInfo };
            
            var outputBuilder = new System.Text.StringBuilder();
            var errorBuilder = new System.Text.StringBuilder();

            process.OutputDataReceived += (sender, e) =>
            {
                if (e.Data != null)
                {
                    outputBuilder.AppendLine(e.Data);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (e.Data != null)
                {
                    errorBuilder.AppendLine(e.Data);
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            var completed = timeout > 0 
                ? await process.WaitForExitAsync(TimeSpan.FromSeconds(timeout))
                : await process.WaitForExitAsync();

            if (!completed)
            {
                process.Kill(true);
                return NodeExecutionResult.Failure($"脚本执行超时 ({timeout}秒)");
            }

            var output = outputBuilder.ToString().Trim();
            var error = errorBuilder.ToString().Trim();
            var exitCode = process.ExitCode;

            Log($"脚本执行完成，退出代码: {exitCode}");

            var outputData = new Dictionary<string, object>
            {
                ["exitCode"] = exitCode,
                ["output"] = output,
                ["error"] = error,
                ["executionTime"] = DateTime.UtcNow,
                ["scriptType"] = scriptType
            };

            if (exitCode == 0)
            {
                Log("脚本执行成功");
                return NodeExecutionResult.Success(outputData, "main");
            }
            else
            {
                Log($"脚本执行失败，错误: {error}", LogLevel.Error);
                return NodeExecutionResult.Success(outputData, "error");
            }
        }

        /// <summary>
        /// 获取执行器信息
        /// </summary>
        private (string fileName, string arguments) GetExecutorInfo(string scriptType, string scriptFile)
        {
            return scriptType.ToLower() switch
            {
                "python" => ("python", $"\"{scriptFile}\""),
                "javascript" => ("node", $"\"{scriptFile}\""),
                "powershell" => ("powershell", $"-ExecutionPolicy Bypass -File \"{scriptFile}\""),
                "bash" => ("bash", $"\"{scriptFile}\""),
                "cmd" => ("cmd", $"/c \"{scriptFile}\""),
                _ => throw new NotSupportedException($"不支持的脚本类型: {scriptType}")
            };
        }

        /// <summary>
        /// 自定义验证逻辑
        /// </summary>
        protected override void OnValidate(NodeConfiguration configuration, 
            List<ValidationError> errors, List<ValidationWarning> warnings)
        {
            // 验证脚本内容
            if (configuration.Parameters.TryGetValue("script", out var scriptObj))
            {
                var script = scriptObj?.ToString() ?? "";
                if (string.IsNullOrWhiteSpace(script))
                {
                    errors.Add(new ValidationError
                    {
                        Field = "script",
                        Message = "脚本内容不能为空",
                        Code = "EMPTY_SCRIPT"
                    });
                }
                else if (script.Length > 100000) // 100KB
                {
                    warnings.Add(new ValidationWarning
                    {
                        Field = "script",
                        Message = "脚本内容过长，可能影响性能",
                        Code = "LARGE_SCRIPT"
                    });
                }
            }

            // 验证超时时间
            if (configuration.Parameters.TryGetValue("timeout", out var timeoutObj))
            {
                if (int.TryParse(timeoutObj?.ToString(), out var timeout))
                {
                    if (timeout < 0)
                    {
                        errors.Add(new ValidationError
                        {
                            Field = "timeout",
                            Message = "超时时间不能为负数",
                            Code = "INVALID_TIMEOUT"
                        });
                    }
                    else if (timeout > 3600)
                    {
                        warnings.Add(new ValidationWarning
                        {
                            Field = "timeout",
                            Message = "超时时间过长，建议不超过1小时",
                            Code = "LONG_TIMEOUT"
                        });
                    }
                }
            }
        }
    }

    /// <summary>
    /// Process 扩展方法
    /// </summary>
    public static class ProcessExtensions
    {
        public static async Task<bool> WaitForExitAsync(this Process process, TimeSpan timeout)
        {
            using var cts = new System.Threading.CancellationTokenSource(timeout);
            try
            {
                await process.WaitForExitAsync(cts.Token);
                return true;
            }
            catch (OperationCanceledException)
            {
                return false;
            }
        }

        public static async Task WaitForExitAsync(this Process process)
        {
            await process.WaitForExitAsync(System.Threading.CancellationToken.None);
        }
    }
}

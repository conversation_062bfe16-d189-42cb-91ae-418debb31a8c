using Microsoft.AspNetCore.Mvc;
using FlowCustom.PluginHost;
using FlowCustom.SDK;
using System.ComponentModel.DataAnnotations;

namespace FlowCustom.Controllers
{
    /// <summary>
    /// 插件管理控制器
    /// 提供插件的查询、安装、卸载等功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PluginsController : ControllerBase
    {
        private readonly IPluginManager _pluginManager;
        private readonly ILogger<PluginsController> _logger;

        public PluginsController(IPluginManager pluginManager, ILogger<PluginsController> logger)
        {
            _pluginManager = pluginManager;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有已加载的插件
        /// </summary>
        /// <returns>插件列表</returns>
        [HttpGet]
        public async Task<ActionResult<List<PluginInfoDto>>> GetPlugins()
        {
            try
            {
                var plugins = _pluginManager.GetAllPlugins();
                var pluginInfos = plugins.Select(plugin => new PluginInfoDto
                {
                    NodeType = plugin.NodeType,
                    DisplayName = plugin.DisplayName,
                    Description = plugin.Description,
                    Category = plugin.Category,
                    Version = plugin.Version,
                    Author = plugin.Author,
                    IsTrigger = plugin.IsTrigger,
                    Icon = plugin.Icon,
                    Definition = MapNodeDefinition(plugin.GetDefinition()),
                    FrontendComponent = plugin.GetFrontendComponent()
                }).ToList();

                return Ok(pluginInfos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取插件列表失败");
                return StatusCode(500, new { message = "获取插件列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 根据节点类型获取插件信息
        /// </summary>
        /// <param name="nodeType">节点类型</param>
        /// <returns>插件信息</returns>
        [HttpGet("{nodeType}")]
        public async Task<ActionResult<PluginInfoDto>> GetPlugin(string nodeType)
        {
            try
            {
                var plugin = _pluginManager.GetPlugin(nodeType);
                if (plugin == null)
                {
                    return NotFound(new { message = $"未找到节点类型为 '{nodeType}' 的插件" });
                }

                var pluginInfo = new PluginInfoDto
                {
                    NodeType = plugin.NodeType,
                    DisplayName = plugin.DisplayName,
                    Description = plugin.Description,
                    Category = plugin.Category,
                    Version = plugin.Version,
                    Author = plugin.Author,
                    IsTrigger = plugin.IsTrigger,
                    Icon = plugin.Icon,
                    Definition = MapNodeDefinition(plugin.GetDefinition()),
                    FrontendComponent = plugin.GetFrontendComponent()
                };

                return Ok(pluginInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取插件信息失败: {nodeType}");
                return StatusCode(500, new { message = "获取插件信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取插件的节点定义
        /// </summary>
        /// <param name="nodeType">节点类型</param>
        /// <returns>节点定义</returns>
        [HttpGet("{nodeType}/definition")]
        public async Task<ActionResult<NodeDefinitionDto>> GetNodeDefinition(string nodeType)
        {
            try
            {
                var plugin = _pluginManager.GetPlugin(nodeType);
                if (plugin == null)
                {
                    return NotFound(new { message = $"未找到节点类型为 '{nodeType}' 的插件" });
                }

                var definition = plugin.GetDefinition();
                var definitionDto = MapNodeDefinition(definition);

                return Ok(definitionDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取节点定义失败: {nodeType}");
                return StatusCode(500, new { message = "获取节点定义失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 验证节点配置
        /// </summary>
        /// <param name="nodeType">节点类型</param>
        /// <param name="request">验证请求</param>
        /// <returns>验证结果</returns>
        [HttpPost("{nodeType}/validate")]
        public async Task<ActionResult<ValidationResultDto>> ValidateNodeConfiguration(
            string nodeType, 
            [FromBody] ValidateNodeConfigurationRequest request)
        {
            try
            {
                var plugin = _pluginManager.GetPlugin(nodeType);
                if (plugin == null)
                {
                    return NotFound(new { message = $"未找到节点类型为 '{nodeType}' 的插件" });
                }

                var configuration = new NodeConfiguration
                {
                    Parameters = request.Parameters ?? new(),
                    IsDisabled = request.IsDisabled,
                    Notes = request.Notes ?? "",
                    CustomInputs = request.CustomInputs?.Select(MapToEndpointDefinition).ToList(),
                    CustomOutputs = request.CustomOutputs?.Select(MapToEndpointDefinition).ToList()
                };

                var validationResult = await plugin.ValidateAsync(configuration);
                
                var resultDto = new ValidationResultDto
                {
                    IsValid = validationResult.IsValid,
                    Errors = validationResult.Errors.Select(e => new ValidationErrorDto
                    {
                        Field = e.Field,
                        Message = e.Message,
                        Code = e.Code
                    }).ToList(),
                    Warnings = validationResult.Warnings.Select(w => new ValidationWarningDto
                    {
                        Field = w.Field,
                        Message = w.Message,
                        Code = w.Code
                    }).ToList()
                };

                return Ok(resultDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证节点配置失败: {nodeType}");
                return StatusCode(500, new { message = "验证节点配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取插件元数据
        /// </summary>
        /// <returns>插件元数据列表</returns>
        [HttpGet("metadata")]
        public async Task<ActionResult<List<PluginMetadataDto>>> GetPluginMetadata()
        {
            try
            {
                var plugins = _pluginManager.GetAllPlugins();
                var metadataList = new List<PluginMetadataDto>();

                foreach (var plugin in plugins)
                {
                    var metadata = _pluginManager.GetPluginMetadata(plugin.NodeType);
                    if (metadata != null)
                    {
                        metadataList.Add(new PluginMetadataDto
                        {
                            Id = metadata.Id,
                            Name = metadata.Name,
                            Version = metadata.Version,
                            Author = metadata.Author,
                            Description = metadata.Description,
                            Dependencies = metadata.Dependencies,
                            SupportedFrameworks = metadata.SupportedFrameworks,
                            Nodes = metadata.Nodes.Select(n => new NodeTypeDefinitionDto
                            {
                                Type = n.Type,
                                ClassName = n.ClassName
                            }).ToList(),
                            Status = _pluginManager.GetPluginStatus(metadata.Id).ToString()
                        });
                    }
                }

                return Ok(metadataList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取插件元数据失败");
                return StatusCode(500, new { message = "获取插件元数据失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 重新加载所有插件
        /// </summary>
        /// <returns>加载结果</returns>
        [HttpPost("reload")]
        public async Task<ActionResult<PluginReloadResultDto>> ReloadPlugins()
        {
            try
            {
                _logger.LogInformation("开始重新加载插件...");
                
                // 卸载现有插件
                await _pluginManager.UnloadPluginsAsync();
                
                // 重新加载插件
                var loadedCount = await _pluginManager.LoadPluginsAsync();
                
                var result = new PluginReloadResultDto
                {
                    Success = true,
                    LoadedCount = loadedCount,
                    Message = $"成功重新加载 {loadedCount} 个插件"
                };

                _logger.LogInformation($"插件重新加载完成: {loadedCount} 个插件");
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新加载插件失败");
                return StatusCode(500, new { 
                    success = false, 
                    message = "重新加载插件失败", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// 映射节点定义
        /// </summary>
        private NodeDefinitionDto MapNodeDefinition(NodeDefinition definition)
        {
            return new NodeDefinitionDto
            {
                NodeType = definition.NodeType,
                DisplayName = definition.DisplayName,
                Description = definition.Description,
                Category = definition.Category,
                Icon = definition.Icon,
                IsTrigger = definition.IsTrigger,
                Parameters = definition.Parameters.Select(p => new ParameterDefinitionDto
                {
                    Name = p.Name,
                    DisplayName = p.DisplayName,
                    Description = p.Description,
                    Type = p.Type.ToString(),
                    Required = p.Required,
                    DefaultValue = p.DefaultValue,
                    Options = p.Options?.Select(o => new ParameterOptionDto
                    {
                        Value = o.Value,
                        Label = o.Label,
                        Description = o.Description
                    }).ToList(),
                    Validation = p.Validation
                }).ToList(),
                Inputs = definition.Inputs.Select(i => new EndpointDefinitionDto
                {
                    Id = i.Id,
                    Name = i.Name,
                    Description = i.Description,
                    Type = i.Type.ToString(),
                    Required = i.Required,
                    Position = new EndpointPositionDto
                    {
                        Side = i.Position.Side,
                        Offset = i.Position.Offset
                    },
                    DataType = i.DataType
                }).ToList(),
                Outputs = definition.Outputs.Select(o => new EndpointDefinitionDto
                {
                    Id = o.Id,
                    Name = o.Name,
                    Description = o.Description,
                    Type = o.Type.ToString(),
                    Required = o.Required,
                    Position = new EndpointPositionDto
                    {
                        Side = o.Position.Side,
                        Offset = o.Position.Offset
                    },
                    DataType = o.DataType
                }).ToList()
            };
        }

        /// <summary>
        /// 映射端点定义
        /// </summary>
        private EndpointDefinition MapToEndpointDefinition(EndpointDefinitionDto dto)
        {
            return new EndpointDefinition
            {
                Id = dto.Id,
                Name = dto.Name,
                Description = dto.Description,
                Type = Enum.TryParse<EndpointType>(dto.Type, out var type) ? type : EndpointType.Data,
                Required = dto.Required,
                Position = new EndpointPosition
                {
                    Side = dto.Position.Side,
                    Offset = dto.Position.Offset
                },
                DataType = dto.DataType
            };
        }
    }

    // DTO 类定义
    public class PluginInfoDto
    {
        public string NodeType { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public bool IsTrigger { get; set; }
        public string Icon { get; set; } = string.Empty;
        public NodeDefinitionDto Definition { get; set; } = null!;
        public FrontendComponentInfo FrontendComponent { get; set; } = null!;
    }

    public class NodeDefinitionDto
    {
        public string NodeType { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public bool IsTrigger { get; set; }
        public List<ParameterDefinitionDto> Parameters { get; set; } = new();
        public List<EndpointDefinitionDto> Inputs { get; set; } = new();
        public List<EndpointDefinitionDto> Outputs { get; set; } = new();
    }

    public class ParameterDefinitionDto
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public bool Required { get; set; }
        public object? DefaultValue { get; set; }
        public List<ParameterOptionDto>? Options { get; set; }
        public Dictionary<string, object> Validation { get; set; } = new();
    }

    public class ParameterOptionDto
    {
        public string Value { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public class EndpointDefinitionDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public bool Required { get; set; }
        public EndpointPositionDto Position { get; set; } = new();
        public string DataType { get; set; } = string.Empty;
    }

    public class EndpointPositionDto
    {
        public string Side { get; set; } = string.Empty;
        public int Offset { get; set; }
    }

    public class ValidateNodeConfigurationRequest
    {
        public Dictionary<string, object> Parameters { get; set; } = new();
        public bool IsDisabled { get; set; }
        public string? Notes { get; set; }
        public List<EndpointDefinitionDto>? CustomInputs { get; set; }
        public List<EndpointDefinitionDto>? CustomOutputs { get; set; }
    }

    public class ValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<ValidationErrorDto> Errors { get; set; } = new();
        public List<ValidationWarningDto> Warnings { get; set; } = new();
    }

    public class ValidationErrorDto
    {
        public string Field { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Code { get; set; }
    }

    public class ValidationWarningDto
    {
        public string Field { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Code { get; set; }
    }

    public class PluginMetadataDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<string> Dependencies { get; set; } = new();
        public List<string> SupportedFrameworks { get; set; } = new();
        public List<NodeTypeDefinitionDto> Nodes { get; set; } = new();
        public string Status { get; set; } = string.Empty;
    }

    public class NodeTypeDefinitionDto
    {
        public string Type { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
    }

    public class PluginReloadResultDto
    {
        public bool Success { get; set; }
        public int LoadedCount { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}

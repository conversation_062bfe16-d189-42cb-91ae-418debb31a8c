/**
 * 节点端点配置
 *
 * 定义不同节点类型的输入输出端点
 * 支持灵活的多端点连线
 * 现在支持从插件系统动态加载端点配置
 */

import type { NodeEndpoint } from '../types/workflow';

// 插件端点缓存
let pluginEndpointsCache: Map<string, { inputs: NodeEndpoint[], outputs: NodeEndpoint[] }> = new Map();

/**
 * 节点端点配置映射
 * 
 * 键：节点类型
 * 值：{ inputs: 输入端点数组, outputs: 输出端点数组 }
 */
export const NODE_ENDPOINTS: Record<string, { inputs: NodeEndpoint[], outputs: NodeEndpoint[] }> = {
  // 手动触发器：只有输出，没有输入
  'manual-trigger': {
    inputs: [],
    outputs: [
      {
        id: 'main',
        name: '主输出',
        description: '触发器激活时的输出',
        type: 'trigger',
        position: { side: 'bottom', offset: 50 }
      }
    ]
  },

  // 脚本执行器：一个输入，一个主输出，一个错误输出
  'script-executor': {
    inputs: [
      {
        id: 'main',
        name: '主输入',
        description: '脚本执行的输入数据',
        type: 'data',
        required: true,
        position: { side: 'top', offset: 50 }
      }
    ],
    outputs: [
      {
        id: 'main',
        name: '成功输出',
        description: '脚本执行成功时的输出',
        type: 'data',
        position: { side: 'bottom', offset: 30 }
      },
      {
        id: 'error',
        name: '错误输出',
        description: '脚本执行失败时的输出',
        type: 'error',
        position: { side: 'bottom', offset: 70 }
      }
    ]
  },

  // HTTP请求：一个输入，成功和失败两个输出
  'http-request': {
    inputs: [
      {
        id: 'main',
        name: '请求输入',
        description: 'HTTP请求的输入数据',
        type: 'data',
        required: true,
        position: { side: 'top', offset: 50 }
      }
    ],
    outputs: [
      {
        id: 'success',
        name: '成功响应',
        description: 'HTTP请求成功时的响应',
        type: 'data',
        position: { side: 'bottom', offset: 30 }
      },
      {
        id: 'error',
        name: '错误响应',
        description: 'HTTP请求失败时的错误信息',
        type: 'error',
        position: { side: 'bottom', offset: 70 }
      }
    ]
  },

  // 条件判断：一个输入，两个输出（真/假）
  'conditional': {
    inputs: [
      {
        id: 'main',
        name: '条件输入',
        description: '用于条件判断的输入数据',
        type: 'data',
        required: true,
        position: { side: 'top', offset: 50 }
      }
    ],
    outputs: [
      {
        id: 'true',
        name: '条件为真',
        description: '条件判断为真时的输出',
        type: 'data',
        position: { side: 'bottom', offset: 25 }
      },
      {
        id: 'false',
        name: '条件为假',
        description: '条件判断为假时的输出',
        type: 'data',
        position: { side: 'bottom', offset: 75 }
      }
    ]
  },

  // 数据合并：多个输入，一个输出
  'data-merge': {
    inputs: [
      {
        id: 'input1',
        name: '输入1',
        description: '第一个数据输入',
        type: 'data',
        position: { side: 'left', offset: 25 }
      },
      {
        id: 'input2',
        name: '输入2',
        description: '第二个数据输入',
        type: 'data',
        position: { side: 'left', offset: 50 }
      },
      {
        id: 'input3',
        name: '输入3',
        description: '第三个数据输入',
        type: 'data',
        position: { side: 'left', offset: 75 }
      }
    ],
    outputs: [
      {
        id: 'merged',
        name: '合并输出',
        description: '合并后的数据输出',
        type: 'data',
        position: { side: 'right', offset: 50 }
      }
    ]
  },

  // 数据分发：一个输入，多个输出
  'data-split': {
    inputs: [
      {
        id: 'main',
        name: '数据输入',
        description: '需要分发的数据',
        type: 'data',
        required: true,
        position: { side: 'left', offset: 50 }
      }
    ],
    outputs: [
      {
        id: 'output1',
        name: '输出1',
        description: '第一个数据输出',
        type: 'data',
        position: { side: 'right', offset: 20 }
      },
      {
        id: 'output2',
        name: '输出2',
        description: '第二个数据输出',
        type: 'data',
        position: { side: 'right', offset: 40 }
      },
      {
        id: 'output3',
        name: '输出3',
        description: '第三个数据输出',
        type: 'data',
        position: { side: 'right', offset: 60 }
      },
      {
        id: 'output4',
        name: '输出4',
        description: '第四个数据输出',
        type: 'data',
        position: { side: 'right', offset: 80 }
      }
    ]
  },

  // 循环节点：输入、循环体输出、完成输出
  'loop': {
    inputs: [
      {
        id: 'main',
        name: '循环输入',
        description: '循环处理的数据',
        type: 'data',
        required: true,
        position: { side: 'top', offset: 50 }
      }
    ],
    outputs: [
      {
        id: 'item',
        name: '循环项',
        description: '当前循环项的输出',
        type: 'data',
        position: { side: 'bottom', offset: 30 }
      },
      {
        id: 'done',
        name: '循环完成',
        description: '循环结束时的输出',
        type: 'data',
        position: { side: 'bottom', offset: 70 }
      }
    ]
  }
};

/**
 * 获取节点类型的端点配置
 *
 * @param nodeType 节点类型
 * @returns 端点配置，如果未找到则返回默认配置
 */
export function getNodeEndpoints(nodeType: string): { inputs: NodeEndpoint[], outputs: NodeEndpoint[] } {
  const config = NODE_ENDPOINTS[nodeType];

  if (config) {
    return config;
  }

  // 检查是否为触发器类型（以-trigger结尾）
  const isTrigger = nodeType.endsWith('-trigger');

  if (isTrigger) {
    // 触发器节点：只有输出，没有输入
    return {
      inputs: [],
      outputs: [
        {
          id: 'main',
          name: '触发输出',
          description: '触发器激活时的输出',
          type: 'trigger',
          position: { side: 'bottom', offset: 50 }
        }
      ]
    };
  }

  // 默认配置：一个输入，一个输出
  return {
    inputs: [
      {
        id: 'main',
        name: '输入',
        description: '节点输入',
        type: 'data',
        position: { side: 'top', offset: 50 }
      }
    ],
    outputs: [
      {
        id: 'main',
        name: '输出',
        description: '节点输出',
        type: 'data',
        position: { side: 'bottom', offset: 50 }
      }
    ]
  };
}

/**
 * 检查节点是否为触发器类型
 *
 * @param nodeType 节点类型
 * @returns 是否为触发器
 */
export function isTriggerNode(nodeType: string): boolean {
  const config = NODE_ENDPOINTS[nodeType];
  return config ? config.inputs.length === 0 : false;
}

/**
 * 从插件API获取端点配置
 */
async function fetchPluginEndpoints(nodeType: string): Promise<{ inputs: NodeEndpoint[], outputs: NodeEndpoint[] } | null> {
  try {
    const response = await fetch(`/api/plugins/${nodeType}/definition`);
    if (!response.ok) {
      return null;
    }

    const definition = await response.json();
    return {
      inputs: definition.inputs || [],
      outputs: definition.outputs || []
    };
  } catch (error) {
    console.warn(`获取插件端点配置失败: ${nodeType}`, error);
    return null;
  }
}

/**
 * 异步获取节点的端点配置（支持插件）
 * @param nodeType 节点类型
 * @returns 端点配置
 */
export async function getNodeEndpointsAsync(nodeType: string): Promise<{ inputs: NodeEndpoint[], outputs: NodeEndpoint[] }> {
  // 首先检查插件缓存
  if (pluginEndpointsCache.has(nodeType)) {
    return pluginEndpointsCache.get(nodeType)!;
  }

  // 尝试从插件API获取
  const pluginEndpoints = await fetchPluginEndpoints(nodeType);
  if (pluginEndpoints) {
    // 缓存插件端点配置
    pluginEndpointsCache.set(nodeType, pluginEndpoints);
    return pluginEndpoints;
  }

  // 检查静态配置
  const config = nodeEndpointsConfig[nodeType];
  if (config) {
    return config;
  }

  // 如果没有找到配置，返回默认配置
  console.warn(`未找到节点类型 ${nodeType} 的端点配置，使用默认配置`);
  return getDefaultEndpoints(nodeType);
}

/**
 * 清除插件端点缓存
 */
export function clearPluginEndpointsCache(): void {
  pluginEndpointsCache.clear();
}

/**
 * 预加载所有插件端点配置
 */
export async function preloadPluginEndpoints(): Promise<void> {
  try {
    const response = await fetch('/api/plugins');
    if (!response.ok) {
      return;
    }

    const plugins = await response.json();
    const loadPromises = plugins.map(async (plugin: any) => {
      const endpoints = await fetchPluginEndpoints(plugin.nodeType);
      if (endpoints) {
        pluginEndpointsCache.set(plugin.nodeType, endpoints);
      }
    });

    await Promise.all(loadPromises);
    console.log(`预加载了 ${pluginEndpointsCache.size} 个插件的端点配置`);
  } catch (error) {
    console.warn('预加载插件端点配置失败', error);
  }
}

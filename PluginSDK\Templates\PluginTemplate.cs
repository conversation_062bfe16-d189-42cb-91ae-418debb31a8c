using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlowCustom.SDK;

namespace FlowCustom.Plugins.Template
{
    /// <summary>
    /// 插件模板
    /// 
    /// 这是一个插件开发模板，展示了如何创建一个基本的节点插件
    /// 复制此模板并根据您的需求进行修改
    /// </summary>
    public class TemplatePlugin : NodePluginBase
    {
        #region 基本信息
        
        /// <summary>
        /// 节点类型标识符（必须唯一）
        /// 建议使用 "公司名-插件名-节点名" 的格式，如 "mycompany-http-request"
        /// </summary>
        public override string NodeType => "template-example";

        /// <summary>
        /// 节点显示名称
        /// 用户在界面上看到的名称
        /// </summary>
        public override string DisplayName => "模板示例";

        /// <summary>
        /// 节点描述
        /// 简要说明节点的功能
        /// </summary>
        public override string Description => "这是一个插件开发模板示例";

        /// <summary>
        /// 节点分类
        /// 用于在节点面板中分组显示
        /// </summary>
        public override string Category => "示例";

        /// <summary>
        /// 插件版本
        /// 遵循语义化版本规范
        /// </summary>
        public override string Version => "1.0.0";

        /// <summary>
        /// 插件作者
        /// </summary>
        public override string Author => "Your Name";

        /// <summary>
        /// 是否为触发器节点
        /// 触发器节点没有输入端点，用于启动工作流
        /// </summary>
        public override bool IsTrigger => false;

        /// <summary>
        /// 节点图标
        /// 可以使用 Emoji 或者图标字体
        /// </summary>
        public override string Icon => "🔧";

        #endregion

        #region 参数定义

        /// <summary>
        /// 定义节点参数
        /// 这些参数将在配置面板中显示，供用户配置
        /// </summary>
        protected override List<ParameterDefinition> GetParameterDefinitions()
        {
            return new List<ParameterDefinition>
            {
                // 文本参数示例
                new ParameterDefinition
                {
                    Name = "message",
                    DisplayName = "消息内容",
                    Description = "要处理的消息内容",
                    Type = ParameterType.String,
                    Required = true,
                    DefaultValue = "Hello, World!"
                },

                // 数字参数示例
                new ParameterDefinition
                {
                    Name = "timeout",
                    DisplayName = "超时时间(秒)",
                    Description = "操作的超时时间",
                    Type = ParameterType.Number,
                    Required = false,
                    DefaultValue = 30,
                    Validation = new Dictionary<string, object>
                    {
                        ["min"] = 1,
                        ["max"] = 300
                    }
                },

                // 布尔参数示例
                new ParameterDefinition
                {
                    Name = "enabled",
                    DisplayName = "启用处理",
                    Description = "是否启用消息处理",
                    Type = ParameterType.Boolean,
                    Required = false,
                    DefaultValue = true
                },

                // 选择参数示例
                new ParameterDefinition
                {
                    Name = "mode",
                    DisplayName = "处理模式",
                    Description = "选择消息处理模式",
                    Type = ParameterType.Select,
                    Required = true,
                    DefaultValue = "normal",
                    Options = new List<ParameterOption>
                    {
                        new ParameterOption { Value = "normal", Label = "普通模式", Description = "标准处理模式" },
                        new ParameterOption { Value = "fast", Label = "快速模式", Description = "快速处理，可能降低质量" },
                        new ParameterOption { Value = "detailed", Label = "详细模式", Description = "详细处理，耗时较长" }
                    }
                },

                // JSON参数示例
                new ParameterDefinition
                {
                    Name = "config",
                    DisplayName = "配置对象",
                    Description = "JSON格式的配置参数",
                    Type = ParameterType.Json,
                    Required = false,
                    DefaultValue = "{\"key\": \"value\"}"
                }
            };
        }

        #endregion

        #region 端点定义

        /// <summary>
        /// 定义输入端点
        /// 如果是触发器节点，可以返回空列表
        /// </summary>
        protected override List<EndpointDefinition> GetInputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "main",
                    Name = "主输入",
                    Description = "主要数据输入",
                    Type = EndpointType.Data,
                    Required = true,
                    Position = new EndpointPosition { Side = "top", Offset = 50 },
                    DataType = "object"
                },

                // 可选的额外输入端点
                new EndpointDefinition
                {
                    Id = "config",
                    Name = "配置输入",
                    Description = "配置参数输入",
                    Type = EndpointType.Data,
                    Required = false,
                    Position = new EndpointPosition { Side = "left", Offset = 30 },
                    DataType = "object"
                }
            };
        }

        /// <summary>
        /// 定义输出端点
        /// 可以定义多个输出端点用于不同的执行路径
        /// </summary>
        protected override List<EndpointDefinition> GetOutputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "success",
                    Name = "成功输出",
                    Description = "处理成功时的输出",
                    Type = EndpointType.Success,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 30 },
                    DataType = "object"
                },

                new EndpointDefinition
                {
                    Id = "error",
                    Name = "错误输出",
                    Description = "处理失败时的输出",
                    Type = EndpointType.Error,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 70 },
                    DataType = "error"
                }
            };
        }

        #endregion

        #region 核心逻辑

        /// <summary>
        /// 核心执行逻辑
        /// 这是插件的主要业务逻辑实现
        /// </summary>
        protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
        {
            // 获取参数值
            var message = GetParameter<string>(context, "message", "");
            var timeout = GetParameter<int>(context, "timeout", 30);
            var enabled = GetParameter<bool>(context, "enabled", true);
            var mode = GetParameter<string>(context, "mode", "normal");
            var configJson = GetParameter<string>(context, "config", "{}");

            Log($"开始处理消息: {message}, 模式: {mode}, 启用: {enabled}");

            // 检查是否启用
            if (!enabled)
            {
                Log("处理已禁用，跳过执行", LogLevel.Warning);
                return NodeExecutionResult.Success(new Dictionary<string, object>
                {
                    ["skipped"] = true,
                    ["reason"] = "处理已禁用"
                }, "success");
            }

            try
            {
                // 解析配置
                var config = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(configJson);
                
                // 获取输入数据
                var inputData = context.InputData;
                Log($"接收到输入数据: {inputData.Count} 项");

                // 模拟处理逻辑
                await Task.Delay(1000, context.CancellationToken); // 模拟耗时操作

                // 根据模式处理
                var result = mode switch
                {
                    "fast" => ProcessFast(message, inputData),
                    "detailed" => ProcessDetailed(message, inputData),
                    _ => ProcessNormal(message, inputData)
                };

                // 添加处理元数据
                result["processedAt"] = DateTime.UtcNow;
                result["mode"] = mode;
                result["nodeId"] = context.NodeId;

                Log($"处理完成，输出数据: {result.Count} 项");

                return NodeExecutionResult.Success(result, "success");
            }
            catch (OperationCanceledException)
            {
                Log("操作被取消", LogLevel.Warning);
                return NodeExecutionResult.Failure("操作被取消");
            }
            catch (Exception ex)
            {
                Log($"处理失败: {ex.Message}", LogLevel.Error);
                
                var errorData = new Dictionary<string, object>
                {
                    ["error"] = ex.Message,
                    ["errorType"] = ex.GetType().Name,
                    ["timestamp"] = DateTime.UtcNow
                };

                return NodeExecutionResult.Success(errorData, "error");
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 普通模式处理
        /// </summary>
        private Dictionary<string, object> ProcessNormal(string message, Dictionary<string, object> inputData)
        {
            return new Dictionary<string, object>
            {
                ["processedMessage"] = $"[NORMAL] {message}",
                ["inputCount"] = inputData.Count,
                ["processingTime"] = "normal"
            };
        }

        /// <summary>
        /// 快速模式处理
        /// </summary>
        private Dictionary<string, object> ProcessFast(string message, Dictionary<string, object> inputData)
        {
            return new Dictionary<string, object>
            {
                ["processedMessage"] = $"[FAST] {message}",
                ["inputCount"] = inputData.Count,
                ["processingTime"] = "fast"
            };
        }

        /// <summary>
        /// 详细模式处理
        /// </summary>
        private Dictionary<string, object> ProcessDetailed(string message, Dictionary<string, object> inputData)
        {
            return new Dictionary<string, object>
            {
                ["processedMessage"] = $"[DETAILED] {message}",
                ["inputCount"] = inputData.Count,
                ["processingTime"] = "detailed",
                ["details"] = new Dictionary<string, object>
                {
                    ["messageLength"] = message.Length,
                    ["hasInput"] = inputData.Count > 0,
                    ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
                }
            };
        }

        #endregion

        #region 验证和生命周期

        /// <summary>
        /// 自定义验证逻辑
        /// </summary>
        protected override void OnValidate(NodeConfiguration configuration, 
            List<ValidationError> errors, List<ValidationWarning> warnings)
        {
            // 验证消息内容
            if (configuration.Parameters.TryGetValue("message", out var messageObj))
            {
                var message = messageObj?.ToString() ?? "";
                if (string.IsNullOrWhiteSpace(message))
                {
                    errors.Add(new ValidationError
                    {
                        Field = "message",
                        Message = "消息内容不能为空",
                        Code = "EMPTY_MESSAGE"
                    });
                }
                else if (message.Length > 1000)
                {
                    warnings.Add(new ValidationWarning
                    {
                        Field = "message",
                        Message = "消息内容过长，可能影响性能",
                        Code = "LONG_MESSAGE"
                    });
                }
            }

            // 验证JSON配置
            if (configuration.Parameters.TryGetValue("config", out var configObj))
            {
                var configJson = configObj?.ToString() ?? "{}";
                try
                {
                    System.Text.Json.JsonSerializer.Deserialize<object>(configJson);
                }
                catch
                {
                    errors.Add(new ValidationError
                    {
                        Field = "config",
                        Message = "配置不是有效的JSON格式",
                        Code = "INVALID_JSON"
                    });
                }
            }
        }

        /// <summary>
        /// 插件初始化
        /// </summary>
        protected override void OnInitialize()
        {
            Log("模板插件初始化完成");
        }

        /// <summary>
        /// 资源清理
        /// </summary>
        protected override void OnDispose()
        {
            Log("模板插件资源清理完成");
        }

        #endregion
    }
}

/*
 * 插件开发指南
 *
 * 1. 基本步骤：
 *    - 继承 NodePluginBase 类
 *    - 实现必要的属性和方法
 *    - 定义参数和端点
 *    - 实现核心执行逻辑
 *
 * 2. 重要方法：
 *    - OnExecuteAsync: 核心业务逻辑
 *    - GetParameterDefinitions: 定义用户可配置的参数
 *    - GetInputDefinitions/GetOutputDefinitions: 定义端点
 *    - OnValidate: 自定义验证逻辑
 *
 * 3. 最佳实践：
 *    - 使用有意义的节点类型标识符
 *    - 提供详细的参数描述
 *    - 实现适当的错误处理
 *    - 添加日志记录
 *    - 支持取消操作
 *
 * 4. 部署：
 *    - 编译为 DLL
 *    - 创建 plugin.json 清单文件
 *    - 放置到插件目录
 *    - 重新加载插件
 */

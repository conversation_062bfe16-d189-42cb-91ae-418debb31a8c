{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "FlowCustomV1": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/flowcustomv1-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}]}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=flowcustomv1.db", "Redis": "localhost:6379"}, "JWT": {"Secret": "FlowCustomV1JwtSecretKey123456789ABCDEF", "ExpiryHours": 24, "Issuer": "FlowCustomV1", "Audience": "FlowCustomV1"}, "FlowCustomV1": {"Plugins": {"Directory": "plugins", "AutoLoad": true, "EnableHotReload": true, "LoadTimeout": "00:00:30"}, "Execution": {"MaxWorkflowExecutionTime": "01:00:00", "MaxConcurrentExecutions": 10, "DefaultTimeout": "00:05:00", "EnableParallelExecution": true, "MaxRetries": 3}, "Storage": {"DataRetentionDays": 30, "MaxExecutionHistorySize": 10000, "EnableCompression": true}, "Security": {"RequireHttps": false, "EnableCors": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"], "EnableRateLimiting": true, "RateLimitRequests": 100, "RateLimitWindow": "00:01:00", "EnableApiKeys": true}, "Features": {"EnableUserRegistration": true, "EnableTeams": true, "EnableWorkflowSharing": true, "EnableExecutionHistory": true, "EnableRealTimeMonitoring": true, "EnableMetrics": true, "EnableAuditLogging": true}, "FileUpload": {"MaxFileSize": 104857600, "AllowedExtensions": [".txt", ".json", ".csv", ".xml", ".yaml", ".yml", ".zip"], "UploadPath": "./uploads"}, "Monitoring": {"EnableHealthChecks": true, "EnableMetrics": true, "MetricsInterval": "00:00:30", "EnablePerformanceCounters": true}, "Logging": {"EnableStructuredLogging": true, "LogLevel": "Information", "EnableSensitiveDataLogging": false, "LogRetentionDays": 30}}}
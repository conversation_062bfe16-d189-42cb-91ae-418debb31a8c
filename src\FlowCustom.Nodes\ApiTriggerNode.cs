using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Nodes;

/// <summary>
/// API 调用触发器节点
/// </summary>
public class ApiTriggerNode : ITriggerNode
{
    private readonly ILogger<ApiTriggerNode> _logger;
    private static readonly ConcurrentDictionary<string, ApiTriggerNode> _activeApiTriggers = new();

    public ApiTriggerNode(ILogger<ApiTriggerNode> logger)
    {
        _logger = logger;
    }

    public string NodeType => "api-trigger";
    public string DisplayName => "API触发器";
    public string Description => "通过REST API端点触发工作流";
    public string Category => "触发器";
    public string Icon => "api";

    public event EventHandler<TriggerEventArgs>? Triggered;

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "endpoint",
                DisplayName = "API端点",
                Description = "自定义端点路径（例如：/api/trigger/my-workflow）",
                Type = ParameterType.String,
                Required = true
            },
            new NodeParameterDefinition
            {
                Name = "httpMethods",
                DisplayName = "HTTP方法",
                Description = "允许的HTTP方法",
                Type = ParameterType.MultiSelect,
                Required = false,
                DefaultValue = new[] { "POST" },
                Options = new[] { "GET", "POST", "PUT", "DELETE", "PATCH" }
            },
            new NodeParameterDefinition
            {
                Name = "requireAuth",
                DisplayName = "需要认证",
                Description = "是否需要身份验证",
                Type = ParameterType.Boolean,
                Required = false,
                DefaultValue = false
            },
            new NodeParameterDefinition
            {
                Name = "apiKey",
                DisplayName = "API密钥",
                Description = "用于身份验证的API密钥（如果需要）",
                Type = ParameterType.String,
                Required = false
            },
            new NodeParameterDefinition
            {
                Name = "responseMessage",
                DisplayName = "响应消息",
                Description = "自定义响应消息",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = "工作流触发成功"
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node, 
        FlowCustom.Core.Models.ExecutionContext context, 
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        return NodeExecutionResult.CreateSuccess(new Dictionary<string, object>
        {
            ["message"] = "API trigger is active",
            ["endpoint"] = GetParameter<string>(node, "endpoint", ""),
            ["methods"] = GetParameter<string[]>(node, "httpMethods", new[] { "POST" })
        });
    }

    public async Task StartAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        var endpoint = GetParameter<string>(node, "endpoint");
        
        if (string.IsNullOrEmpty(endpoint))
        {
            throw new ArgumentException("API endpoint is required");
        }

        // 确保端点以 / 开头
        if (!endpoint.StartsWith("/"))
        {
            endpoint = "/" + endpoint;
        }

        var triggerKey = $"{node.WorkflowId}:{endpoint}";
        
        if (_activeApiTriggers.TryAdd(triggerKey, this))
        {
            _logger.LogInformation("Started API trigger: {Endpoint} for workflow: {WorkflowId}", 
                endpoint, node.WorkflowId);
        }
        else
        {
            _logger.LogWarning("API endpoint already in use: {Endpoint}", endpoint);
        }

        await Task.CompletedTask;
    }

    public async Task StopAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        var endpoint = GetParameter<string>(node, "endpoint");
        
        if (string.IsNullOrEmpty(endpoint))
        {
            return;
        }

        if (!endpoint.StartsWith("/"))
        {
            endpoint = "/" + endpoint;
        }

        var triggerKey = $"{node.WorkflowId}:{endpoint}";
        
        if (_activeApiTriggers.TryRemove(triggerKey, out _))
        {
            _logger.LogInformation("Stopped API trigger: {Endpoint} for workflow: {WorkflowId}", 
                endpoint, node.WorkflowId);
        }

        await Task.CompletedTask;
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.ContainsKey("endpoint") || 
            string.IsNullOrEmpty(parameters["endpoint"]?.ToString()))
        {
            errors.Add("API endpoint is required");
        }

        return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
    }

    /// <summary>
    /// 处理传入的 API 请求
    /// </summary>
    public static async Task<(bool Success, string Response)> HandleApiRequest(
        string path, 
        string method, 
        Dictionary<string, object> data,
        Dictionary<string, string> headers,
        string? apiKey = null)
    {
        var matchingTriggers = _activeApiTriggers.Where(kvp => 
        {
            var parts = kvp.Key.Split(':');
            return parts.Length > 1 && parts[1] == path;
        }).ToList();

        if (!matchingTriggers.Any())
        {
            return (false, "API endpoint not found");
        }

        foreach (var trigger in matchingTriggers)
        {
            try
            {
                var parts = trigger.Key.Split(':');
                var workflowId = Guid.Parse(parts[0]);

                var triggerArgs = new TriggerEventArgs
                {
                    WorkflowId = workflowId,
                    NodeId = Guid.NewGuid(), // TODO: 获取实际的节点ID
                    Data = new Dictionary<string, object>
                    {
                        ["method"] = method,
                        ["path"] = path,
                        ["headers"] = headers,
                        ["body"] = data,
                        ["timestamp"] = DateTime.UtcNow,
                        ["apiKey"] = apiKey ?? ""
                    },
                    Source = "api"
                };

                trigger.Value.Triggered?.Invoke(trigger.Value, triggerArgs);
                return (true, "Workflow triggered successfully");
            }
            catch (Exception ex)
            {
                return (false, $"Error triggering workflow: {ex.Message}");
            }
        }

        return (false, "No active triggers found");
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

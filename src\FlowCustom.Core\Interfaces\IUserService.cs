using FlowCustom.Core.Models;

namespace FlowCustom.Core.Interfaces;

/// <summary>
/// 用户服务接口
/// </summary>
public interface IUserService
{
    /// <summary>
    /// 创建用户
    /// </summary>
    Task<User> CreateUserAsync(User user, string password);
    
    /// <summary>
    /// 获取用户
    /// </summary>
    Task<User?> GetUserAsync(Guid userId);
    
    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    Task<User?> GetUserByUsernameAsync(string username);
    
    /// <summary>
    /// 根据邮箱获取用户
    /// </summary>
    Task<User?> GetUserByEmailAsync(string email);
    
    /// <summary>
    /// 更新用户
    /// </summary>
    Task<User> UpdateUserAsync(User user);
    
    /// <summary>
    /// 删除用户
    /// </summary>
    Task DeleteUserAsync(Guid userId);
    
    /// <summary>
    /// 验证用户密码
    /// </summary>
    Task<bool> ValidatePasswordAsync(Guid userId, string password);
    
    /// <summary>
    /// 更新用户密码
    /// </summary>
    Task UpdatePasswordAsync(Guid userId, string newPassword);
    
    /// <summary>
    /// 获取用户列表
    /// </summary>
    Task<IEnumerable<User>> GetUsersAsync(int page = 1, int pageSize = 50);
}

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// 用户登录
    /// </summary>
    Task<AuthResult> LoginAsync(string username, string password);
    
    /// <summary>
    /// 刷新令牌
    /// </summary>
    Task<AuthResult> RefreshTokenAsync(string refreshToken);
    
    /// <summary>
    /// 用户登出
    /// </summary>
    Task LogoutAsync(string token);
    
    /// <summary>
    /// 验证令牌
    /// </summary>
    Task<TokenValidationResult> ValidateTokenAsync(string token);
    
    /// <summary>
    /// 生成 API 密钥
    /// </summary>
    Task<ApiKey> GenerateApiKeyAsync(Guid userId, string name, List<string> scopes, DateTime? expiresAt = null);
    
    /// <summary>
    /// 验证 API 密钥
    /// </summary>
    Task<ApiKeyValidationResult> ValidateApiKeyAsync(string apiKey);
}

/// <summary>
/// 权限服务接口
/// </summary>
public interface IPermissionService
{
    /// <summary>
    /// 检查用户权限
    /// </summary>
    Task<bool> HasPermissionAsync(Guid userId, string resource, string action);
    
    /// <summary>
    /// 检查工作流权限
    /// </summary>
    Task<bool> HasWorkflowPermissionAsync(Guid userId, Guid workflowId, WorkflowAccessLevel requiredLevel);
    
    /// <summary>
    /// 授予工作流权限
    /// </summary>
    Task GrantWorkflowPermissionAsync(Guid workflowId, Guid userId, WorkflowAccessLevel accessLevel, string grantedBy);
    
    /// <summary>
    /// 撤销工作流权限
    /// </summary>
    Task RevokeWorkflowPermissionAsync(Guid workflowId, Guid userId);
    
    /// <summary>
    /// 获取用户角色
    /// </summary>
    Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId);
    
    /// <summary>
    /// 分配角色给用户
    /// </summary>
    Task AssignRoleAsync(Guid userId, string roleName, string assignedBy);
    
    /// <summary>
    /// 移除用户角色
    /// </summary>
    Task RemoveRoleAsync(Guid userId, string roleName);
}

/// <summary>
/// 团队服务接口
/// </summary>
public interface ITeamService
{
    /// <summary>
    /// 创建团队
    /// </summary>
    Task<Team> CreateTeamAsync(Team team);
    
    /// <summary>
    /// 获取团队
    /// </summary>
    Task<Team?> GetTeamAsync(Guid teamId);
    
    /// <summary>
    /// 更新团队
    /// </summary>
    Task<Team> UpdateTeamAsync(Team team);
    
    /// <summary>
    /// 删除团队
    /// </summary>
    Task DeleteTeamAsync(Guid teamId);
    
    /// <summary>
    /// 添加团队成员
    /// </summary>
    Task AddMemberAsync(Guid teamId, Guid userId, TeamRole role, string invitedBy);
    
    /// <summary>
    /// 移除团队成员
    /// </summary>
    Task RemoveMemberAsync(Guid teamId, Guid userId);
    
    /// <summary>
    /// 获取用户团队
    /// </summary>
    Task<IEnumerable<Team>> GetUserTeamsAsync(Guid userId);
}

/// <summary>
/// 审计服务接口
/// </summary>
public interface IAuditService
{
    /// <summary>
    /// 记录审计日志
    /// </summary>
    Task LogAsync(AuditLog auditLog);
    
    /// <summary>
    /// 获取审计日志
    /// </summary>
    Task<IEnumerable<AuditLog>> GetAuditLogsAsync(
        Guid? userId = null,
        string? resource = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int page = 1,
        int pageSize = 50);
}

/// <summary>
/// 认证结果
/// </summary>
public class AuthResult
{
    public bool Success { get; set; }
    public string? AccessToken { get; set; }
    public string? RefreshToken { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public User? User { get; set; }
    public string? Error { get; set; }
}

/// <summary>
/// 令牌验证结果
/// </summary>
public class TokenValidationResult
{
    public bool IsValid { get; set; }
    public Guid? UserId { get; set; }
    public string? Username { get; set; }
    public List<string> Roles { get; set; } = new();
    public string? Error { get; set; }
}

/// <summary>
/// API 密钥验证结果
/// </summary>
public class ApiKeyValidationResult
{
    public bool IsValid { get; set; }
    public Guid? UserId { get; set; }
    public List<string> Scopes { get; set; } = new();
    public string? Error { get; set; }
}

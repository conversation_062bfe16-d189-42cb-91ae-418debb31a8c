#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连线显示问题修复验证脚本
专门测试前端连线显示的修复
"""

import requests
import json
import uuid
import time

# API 基础URL
BASE_URL = "http://localhost:5053/api"

def create_test_workflow_with_connections():
    """创建一个带连线的测试工作流"""
    print("🔗 创建带连线的测试工作流...")
    
    # 1. 创建工作流
    workflow_data = {
        "name": "连线显示测试工作流",
        "description": "专门测试前端连线显示修复"
    }
    
    response = requests.post(f"{BASE_URL}/workflow", json=workflow_data)
    if response.status_code != 201:
        print(f"❌ 工作流创建失败: {response.status_code}")
        return None
    
    workflow = response.json()
    workflow_id = workflow["id"]
    print(f"✅ 工作流创建成功: {workflow_id}")
    
    # 2. 创建简单的节点结构
    node1_id = str(uuid.uuid4())
    node2_id = str(uuid.uuid4())
    node3_id = str(uuid.uuid4())
    
    nodes = [
        {
            "id": node1_id,
            "workflowId": workflow_id,
            "name": "🚀 开始节点",
            "type": "manual-trigger",
            "position": {"x": 100, "y": 200},
            "parameters": {
                "buttonText": "开始测试",
                "enabled": True
            },
            "isDisabled": False,
            "notes": "测试起始节点"
        },
        {
            "id": node2_id,
            "workflowId": workflow_id,
            "name": "🔄 处理节点",
            "type": "script-executor",
            "position": {"x": 400, "y": 200},
            "parameters": {
                "scriptType": "python",
                "script": "print('处理节点执行')\nprint('连线测试成功')",
                "timeout": 30
            },
            "isDisabled": False,
            "notes": "中间处理节点"
        },
        {
            "id": node3_id,
            "workflowId": workflow_id,
            "name": "🎯 结束节点",
            "type": "script-executor",
            "position": {"x": 700, "y": 200},
            "parameters": {
                "scriptType": "javascript",
                "script": "console.log('结束节点执行'); console.log('连线显示正常');",
                "timeout": 30
            },
            "isDisabled": False,
            "notes": "测试结束节点"
        }
    ]
    
    # 3. 创建连线 - 这是测试的重点
    conn1_id = str(uuid.uuid4())
    conn2_id = str(uuid.uuid4())
    
    connections = [
        {
            "id": conn1_id,
            "workflowId": workflow_id,
            "sourceNodeId": node1_id,
            "targetNodeId": node2_id,
            "sourceOutput": "main",
            "targetInput": "main"
        },
        {
            "id": conn2_id,
            "workflowId": workflow_id,
            "sourceNodeId": node2_id,
            "targetNodeId": node3_id,
            "sourceOutput": "main",
            "targetInput": "main"
        }
    ]
    
    print(f"📦 创建了 {len(nodes)} 个节点和 {len(connections)} 条连线")
    print(f"🔗 连线详情:")
    for i, conn in enumerate(connections, 1):
        print(f"   连线{i}: {conn['sourceNodeId'][:8]}... -> {conn['targetNodeId'][:8]}...")
        print(f"           ID: {conn['id']}")
    
    # 4. 保存工作流
    workflow["nodes"] = nodes
    workflow["connections"] = connections
    workflow["isActive"] = True
    
    response = requests.put(f"{BASE_URL}/workflow/{workflow_id}", json=workflow)
    if response.status_code != 200:
        print(f"❌ 工作流保存失败: {response.status_code}")
        try:
            error_detail = response.json()
            print(f"   错误详情: {error_detail}")
        except:
            print(f"   错误文本: {response.text}")
        return None
    
    print(f"✅ 工作流保存成功")
    
    return workflow_id

def verify_workflow_data(workflow_id):
    """验证工作流数据的完整性"""
    print(f"\n🔍 验证工作流数据完整性...")
    
    # 获取工作流数据
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 获取工作流失败: {response.status_code}")
        return False
    
    workflow = response.json()
    nodes = workflow.get("nodes", [])
    connections = workflow.get("connections", [])
    
    print(f"📊 工作流数据验证:")
    print(f"   工作流ID: {workflow.get('id', 'N/A')}")
    print(f"   工作流名称: {workflow.get('name', 'N/A')}")
    print(f"   节点数量: {len(nodes)}")
    print(f"   连线数量: {len(connections)}")
    
    # 验证连线数据结构
    if connections:
        print(f"🔗 连线数据验证:")
        for i, conn in enumerate(connections, 1):
            print(f"   连线{i}:")
            print(f"     ID: {conn.get('id', 'N/A')}")
            print(f"     工作流ID: {conn.get('workflowId', 'N/A')}")
            print(f"     源节点: {conn.get('sourceNodeId', 'N/A')[:8]}...")
            print(f"     目标节点: {conn.get('targetNodeId', 'N/A')[:8]}...")
            print(f"     源输出: {conn.get('sourceOutput', 'N/A')}")
            print(f"     目标输入: {conn.get('targetInput', 'N/A')}")
            
            # 检查必要字段
            required_fields = ['id', 'workflowId', 'sourceNodeId', 'targetNodeId', 'sourceOutput', 'targetInput']
            missing_fields = [field for field in required_fields if not conn.get(field)]
            
            if missing_fields:
                print(f"     ❌ 缺少字段: {missing_fields}")
                return False
            else:
                print(f"     ✅ 数据完整")
    else:
        print(f"❌ 没有找到连线数据!")
        return False
    
    # 验证节点数据
    if nodes:
        print(f"📦 节点数据验证:")
        for i, node in enumerate(nodes, 1):
            print(f"   节点{i}: {node.get('name', 'N/A')} ({node.get('type', 'N/A')})")
            print(f"     位置: x={node.get('position', {}).get('x', 'N/A')}, y={node.get('position', {}).get('y', 'N/A')}")
    else:
        print(f"❌ 没有找到节点数据!")
        return False
    
    print(f"✅ 工作流数据验证通过")
    return True

def test_workflow_execution(workflow_id):
    """测试工作流执行以验证连线功能"""
    print(f"\n🚀 测试工作流执行...")
    
    response = requests.post(f"{BASE_URL}/trigger/manual/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 工作流执行失败: {response.status_code}")
        return False
    
    result = response.json()
    execution_id = result.get("executionId")
    print(f"✅ 工作流执行开始，执行ID: {execution_id}")
    
    # 等待执行完成
    time.sleep(3)
    
    # 检查执行结果
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}/history")
    if response.status_code == 200:
        executions = response.json()
        if executions:
            latest = executions[0]
            status = latest.get('status', 0)
            node_executions = latest.get('nodeExecutions', [])
            
            print(f"📊 执行结果:")
            print(f"   执行状态: {'成功' if status == 2 else '失败' if status == 3 else '运行中'}")
            print(f"   节点执行数: {len(node_executions)}")
            
            if len(node_executions) == 3:
                print(f"✅ 所有节点都执行了，连线功能正常!")
                return True
            else:
                print(f"⚠️ 只有{len(node_executions)}个节点执行，连线可能有问题")
                return False
    
    print(f"❌ 无法获取执行结果")
    return False

def main():
    print("🔗 连线显示问题修复验证")
    print("="*60)
    
    try:
        # 1. 创建测试工作流
        workflow_id = create_test_workflow_with_connections()
        if not workflow_id:
            print("❌ 测试失败：无法创建工作流")
            return
        
        # 2. 验证数据完整性
        if not verify_workflow_data(workflow_id):
            print("❌ 测试失败：数据验证不通过")
            return
        
        # 3. 测试执行功能
        if not test_workflow_execution(workflow_id):
            print("❌ 测试失败：执行验证不通过")
            return
        
        print("\n" + "="*60)
        print("✅ 连线显示修复验证通过!")
        print(f"📝 测试工作流ID: {workflow_id}")
        print(f"🔗 前端地址: http://localhost:5173")
        
        print("\n🎯 修复的问题:")
        print("   ✅ App.tsx - 选择工作流时重新加载完整数据")
        print("   ✅ WorkflowEditor.tsx - 修复连线初始化逻辑")
        print("   ✅ 移除了length > 0的条件限制")
        print("   ✅ 确保空数组也会正确设置状态")
        
        print("\n💡 前端使用说明:")
        print("   1. 打开前端页面")
        print("   2. 在工作流列表中点击'编辑'按钮")
        print("   3. 应该能看到带箭头的连线")
        print("   4. 连线应该正确连接各个节点")
        print("   5. 保存后连线不应该消失")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

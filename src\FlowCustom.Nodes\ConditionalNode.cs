using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace FlowCustom.Nodes;

/// <summary>
/// 条件判断节点
/// </summary>
public class ConditionalNode : INode
{
    private readonly ILogger<ConditionalNode> _logger;

    public ConditionalNode(ILogger<ConditionalNode> logger)
    {
        _logger = logger;
    }

    public string NodeType => "conditional";

    public string DisplayName => "条件判断";

    public string Description => "根据条件表达式判断执行路径";

    public string Category => "逻辑";

    public string Icon => "condition";

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "condition",
                DisplayName = "条件表达式",
                Description = "JavaScript表达式，返回true或false",
                Type = ParameterType.String,
                Required = true,
                DefaultValue = "data.value > 0"
            },
            new NodeParameterDefinition
            {
                Name = "inputData",
                DisplayName = "输入数据",
                Description = "用于条件判断的数据（JSON格式）",
                Type = ParameterType.Object,
                Required = false,
                DefaultValue = new Dictionary<string, object> { ["value"] = 1 }
            },
            new NodeParameterDefinition
            {
                Name = "trueOutput",
                DisplayName = "True输出",
                Description = "条件为真时的输出数据",
                Type = ParameterType.Object,
                Required = false,
                DefaultValue = new Dictionary<string, object> { ["result"] = "condition_true" }
            },
            new NodeParameterDefinition
            {
                Name = "falseOutput",
                DisplayName = "False输出",
                Description = "条件为假时的输出数据",
                Type = ParameterType.Object,
                Required = false,
                DefaultValue = new Dictionary<string, object> { ["result"] = "condition_false" }
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node,
        FlowCustom.Core.Models.ExecutionContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var condition = GetParameter<string>(node, "condition", "true");
            var inputData = GetParameter<Dictionary<string, object>>(node, "inputData", new Dictionary<string, object>());
            var trueOutput = GetParameter<Dictionary<string, object>>(node, "trueOutput", new Dictionary<string, object>());
            var falseOutput = GetParameter<Dictionary<string, object>>(node, "falseOutput", new Dictionary<string, object>());

            _logger.LogInformation("开始执行条件判断节点: {NodeId}, 条件: {Condition}", node.Id, condition);

            // 合并上下文数据和输入数据
            var evaluationData = new Dictionary<string, object>(context.GlobalData);
            foreach (var kvp in inputData)
            {
                evaluationData[kvp.Key] = kvp.Value;
            }

            // 评估条件表达式
            var conditionResult = await EvaluateConditionAsync(condition, evaluationData);

            _logger.LogInformation("条件判断结果: {NodeId}, 条件: {Condition}, 结果: {Result}", 
                node.Id, condition, conditionResult);

            // 根据条件结果选择输出
            var outputData = conditionResult ? trueOutput : falseOutput;
            
            // 添加条件判断的元数据
            var resultData = new Dictionary<string, object>(outputData)
            {
                ["conditionResult"] = conditionResult,
                ["condition"] = condition,
                ["evaluatedAt"] = DateTime.UtcNow,
                ["inputData"] = evaluationData
            };

            // 设置输出端口
            var outputPort = conditionResult ? "true" : "false";

            return NodeExecutionResult.CreateSuccess(resultData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "条件判断节点执行失败: {NodeId}", node.Id);
            return NodeExecutionResult.CreateError($"条件判断节点执行失败: {ex.Message}");
        }
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.TryGetValue("condition", out var conditionObj) || 
            string.IsNullOrWhiteSpace(conditionObj?.ToString()))
        {
            errors.Add("条件表达式不能为空");
        }

        return errors.Count == 0 ? ValidationResult.Success() : ValidationResult.Failure(errors.ToArray());
    }

    private async Task<bool> EvaluateConditionAsync(string condition, Dictionary<string, object> data)
    {
        try
        {
            // 简单的条件评估实现
            // 在实际项目中，可以使用更强大的表达式引擎，如 DynamicExpresso 或 NCalc
            
            // 替换常见的数据引用
            var evaluatedCondition = condition;
            
            // 处理 data.property 格式的引用
            foreach (var kvp in data)
            {
                var placeholder = $"data.{kvp.Key}";
                if (evaluatedCondition.Contains(placeholder))
                {
                    var value = kvp.Value?.ToString() ?? "null";
                    if (kvp.Value is string)
                    {
                        value = $"\"{value}\"";
                    }
                    evaluatedCondition = evaluatedCondition.Replace(placeholder, value);
                }
            }

            // 处理直接属性引用
            foreach (var kvp in data)
            {
                if (evaluatedCondition.Contains(kvp.Key))
                {
                    var value = kvp.Value?.ToString() ?? "null";
                    if (kvp.Value is string)
                    {
                        value = $"\"{value}\"";
                    }
                    evaluatedCondition = evaluatedCondition.Replace(kvp.Key, value);
                }
            }

            _logger.LogDebug("原始条件: {Original}, 评估后: {Evaluated}", condition, evaluatedCondition);

            // 使用简单的条件评估
            return await EvaluateSimpleCondition(evaluatedCondition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "条件评估失败: {Condition}", condition);
            return false;
        }
    }

    private async Task<bool> EvaluateSimpleCondition(string condition)
    {
        await Task.CompletedTask;

        // 移除空格
        condition = condition.Trim();

        // 处理简单的比较操作
        if (condition.Contains(">="))
        {
            var parts = condition.Split(">=", StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 2 && 
                double.TryParse(parts[0].Trim().Trim('"'), out var left) && 
                double.TryParse(parts[1].Trim().Trim('"'), out var right))
            {
                return left >= right;
            }
        }
        else if (condition.Contains("<="))
        {
            var parts = condition.Split("<=", StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 2 && 
                double.TryParse(parts[0].Trim().Trim('"'), out var left) && 
                double.TryParse(parts[1].Trim().Trim('"'), out var right))
            {
                return left <= right;
            }
        }
        else if (condition.Contains("=="))
        {
            var parts = condition.Split("==", StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 2)
            {
                var left = parts[0].Trim().Trim('"');
                var right = parts[1].Trim().Trim('"');
                return left.Equals(right, StringComparison.OrdinalIgnoreCase);
            }
        }
        else if (condition.Contains("!="))
        {
            var parts = condition.Split("!=", StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 2)
            {
                var left = parts[0].Trim().Trim('"');
                var right = parts[1].Trim().Trim('"');
                return !left.Equals(right, StringComparison.OrdinalIgnoreCase);
            }
        }
        else if (condition.Contains(">"))
        {
            var parts = condition.Split(">", StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 2 && 
                double.TryParse(parts[0].Trim().Trim('"'), out var left) && 
                double.TryParse(parts[1].Trim().Trim('"'), out var right))
            {
                return left > right;
            }
        }
        else if (condition.Contains("<"))
        {
            var parts = condition.Split("<", StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 2 && 
                double.TryParse(parts[0].Trim().Trim('"'), out var left) && 
                double.TryParse(parts[1].Trim().Trim('"'), out var right))
            {
                return left < right;
            }
        }

        // 处理布尔值
        if (bool.TryParse(condition, out var boolResult))
        {
            return boolResult;
        }

        // 默认返回false
        return false;
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                // 处理字符串类型
                if (typeof(T) == typeof(string))
                {
                    return (T)(object)(value?.ToString() ?? string.Empty);
                }

                // 处理字典类型
                if (typeof(T) == typeof(Dictionary<string, object>))
                {
                    if (value is Dictionary<string, object> dictValue)
                        return (T)(object)dictValue;
                    if (value is JsonElement jsonElement)
                    {
                        var dict = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonElement.GetRawText());
                        return (T)(object)(dict ?? new Dictionary<string, object>());
                    }
                    return (T)(object)new Dictionary<string, object>();
                }

                // 其他类型尝试直接转换
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法转换参数 {ParameterName} 的值 '{Value}'，使用默认值", parameterName, value);
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

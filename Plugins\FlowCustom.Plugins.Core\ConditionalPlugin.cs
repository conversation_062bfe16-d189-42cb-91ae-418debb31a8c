using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using FlowCustom.SDK;

namespace FlowCustom.Plugins.Core
{
    /// <summary>
    /// 条件判断节点插件
    /// 根据条件判断选择不同的执行路径
    /// </summary>
    public class ConditionalPlugin : NodePluginBase
    {
        #region 基本信息

        public override string NodeType => "conditional";
        public override string DisplayName => "条件判断";
        public override string Description => "根据条件判断选择执行路径";
        public override string Category => "逻辑";
        public override string Author => "FlowCustom Team";
        public override string Version => "1.0.0";
        public override bool IsTrigger => false;
        public override string Icon => "🔀";

        #endregion

        #region 参数定义

        protected override List<ParameterDefinition> GetParameterDefinitions()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "conditions",
                    DisplayName = "条件配置",
                    Description = "条件判断配置 (JSON格式)",
                    Type = ParameterType.Json,
                    Required = true,
                    DefaultValue = JsonSerializer.Serialize(new
                    {
                        rules = new[]
                        {
                            new
                            {
                                field = "status",
                                operator = "equals",
                                value = "success",
                                output = "true"
                            }
                        },
                        logic = "AND"
                    }, new JsonSerializerOptions { WriteIndented = true })
                },

                new ParameterDefinition
                {
                    Name = "defaultOutput",
                    DisplayName = "默认输出",
                    Description = "当所有条件都不满足时的输出端点",
                    Type = ParameterType.Select,
                    Required = false,
                    DefaultValue = "false",
                    Options = new List<ParameterOption>
                    {
                        new ParameterOption { Value = "true", Label = "True", Description = "条件为真" },
                        new ParameterOption { Value = "false", Label = "False", Description = "条件为假" },
                        new ParameterOption { Value = "error", Label = "Error", Description = "错误输出" }
                    }
                },

                new ParameterDefinition
                {
                    Name = "passThrough",
                    DisplayName = "传递数据",
                    Description = "是否将输入数据传递到输出",
                    Type = ParameterType.Boolean,
                    Required = false,
                    DefaultValue = true
                }
            };
        }

        #endregion

        #region 端点定义

        protected override List<EndpointDefinition> GetInputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "main",
                    Name = "数据输入",
                    Description = "要进行条件判断的数据",
                    Type = EndpointType.Data,
                    Required = true,
                    Position = new EndpointPosition { Side = "top", Offset = 50 },
                    DataType = "object"
                }
            };
        }

        protected override List<EndpointDefinition> GetOutputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "true",
                    Name = "条件为真",
                    Description = "条件判断为真时的输出",
                    Type = EndpointType.Success,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 25 },
                    DataType = "object"
                },

                new EndpointDefinition
                {
                    Id = "false",
                    Name = "条件为假",
                    Description = "条件判断为假时的输出",
                    Type = EndpointType.Condition,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 50 },
                    DataType = "object"
                },

                new EndpointDefinition
                {
                    Id = "error",
                    Name = "错误输出",
                    Description = "条件判断出错时的输出",
                    Type = EndpointType.Error,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 75 },
                    DataType = "error"
                }
            };
        }

        #endregion

        #region 核心逻辑

        protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
        {
            var conditionsJson = GetParameter<string>(context, "conditions", "{}");
            var defaultOutput = GetParameter<string>(context, "defaultOutput", "false");
            var passThrough = GetParameter<bool>(context, "passThrough", true);

            Log("开始条件判断");

            try
            {
                // 获取输入数据
                var inputData = context.InputData.ContainsKey("main") 
                    ? context.InputData["main"] as Dictionary<string, object> ?? new()
                    : new Dictionary<string, object>();

                // 解析条件配置
                var conditionConfig = JsonSerializer.Deserialize<ConditionConfig>(conditionsJson);
                if (conditionConfig?.Rules == null || conditionConfig.Rules.Length == 0)
                {
                    return NodeExecutionResult.Failure("条件配置为空");
                }

                // 评估条件
                var result = await EvaluateConditionsAsync(conditionConfig, inputData, context);
                
                // 准备输出数据
                var outputData = new Dictionary<string, object>();
                
                if (passThrough)
                {
                    // 传递输入数据
                    foreach (var kvp in inputData)
                    {
                        outputData[kvp.Key] = kvp.Value;
                    }
                }

                // 添加条件判断结果
                outputData["_conditionResult"] = new Dictionary<string, object>
                {
                    ["result"] = result.IsMatch,
                    ["matchedRules"] = result.MatchedRules,
                    ["evaluatedAt"] = DateTime.UtcNow,
                    ["logic"] = conditionConfig.Logic
                };

                var nextEndpoint = result.IsMatch ? "true" : defaultOutput;
                
                Log($"条件判断完成: {result.IsMatch}, 输出端点: {nextEndpoint}");

                return NodeExecutionResult.Success(outputData, nextEndpoint);
            }
            catch (JsonException ex)
            {
                Log($"条件配置JSON解析失败: {ex.Message}", LogLevel.Error);
                return NodeExecutionResult.Success(new Dictionary<string, object>
                {
                    ["error"] = "条件配置格式错误",
                    ["details"] = ex.Message
                }, "error");
            }
            catch (Exception ex)
            {
                Log($"条件判断失败: {ex.Message}", LogLevel.Error);
                return NodeExecutionResult.Success(new Dictionary<string, object>
                {
                    ["error"] = ex.Message,
                    ["errorType"] = ex.GetType().Name
                }, "error");
            }
        }

        /// <summary>
        /// 评估条件
        /// </summary>
        private async Task<ConditionResult> EvaluateConditionsAsync(ConditionConfig config, 
            Dictionary<string, object> data, NodeExecutionContext context)
        {
            var matchedRules = new List<string>();
            var results = new List<bool>();

            foreach (var rule in config.Rules)
            {
                var ruleResult = EvaluateRule(rule, data);
                results.Add(ruleResult);
                
                if (ruleResult)
                {
                    matchedRules.Add($"{rule.Field} {rule.Operator} {rule.Value}");
                }

                Log($"规则评估: {rule.Field} {rule.Operator} {rule.Value} = {ruleResult}");
            }

            // 根据逻辑运算符计算最终结果
            var finalResult = config.Logic?.ToUpper() == "OR" 
                ? results.Exists(r => r)  // OR: 任一为真
                : results.TrueForAll(r => r); // AND: 全部为真

            await Task.CompletedTask; // 保持异步签名

            return new ConditionResult
            {
                IsMatch = finalResult,
                MatchedRules = matchedRules.ToArray()
            };
        }

        /// <summary>
        /// 评估单个规则
        /// </summary>
        private bool EvaluateRule(ConditionRule rule, Dictionary<string, object> data)
        {
            if (!data.TryGetValue(rule.Field, out var fieldValue))
            {
                return false; // 字段不存在
            }

            var fieldStr = fieldValue?.ToString() ?? "";
            var valueStr = rule.Value?.ToString() ?? "";

            return rule.Operator?.ToLower() switch
            {
                "equals" => string.Equals(fieldStr, valueStr, StringComparison.OrdinalIgnoreCase),
                "not_equals" => !string.Equals(fieldStr, valueStr, StringComparison.OrdinalIgnoreCase),
                "contains" => fieldStr.Contains(valueStr, StringComparison.OrdinalIgnoreCase),
                "not_contains" => !fieldStr.Contains(valueStr, StringComparison.OrdinalIgnoreCase),
                "starts_with" => fieldStr.StartsWith(valueStr, StringComparison.OrdinalIgnoreCase),
                "ends_with" => fieldStr.EndsWith(valueStr, StringComparison.OrdinalIgnoreCase),
                "greater_than" => CompareNumeric(fieldValue, rule.Value, (a, b) => a > b),
                "less_than" => CompareNumeric(fieldValue, rule.Value, (a, b) => a < b),
                "greater_equal" => CompareNumeric(fieldValue, rule.Value, (a, b) => a >= b),
                "less_equal" => CompareNumeric(fieldValue, rule.Value, (a, b) => a <= b),
                "is_empty" => string.IsNullOrEmpty(fieldStr),
                "is_not_empty" => !string.IsNullOrEmpty(fieldStr),
                "is_null" => fieldValue == null,
                "is_not_null" => fieldValue != null,
                _ => false
            };
        }

        /// <summary>
        /// 数值比较
        /// </summary>
        private bool CompareNumeric(object left, object right, Func<double, double, bool> comparer)
        {
            if (double.TryParse(left?.ToString(), out var leftNum) && 
                double.TryParse(right?.ToString(), out var rightNum))
            {
                return comparer(leftNum, rightNum);
            }
            return false;
        }

        #endregion

        #region 验证逻辑

        protected override void OnValidate(NodeConfiguration configuration, 
            List<ValidationError> errors, List<ValidationWarning> warnings)
        {
            // 验证条件配置
            if (configuration.Parameters.TryGetValue("conditions", out var conditionsObj))
            {
                var conditionsJson = conditionsObj?.ToString() ?? "{}";
                try
                {
                    var config = JsonSerializer.Deserialize<ConditionConfig>(conditionsJson);
                    if (config?.Rules == null || config.Rules.Length == 0)
                    {
                        errors.Add(new ValidationError
                        {
                            Field = "conditions",
                            Message = "至少需要配置一个条件规则",
                            Code = "NO_RULES"
                        });
                    }
                    else
                    {
                        foreach (var rule in config.Rules)
                        {
                            if (string.IsNullOrWhiteSpace(rule.Field))
                            {
                                errors.Add(new ValidationError
                                {
                                    Field = "conditions",
                                    Message = "条件规则的字段名不能为空",
                                    Code = "EMPTY_FIELD"
                                });
                            }
                            if (string.IsNullOrWhiteSpace(rule.Operator))
                            {
                                errors.Add(new ValidationError
                                {
                                    Field = "conditions",
                                    Message = "条件规则的操作符不能为空",
                                    Code = "EMPTY_OPERATOR"
                                });
                            }
                        }
                    }
                }
                catch
                {
                    errors.Add(new ValidationError
                    {
                        Field = "conditions",
                        Message = "条件配置必须是有效的JSON格式",
                        Code = "INVALID_JSON"
                    });
                }
            }
        }

        #endregion

        #region 数据模型

        private class ConditionConfig
        {
            public ConditionRule[] Rules { get; set; } = Array.Empty<ConditionRule>();
            public string Logic { get; set; } = "AND";
        }

        private class ConditionRule
        {
            public string Field { get; set; } = "";
            public string Operator { get; set; } = "";
            public object? Value { get; set; }
            public string Output { get; set; } = "";
        }

        private class ConditionResult
        {
            public bool IsMatch { get; set; }
            public string[] MatchedRules { get; set; } = Array.Empty<string>();
        }

        #endregion

        #region 生命周期

        protected override void OnInitialize()
        {
            Log("条件判断插件初始化完成");
        }

        protected override void OnDispose()
        {
            Log("条件判断插件资源清理完成");
        }

        #endregion
    }
}

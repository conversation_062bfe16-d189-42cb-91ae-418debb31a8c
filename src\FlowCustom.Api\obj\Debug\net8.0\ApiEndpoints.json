[{"ContainingType": "FlowCustom.Api.Controllers.NodesController", "Method": "GetAllNodes", "RelativePath": "api/Nodes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Interfaces.NodeDefinition, FlowCustom.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.NodesController", "Method": "GetNode", "RelativePath": "api/Nodes/{nodeType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "nodeType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Interfaces.NodeDefinition", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.NodesController", "Method": "GetCategories", "RelativePath": "api/Nodes/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "GetWorkflows", "RelativePath": "api/Workflow", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Models.Workflow, FlowCustom.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "CreateWorkflow", "RelativePath": "api/Workflow", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflow", "Type": "FlowCustom.Core.Models.Workflow", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.Workflow", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "GetWorkflow", "RelativePath": "api/Workflow/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.Workflow", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "UpdateWorkflow", "RelativePath": "api/Workflow/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "workflow", "Type": "FlowCustom.Core.Models.Workflow", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.Workflow", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "DeleteWorkflow", "RelativePath": "api/Workflow/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "ExecuteWorkflow", "RelativePath": "api/Workflow/{id}/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "inputData", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.WorkflowExecution", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "GetExecutionHistory", "RelativePath": "api/Workflow/{id}/executions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Models.WorkflowExecution, FlowCustom.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "ToggleWorkflow", "RelativePath": "api/Workflow/{id}/toggle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "isActive", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "GetExecution", "RelativePath": "api/Workflow/executions/{executionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "executionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.WorkflowExecution", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "StopExecution", "RelativePath": "api/Workflow/executions/{executionId}/stop", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "executionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}]
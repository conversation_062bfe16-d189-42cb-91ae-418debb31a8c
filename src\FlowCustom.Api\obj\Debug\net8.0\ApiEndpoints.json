[{"ContainingType": "FlowCustom.Api.Controllers.AuthController", "Method": "GenerateApiKey", "RelativePath": "api/Auth/api-keys", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FlowCustom.Api.Controllers.GenerateApiKeyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.ApiKey", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FlowCustom.Api.Controllers.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FlowCustom.Api.Controllers.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Interfaces.AuthResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.AuthController", "Method": "UpdateCurrentUser", "RelativePath": "api/Auth/me", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FlowCustom.Api.Controllers.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FlowCustom.Api.Controllers.RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Interfaces.AuthResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FlowCustom.Api.Controllers.RegisterRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Interfaces.AuthResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.LogController", "Method": "GetLogs", "RelativePath": "api/Log", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.String", "IsRequired": false}, {"Name": "executionId", "Type": "System.String", "IsRequired": false}, {"Name": "level", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Models.SystemLogEntry, FlowCustom.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.LogController", "Method": "AddLog", "RelativePath": "api/Log", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FlowCustom.Api.Controllers.AddLogRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.LogController", "Method": "ClearLogs", "RelativePath": "api/Log", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.String", "IsRequired": false}, {"Name": "level", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.LogController", "Method": "GetExecutions", "RelativePath": "api/Log/executions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Models.WorkflowExecution, FlowCustom.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.LogController", "Method": "GetExecution", "RelativePath": "api/Log/executions/{executionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "executionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.WorkflowExecution", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.MonitoringController", "Method": "GetActiveExecutions", "RelativePath": "api/Monitoring/active-executions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Models.WorkflowExecution, FlowCustom.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.MonitoringController", "Method": "CleanupOldExecutions", "RelativePath": "api/Monitoring/cleanup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "retentionDays", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.MonitoringController", "Method": "GetExecutions", "RelativePath": "api/Monitoring/executions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Models.WorkflowExecution, FlowCustom.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.MonitoringController", "Method": "GetExecution", "RelativePath": "api/Monitoring/executions/{executionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "executionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.WorkflowExecution", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.MonitoringController", "Method": "GetExecutionLogs", "RelativePath": "api/Monitoring/executions/{executionId}/logs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "executionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Data.ExecutionLog, FlowCustom.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.MonitoringController", "Method": "GetSystemMetrics", "RelativePath": "api/Monitoring/metrics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "FlowCustom.Engine.SystemMetrics", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.MonitoringController", "Method": "GetWorkflowPerformance", "RelativePath": "api/Monitoring/performance/{workflowId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.Guid", "IsRequired": true}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "FlowCustom.Api.Controllers.WorkflowPerformanceReport", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.MonitoringController", "Method": "GetStatistics", "RelativePath": "api/Monitoring/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.ExecutionStatistics", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.NodesController", "Method": "GetAllNodes", "RelativePath": "api/Nodes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Interfaces.NodeDefinition, FlowCustom.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.NodesController", "Method": "GetNode", "RelativePath": "api/Nodes/{nodeType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "nodeType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Interfaces.NodeDefinition", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.NodesController", "Method": "GetCategories", "RelativePath": "api/Nodes/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleApiTrigger", "RelativePath": "api/Trigger/api/{path}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleApiTrigger", "RelativePath": "api/Trigger/api/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleApiTrigger", "RelativePath": "api/Trigger/api/{path}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleApiTrigger", "RelativePath": "api/Trigger/api/{path}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleApiTrigger", "RelativePath": "api/Trigger/api/{path}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "ManualTrigger", "RelativePath": "api/Trigger/manual/{workflowId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.Guid", "IsRequired": true}, {"Name": "data", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "GetTriggerStatus", "RelativePath": "api/Trigger/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleWebhook", "RelativePath": "api/Trigger/webhook/{path}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleWebhook", "RelativePath": "api/Trigger/webhook/{path}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleWebhook", "RelativePath": "api/Trigger/webhook/{path}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleWebhook", "RelativePath": "api/Trigger/webhook/{path}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.TriggerController", "Method": "HandleWebhook", "RelativePath": "api/Trigger/webhook/{path}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "path", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "GetWorkflows", "RelativePath": "api/Workflow", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Models.Workflow, FlowCustom.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "CreateWorkflow", "RelativePath": "api/Workflow", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "FlowCustom.Api.Controllers.CreateWorkflowRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.Workflow", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "GetWorkflow", "RelativePath": "api/Workflow/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.Workflow", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "UpdateWorkflow", "RelativePath": "api/Workflow/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "workflow", "Type": "FlowCustom.Core.Models.Workflow", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.Workflow", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "DeleteWorkflow", "RelativePath": "api/Workflow/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "ExecuteWorkflow", "RelativePath": "api/Workflow/{id}/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "inputData", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.WorkflowExecution", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "GetExecutionHistory", "RelativePath": "api/Workflow/{id}/executions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Models.WorkflowExecution, FlowCustom.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "GetWorkflowExecutions", "RelativePath": "api/Workflow/{id}/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[FlowCustom.Core.Models.WorkflowExecution, FlowCustom.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "ToggleWorkflow", "RelativePath": "api/Workflow/{id}/toggle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "isActive", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "CancelExecution", "RelativePath": "api/Workflow/{workflowId}/executions/{executionId}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.Guid", "IsRequired": true}, {"Name": "executionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "GetExecution", "RelativePath": "api/Workflow/executions/{executionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "executionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "FlowCustom.Core.Models.WorkflowExecution", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "FlowCustom.Api.Controllers.WorkflowController", "Method": "StopExecution", "RelativePath": "api/Workflow/executions/{executionId}/stop", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "executionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.HealthController", "Method": "Get", "RelativePath": "Health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.HealthController", "Method": "GetDetailed", "RelativePath": "Health/detailed", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.HealthController", "Method": "Live", "RelativePath": "Health/live", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "FlowCustom.Api.Controllers.HealthController", "Method": "Ready", "RelativePath": "Health/ready", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}]
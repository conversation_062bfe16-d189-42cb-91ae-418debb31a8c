import React, { useState, useCallback, useRef, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  ReactFlow,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
} from '@xyflow/react';
import type {
  Node,
  Edge,
  Connection,
  ReactFlowInstance,
} from '@xyflow/react';

import CustomNode from './nodes/CustomNode';
import NodeSidebar from './NodeSidebar';
import NodeConfigPanel from './NodeConfigPanel';
import LogViewer from './LogViewer';
import DebugPanel from './DebugPanel';
import WorkflowStatusBar from './WorkflowStatusBar';
import type { WorkflowNode, NodeConnection } from '../types/workflow';

// 节点定义接口
interface NodeDefinition {
  nodeType: string;
  displayName: string;
  description: string;
  category: string;
  icon: string;
  isTrigger: boolean;
  parameters: Array<{
    name: string;
    displayName: string;
    description: string;
    type: number;
    required: boolean;
    defaultValue: any;
  }>;
}

const nodeTypes = {
  custom: CustomNode,
};

interface WorkflowEditorProps {
  workflowId?: string;
  initialNodes?: WorkflowNode[];
  initialConnections?: NodeConnection[];
  onSave?: (nodes: WorkflowNode[], connections: NodeConnection[]) => void;
  onBack?: () => void;
}

// 节点加载服务
class NodeLoaderService {
  private static instance: NodeLoaderService;
  private nodeDefinitions: NodeDefinition[] = [];
  private nodeDisplayNames: Record<string, string> = {};

  static getInstance(): NodeLoaderService {
    if (!NodeLoaderService.instance) {
      NodeLoaderService.instance = new NodeLoaderService();
    }
    return NodeLoaderService.instance;
  }

  async loadNodeDefinitions(): Promise<NodeDefinition[]> {
    try {
      const response = await fetch('http://localhost:5053/api/nodes');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const nodes = await response.json();
      this.nodeDefinitions = nodes;
      
      // 构建显示名称映射
      this.nodeDisplayNames = {};
      nodes.forEach((node: NodeDefinition) => {
        this.nodeDisplayNames[node.nodeType] = node.displayName;
      });
      
      return nodes;
    } catch (error) {
      console.error('加载节点定义失败:', error);
      return this.getDefaultNodeDefinitions();
    }
  }

  getNodeDisplayName(nodeType: string): string {
    return this.nodeDisplayNames[nodeType] || nodeType;
  }

  getNodeDefinitions(): NodeDefinition[] {
    return this.nodeDefinitions;
  }

  private getDefaultNodeDefinitions(): NodeDefinition[] {
    return [
      {
        nodeType: 'http-request',
        displayName: 'HTTP请求',
        description: '向外部API发送HTTP请求',
        category: '网络',
        icon: 'globe',
        isTrigger: false,
        parameters: []
      },
      {
        nodeType: 'set',
        displayName: '数据设置',
        description: '设置值和转换数据',
        category: '数据',
        icon: 'edit',
        isTrigger: false,
        parameters: []
      },
      {
        nodeType: 'manual-trigger',
        displayName: '手动触发器',
        description: '通过手动点击按钮触发工作流执行',
        category: '触发器',
        icon: 'hand-pointer',
        isTrigger: true,
        parameters: []
      },
      {
        nodeType: 'script-executor',
        displayName: '脚本执行器',
        description: '执行Python、Bash或JavaScript脚本',
        category: '工具',
        icon: 'code',
        isTrigger: false,
        parameters: []
      },
      {
        nodeType: 'conditional',
        displayName: '条件判断',
        description: '根据条件表达式判断执行路径',
        category: '逻辑',
        icon: 'condition',
        isTrigger: false,
        parameters: []
      },
      {
        nodeType: 'loop',
        displayName: '循环',
        description: '对数组或指定次数进行循环处理',
        category: '逻辑',
        icon: 'loop',
        isTrigger: false,
        parameters: []
      },
      {
        nodeType: 'data-transform',
        displayName: '数据转换',
        description: '对数据进行各种转换操作',
        category: '数据',
        icon: 'transform',
        isTrigger: false,
        parameters: []
      }
    ];
  }
}

const WorkflowEditor: React.FC<WorkflowEditorProps> = ({
  workflowId,
  initialNodes = [],
  initialConnections = [],
  onSave,
  onBack,
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [showLogs, setShowLogs] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [nodeDefinitions, setNodeDefinitions] = useState<NodeDefinition[]>([]);
  const [executionStatus, setExecutionStatus] = useState<{[nodeId: string]: {
    status: 'idle' | 'running' | 'success' | 'error';
    result?: any;
    duration?: string;
    error?: string;
  }}>({});
  const [isExecuting, setIsExecuting] = useState(false);
  const nodeLoader = NodeLoaderService.getInstance();

  // 初始化节点定义
  useEffect(() => {
    const loadNodes = async () => {
      const definitions = await nodeLoader.loadNodeDefinitions();
      setNodeDefinitions(definitions);
    };
    loadNodes();
  }, []);

  // Convert NodeConnection to ReactFlow Edge - 基础转换，不包含执行状态
  const convertToReactFlowEdges = useCallback((connections: NodeConnection[]): Edge[] => {
    console.log('convertToReactFlowEdges 被调用:', connections);
    const edges = connections.map(conn => {
      const edge = {
        id: conn.id,
        source: conn.sourceNodeId,
        target: conn.targetNodeId,
        sourceHandle: conn.sourceOutput,
        targetHandle: conn.targetInput,
        type: 'smoothstep',
        animated: false, // 默认不动画
        style: { stroke: '#3b82f6', strokeWidth: 2 },
        markerEnd: {
          type: 'arrowclosed',
          color: '#3b82f6',
        },
      };
      console.log('转换连线:', conn.id, '->', edge);
      return edge;
    });
    console.log('转换后的edges:', edges);
    return edges;
  }, []);

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // Node delete handler
  const handleNodeDelete = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) =>
      edge.source !== nodeId && edge.target !== nodeId
    ));
    setSelectedNode((current) => {
      if (current && current.id === nodeId) {
        return null;
      }
      return current;
    });
  }, [setNodes, setEdges]);

  // Convert WorkflowNode to ReactFlow Node - 基础转换，不包含执行状态
  const convertToReactFlowNodes = useCallback((workflowNodes: WorkflowNode[]): Node[] => {
    return workflowNodes.map(node => ({
      id: node.id,
      type: 'custom',
      position: node.position,
      data: {
        ...node,
        onDelete: () => handleNodeDelete(node.id),
        onEdit: () => setSelectedNode(node),
        executionStatus: 'idle', // 默认状态
        executionResult: undefined,
        isExecuting: false,
      } as Record<string, unknown>,
    }));
  }, [handleNodeDelete]);

  /**
   * 初始化节点数据的Effect Hook
   *
   * 关键设计决策：
   * 1. 只依赖initialNodes.length而不是整个数组，避免循环依赖
   * 2. 使用convertToReactFlowNodes进行基础转换，不包含执行状态
   * 3. 每次initialNodes变化时都重新初始化
   *
   * 重要修复：
   * - 移除了length > 0的条件，确保即使是空数组也会正确设置
   * - 这样可以清除之前的节点，避免显示错误的节点
   *
   * 为什么这样设计：
   * - 避免无限循环：如果依赖整个initialNodes数组，会导致无限重新渲染
   * - 保持状态稳定：只在真正需要时重新初始化节点
   * - 分离关注点：基础数据初始化与状态更新分开处理
   */
  useEffect(() => {
    console.log('初始化节点数据:', initialNodes.length, initialNodes);
    // 总是设置nodes，即使是空数组也要设置，这样可以清除之前的节点
    setNodes(convertToReactFlowNodes(initialNodes));
  }, [initialNodes.length, convertToReactFlowNodes, setNodes]);

  /**
   * 初始化连线数据的Effect Hook
   *
   * 连线初始化逻辑：
   * 1. 将NodeConnection格式转换为ReactFlow Edge格式
   * 2. 设置默认样式：蓝色、带箭头、不动画
   * 3. 每次initialConnections变化时都重新初始化
   *
   * 重要修复：
   * - 移除了length > 0的条件，确保即使是空数组也会正确设置
   * - 这样可以清除之前的连线，避免显示错误的连线
   *
   * 注意事项：
   * - 连线的动画和颜色变化由执行状态更新Effect处理
   * - 这里只负责基础的连线数据结构转换
   */
  useEffect(() => {
    console.log('=== 连线初始化 useEffect 触发 ===');
    console.log('initialConnections.length:', initialConnections.length);
    console.log('initialConnections:', initialConnections);
    console.log('当前edges状态:', edges);

    // 总是设置edges，即使是空数组也要设置，这样可以清除之前的连线
    const newEdges = convertToReactFlowEdges(initialConnections);
    console.log('即将设置的新edges:', newEdges);
    setEdges(newEdges);

    console.log('=== 连线初始化 useEffect 完成 ===');
  }, [initialConnections.length, convertToReactFlowEdges, setEdges]);

  /**
   * 节点执行状态更新的Effect Hook
   *
   * 状态更新逻辑：
   * 1. 监听executionStatus和isExecuting的变化
   * 2. 只更新节点的执行相关属性，不改变基础数据结构
   * 3. 为每个节点添加执行状态、结果和运行标识
   *
   * 执行状态映射：
   * - idle: 空闲状态，无特殊样式
   * - running: 运行中，橙色边框+旋转动画
   * - success: 成功，绿色边框+成功图标
   * - error: 失败，红色边框+错误图标
   *
   * 性能优化：
   * - 只在有执行状态数据时才更新
   * - 使用函数式更新，避免不必要的重新渲染
   */
  useEffect(() => {
    if (Object.keys(executionStatus).length > 0 || isExecuting) {
      console.log('更新节点执行状态:', executionStatus);
      setNodes(prevNodes => prevNodes.map(node => {
        const nodeData = node.data as WorkflowNode;
        const nodeExecutionStatus = executionStatus[nodeData.id];

        return {
          ...node,
          data: {
            ...nodeData,
            // 执行状态：从executionStatus获取，默认为idle
            executionStatus: nodeExecutionStatus?.status || 'idle',
            // 执行结果：包含输出数据、错误信息等
            executionResult: nodeExecutionStatus?.result,
            // 是否正在执行：全局执行状态 && 当前节点状态为running
            isExecuting: isExecuting && nodeExecutionStatus?.status === 'running',
          }
        };
      }));
    }
  }, [executionStatus, isExecuting, setNodes]);

  /**
   * 连线执行状态更新的Effect Hook
   *
   * 连线动画逻辑：
   * 1. 检查连线两端节点的执行状态
   * 2. 如果源节点成功且目标节点正在运行，则显示动画
   * 3. 动画效果：橙色、加粗、流动动画
   *
   * 视觉反馈设计：
   * - 默认状态：蓝色静态连线
   * - 数据流动时：橙色动画连线
   * - 完成后：恢复蓝色静态状态
   *
   * 这种设计让用户可以清楚地看到数据在工作流中的流动路径
   */
  useEffect(() => {
    if (Object.keys(executionStatus).length > 0 || isExecuting) {
      console.log('更新连线执行状态');
      setEdges(prevEdges => prevEdges.map(edge => {
        const sourceStatus = executionStatus[edge.source];
        const targetStatus = executionStatus[edge.target];

        // 判断是否应该显示动画：源节点成功 && 目标节点运行中
        const shouldAnimate = isExecuting &&
          sourceStatus?.status === 'success' &&
          targetStatus?.status === 'running';

        return {
          ...edge,
          // 动画开关
          animated: shouldAnimate,
          // 动态样式：颜色和粗细
          style: {
            stroke: shouldAnimate ? '#f59e0b' : '#3b82f6', // 橙色 vs 蓝色
            strokeWidth: shouldAnimate ? 3 : 2 // 加粗 vs 正常
          },
          // 箭头样式：颜色跟随连线
          markerEnd: {
            type: 'arrowclosed',
            color: shouldAnimate ? '#f59e0b' : '#3b82f6',
          },
        };
      }));
    }
  }, [executionStatus, isExecuting, setEdges]);

  /**
   * 处理节点连接的回调函数
   *
   * 连接创建流程：
   * 1. 接收ReactFlow的Connection参数（包含source、target、sourceHandle、targetHandle）
   * 2. 生成唯一的连线ID
   * 3. 设置连线的视觉样式和行为
   * 4. 添加到edges状态中
   *
   * 连线样式设计：
   * - type: 'smoothstep' - 平滑的阶梯式连线，比直线更美观
   * - animated: false - 默认不动画，只在执行时才动画
   * - stroke: '#3b82f6' - 蓝色主题色
   * - strokeWidth: 2 - 适中的线条粗细
   * - markerEnd: 箭头标记，指示数据流向
   *
   * 为什么使用smoothstep：
   * - 避免连线重叠时的视觉混乱
   * - 提供更清晰的路径指示
   * - 符合现代流程图的设计规范
   */
  const onConnect = useCallback(
    (params: Connection) => {
      console.log('创建新连线:', params);

      // 创建新的连线对象
      const newEdge = {
        ...params, // 包含source、target、sourceHandle、targetHandle
        id: uuidv4(), // 生成唯一ID
        type: 'smoothstep', // 平滑阶梯式连线
        animated: false, // 新连线默认不动画
        style: {
          stroke: '#3b82f6', // 蓝色主题
          strokeWidth: 2 // 标准粗细
        },
        markerEnd: {
          type: 'arrowclosed', // 封闭箭头
          color: '#3b82f6', // 箭头颜色与连线一致
        },
      };

      // 添加到edges状态，ReactFlow会自动处理重复连线的防护
      setEdges((eds) => addEdge(newEdge, eds));

      console.log('连线创建成功:', newEdge.id);
    },
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    const nodeData = node.data as WorkflowNode;
    setSelectedNode(nodeData);
  }, []);

  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
  }, []);

  const handleNodeUpdate = useCallback((nodeId: string, updates: Partial<WorkflowNode>) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const updatedData = { ...node.data, ...updates } as WorkflowNode;
          return { ...node, data: updatedData };
        }
        return node;
      })
    );

    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode({ ...selectedNode, ...updates });
    }
  }, [selectedNode, setNodes]);

  const handleTestRun = useCallback(async () => {
    if (isTestRunning) return;

    setIsTestRunning(true);
    const executionId = uuidv4();
    setCurrentExecutionId(executionId);
    setShowDebug(true);

    try {
      console.log('开始试运行工作流...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.log('试运行完成');
    } catch (error) {
      console.error('试运行失败:', error);
    } finally {
      setIsTestRunning(false);
    }
  }, [isTestRunning]);

  // 手动触发工作流
  const handleManualTrigger = useCallback(async () => {
    if (!workflowId) {
      alert('请先保存工作流');
      return;
    }

    const manualTriggerNode = nodes.find(node => {
      const nodeData = node.data as WorkflowNode;
      return nodeData.type === 'manual-trigger';
    });

    if (!manualTriggerNode) {
      alert('工作流中没有手动触发器节点');
      return;
    }

    const nodeData = manualTriggerNode.data as WorkflowNode;
    const confirmMessage = nodeData.parameters?.confirmMessage as string;

    if (confirmMessage && !window.confirm(confirmMessage)) {
      return;
    }

    try {
      // 重置执行状态
      setExecutionStatus({});
      setIsExecuting(true);

      // 设置所有节点为idle状态
      const initialStatus: {[nodeId: string]: any} = {};
      nodes.forEach(node => {
        initialStatus[node.id] = { status: 'idle' };
      });
      setExecutionStatus(initialStatus);

      const response = await fetch(`http://localhost:5053/api/trigger/manual/${workflowId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          triggeredBy: 'user',
          triggeredAt: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setCurrentExecutionId(result.executionId);
        setShowDebug(true);

        // 开始监控执行状态
        monitorExecution(result.executionId);
      } else {
        alert(`工作流触发失败: ${result.error || '未知错误'}`);
        setIsExecuting(false);
      }
    } catch (error) {
      console.error('手动触发失败:', error);
      alert('手动触发失败，请检查网络连接和后端服务');
      setIsExecuting(false);
    }
  }, [workflowId, nodes]);

  /**
   * 实时监控工作流执行状态的核心方法
   *
   * 监控机制设计：
   * 1. 轮询策略：每500ms查询一次执行状态
   * 2. 超时保护：最多轮询60次（30秒），防止无限轮询
   * 3. 状态映射：将后端状态码转换为前端可理解的状态
   * 4. 实时更新：立即更新UI显示执行进度
   *
   * 状态码映射：
   * - 1: Running -> 'running'
   * - 2: Success -> 'success'
   * - 3: Error -> 'error'
   * - 其他: 默认为 'running'
   *
   * 监控流程：
   * 1. 发起HTTP请求获取执行历史
   * 2. 查找当前执行记录
   * 3. 解析节点执行状态
   * 4. 更新前端状态
   * 5. 检查是否完成，决定是否继续轮询
   *
   * 错误处理：
   * - 网络错误：记录日志，停止监控
   * - 数据解析错误：忽略本次轮询，继续下次
   * - 超时：自动停止监控，避免资源浪费
   */
  const monitorExecution = useCallback(async (executionId: string) => {
    const pollInterval = 500; // 轮询间隔：500毫秒
    const maxPolls = 60; // 最大轮询次数：60次 = 30秒
    let pollCount = 0;

    console.log(`开始监控执行: ${executionId}`);

    /**
     * 轮询函数：递归调用自身实现持续监控
     */
    const poll = async () => {
      try {
        // 1. 获取工作流执行历史
        const response = await fetch(`http://localhost:5053/api/workflow/${workflowId}/history`);
        if (!response.ok) {
          console.warn(`获取执行历史失败: ${response.status}`);
          return; // 跳过本次轮询，继续下次
        }

        const executions = await response.json();

        // 2. 查找当前执行记录
        const currentExecution = executions.find((exec: any) => exec.id === executionId);

        if (currentExecution) {
          console.log(`执行状态更新: ${currentExecution.status}`, currentExecution);

          // 3. 构建节点状态映射
          const newStatus: {[nodeId: string]: any} = {};

          if (currentExecution.nodeExecutions && Array.isArray(currentExecution.nodeExecutions)) {
            currentExecution.nodeExecutions.forEach((nodeExec: any) => {
              // 状态码转换：2=成功, 3=失败, 其他=运行中
              const status = nodeExec.status === 2 ? 'success' :
                           nodeExec.status === 3 ? 'error' : 'running';

              newStatus[nodeExec.nodeId] = {
                status: status,
                result: {
                  status: status,
                  duration: nodeExec.duration || '00:00:00',
                  error: nodeExec.error,
                  outputData: nodeExec.outputData
                }
              };
            });
          }

          // 4. 更新前端状态，触发UI重新渲染
          setExecutionStatus(newStatus);

          // 5. 检查工作流是否完成（成功或失败）
          if (currentExecution.status === 2 || currentExecution.status === 3) {
            const statusText = currentExecution.status === 2 ? '成功' : '失败';
            console.log(`工作流执行完成: ${statusText}`);
            setIsExecuting(false);
            return; // 停止监控
          }
        } else {
          console.warn(`未找到执行记录: ${executionId}`);
        }

        // 6. 继续轮询（如果未超时）
        pollCount++;
        if (pollCount < maxPolls) {
          setTimeout(poll, pollInterval);
        } else {
          console.warn('监控超时，停止轮询');
          setIsExecuting(false);
        }

      } catch (error) {
        console.error('监控执行状态失败:', error);
        setIsExecuting(false); // 发生错误时停止监控
      }
    };

    // 开始第一次轮询
    poll();
  }, [workflowId]);

  // 检查是否有手动触发器节点
  const hasManualTrigger = nodes.some(node => {
    const nodeData = node.data as WorkflowNode;
    return nodeData.type === 'manual-trigger';
  });

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const nodeType = event.dataTransfer.getData('application/reactflow');

      if (typeof nodeType === 'undefined' || !nodeType) {
        return;
      }

      if (reactFlowWrapper.current && reactFlowInstance) {
        const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
        const position = reactFlowInstance.screenToFlowPosition({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        });

        // 使用动态节点定义获取显示名称
        const getNodeDisplayName = (nodeType: string): string => {
          return nodeLoader.getNodeDisplayName(nodeType);
        };

        // 获取节点的默认参数
        const getNodeDefaultParameters = (nodeType: string): Record<string, any> => {
          const nodeDef = nodeDefinitions.find(def => def.nodeType === nodeType);
          if (!nodeDef) return {};

          const defaultParams: Record<string, any> = {};
          nodeDef.parameters.forEach(param => {
            if (param.defaultValue !== undefined) {
              defaultParams[param.name] = param.defaultValue;
            }
          });
          return defaultParams;
        };

        const nodeId = uuidv4();
        const newNode: Node = {
          id: nodeId,
          type: 'custom',
          position,
          data: {
            id: nodeId,
            workflowId: workflowId || '',
            name: getNodeDisplayName(nodeType),
            type: nodeType,
            position,
            parameters: getNodeDefaultParameters(nodeType),
            isDisabled: false,
            notes: '',
            onDelete: () => handleNodeDelete(nodeId),
            onEdit: () => {
              const nodeData = nodes.find(n => n.id === nodeId)?.data as WorkflowNode;
              if (nodeData) {
                setSelectedNode(nodeData);
              }
            },
          } as Record<string, unknown>,
        };

        setNodes((nds) => nds.concat(newNode));
      }
    },
    [reactFlowInstance, workflowId, setNodes, handleNodeDelete, nodes, nodeLoader, nodeDefinitions]
  );

  const onNodeDragStart = useCallback((event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  }, []);

  /**
   * 保存工作流的核心方法
   *
   * 保存流程详解：
   * 1. 数据转换：将ReactFlow的nodes和edges转换为后端需要的格式
   * 2. 数据合并：获取当前工作流信息，保留其他字段，只更新nodes和connections
   * 3. 后端保存：调用PUT API保存到数据库
   * 4. 状态更新：更新前端的保存时间状态
   * 5. 回调通知：如果有父组件回调，则通知保存完成
   *
   * 关键点：
   * - 保存后不重新加载数据，避免连线消失
   * - 保持ReactFlow的内部状态不变
   * - 只更新保存时间和成功提示
   */
  const handleSave = useCallback(async () => {
    if (!workflowId) {
      alert('无法保存：工作流ID不存在');
      return;
    }

    try {
      console.log('开始保存工作流...', { nodeCount: nodes.length, edgeCount: edges.length });

      // 1. 将ReactFlow节点转换为WorkflowNode格式
      // 保留节点的所有数据，同时更新位置信息
      const workflowNodes: WorkflowNode[] = nodes.map(node => {
        const nodeData = node.data as WorkflowNode;
        return {
          ...nodeData,
          position: node.position, // 更新节点位置
        };
      });

      // 2. 将ReactFlow边转换为NodeConnection格式
      // 确保连线信息完整且格式正确
      const connections: NodeConnection[] = edges.map(edge => ({
        id: edge.id,
        workflowId: workflowId,
        sourceNodeId: edge.source,
        targetNodeId: edge.target,
        sourceOutput: edge.sourceHandle || 'main',
        targetInput: edge.targetHandle || 'main',
      }));

      console.log('转换后的数据:', {
        workflowNodes: workflowNodes.length,
        connections: connections.length,
        connectionDetails: connections.map(c => ({
          id: c.id,
          from: c.sourceNodeId.substring(0, 8),
          to: c.targetNodeId.substring(0, 8)
        }))
      });

      // 3. 获取当前工作流信息，保留其他字段
      const currentWorkflowResponse = await fetch(`http://localhost:5053/api/workflow/${workflowId}`);
      if (!currentWorkflowResponse.ok) {
        throw new Error('获取工作流信息失败');
      }
      const currentWorkflow = await currentWorkflowResponse.json();

      // 4. 构建更新的工作流数据
      // 只更新nodes和connections，保留其他所有字段
      const updatedWorkflow = {
        ...currentWorkflow,
        nodes: workflowNodes,
        connections: connections,
        updatedAt: new Date().toISOString(),
      };

      // 5. 保存到后端
      const response = await fetch(`http://localhost:5053/api/workflow/${workflowId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedWorkflow),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`保存失败: ${response.status} - ${errorText}`);
      }

      // 6. 更新前端状态
      setLastSaved(new Date());
      console.log('工作流保存成功');
      alert('工作流保存成功！');

      // 7. 通知父组件（如果有回调）
      if (onSave) {
        onSave(workflowNodes, connections);
      }

      // 注意：这里不重新加载数据，保持ReactFlow的当前状态
      // 这样可以避免连线消失的问题

    } catch (error) {
      console.error('保存工作流失败:', error);
      alert(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }, [nodes, edges, workflowId, onSave]);

  return (
    <div className="flex h-screen" style={{ backgroundColor: 'var(--coze-bg-secondary)' }}>
      {/* Main Editor */}
      <div className={`${showLogs ? 'flex-1' : 'flex-1'} relative flex flex-col`}>
        {/* Header */}
        <div className="coze-card border-0 rounded-none shadow-sm">
          <div className="px-6 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack || (() => window.history.back())}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                返回工作流列表
              </button>
              <div className="h-6 w-px" style={{ backgroundColor: 'var(--coze-border)' }}></div>
              <h1 className="text-xl font-semibold" style={{ color: 'var(--coze-text-primary)' }}>
                工作流编辑器
              </h1>
            </div>
            <div className="flex items-center space-x-3">
              {hasManualTrigger && (
                <button
                  onClick={handleManualTrigger}
                  className="coze-btn coze-btn-primary"
                  title="手动触发工作流"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                  </svg>
                  立即执行
                </button>
              )}
              <button
                onClick={handleTestRun}
                disabled={isTestRunning || nodes.length === 0}
                className={`coze-btn ${isTestRunning ? 'coze-btn-warning' : 'coze-btn-success'}`}
              >
                {isTestRunning ? (
                  <>
                    <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    试运行中...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M5 12h14l-1 7H6l-1-7z" />
                    </svg>
                    试运行
                  </>
                )}
              </button>
              <button
                onClick={() => setShowLogs(!showLogs)}
                className={`coze-btn ${showLogs ? 'coze-btn-primary' : 'coze-btn-secondary'}`}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                {showLogs ? '隐藏日志' : '显示日志'}
              </button>
              <button
                onClick={() => setShowDebug(true)}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
                调试
              </button>
              <div className="h-6 w-px" style={{ backgroundColor: 'var(--coze-border)' }}></div>
              <button
                onClick={handleSave}
                className="coze-btn coze-btn-primary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                保存
              </button>
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div ref={reactFlowWrapper} className="flex-1">
          {/* 调试信息显示 */}
          <div style={{ position: 'absolute', top: 10, left: 10, zIndex: 1000, background: 'rgba(0,0,0,0.8)', color: 'white', padding: '10px', borderRadius: '5px', fontSize: '12px' }}>
            <div>节点数量: {nodes.length}</div>
            <div>连线数量: {edges.length}</div>
            <div>连线详情: {edges.map(e => `${e.source.substring(0,4)}→${e.target.substring(0,4)}`).join(', ')}</div>
          </div>

          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={(instance) => {
              console.log('ReactFlow 初始化完成:', { nodes: nodes.length, edges: edges.length });
              setReactFlowInstance(instance);
            }}
            onDrop={onDrop}
            onDragOver={onDragOver}
            onNodeClick={onNodeClick}
            onPaneClick={onPaneClick}
            nodeTypes={nodeTypes}
            fitView
            style={{ backgroundColor: 'var(--coze-bg-secondary)' }}
          >
            <Controls />
            <MiniMap />
            <Background variant={BackgroundVariant.Dots} gap={20} size={1} color="var(--coze-border)" />
          </ReactFlow>
        </div>

        {/* Status Bar */}
        <WorkflowStatusBar
          nodeCount={nodes.length}
          connectionCount={edges.length}
          selectedNodeId={selectedNode?.id}
          isTestRunning={isTestRunning}
          lastSaved={lastSaved}
        />
      </div>

      {/* Node Sidebar */}
      <NodeSidebar
        onNodeDragStart={onNodeDragStart}
        nodeDefinitions={nodeDefinitions}
      />

      {/* Node Config Panel */}
      <NodeConfigPanel
        selectedNode={selectedNode}
        onNodeUpdate={handleNodeUpdate}
        onClose={() => setSelectedNode(null)}
      />

      {/* Log Panel */}
      {showLogs && (
        <div className="w-96 bg-white border-l border-gray-200">
          <LogViewer workflowId={workflowId} autoRefresh={true} />
        </div>
      )}

      {/* Debug Panel */}
      <DebugPanel
        isOpen={showDebug}
        onClose={() => setShowDebug(false)}
        workflowId={workflowId}
        executionId={currentExecutionId}
      />
    </div>
  );
};

const WorkflowEditorWithProvider: React.FC<WorkflowEditorProps> = (props) => (
  <ReactFlowProvider>
    <WorkflowEditor {...props} />
  </ReactFlowProvider>
);

export default WorkflowEditorWithProvider;

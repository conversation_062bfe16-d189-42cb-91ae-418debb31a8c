import React, { useState, useCallback, useRef, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  ReactFlow,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
} from '@xyflow/react';
import type {
  Node,
  Edge,
  Connection,
  ReactFlowInstance,
} from '@xyflow/react';

import CustomNode from './nodes/CustomNode';
import NodeSidebar from './NodeSidebar';
import NodeConfigPanel from './NodeConfigPanel';
import LogViewer from './LogViewer';
import DebugPanel from './DebugPanel';
import WorkflowStatusBar from './WorkflowStatusBar';
import type { WorkflowNode, NodeConnection } from '../types/workflow';

const nodeTypes = {
  custom: CustomNode,
};

interface WorkflowEditorProps {
  workflowId?: string;
  initialNodes?: WorkflowNode[];
  initialConnections?: NodeConnection[];
  onSave?: (nodes: WorkflowNode[], connections: NodeConnection[]) => void;
  onBack?: () => void;
}

const WorkflowEditor: React.FC<WorkflowEditorProps> = ({
  workflowId,
  initialNodes = [],
  initialConnections = [],
  onSave,
  onBack,
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [showLogs, setShowLogs] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Node delete handler
  const handleNodeDelete = useCallback((nodeId: string) => {
    // Remove node
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));

    // Remove connected edges
    setEdges((eds) => eds.filter((edge) =>
      edge.source !== nodeId && edge.target !== nodeId
    ));

    // Clear selection if deleted node was selected
    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode(null);
    }
  }, [selectedNode, setNodes, setEdges]);

  // Convert WorkflowNode to ReactFlow Node
  const convertToReactFlowNodes = useCallback((workflowNodes: WorkflowNode[]): Node[] => {
    return workflowNodes.map(node => ({
      id: node.id,
      type: 'custom',
      position: node.position,
      data: {
        ...node,
        onDelete: () => handleNodeDelete(node.id),
        onEdit: () => setSelectedNode(node),
      } as Record<string, unknown>,
    }));
  }, [handleNodeDelete]);

  // Convert NodeConnection to ReactFlow Edge
  const convertToReactFlowEdges = (connections: NodeConnection[]): Edge[] => {
    return connections.map(conn => ({
      id: conn.id,
      source: conn.sourceNodeId,
      target: conn.targetNodeId,
      sourceHandle: conn.sourceOutput,
      targetHandle: conn.targetInput,
    }));
  };

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState(convertToReactFlowEdges(initialConnections));

  // Initialize nodes with callbacks
  useEffect(() => {
    setNodes(convertToReactFlowNodes(initialNodes));
  }, [initialNodes, convertToReactFlowNodes]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    const nodeData = node.data as WorkflowNode;
    setSelectedNode(nodeData);
  }, []);

  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
  }, []);

  const handleNodeUpdate = useCallback((nodeId: string, updates: Partial<WorkflowNode>) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const updatedData = { ...node.data, ...updates } as WorkflowNode;
          return { ...node, data: updatedData };
        }
        return node;
      })
    );

    // Update selected node if it's the one being updated
    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode({ ...selectedNode, ...updates });
    }
  }, [selectedNode]);



  const handleTestRun = useCallback(async () => {
    if (isTestRunning) return;

    setIsTestRunning(true);
    const executionId = uuidv4();
    setCurrentExecutionId(executionId);
    setShowDebug(true);

    try {
      // 模拟试运行
      console.log('开始试运行工作流...');

      // 这里应该调用后端API进行试运行
      // const response = await api.testWorkflow(workflowId, { nodes, edges });

      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 3000));

      console.log('试运行完成');
    } catch (error) {
      console.error('试运行失败:', error);
    } finally {
      setIsTestRunning(false);
    }
  }, [isTestRunning, workflowId, nodes, edges]);

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const nodeType = event.dataTransfer.getData('application/reactflow');

      if (typeof nodeType === 'undefined' || !nodeType) {
        return;
      }

      if (reactFlowWrapper.current && reactFlowInstance) {
        const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
        const position = reactFlowInstance.screenToFlowPosition({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        });

        const getNodeDisplayName = (nodeType: string): string => {
          const displayNames: Record<string, string> = {
            'http-request': 'HTTP请求',
            'set': '数据设置',
            'webhook-trigger': 'Webhook触发器',
            'cron-trigger': '定时触发器',
            'api-trigger': 'API触发器',
            'file-watcher-trigger': '文件监控触发器',
            'manual-trigger': '手动触发器',
          };
          return displayNames[nodeType] || nodeType;
        };

        const nodeId = uuidv4();
        const newNode: Node = {
          id: nodeId,
          type: 'custom',
          position,
          data: {
            id: nodeId,
            workflowId: workflowId || '',
            name: getNodeDisplayName(nodeType),
            type: nodeType,
            position,
            parameters: {},
            isDisabled: false,
            notes: '',
            onDelete: () => handleNodeDelete(nodeId),
            onEdit: () => {
              const nodeData = nodes.find(n => n.id === nodeId)?.data as WorkflowNode;
              if (nodeData) {
                setSelectedNode(nodeData);
              }
            },
          } as Record<string, unknown>,
        };

        setNodes((nds) => nds.concat(newNode));
      }
    },
    [reactFlowInstance, workflowId, setNodes, handleNodeDelete, nodes]
  );

  const onNodeDragStart = useCallback((event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleSave = useCallback(() => {
    if (onSave) {
      // Convert ReactFlow nodes back to WorkflowNode
      const workflowNodes: WorkflowNode[] = nodes.map(node => ({
        ...(node.data as WorkflowNode),
        position: node.position,
      }));

      // Convert ReactFlow edges back to NodeConnection
      const connections: NodeConnection[] = edges.map(edge => ({
        id: edge.id,
        workflowId: workflowId || '',
        sourceNodeId: edge.source,
        targetNodeId: edge.target,
        sourceOutput: edge.sourceHandle || 'main',
        targetInput: edge.targetHandle || 'main',
      }));

      onSave(workflowNodes, connections);
      setLastSaved(new Date());
    }
  }, [nodes, edges, workflowId, onSave]);

  return (
    <div className="flex h-screen" style={{ backgroundColor: 'var(--coze-bg-secondary)' }}>
      {/* Main Editor */}
      <div className={`${showLogs ? 'flex-1' : 'flex-1'} relative flex flex-col`}>
        {/* Header */}
        <div className="coze-card border-0 rounded-none shadow-sm">
          <div className="px-6 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack || (() => window.history.back())}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                返回工作流列表
              </button>
              <div className="h-6 w-px" style={{ backgroundColor: 'var(--coze-border)' }}></div>
              <h1 className="text-xl font-semibold" style={{ color: 'var(--coze-text-primary)' }}>
                工作流编辑器
              </h1>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleTestRun}
                disabled={isTestRunning || nodes.length === 0}
                className={`coze-btn ${isTestRunning ? 'coze-btn-warning' : 'coze-btn-success'}`}
              >
                {isTestRunning ? (
                  <>
                    <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    试运行中...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M5 12h14l-1 7H6l-1-7z" />
                    </svg>
                    试运行
                  </>
                )}
              </button>
              <button
                onClick={() => setShowDebug(true)}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
                调试
              </button>
              <div className="h-6 w-px" style={{ backgroundColor: 'var(--coze-border)' }}></div>
              <button
                onClick={handleSave}
                className="coze-btn coze-btn-primary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                保存
              </button>
              <button
                onClick={() => {
                  if (window.confirm('确定要清空所有节点吗？')) {
                    setNodes([]);
                    setEdges([]);
                    setSelectedNode(null);
                  }
                }}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                清空
              </button>
              <button
                onClick={() => setShowLogs(!showLogs)}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                {showLogs ? '隐藏日志' : '显示日志'}
              </button>
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div ref={reactFlowWrapper} className="flex-1">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            onNodeClick={onNodeClick}
            onPaneClick={onPaneClick}
            nodeTypes={nodeTypes}
            fitView
            style={{ backgroundColor: 'var(--coze-bg-secondary)' }}
          >
            <Controls
              style={{
                backgroundColor: 'var(--coze-bg-primary)',
                border: `1px solid var(--coze-border)`,
                borderRadius: 'var(--coze-radius-md)',
                boxShadow: 'var(--coze-shadow-md)',
              }}
            />
            <MiniMap
              style={{
                backgroundColor: 'var(--coze-bg-primary)',
                border: `1px solid var(--coze-border)`,
                borderRadius: 'var(--coze-radius-md)',
                boxShadow: 'var(--coze-shadow-md)',
              }}
            />
            <Background variant={BackgroundVariant.Dots} gap={20} size={1} color="var(--coze-border)" />
          </ReactFlow>
        </div>

        {/* Status Bar */}
        <WorkflowStatusBar
          nodeCount={nodes.length}
          connectionCount={edges.length}
          selectedNodeId={selectedNode?.id}
          isTestRunning={isTestRunning}
          lastSaved={lastSaved}
        />
      </div>

      {/* Node Sidebar */}
      <NodeSidebar onNodeDragStart={onNodeDragStart} />

      {/* Node Config Panel */}
      <NodeConfigPanel
        selectedNode={selectedNode}
        onNodeUpdate={handleNodeUpdate}
        onClose={() => setSelectedNode(null)}
      />

      {/* Log Panel */}
      {showLogs && (
        <div className="w-96 bg-white border-l border-gray-200">
          <LogViewer workflowId={workflowId} autoRefresh={true} />
        </div>
      )}

      {/* Debug Panel */}
      <DebugPanel
        isOpen={showDebug}
        onClose={() => setShowDebug(false)}
        workflowId={workflowId}
        executionId={currentExecutionId}
      />
    </div>
  );
};

const WorkflowEditorWithProvider: React.FC<WorkflowEditorProps> = (props) => (
  <ReactFlowProvider>
    <WorkflowEditor {...props} />
  </ReactFlowProvider>
);

export default WorkflowEditorWithProvider;

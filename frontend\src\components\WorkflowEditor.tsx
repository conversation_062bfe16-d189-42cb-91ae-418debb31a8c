import React, { useState, useCallback, useRef } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
  ReactFlowInstance,
} from '@xyflow/react';
import { v4 as uuidv4 } from 'uuid';

import CustomNode from './nodes/CustomNode';
import NodeSidebar from './NodeSidebar';
import { WorkflowNode, NodeConnection } from '../types/workflow';

const nodeTypes = {
  custom: CustomNode,
};

interface WorkflowEditorProps {
  workflowId?: string;
  initialNodes?: WorkflowNode[];
  initialConnections?: NodeConnection[];
  onSave?: (nodes: WorkflowNode[], connections: NodeConnection[]) => void;
}

const WorkflowEditor: React.FC<WorkflowEditorProps> = ({
  workflowId,
  initialNodes = [],
  initialConnections = [],
  onSave,
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  
  // Convert WorkflowNode to ReactFlow Node
  const convertToReactFlowNodes = (workflowNodes: WorkflowNode[]): Node[] => {
    return workflowNodes.map(node => ({
      id: node.id,
      type: 'custom',
      position: node.position,
      data: node,
    }));
  };

  // Convert NodeConnection to ReactFlow Edge
  const convertToReactFlowEdges = (connections: NodeConnection[]): Edge[] => {
    return connections.map(conn => ({
      id: conn.id,
      source: conn.sourceNodeId,
      target: conn.targetNodeId,
      sourceHandle: conn.sourceOutput,
      targetHandle: conn.targetInput,
    }));
  };

  const [nodes, setNodes, onNodesChange] = useNodesState(convertToReactFlowNodes(initialNodes));
  const [edges, setEdges, onEdgesChange] = useEdgesState(convertToReactFlowEdges(initialConnections));

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const nodeType = event.dataTransfer.getData('application/reactflow');

      if (typeof nodeType === 'undefined' || !nodeType) {
        return;
      }

      if (reactFlowWrapper.current && reactFlowInstance) {
        const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
        const position = reactFlowInstance.screenToFlowPosition({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        });

        const newNode: Node = {
          id: uuidv4(),
          type: 'custom',
          position,
          data: {
            id: uuidv4(),
            workflowId: workflowId || '',
            name: `${nodeType} Node`,
            type: nodeType,
            position,
            parameters: {},
            isDisabled: false,
            notes: '',
          },
        };

        setNodes((nds) => nds.concat(newNode));
      }
    },
    [reactFlowInstance, workflowId, setNodes]
  );

  const onNodeDragStart = useCallback((event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleSave = useCallback(() => {
    if (onSave) {
      // Convert ReactFlow nodes back to WorkflowNode
      const workflowNodes: WorkflowNode[] = nodes.map(node => ({
        ...node.data,
        position: node.position,
      }));

      // Convert ReactFlow edges back to NodeConnection
      const connections: NodeConnection[] = edges.map(edge => ({
        id: edge.id,
        workflowId: workflowId || '',
        sourceNodeId: edge.source,
        targetNodeId: edge.target,
        sourceOutput: edge.sourceHandle || 'main',
        targetInput: edge.targetHandle || 'main',
      }));

      onSave(workflowNodes, connections);
    }
  }, [nodes, edges, workflowId, onSave]);

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Main Editor */}
      <div className="flex-1 relative">
        <div ref={reactFlowWrapper} className="w-full h-full">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            nodeTypes={nodeTypes}
            fitView
            className="bg-gray-50"
          >
            <Controls />
            <MiniMap />
            <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
          </ReactFlow>
        </div>

        {/* Toolbar */}
        <div className="absolute top-4 left-4 bg-white rounded-lg shadow-md p-2 flex gap-2">
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            Save
          </button>
          <button
            onClick={() => setNodes([])}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Clear
          </button>
        </div>
      </div>

      {/* Node Sidebar */}
      <NodeSidebar onNodeDragStart={onNodeDragStart} />
    </div>
  );
};

const WorkflowEditorWithProvider: React.FC<WorkflowEditorProps> = (props) => (
  <ReactFlowProvider>
    <WorkflowEditor {...props} />
  </ReactFlowProvider>
);

export default WorkflowEditorWithProvider;

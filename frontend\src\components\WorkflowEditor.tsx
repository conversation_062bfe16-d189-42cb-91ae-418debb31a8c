import React, { useState, useCallback, useRef, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  ReactFlow,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
} from '@xyflow/react';
import type {
  Node,
  Edge,
  Connection,
  ReactFlowInstance,
} from '@xyflow/react';

import CustomNode from './nodes/CustomNode';
import NodeSidebar from './NodeSidebar';
import NodeConfigPanel from './NodeConfigPanel';
import LogViewer from './LogViewer';
import DebugPanel from './DebugPanel';
import WorkflowStatusBar from './WorkflowStatusBar';
import type { WorkflowNode, NodeConnection } from '../types/workflow';

// 节点定义接口
interface NodeDefinition {
  nodeType: string;
  displayName: string;
  description: string;
  category: string;
  icon: string;
  isTrigger: boolean;
  parameters: Array<{
    name: string;
    displayName: string;
    description: string;
    type: number;
    required: boolean;
    defaultValue: any;
  }>;
}

const nodeTypes = {
  custom: CustomNode,
};

interface WorkflowEditorProps {
  workflowId?: string;
  initialNodes?: WorkflowNode[];
  initialConnections?: NodeConnection[];
  onSave?: (nodes: WorkflowNode[], connections: NodeConnection[]) => void;
  onBack?: () => void;
}

// 节点加载服务
class NodeLoaderService {
  private static instance: NodeLoaderService;
  private nodeDefinitions: NodeDefinition[] = [];
  private nodeDisplayNames: Record<string, string> = {};

  static getInstance(): NodeLoaderService {
    if (!NodeLoaderService.instance) {
      NodeLoaderService.instance = new NodeLoaderService();
    }
    return NodeLoaderService.instance;
  }

  async loadNodeDefinitions(): Promise<NodeDefinition[]> {
    try {
      const response = await fetch('http://localhost:5053/api/nodes');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const nodes = await response.json();
      this.nodeDefinitions = nodes;
      
      // 构建显示名称映射
      this.nodeDisplayNames = {};
      nodes.forEach((node: NodeDefinition) => {
        this.nodeDisplayNames[node.nodeType] = node.displayName;
      });
      
      return nodes;
    } catch (error) {
      console.error('加载节点定义失败:', error);
      return this.getDefaultNodeDefinitions();
    }
  }

  getNodeDisplayName(nodeType: string): string {
    return this.nodeDisplayNames[nodeType] || nodeType;
  }

  getNodeDefinitions(): NodeDefinition[] {
    return this.nodeDefinitions;
  }

  private getDefaultNodeDefinitions(): NodeDefinition[] {
    return [
      {
        nodeType: 'http-request',
        displayName: 'HTTP请求',
        description: '向外部API发送HTTP请求',
        category: '网络',
        icon: 'globe',
        isTrigger: false,
        parameters: []
      },
      {
        nodeType: 'set',
        displayName: '数据设置',
        description: '设置值和转换数据',
        category: '数据',
        icon: 'edit',
        isTrigger: false,
        parameters: []
      },
      {
        nodeType: 'manual-trigger',
        displayName: '手动触发器',
        description: '通过手动点击按钮触发工作流执行',
        category: '触发器',
        icon: 'hand-pointer',
        isTrigger: true,
        parameters: []
      }
    ];
  }
}

const WorkflowEditor: React.FC<WorkflowEditorProps> = ({
  workflowId,
  initialNodes = [],
  initialConnections = [],
  onSave,
  onBack,
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [showLogs, setShowLogs] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [nodeDefinitions, setNodeDefinitions] = useState<NodeDefinition[]>([]);
  const nodeLoader = NodeLoaderService.getInstance();

  // 初始化节点定义
  useEffect(() => {
    const loadNodes = async () => {
      const definitions = await nodeLoader.loadNodeDefinitions();
      setNodeDefinitions(definitions);
    };
    loadNodes();
  }, []);

  // Convert NodeConnection to ReactFlow Edge
  const convertToReactFlowEdges = useCallback((connections: NodeConnection[]): Edge[] => {
    return connections.map(conn => ({
      id: conn.id,
      source: conn.sourceNodeId,
      target: conn.targetNodeId,
      sourceHandle: conn.sourceOutput,
      targetHandle: conn.targetInput,
    }));
  }, []);

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState(convertToReactFlowEdges(initialConnections));

  // Node delete handler
  const handleNodeDelete = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) =>
      edge.source !== nodeId && edge.target !== nodeId
    ));
    setSelectedNode((current) => {
      if (current && current.id === nodeId) {
        return null;
      }
      return current;
    });
  }, [setNodes, setEdges]);

  // Convert WorkflowNode to ReactFlow Node
  const convertToReactFlowNodes = useCallback((workflowNodes: WorkflowNode[]): Node[] => {
    return workflowNodes.map(node => ({
      id: node.id,
      type: 'custom',
      position: node.position,
      data: {
        ...node,
        onDelete: () => handleNodeDelete(node.id),
        onEdit: () => setSelectedNode(node),
      } as Record<string, unknown>,
    }));
  }, [handleNodeDelete]);

  // Initialize nodes with callbacks
  useEffect(() => {
    setNodes(convertToReactFlowNodes(initialNodes));
  }, [initialNodes, convertToReactFlowNodes, setNodes]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    const nodeData = node.data as WorkflowNode;
    setSelectedNode(nodeData);
  }, []);

  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
  }, []);

  const handleNodeUpdate = useCallback((nodeId: string, updates: Partial<WorkflowNode>) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const updatedData = { ...node.data, ...updates } as WorkflowNode;
          return { ...node, data: updatedData };
        }
        return node;
      })
    );

    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode({ ...selectedNode, ...updates });
    }
  }, [selectedNode, setNodes]);

  const handleTestRun = useCallback(async () => {
    if (isTestRunning) return;

    setIsTestRunning(true);
    const executionId = uuidv4();
    setCurrentExecutionId(executionId);
    setShowDebug(true);

    try {
      console.log('开始试运行工作流...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.log('试运行完成');
    } catch (error) {
      console.error('试运行失败:', error);
    } finally {
      setIsTestRunning(false);
    }
  }, [isTestRunning]);

  // 手动触发工作流
  const handleManualTrigger = useCallback(async () => {
    if (!workflowId) {
      alert('请先保存工作流');
      return;
    }

    const manualTriggerNode = nodes.find(node => {
      const nodeData = node.data as WorkflowNode;
      return nodeData.type === 'manual-trigger';
    });

    if (!manualTriggerNode) {
      alert('工作流中没有手动触发器节点');
      return;
    }

    const nodeData = manualTriggerNode.data as WorkflowNode;
    const confirmMessage = nodeData.parameters?.confirmMessage as string;
    
    if (confirmMessage && !window.confirm(confirmMessage)) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5053/api/trigger/manual/${workflowId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          triggeredBy: 'user',
          triggeredAt: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        alert('工作流触发成功！');
        setCurrentExecutionId(result.executionId);
        setShowDebug(true);
      } else {
        alert(`工作流触发失败: ${result.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('手动触发失败:', error);
      alert('手动触发失败，请检查网络连接和后端服务');
    }
  }, [workflowId, nodes]);

  // 检查是否有手动触发器节点
  const hasManualTrigger = nodes.some(node => {
    const nodeData = node.data as WorkflowNode;
    return nodeData.type === 'manual-trigger';
  });

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const nodeType = event.dataTransfer.getData('application/reactflow');

      if (typeof nodeType === 'undefined' || !nodeType) {
        return;
      }

      if (reactFlowWrapper.current && reactFlowInstance) {
        const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
        const position = reactFlowInstance.screenToFlowPosition({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        });

        // 使用动态节点定义获取显示名称
        const getNodeDisplayName = (nodeType: string): string => {
          return nodeLoader.getNodeDisplayName(nodeType);
        };

        // 获取节点的默认参数
        const getNodeDefaultParameters = (nodeType: string): Record<string, any> => {
          const nodeDef = nodeDefinitions.find(def => def.nodeType === nodeType);
          if (!nodeDef) return {};

          const defaultParams: Record<string, any> = {};
          nodeDef.parameters.forEach(param => {
            if (param.defaultValue !== undefined) {
              defaultParams[param.name] = param.defaultValue;
            }
          });
          return defaultParams;
        };

        const nodeId = uuidv4();
        const newNode: Node = {
          id: nodeId,
          type: 'custom',
          position,
          data: {
            id: nodeId,
            workflowId: workflowId || '',
            name: getNodeDisplayName(nodeType),
            type: nodeType,
            position,
            parameters: getNodeDefaultParameters(nodeType),
            isDisabled: false,
            notes: '',
            onDelete: () => handleNodeDelete(nodeId),
            onEdit: () => {
              const nodeData = nodes.find(n => n.id === nodeId)?.data as WorkflowNode;
              if (nodeData) {
                setSelectedNode(nodeData);
              }
            },
          } as Record<string, unknown>,
        };

        setNodes((nds) => nds.concat(newNode));
      }
    },
    [reactFlowInstance, workflowId, setNodes, handleNodeDelete, nodes, nodeLoader, nodeDefinitions]
  );

  const onNodeDragStart = useCallback((event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleSave = useCallback(() => {
    if (onSave) {
      // Convert ReactFlow nodes back to WorkflowNode
      const workflowNodes: WorkflowNode[] = nodes.map(node => ({
        ...(node.data as WorkflowNode),
        position: node.position,
      }));

      // Convert ReactFlow edges back to NodeConnection
      const connections: NodeConnection[] = edges.map(edge => ({
        id: edge.id,
        workflowId: workflowId || '',
        sourceNodeId: edge.source,
        targetNodeId: edge.target,
        sourceOutput: edge.sourceHandle || 'main',
        targetInput: edge.targetHandle || 'main',
      }));

      onSave(workflowNodes, connections);
      setLastSaved(new Date());
    }
  }, [nodes, edges, workflowId, onSave]);

  return (
    <div className="flex h-screen" style={{ backgroundColor: 'var(--coze-bg-secondary)' }}>
      {/* Main Editor */}
      <div className={`${showLogs ? 'flex-1' : 'flex-1'} relative flex flex-col`}>
        {/* Header */}
        <div className="coze-card border-0 rounded-none shadow-sm">
          <div className="px-6 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack || (() => window.history.back())}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                返回工作流列表
              </button>
              <div className="h-6 w-px" style={{ backgroundColor: 'var(--coze-border)' }}></div>
              <h1 className="text-xl font-semibold" style={{ color: 'var(--coze-text-primary)' }}>
                工作流编辑器
              </h1>
            </div>
            <div className="flex items-center space-x-3">
              {hasManualTrigger && (
                <button
                  onClick={handleManualTrigger}
                  className="coze-btn coze-btn-primary"
                  title="手动触发工作流"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                  </svg>
                  立即执行
                </button>
              )}
              <button
                onClick={handleTestRun}
                disabled={isTestRunning || nodes.length === 0}
                className={`coze-btn ${isTestRunning ? 'coze-btn-warning' : 'coze-btn-success'}`}
              >
                {isTestRunning ? (
                  <>
                    <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    试运行中...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M5 12h14l-1 7H6l-1-7z" />
                    </svg>
                    试运行
                  </>
                )}
              </button>
              <button
                onClick={() => setShowDebug(true)}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
                调试
              </button>
              <div className="h-6 w-px" style={{ backgroundColor: 'var(--coze-border)' }}></div>
              <button
                onClick={handleSave}
                className="coze-btn coze-btn-primary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                保存
              </button>
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div ref={reactFlowWrapper} className="flex-1">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            onNodeClick={onNodeClick}
            onPaneClick={onPaneClick}
            nodeTypes={nodeTypes}
            fitView
            style={{ backgroundColor: 'var(--coze-bg-secondary)' }}
          >
            <Controls />
            <MiniMap />
            <Background variant={BackgroundVariant.Dots} gap={20} size={1} color="var(--coze-border)" />
          </ReactFlow>
        </div>

        {/* Status Bar */}
        <WorkflowStatusBar
          nodeCount={nodes.length}
          connectionCount={edges.length}
          selectedNodeId={selectedNode?.id}
          isTestRunning={isTestRunning}
          lastSaved={lastSaved}
        />
      </div>

      {/* Node Sidebar */}
      <NodeSidebar
        onNodeDragStart={onNodeDragStart}
        nodeDefinitions={nodeDefinitions}
      />

      {/* Node Config Panel */}
      <NodeConfigPanel
        selectedNode={selectedNode}
        onNodeUpdate={handleNodeUpdate}
        onClose={() => setSelectedNode(null)}
      />

      {/* Log Panel */}
      {showLogs && (
        <div className="w-96 bg-white border-l border-gray-200">
          <LogViewer workflowId={workflowId} autoRefresh={true} />
        </div>
      )}

      {/* Debug Panel */}
      <DebugPanel
        isOpen={showDebug}
        onClose={() => setShowDebug(false)}
        workflowId={workflowId}
        executionId={currentExecutionId}
      />
    </div>
  );
};

const WorkflowEditorWithProvider: React.FC<WorkflowEditorProps> = (props) => (
  <ReactFlowProvider>
    <WorkflowEditor {...props} />
  </ReactFlowProvider>
);

export default WorkflowEditorWithProvider;

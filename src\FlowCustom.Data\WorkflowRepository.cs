using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;

namespace FlowCustom.Data;

/// <summary>
/// 工作流数据仓储实现
/// </summary>
public class WorkflowRepository : IWorkflowService
{
    // 临时使用内存存储，后续可以替换为数据库实现
    private readonly Dictionary<Guid, Workflow> _workflows = new();
    private readonly Dictionary<Guid, List<WorkflowExecution>> _executions = new();

    public async Task<Workflow> CreateWorkflowAsync(Workflow workflow)
    {
        workflow.Id = Guid.NewGuid();
        workflow.CreatedAt = DateTime.UtcNow;
        workflow.UpdatedAt = DateTime.UtcNow;
        
        _workflows[workflow.Id] = workflow;
        
        await Task.CompletedTask;
        return workflow;
    }

    public async Task<Workflow> UpdateWorkflowAsync(Workflow workflow)
    {
        if (!_workflows.ContainsKey(workflow.Id))
        {
            throw new ArgumentException($"Workflow not found: {workflow.Id}");
        }

        workflow.UpdatedAt = DateTime.UtcNow;
        _workflows[workflow.Id] = workflow;
        
        await Task.CompletedTask;
        return workflow;
    }

    public async Task DeleteWorkflowAsync(Guid workflowId)
    {
        _workflows.Remove(workflowId);
        _executions.Remove(workflowId);
        
        await Task.CompletedTask;
    }

    public async Task<Workflow?> GetWorkflowAsync(Guid workflowId)
    {
        await Task.CompletedTask;
        return _workflows.TryGetValue(workflowId, out var workflow) ? workflow : null;
    }

    public async Task<IEnumerable<Workflow>> GetUserWorkflowsAsync(string userId)
    {
        await Task.CompletedTask;
        return _workflows.Values.Where(w => w.CreatedBy == userId);
    }

    public async Task SetWorkflowActiveAsync(Guid workflowId, bool isActive)
    {
        if (_workflows.TryGetValue(workflowId, out var workflow))
        {
            workflow.IsActive = isActive;
            workflow.UpdatedAt = DateTime.UtcNow;
        }
        
        await Task.CompletedTask;
    }

    public async Task<ValidationResult> ValidateWorkflowAsync(Workflow workflow)
    {
        var errors = new List<string>();

        if (string.IsNullOrEmpty(workflow.Name))
        {
            errors.Add("Workflow name is required");
        }

        if (!workflow.Nodes.Any())
        {
            errors.Add("Workflow must have at least one node");
        }

        // 验证节点连接的有效性
        foreach (var connection in workflow.Connections)
        {
            var sourceNode = workflow.Nodes.FirstOrDefault(n => n.Id == connection.SourceNodeId);
            var targetNode = workflow.Nodes.FirstOrDefault(n => n.Id == connection.TargetNodeId);

            if (sourceNode == null)
            {
                errors.Add($"Source node not found for connection: {connection.Id}");
            }

            if (targetNode == null)
            {
                errors.Add($"Target node not found for connection: {connection.Id}");
            }
        }

        await Task.CompletedTask;
        return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
    }

    /// <summary>
    /// 保存工作流执行记录
    /// </summary>
    public async Task SaveExecutionAsync(WorkflowExecution execution)
    {
        if (!_executions.ContainsKey(execution.WorkflowId))
        {
            _executions[execution.WorkflowId] = new List<WorkflowExecution>();
        }

        _executions[execution.WorkflowId].Add(execution);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取工作流执行记录
    /// </summary>
    public async Task<WorkflowExecution?> GetExecutionAsync(Guid executionId)
    {
        await Task.CompletedTask;
        
        foreach (var executions in _executions.Values)
        {
            var execution = executions.FirstOrDefault(e => e.Id == executionId);
            if (execution != null)
            {
                return execution;
            }
        }

        return null;
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetExecutionHistoryAsync(
        Guid workflowId, 
        int page = 1, 
        int pageSize = 50)
    {
        await Task.CompletedTask;
        
        if (!_executions.TryGetValue(workflowId, out var executions))
        {
            return Enumerable.Empty<WorkflowExecution>();
        }

        return executions
            .OrderByDescending(e => e.StartedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize);
    }
}

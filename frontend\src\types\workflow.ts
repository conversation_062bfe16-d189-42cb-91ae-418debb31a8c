export interface NodePosition {
  x: number;
  y: number;
}

export interface WorkflowNode extends Record<string, unknown> {
  id: string;
  workflowId: string;
  name: string;
  type: string;
  position: NodePosition;
  parameters: Record<string, unknown>;
  isDisabled: boolean;
  notes: string;
  /** 自定义输入端点配置 - 覆盖默认配置 */
  customInputs?: NodeEndpoint[];
  /** 自定义输出端点配置 - 覆盖默认配置 */
  customOutputs?: NodeEndpoint[];
}

export interface NodeConnection {
  id: string;
  workflowId: string;
  sourceNodeId: string;
  targetNodeId: string;
  sourceOutput: string;
  targetInput: string;
}

export interface WorkflowSettings {
  timeout?: number;
  maxRetries: number;
  saveExecutionProgress: boolean;
  variables: Record<string, unknown>;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  nodes: WorkflowNode[];
  connections: NodeConnection[];
  settings: WorkflowSettings;
}

export interface NodeParameterDefinition {
  name: string;
  displayName: string;
  description: string;
  type: ParameterType;
  required: boolean;
  defaultValue?: unknown;
  options?: string[];
  validation: Record<string, unknown>;
}

export const ParameterType = {
  String: 'String',
  Number: 'Number',
  Boolean: 'Boolean',
  Object: 'Object',
  Array: 'Array',
  Select: 'Select',
  MultiSelect: 'MultiSelect',
  File: 'File',
  Credential: 'Credential',
  Expression: 'Expression'
} as const;

export type ParameterType = typeof ParameterType[keyof typeof ParameterType];

/**
 * 节点端点定义
 *
 * 定义节点的输入或输出连接点
 */
export interface NodeEndpoint {
  /** 端点ID，用于连线匹配 */
  id: string;
  /** 端点显示名称 */
  name: string;
  /** 端点描述 */
  description?: string;
  /** 端点类型：数据类型标识 */
  type: string;
  /** 是否必需 */
  required?: boolean;
  /** 端点位置：相对于节点的位置 */
  position: {
    /** 相对位置：top, bottom, left, right */
    side: 'top' | 'bottom' | 'left' | 'right';
    /** 在该边的偏移百分比 (0-100) */
    offset: number;
  };
}

/**
 * 节点定义接口
 *
 * 扩展支持多输入输出端点
 */
export interface NodeDefinition {
  nodeType: string;
  displayName: string;
  description: string;
  category: string;
  icon: string;
  isTrigger: boolean;
  parameters: NodeParameterDefinition[];
  /** 输入端点定义 */
  inputs?: NodeEndpoint[];
  /** 输出端点定义 */
  outputs?: NodeEndpoint[];
}

export const ExecutionStatus = {
  Waiting: 'Waiting',
  Running: 'Running',
  Success: 'Success',
  Error: 'Error',
  Cancelled: 'Cancelled',
  Timeout: 'Timeout'
} as const;

export type ExecutionStatus = typeof ExecutionStatus[keyof typeof ExecutionStatus];

export interface NodeExecution {
  id: string;
  executionId: string;
  nodeId: string;
  nodeName: string;
  nodeType: string;
  status: ExecutionStatus;
  startedAt: string;
  finishedAt?: string;
  duration?: number;
  error?: string;
  inputData: Record<string, unknown>;
  outputData: Record<string, unknown>;
  retryCount: number;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: ExecutionStatus;
  startedAt: string;
  finishedAt?: string;
  error?: string;
  triggeredBy?: string;
  inputData: Record<string, unknown>;
  outputData: Record<string, unknown>;
  nodeExecutions: NodeExecution[];
}

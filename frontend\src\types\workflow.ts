export interface NodePosition {
  x: number;
  y: number;
}

export interface WorkflowNode extends Record<string, unknown> {
  id: string;
  workflowId: string;
  name: string;
  type: string;
  position: NodePosition;
  parameters: Record<string, unknown>;
  isDisabled: boolean;
  notes: string;
}

export interface NodeConnection {
  id: string;
  workflowId: string;
  sourceNodeId: string;
  targetNodeId: string;
  sourceOutput: string;
  targetInput: string;
}

export interface WorkflowSettings {
  timeout?: number;
  maxRetries: number;
  saveExecutionProgress: boolean;
  variables: Record<string, unknown>;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  nodes: WorkflowNode[];
  connections: NodeConnection[];
  settings: WorkflowSettings;
}

export interface NodeParameterDefinition {
  name: string;
  displayName: string;
  description: string;
  type: ParameterType;
  required: boolean;
  defaultValue?: unknown;
  options?: string[];
  validation: Record<string, unknown>;
}

export const ParameterType = {
  String: 'String',
  Number: 'Number',
  Boolean: 'Boolean',
  Object: 'Object',
  Array: 'Array',
  Select: 'Select',
  MultiSelect: 'MultiSelect',
  File: 'File',
  Credential: 'Credential',
  Expression: 'Expression'
} as const;

export type ParameterType = typeof ParameterType[keyof typeof ParameterType];

export interface NodeDefinition {
  nodeType: string;
  displayName: string;
  description: string;
  category: string;
  icon: string;
  isTrigger: boolean;
  parameters: NodeParameterDefinition[];
}

export const ExecutionStatus = {
  Waiting: 'Waiting',
  Running: 'Running',
  Success: 'Success',
  Error: 'Error',
  Cancelled: 'Cancelled',
  Timeout: 'Timeout'
} as const;

export type ExecutionStatus = typeof ExecutionStatus[keyof typeof ExecutionStatus];

export interface NodeExecution {
  id: string;
  executionId: string;
  nodeId: string;
  nodeName: string;
  nodeType: string;
  status: ExecutionStatus;
  startedAt: string;
  finishedAt?: string;
  duration?: number;
  error?: string;
  inputData: Record<string, unknown>;
  outputData: Record<string, unknown>;
  retryCount: number;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: ExecutionStatus;
  startedAt: string;
  finishedAt?: string;
  error?: string;
  triggeredBy?: string;
  inputData: Record<string, unknown>;
  outputData: Record<string, unknown>;
  nodeExecutions: NodeExecution[];
}

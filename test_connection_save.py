#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连线保存功能测试脚本
专门测试连线保存后不消失的问题
"""

import requests
import json
import uuid
import time

# API 基础URL
BASE_URL = "http://localhost:5053/api"

def test_connection_save_and_reload():
    """测试连线保存和重新加载功能"""
    print("🔗 测试连线保存和重新加载功能...")
    
    # 1. 创建测试工作流
    workflow_data = {
        "name": "连线保存测试",
        "description": "测试连线保存后不消失的问题"
    }
    
    response = requests.post(f"{BASE_URL}/workflow", json=workflow_data)
    if response.status_code != 201:
        print(f"❌ 工作流创建失败: {response.status_code}")
        return None
    
    workflow = response.json()
    workflow_id = workflow["id"]
    print(f"✅ 工作流创建成功: {workflow_id}")
    
    # 2. 创建节点
    node1_id = str(uuid.uuid4())
    node2_id = str(uuid.uuid4())
    node3_id = str(uuid.uuid4())
    
    nodes = [
        {
            "id": node1_id,
            "workflowId": workflow_id,
            "name": "起始节点",
            "type": "manual-trigger",
            "position": {"x": 100, "y": 100},
            "parameters": {
                "buttonText": "开始",
                "enabled": True
            },
            "isDisabled": False,
            "notes": "起始节点"
        },
        {
            "id": node2_id,
            "workflowId": workflow_id,
            "name": "中间节点",
            "type": "script-executor",
            "position": {"x": 300, "y": 100},
            "parameters": {
                "scriptType": "python",
                "script": "print('中间节点执行')",
                "timeout": 30
            },
            "isDisabled": False,
            "notes": "中间处理节点"
        },
        {
            "id": node3_id,
            "workflowId": workflow_id,
            "name": "结束节点",
            "type": "script-executor",
            "position": {"x": 500, "y": 100},
            "parameters": {
                "scriptType": "javascript",
                "script": "console.log('结束节点执行');",
                "timeout": 30
            },
            "isDisabled": False,
            "notes": "结束节点"
        }
    ]
    
    # 3. 创建连线 - 重点测试对象
    conn1_id = str(uuid.uuid4())
    conn2_id = str(uuid.uuid4())
    
    connections = [
        {
            "id": conn1_id,
            "workflowId": workflow_id,
            "sourceNodeId": node1_id,
            "targetNodeId": node2_id,
            "sourceOutput": "main",
            "targetInput": "main"
        },
        {
            "id": conn2_id,
            "workflowId": workflow_id,
            "sourceNodeId": node2_id,
            "targetNodeId": node3_id,
            "sourceOutput": "main",
            "targetInput": "main"
        }
    ]
    
    print(f"📦 创建了 {len(nodes)} 个节点和 {len(connections)} 条连线")
    for i, conn in enumerate(connections, 1):
        print(f"   连线{i}: {conn['sourceNodeId'][:8]}... -> {conn['targetNodeId'][:8]}...")
    
    # 4. 第一次保存 - 保存节点和连线
    workflow["nodes"] = nodes
    workflow["connections"] = connections
    workflow["isActive"] = True
    
    response = requests.put(f"{BASE_URL}/workflow/{workflow_id}", json=workflow)
    if response.status_code != 200:
        print(f"❌ 第一次保存失败: {response.status_code}")
        try:
            error_detail = response.json()
            print(f"   错误详情: {error_detail}")
        except:
            print(f"   错误文本: {response.text}")
        return None
    
    print(f"✅ 第一次保存成功")
    
    # 5. 立即重新加载 - 验证连线是否正确保存
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 重新加载失败: {response.status_code}")
        return None
    
    loaded_workflow = response.json()
    loaded_nodes = loaded_workflow.get("nodes", [])
    loaded_connections = loaded_workflow.get("connections", [])
    
    print(f"📋 重新加载结果:")
    print(f"   节点数量: {len(loaded_nodes)} (期望: {len(nodes)})")
    print(f"   连线数量: {len(loaded_connections)} (期望: {len(connections)})")
    
    # 6. 验证连线数据完整性
    if len(loaded_connections) != len(connections):
        print(f"❌ 连线数量不匹配!")
        return False
    
    # 验证每条连线的详细信息
    for i, (original, loaded) in enumerate(zip(connections, loaded_connections), 1):
        print(f"   连线{i}验证:")
        print(f"     ID: {loaded.get('id', 'N/A')}")
        print(f"     源节点: {loaded.get('sourceNodeId', 'N/A')[:8]}...")
        print(f"     目标节点: {loaded.get('targetNodeId', 'N/A')[:8]}...")
        print(f"     源输出: {loaded.get('sourceOutput', 'N/A')}")
        print(f"     目标输入: {loaded.get('targetInput', 'N/A')}")
        
        # 检查关键字段
        if (loaded.get('sourceNodeId') != original['sourceNodeId'] or
            loaded.get('targetNodeId') != original['targetNodeId']):
            print(f"     ❌ 连线{i}数据不匹配!")
            return False
        else:
            print(f"     ✅ 连线{i}数据正确")
    
    # 7. 模拟前端操作 - 修改节点位置后再次保存
    print(f"\n🔄 模拟前端编辑操作...")
    
    # 修改节点位置（模拟用户拖拽）
    for node in loaded_nodes:
        node['position']['x'] += 50  # 向右移动50像素
    
    # 保持连线不变
    updated_workflow = loaded_workflow
    updated_workflow["nodes"] = loaded_nodes
    updated_workflow["connections"] = loaded_connections  # 保持原有连线
    
    # 第二次保存
    response = requests.put(f"{BASE_URL}/workflow/{workflow_id}", json=updated_workflow)
    if response.status_code != 200:
        print(f"❌ 第二次保存失败: {response.status_code}")
        return False
    
    print(f"✅ 第二次保存成功（模拟前端编辑后保存）")
    
    # 8. 再次重新加载 - 验证连线是否仍然存在
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 第二次重新加载失败: {response.status_code}")
        return False
    
    final_workflow = response.json()
    final_connections = final_workflow.get("connections", [])
    
    print(f"🔍 第二次加载后的连线数量: {len(final_connections)} (期望: {len(connections)})")
    
    if len(final_connections) != len(connections):
        print(f"❌ 连线在第二次保存后消失了!")
        print(f"   原始连线数: {len(connections)}")
        print(f"   最终连线数: {len(final_connections)}")
        return False
    
    print(f"✅ 连线在多次保存后仍然正确保存!")
    
    # 9. 执行工作流验证连线功能
    print(f"\n🚀 执行工作流验证连线功能...")
    
    response = requests.post(f"{BASE_URL}/trigger/manual/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 工作流执行失败: {response.status_code}")
        return False
    
    result = response.json()
    execution_id = result.get("executionId")
    print(f"✅ 工作流执行成功，执行ID: {execution_id}")
    
    # 等待执行完成
    time.sleep(3)
    
    # 检查执行结果
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}/history")
    if response.status_code == 200:
        executions = response.json()
        if executions:
            latest = executions[0]
            status = latest.get('status', 0)
            node_executions = latest.get('nodeExecutions', [])
            
            print(f"📊 执行结果:")
            print(f"   工作流状态: {'成功' if status == 2 else '失败' if status == 3 else '运行中'}")
            print(f"   节点执行数: {len(node_executions)}")
            
            # 如果有3个节点都执行了，说明连线工作正常
            if len(node_executions) == 3:
                print(f"✅ 所有节点都执行了，连线功能正常!")
            else:
                print(f"⚠️ 只有{len(node_executions)}个节点执行，可能连线有问题")
    
    return True

def main():
    print("🔗 连线保存功能专项测试")
    print("="*50)
    
    try:
        success = test_connection_save_and_reload()
        
        print("\n" + "="*50)
        if success:
            print("✅ 连线保存功能测试通过!")
            print("\n🎯 测试验证的功能:")
            print("   ✅ 连线正确保存到数据库")
            print("   ✅ 连线正确从数据库加载")
            print("   ✅ 连线在多次保存后不消失")
            print("   ✅ 连线功能正常工作（节点按顺序执行）")
            print("\n💡 前端使用建议:")
            print("   1. 保存后不要重新加载数据，保持ReactFlow状态")
            print("   2. 只在必要时（如页面刷新）才重新加载")
            print("   3. 使用状态更新而不是数据重载来更新UI")
        else:
            print("❌ 连线保存功能测试失败!")
            print("\n🔧 可能的问题:")
            print("   - 后端API保存逻辑有问题")
            print("   - 数据库连线表结构问题")
            print("   - 前端数据转换格式错误")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

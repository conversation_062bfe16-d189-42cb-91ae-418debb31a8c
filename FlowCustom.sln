﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

# 解决方案文件夹
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Plugins", "Plugins", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PluginSDK", "PluginSDK", "{C3D4E5F6-G7H8-9012-CDEF-345678901234}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{D4E5F6G7-H8I9-0123-DEF0-456789012345}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Legacy", "Legacy", "{E5F6G7H8-I9J0-1234-EF01-567890123456}"
EndProject

# 核心框架项目
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.PluginHost", "Core\FlowCustom.PluginHost\FlowCustom.PluginHost.csproj", "{F6G7H8I9-J0K1-2345-F012-678901234567}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Engine", "Core\FlowCustom.Engine\FlowCustom.Engine.csproj", "{G7H8I9J0-K1L2-3456-0123-789012345678}"
EndProject

# 插件SDK
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.SDK", "PluginSDK\FlowCustom.SDK\FlowCustom.SDK.csproj", "{H8I9J0K1-L2M3-4567-1234-890123456789}"
EndProject

# 插件项目
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Plugins.Core", "Plugins\FlowCustom.Plugins.Core\FlowCustom.Plugins.Core.csproj", "{I9J0K1L2-M3N4-5678-2345-901234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Plugins.Http", "Plugins\FlowCustom.Plugins.Http\FlowCustom.Plugins.Http.csproj", "{J0K1L2M3-N4O5-6789-3456-012345678901}"
EndProject

# 测试项目
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Tests", "tests\FlowCustom.Tests\FlowCustom.Tests.csproj", "{301C3AD2-1191-4758-9DB3-DE0E948EA815}"
EndProject

# 遗留项目（向后兼容）
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Core", "src\FlowCustom.Core\FlowCustom.Core.csproj", "{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Api", "src\FlowCustom.Api\FlowCustom.Api.csproj", "{01B332F7-0363-4375-BF8A-A372F78ADDD0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Data", "src\FlowCustom.Data\FlowCustom.Data.csproj", "{ED8AE694-065E-4D46-990B-627DF8182C71}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Engine.Legacy", "src\FlowCustom.Engine\FlowCustom.Engine.csproj", "{70FBADCF-A390-4F36-A68C-0394E1C58262}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Nodes", "src\FlowCustom.Nodes\FlowCustom.Nodes.csproj", "{77033782-2640-4E2F-A69B-9D42767C49C3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		# 核心框架项目配置
		{F6G7H8I9-J0K1-2345-F012-678901234567}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-F012-678901234567}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-F012-678901234567}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6G7H8I9-J0K1-2345-F012-678901234567}.Release|Any CPU.Build.0 = Release|Any CPU
		{G7H8I9J0-K1L2-3456-0123-789012345678}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-0123-789012345678}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-0123-789012345678}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{G7H8I9J0-K1L2-3456-0123-789012345678}.Release|Any CPU.Build.0 = Release|Any CPU

		# 插件SDK配置
		{H8I9J0K1-L2M3-4567-1234-890123456789}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-1234-890123456789}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-1234-890123456789}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{H8I9J0K1-L2M3-4567-1234-890123456789}.Release|Any CPU.Build.0 = Release|Any CPU

		# 插件项目配置
		{I9J0K1L2-M3N4-5678-2345-901234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-2345-901234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-2345-901234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{I9J0K1L2-M3N4-5678-2345-901234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Release|Any CPU.Build.0 = Release|Any CPU

		# 测试项目配置
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|Any CPU.Build.0 = Release|Any CPU

		# 遗留项目配置（向后兼容）
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|x64.Build.0 = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|x86.Build.0 = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|x64.ActiveCfg = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|x64.Build.0 = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|x86.ActiveCfg = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|x86.Build.0 = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|x64.Build.0 = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|x86.Build.0 = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|Any CPU.Build.0 = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|x64.ActiveCfg = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|x64.Build.0 = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|x86.ActiveCfg = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|x86.Build.0 = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|x64.ActiveCfg = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|x64.Build.0 = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|x86.ActiveCfg = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|x86.Build.0 = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|x64.ActiveCfg = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|x64.Build.0 = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|x86.ActiveCfg = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|x86.Build.0 = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|x64.ActiveCfg = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|x64.Build.0 = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|x86.ActiveCfg = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|x86.Build.0 = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|Any CPU.Build.0 = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|x64.ActiveCfg = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|x64.Build.0 = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|x86.ActiveCfg = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|x86.Build.0 = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|x64.Build.0 = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|x86.Build.0 = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|Any CPU.Build.0 = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|x64.ActiveCfg = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|x64.Build.0 = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|x86.ActiveCfg = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|x86.Build.0 = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|x64.ActiveCfg = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|x64.Build.0 = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|x86.ActiveCfg = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|x86.Build.0 = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|Any CPU.Build.0 = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|x64.ActiveCfg = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|x64.Build.0 = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|x86.ActiveCfg = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		# 核心框架项目
		{F6G7H8I9-J0K1-2345-F012-678901234567} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{G7H8I9J0-K1L2-3456-0123-789012345678} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}

		# 插件SDK
		{H8I9J0K1-L2M3-4567-1234-890123456789} = {C3D4E5F6-G7H8-9012-CDEF-345678901234}

		# 插件项目
		{I9J0K1L2-M3N4-5678-2345-901234567890} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}
		{J0K1L2M3-N4O5-6789-3456-012345678901} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}

		# 测试项目
		{301C3AD2-1191-4758-9DB3-DE0E948EA815} = {D4E5F6G7-H8I9-0123-DEF0-456789012345}

		# 遗留项目
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE} = {E5F6G7H8-I9J0-1234-EF01-567890123456}
		{01B332F7-0363-4375-BF8A-A372F78ADDD0} = {E5F6G7H8-I9J0-1234-EF01-567890123456}
		{ED8AE694-065E-4D46-990B-627DF8182C71} = {E5F6G7H8-I9J0-1234-EF01-567890123456}
		{70FBADCF-A390-4F36-A68C-0394E1C58262} = {E5F6G7H8-I9J0-1234-EF01-567890123456}
		{77033782-2640-4E2F-A69B-9D42767C49C3} = {E5F6G7H8-I9J0-1234-EF01-567890123456}
	EndGlobalSection
EndGlobal

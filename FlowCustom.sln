﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Core", "src\FlowCustom.Core\FlowCustom.Core.csproj", "{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Api", "src\FlowCustom.Api\FlowCustom.Api.csproj", "{01B332F7-0363-4375-BF8A-A372F78ADDD0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Data", "src\FlowCustom.Data\FlowCustom.Data.csproj", "{ED8AE694-065E-4D46-990B-627DF8182C71}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Engine", "src\FlowCustom.Engine\FlowCustom.Engine.csproj", "{70FBADCF-A390-4F36-A68C-0394E1C58262}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Nodes", "src\FlowCustom.Nodes\FlowCustom.Nodes.csproj", "{77033782-2640-4E2F-A69B-9D42767C49C3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlowCustom.Tests", "tests\FlowCustom.Tests\FlowCustom.Tests.csproj", "{301C3AD2-1191-4758-9DB3-DE0E948EA815}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|x64.Build.0 = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Debug|x86.Build.0 = Debug|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|x64.ActiveCfg = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|x64.Build.0 = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|x86.ActiveCfg = Release|Any CPU
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}.Release|x86.Build.0 = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|x64.Build.0 = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Debug|x86.Build.0 = Debug|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|Any CPU.Build.0 = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|x64.ActiveCfg = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|x64.Build.0 = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|x86.ActiveCfg = Release|Any CPU
		{01B332F7-0363-4375-BF8A-A372F78ADDD0}.Release|x86.Build.0 = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|x64.ActiveCfg = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|x64.Build.0 = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|x86.ActiveCfg = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Debug|x86.Build.0 = Debug|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|x64.ActiveCfg = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|x64.Build.0 = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|x86.ActiveCfg = Release|Any CPU
		{ED8AE694-065E-4D46-990B-627DF8182C71}.Release|x86.Build.0 = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|x64.ActiveCfg = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|x64.Build.0 = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|x86.ActiveCfg = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Debug|x86.Build.0 = Debug|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|Any CPU.Build.0 = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|x64.ActiveCfg = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|x64.Build.0 = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|x86.ActiveCfg = Release|Any CPU
		{70FBADCF-A390-4F36-A68C-0394E1C58262}.Release|x86.Build.0 = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|x64.Build.0 = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Debug|x86.Build.0 = Debug|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|Any CPU.Build.0 = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|x64.ActiveCfg = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|x64.Build.0 = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|x86.ActiveCfg = Release|Any CPU
		{77033782-2640-4E2F-A69B-9D42767C49C3}.Release|x86.Build.0 = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|x64.ActiveCfg = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|x64.Build.0 = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|x86.ActiveCfg = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Debug|x86.Build.0 = Debug|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|Any CPU.Build.0 = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|x64.ActiveCfg = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|x64.Build.0 = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|x86.ActiveCfg = Release|Any CPU
		{301C3AD2-1191-4758-9DB3-DE0E948EA815}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{01B332F7-0363-4375-BF8A-A372F78ADDD0} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{ED8AE694-065E-4D46-990B-627DF8182C71} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{70FBADCF-A390-4F36-A68C-0394E1C58262} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{77033782-2640-4E2F-A69B-9D42767C49C3} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{301C3AD2-1191-4758-9DB3-DE0E948EA815} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
	EndGlobalSection
EndGlobal

{"id": "flowcustomv1-plugins-core", "name": "FlowCustomV1 Core Plugins", "description": "Core plugins for FlowCustomV1 workflow automation platform", "version": "1.0.0", "author": "FlowCustom Team", "assemblyName": "FlowCustomV1.Plugins.Core", "targetFramework": "net8.0", "minEngineVersion": "1.0.0", "maxEngineVersion": "2.0.0", "dependencies": [{"name": "FlowCustomV1.SDK", "version": "1.0.0"}], "nodes": [{"type": "manual-trigger", "class": "FlowCustomV1.Plugins.Core.ManualTriggerPlugin", "displayName": "手动触发器", "category": "触发器", "description": "手动触发工作流执行，适用于需要人工干预启动的场景", "icon": "🚀", "isTrigger": true, "tags": ["trigger", "manual", "user-action"]}, {"type": "set", "class": "FlowCustomV1.Plugins.Core.SetNodePlugin", "displayName": "数据设置", "category": "数据", "description": "设置值和转换数据，支持表达式计算和变量替换", "icon": "📝", "isTrigger": false, "tags": ["data", "transform", "set", "variables"]}], "metadata": {"buildDate": "2024-12-22T00:00:00Z", "buildNumber": "*******"}}
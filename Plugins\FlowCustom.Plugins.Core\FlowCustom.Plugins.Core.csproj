<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\PluginSDK\FlowCustom.SDK\FlowCustom.SDK.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.Diagnostics.Process" Version="4.3.0" />
  </ItemGroup>

  <!-- 插件部署配置 -->
  <Target Name="CopyPluginFiles" AfterTargets="Build">
    <ItemGroup>
      <PluginFiles Include="$(OutputPath)**/*" />
      <PluginManifest Include="plugin.json" />
    </ItemGroup>
    
    <MakeDir Directories="$(SolutionDir)Runtime\plugins\FlowCustom.Plugins.Core" />
    
    <Copy SourceFiles="@(PluginFiles)" 
          DestinationFolder="$(SolutionDir)Runtime\plugins\FlowCustom.Plugins.Core" 
          SkipUnchangedFiles="true" />
    
    <Copy SourceFiles="@(PluginManifest)" 
          DestinationFolder="$(SolutionDir)Runtime\plugins\FlowCustom.Plugins.Core" 
          SkipUnchangedFiles="true" />
  </Target>

</Project>

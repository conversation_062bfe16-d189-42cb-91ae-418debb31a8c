using FlowCustom.Core.Models;
using FlowCustom.Data;
using FlowCustom.Engine;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustom.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MonitoringController : ControllerBase
{
    private readonly ExecutionHistoryService _executionHistoryService;
    private readonly MonitoringService _monitoringService;

    public MonitoringController(
        ExecutionHistoryService executionHistoryService,
        MonitoringService monitoringService)
    {
        _executionHistoryService = executionHistoryService;
        _monitoringService = monitoringService;
    }

    /// <summary>
    /// 获取执行历史
    /// </summary>
    [HttpGet("executions")]
    public async Task<ActionResult<IEnumerable<WorkflowExecution>>> GetExecutions(
        [FromQuery] Guid? workflowId = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] ExecutionStatus? status = null)
    {
        IEnumerable<WorkflowExecution> executions;
        
        if (workflowId.HasValue)
        {
            executions = await _executionHistoryService.GetExecutionHistoryAsync(workflowId.Value, page, pageSize);
        }
        else
        {
            executions = await _executionHistoryService.GetAllExecutionsAsync(page, pageSize, status);
        }

        return Ok(executions);
    }

    /// <summary>
    /// 获取执行详情
    /// </summary>
    [HttpGet("executions/{executionId}")]
    public async Task<ActionResult<WorkflowExecution>> GetExecution(Guid executionId)
    {
        var execution = await _executionHistoryService.GetExecutionAsync(executionId);
        if (execution == null)
        {
            return NotFound();
        }
        return Ok(execution);
    }

    /// <summary>
    /// 获取执行日志
    /// </summary>
    [HttpGet("executions/{executionId}/logs")]
    public async Task<ActionResult<IEnumerable<ExecutionLog>>> GetExecutionLogs(Guid executionId)
    {
        var logs = await _executionHistoryService.GetExecutionLogsAsync(executionId);
        return Ok(logs);
    }

    /// <summary>
    /// 获取活动执行列表
    /// </summary>
    [HttpGet("active-executions")]
    public async Task<ActionResult<IEnumerable<WorkflowExecution>>> GetActiveExecutions()
    {
        var executions = await _monitoringService.GetActiveExecutionsAsync();
        return Ok(executions);
    }

    /// <summary>
    /// 获取执行统计信息
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<ExecutionStatistics>> GetStatistics(
        [FromQuery] Guid? workflowId = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var statistics = await _executionHistoryService.GetExecutionStatisticsAsync(workflowId, startDate, endDate);
        return Ok(statistics);
    }

    /// <summary>
    /// 获取系统指标
    /// </summary>
    [HttpGet("metrics")]
    public async Task<ActionResult<SystemMetrics>> GetSystemMetrics()
    {
        var metrics = await _monitoringService.GetSystemMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// 获取工作流性能报告
    /// </summary>
    [HttpGet("performance/{workflowId}")]
    public async Task<ActionResult<WorkflowPerformanceReport>> GetWorkflowPerformance(
        Guid workflowId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var executions = await _executionHistoryService.GetExecutionHistoryAsync(workflowId, 1, 1000);
        
        if (startDate.HasValue)
        {
            executions = executions.Where(e => e.StartedAt >= startDate.Value);
        }
        
        if (endDate.HasValue)
        {
            executions = executions.Where(e => e.StartedAt <= endDate.Value);
        }

        var executionList = executions.ToList();
        var nodePerformance = new Dictionary<string, NodePerformanceMetrics>();

        foreach (var execution in executionList)
        {
            foreach (var nodeExecution in execution.NodeExecutions)
            {
                if (!nodePerformance.ContainsKey(nodeExecution.NodeType))
                {
                    nodePerformance[nodeExecution.NodeType] = new NodePerformanceMetrics
                    {
                        NodeType = nodeExecution.NodeType,
                        ExecutionCount = 0,
                        SuccessCount = 0,
                        FailureCount = 0,
                        TotalDuration = 0,
                        AverageDuration = 0
                    };
                }

                var metrics = nodePerformance[nodeExecution.NodeType];
                metrics.ExecutionCount++;
                
                if (nodeExecution.Status == ExecutionStatus.Success)
                    metrics.SuccessCount++;
                else if (nodeExecution.Status == ExecutionStatus.Error)
                    metrics.FailureCount++;

                if (nodeExecution.Duration.HasValue)
                {
                    metrics.TotalDuration += nodeExecution.Duration.Value.TotalMilliseconds;
                    metrics.AverageDuration = metrics.TotalDuration / metrics.ExecutionCount;
                }
            }
        }

        var report = new WorkflowPerformanceReport
        {
            WorkflowId = workflowId,
            TotalExecutions = executionList.Count,
            SuccessfulExecutions = executionList.Count(e => e.Status == ExecutionStatus.Success),
            FailedExecutions = executionList.Count(e => e.Status == ExecutionStatus.Error),
            AverageExecutionTime = executionList
                .Where(e => e.FinishedAt.HasValue)
                .Select(e => e.FinishedAt!.Value.Subtract(e.StartedAt).TotalMilliseconds)
                .DefaultIfEmpty()
                .Average(),
            NodePerformance = nodePerformance.Values.ToList(),
            ReportGeneratedAt = DateTime.UtcNow
        };

        return Ok(report);
    }

    /// <summary>
    /// 清理旧的执行记录
    /// </summary>
    [HttpPost("cleanup")]
    public async Task<IActionResult> CleanupOldExecutions([FromQuery] int retentionDays = 30)
    {
        var retentionPeriod = TimeSpan.FromDays(retentionDays);
        await _executionHistoryService.CleanupOldExecutionsAsync(retentionPeriod);
        return Ok(new { message = $"Cleaned up executions older than {retentionDays} days" });
    }
}

/// <summary>
/// 工作流性能报告
/// </summary>
public class WorkflowPerformanceReport
{
    public Guid WorkflowId { get; set; }
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public double AverageExecutionTime { get; set; }
    public List<NodePerformanceMetrics> NodePerformance { get; set; } = new();
    public DateTime ReportGeneratedAt { get; set; }
}

/// <summary>
/// 节点性能指标
/// </summary>
public class NodePerformanceMetrics
{
    public string NodeType { get; set; } = string.Empty;
    public int ExecutionCount { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public double TotalDuration { get; set; }
    public double AverageDuration { get; set; }
    public double SuccessRate => ExecutionCount > 0 ? (double)SuccessCount / ExecutionCount * 100 : 0;
}

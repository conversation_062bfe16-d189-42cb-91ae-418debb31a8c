using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlowCustom.PluginHost;
using FlowCustom.SDK;
using Microsoft.Extensions.Logging;

// 临时定义，实际应该在单独的文件中
namespace FlowCustom.Engine
{
    public interface IWorkflowEngine
    {
        Task<WorkflowExecutionResult> ExecuteWorkflowAsync(WorkflowExecutionRequest request, CancellationToken cancellationToken = default);
    }

    public class WorkflowExecutionRequest
    {
        public string WorkflowId { get; set; } = string.Empty;
        public Workflow Workflow { get; set; } = null!;
        public Dictionary<string, object>? Variables { get; set; }
        public Dictionary<string, object>? TriggerData { get; set; }
    }

    public class WorkflowExecutionResult
    {
        public string ExecutionId { get; set; } = string.Empty;
        public string WorkflowId { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan? Duration { get; set; }
        public WorkflowExecutionStatus Status { get; set; }
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public List<NodeExecutionResult> NodeResults { get; set; } = new();
    }

    public enum WorkflowExecutionStatus
    {
        Pending,
        Running,
        Completed,
        Failed,
        Cancelled
    }

    public class Workflow
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public List<WorkflowNode> Nodes { get; set; } = new();
        public List<WorkflowConnection> Connections { get; set; } = new();
    }

    public class WorkflowNode
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public Dictionary<string, object>? Parameters { get; set; }
        public bool IsDisabled { get; set; }
        public string? Notes { get; set; }
        public List<dynamic>? CustomInputs { get; set; }
        public List<dynamic>? CustomOutputs { get; set; }
    }

    public class WorkflowConnection
    {
        public string Id { get; set; } = string.Empty;
        public string SourceNodeId { get; set; } = string.Empty;
        public string TargetNodeId { get; set; } = string.Empty;
        public string SourceOutput { get; set; } = string.Empty;
        public string TargetInput { get; set; } = string.Empty;
    }

    public class WorkflowValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    public class ExecutionGraph
    {
        public Dictionary<string, ExecutionNode> Nodes { get; set; } = new();
        public Dictionary<string, ExecutionConnection> Connections { get; set; } = new();

        public void AddNode(ExecutionNode node)
        {
            Nodes[node.Id] = node;
        }

        public void AddConnection(ExecutionConnection connection)
        {
            Connections[connection.Id] = connection;
        }
    }

    public class ExecutionNode
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public NodeConfiguration Configuration { get; set; } = null!;
        public INodePlugin? Plugin { get; set; }
    }

    public class ExecutionConnection
    {
        public string Id { get; set; } = string.Empty;
        public string SourceNodeId { get; set; } = string.Empty;
        public string TargetNodeId { get; set; } = string.Empty;
        public string SourceOutput { get; set; } = string.Empty;
        public string TargetInput { get; set; } = string.Empty;
    }
}

namespace FlowCustom.Engine
{
    /// <summary>
    /// 基于插件的工作流执行引擎
    /// </summary>
    public class PluginBasedWorkflowEngine : IWorkflowEngine
    {
        private readonly IPluginManager _pluginManager;
        private readonly ILogger<PluginBasedWorkflowEngine> _logger;
        private readonly IServiceProvider _serviceProvider;

        public PluginBasedWorkflowEngine(
            IPluginManager pluginManager,
            ILogger<PluginBasedWorkflowEngine> logger,
            IServiceProvider serviceProvider)
        {
            _pluginManager = pluginManager;
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// 执行工作流
        /// </summary>
        public async Task<WorkflowExecutionResult> ExecuteWorkflowAsync(
            WorkflowExecutionRequest request,
            CancellationToken cancellationToken = default)
        {
            var executionId = Guid.NewGuid().ToString();
            _logger.LogInformation($"开始执行工作流: {request.WorkflowId}, 执行ID: {executionId}");

            var result = new WorkflowExecutionResult
            {
                ExecutionId = executionId,
                WorkflowId = request.WorkflowId,
                StartTime = DateTime.UtcNow,
                Status = WorkflowExecutionStatus.Running,
                NodeResults = new List<NodeExecutionResult>()
            };

            try
            {
                // 验证工作流
                var validationResult = await ValidateWorkflowAsync(request.Workflow);
                if (!validationResult.IsValid)
                {
                    result.Status = WorkflowExecutionStatus.Failed;
                    result.ErrorMessage = string.Join("; ", validationResult.Errors);
                    return result;
                }

                // 构建执行图
                var executionGraph = BuildExecutionGraph(request.Workflow);
                
                // 执行节点
                await ExecuteNodesAsync(executionGraph, request, result, cancellationToken);

                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime.Value - result.StartTime;

                // 确定最终状态
                if (result.NodeResults.Any(r => !r.Success))
                {
                    result.Status = WorkflowExecutionStatus.Failed;
                }
                else
                {
                    result.Status = WorkflowExecutionStatus.Completed;
                }

                _logger.LogInformation($"工作流执行完成: {request.WorkflowId}, 状态: {result.Status}");
            }
            catch (OperationCanceledException)
            {
                result.Status = WorkflowExecutionStatus.Cancelled;
                result.ErrorMessage = "工作流执行被取消";
                _logger.LogWarning($"工作流执行被取消: {request.WorkflowId}");
            }
            catch (Exception ex)
            {
                result.Status = WorkflowExecutionStatus.Failed;
                result.ErrorMessage = ex.Message;
                result.Exception = ex;
                _logger.LogError(ex, $"工作流执行失败: {request.WorkflowId}");
            }

            return result;
        }

        /// <summary>
        /// 验证工作流
        /// </summary>
        private async Task<WorkflowValidationResult> ValidateWorkflowAsync(Workflow workflow)
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            // 验证节点
            foreach (var node in workflow.Nodes)
            {
                var plugin = _pluginManager.GetPlugin(node.Type);
                if (plugin == null)
                {
                    errors.Add($"节点 '{node.Name}' 的类型 '{node.Type}' 没有对应的插件");
                    continue;
                }

                try
                {
                    var nodeConfig = new NodeConfiguration
                    {
                        Parameters = node.Parameters?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new(),
                        IsDisabled = node.IsDisabled,
                        Notes = node.Notes ?? "",
                        CustomInputs = node.CustomInputs?.Select(MapToEndpointDefinition).ToList(),
                        CustomOutputs = node.CustomOutputs?.Select(MapToEndpointDefinition).ToList()
                    };

                    var validationResult = await plugin.ValidateAsync(nodeConfig);
                    if (!validationResult.IsValid)
                    {
                        errors.AddRange(validationResult.Errors.Select(e => $"节点 '{node.Name}': {e.Message}"));
                    }
                    warnings.AddRange(validationResult.Warnings.Select(w => $"节点 '{node.Name}': {w.Message}"));
                }
                catch (Exception ex)
                {
                    errors.Add($"验证节点 '{node.Name}' 时发生错误: {ex.Message}");
                }
            }

            // 验证连接
            foreach (var connection in workflow.Connections)
            {
                var sourceNode = workflow.Nodes.FirstOrDefault(n => n.Id == connection.SourceNodeId);
                var targetNode = workflow.Nodes.FirstOrDefault(n => n.Id == connection.TargetNodeId);

                if (sourceNode == null)
                {
                    errors.Add($"连接的源节点不存在: {connection.SourceNodeId}");
                }

                if (targetNode == null)
                {
                    errors.Add($"连接的目标节点不存在: {connection.TargetNodeId}");
                }
            }

            return new WorkflowValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors,
                Warnings = warnings
            };
        }

        /// <summary>
        /// 构建执行图
        /// </summary>
        private ExecutionGraph BuildExecutionGraph(Workflow workflow)
        {
            var graph = new ExecutionGraph();
            
            // 添加节点
            foreach (var node in workflow.Nodes)
            {
                graph.AddNode(new ExecutionNode
                {
                    Id = node.Id,
                    Name = node.Name,
                    Type = node.Type,
                    Configuration = new NodeConfiguration
                    {
                        Parameters = node.Parameters?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new(),
                        IsDisabled = node.IsDisabled,
                        Notes = node.Notes ?? "",
                        CustomInputs = node.CustomInputs?.Select(MapToEndpointDefinition).ToList(),
                        CustomOutputs = node.CustomOutputs?.Select(MapToEndpointDefinition).ToList()
                    },
                    Plugin = _pluginManager.GetPlugin(node.Type)
                });
            }

            // 添加连接
            foreach (var connection in workflow.Connections)
            {
                graph.AddConnection(new ExecutionConnection
                {
                    Id = connection.Id,
                    SourceNodeId = connection.SourceNodeId,
                    TargetNodeId = connection.TargetNodeId,
                    SourceOutput = connection.SourceOutput,
                    TargetInput = connection.TargetInput
                });
            }

            return graph;
        }

        /// <summary>
        /// 执行节点
        /// </summary>
        private async Task ExecuteNodesAsync(
            ExecutionGraph graph,
            WorkflowExecutionRequest request,
            WorkflowExecutionResult result,
            CancellationToken cancellationToken)
        {
            var executedNodes = new HashSet<string>();
            var nodeData = new Dictionary<string, Dictionary<string, object>>();

            // 找到起始节点（触发器节点）
            var startNodes = graph.Nodes.Values.Where(n => n.Plugin?.IsTrigger == true).ToList();
            
            if (!startNodes.Any())
            {
                throw new InvalidOperationException("工作流中没有找到触发器节点");
            }

            // 执行起始节点
            foreach (var startNode in startNodes)
            {
                await ExecuteNodeRecursiveAsync(
                    startNode, graph, request, result, 
                    executedNodes, nodeData, cancellationToken);
            }
        }

        /// <summary>
        /// 递归执行节点
        /// </summary>
        private async Task ExecuteNodeRecursiveAsync(
            ExecutionNode node,
            ExecutionGraph graph,
            WorkflowExecutionRequest request,
            WorkflowExecutionResult result,
            HashSet<string> executedNodes,
            Dictionary<string, Dictionary<string, object>> nodeData,
            CancellationToken cancellationToken)
        {
            if (executedNodes.Contains(node.Id) || node.Configuration.IsDisabled)
            {
                return;
            }

            _logger.LogDebug($"执行节点: {node.Name} ({node.Type})");

            try
            {
                // 准备输入数据
                var inputData = PrepareInputData(node, graph, nodeData);

                // 创建执行上下文
                var context = new NodeExecutionContext
                {
                    NodeId = node.Id,
                    WorkflowId = request.WorkflowId,
                    ExecutionId = result.ExecutionId,
                    Configuration = node.Configuration,
                    InputData = inputData,
                    Variables = request.Variables ?? new(),
                    ServiceProvider = _serviceProvider,
                    CancellationToken = cancellationToken,
                    Logger = new PluginLogger(_logger, node.Name)
                };

                // 执行节点
                var nodeResult = await node.Plugin!.ExecuteAsync(context);
                nodeResult.NodeId = node.Id;
                nodeResult.NodeName = node.Name;
                nodeResult.NodeType = node.Type;
                nodeResult.StartTime = DateTime.UtcNow;
                nodeResult.EndTime = DateTime.UtcNow;

                result.NodeResults.Add(nodeResult);
                executedNodes.Add(node.Id);

                // 保存输出数据
                if (nodeResult.Success && nodeResult.OutputData.Any())
                {
                    nodeData[node.Id] = nodeResult.OutputData;
                }

                _logger.LogDebug($"节点执行完成: {node.Name}, 成功: {nodeResult.Success}");

                // 如果执行成功，继续执行下游节点
                if (nodeResult.Success)
                {
                    var nextNodes = GetNextNodes(node, graph, nodeResult.NextEndpoint);
                    foreach (var nextNode in nextNodes)
                    {
                        await ExecuteNodeRecursiveAsync(
                            nextNode, graph, request, result,
                            executedNodes, nodeData, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"节点执行异常: {node.Name}");
                
                var errorResult = new NodeExecutionResult
                {
                    NodeId = node.Id,
                    NodeName = node.Name,
                    NodeType = node.Type,
                    Success = false,
                    ErrorMessage = ex.Message,
                    Exception = ex,
                    StartTime = DateTime.UtcNow,
                    EndTime = DateTime.UtcNow
                };
                
                result.NodeResults.Add(errorResult);
                executedNodes.Add(node.Id);
            }
        }

        /// <summary>
        /// 准备输入数据
        /// </summary>
        private Dictionary<string, object> PrepareInputData(
            ExecutionNode node,
            ExecutionGraph graph,
            Dictionary<string, Dictionary<string, object>> nodeData)
        {
            var inputData = new Dictionary<string, object>();

            // 获取输入连接
            var inputConnections = graph.Connections.Values
                .Where(c => c.TargetNodeId == node.Id)
                .ToList();

            foreach (var connection in inputConnections)
            {
                if (nodeData.TryGetValue(connection.SourceNodeId, out var sourceData))
                {
                    if (sourceData.TryGetValue(connection.SourceOutput, out var value))
                    {
                        inputData[connection.TargetInput] = value;
                    }
                    else
                    {
                        // 如果没有指定输出，使用所有输出数据
                        inputData[connection.TargetInput] = sourceData;
                    }
                }
            }

            return inputData;
        }

        /// <summary>
        /// 获取下一个要执行的节点
        /// </summary>
        private List<ExecutionNode> GetNextNodes(
            ExecutionNode currentNode,
            ExecutionGraph graph,
            string? outputEndpoint)
        {
            var nextNodes = new List<ExecutionNode>();

            var outgoingConnections = graph.Connections.Values
                .Where(c => c.SourceNodeId == currentNode.Id)
                .ToList();

            // 如果指定了输出端点，只获取该端点的连接
            if (!string.IsNullOrEmpty(outputEndpoint))
            {
                outgoingConnections = outgoingConnections
                    .Where(c => c.SourceOutput == outputEndpoint)
                    .ToList();
            }

            foreach (var connection in outgoingConnections)
            {
                if (graph.Nodes.TryGetValue(connection.TargetNodeId, out var targetNode))
                {
                    nextNodes.Add(targetNode);
                }
            }

            return nextNodes;
        }

        /// <summary>
        /// 映射端点定义
        /// </summary>
        private EndpointDefinition MapToEndpointDefinition(dynamic endpoint)
        {
            return new EndpointDefinition
            {
                Id = endpoint.Id ?? "",
                Name = endpoint.Name ?? "",
                Description = endpoint.Description ?? "",
                Type = Enum.TryParse<EndpointType>(endpoint.Type?.ToString(), out var type) ? type : EndpointType.Data,
                Required = endpoint.Required ?? false,
                Position = new EndpointPosition
                {
                    Side = endpoint.Position?.Side ?? "bottom",
                    Offset = endpoint.Position?.Offset ?? 50
                },
                DataType = endpoint.DataType ?? "any"
            };
        }
    }

    /// <summary>
    /// 插件日志记录器
    /// </summary>
    public class PluginLogger : FlowCustom.SDK.ILogger
    {
        private readonly ILogger _logger;
        private readonly string _nodeName;

        public PluginLogger(ILogger logger, string nodeName)
        {
            _logger = logger;
            _nodeName = nodeName;
        }

        public void LogDebug(string message)
        {
            _logger.LogDebug($"[{_nodeName}] {message}");
        }

        public void LogInfo(string message)
        {
            _logger.LogInformation($"[{_nodeName}] {message}");
        }

        public void LogWarning(string message)
        {
            _logger.LogWarning($"[{_nodeName}] {message}");
        }

        public void LogError(string message, Exception? exception = null)
        {
            _logger.LogError(exception, $"[{_nodeName}] {message}");
        }
    }
}

using FlowCustomV1.Core.Models;
using FlowCustomV1.SDK.Attributes;
using FlowCustomV1.SDK.Base;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Plugins.Core;

/// <summary>
/// 手动触发器插件
/// 提供手动触发工作流执行的功能
/// </summary>
[NodePlugin("manual-trigger",
    DisplayName = "手动触发器",
    Description = "手动触发工作流执行，适用于需要人工干预启动的场景",
    Category = "触发器",
    Icon = "🚀",
    Version = "1.0.0",
    Author = "FlowCustom Team",
    IsTrigger = true)]
public class ManualTriggerPlugin : NodePluginBase
{
    public override string NodeType => "manual-trigger";
    public override string DisplayName => "手动触发器";
    public override string Description => "手动触发工作流执行，适用于需要人工干预启动的场景";
    public override string Category => "触发器";
    public override string Icon => "🚀";
    public override bool IsTrigger => true;

    public ManualTriggerPlugin() : base()
    {
    }

    public ManualTriggerPlugin(ILogger<ManualTriggerPlugin> logger) : base(logger)
    {
    }

    #region 参数定义

    protected override IEnumerable<ParameterDefinition> GetParameterDefinitionsCore()
    {
        return new[]
        {
            new ParameterDefinition
            {
                Name = "enabled",
                DisplayName = "启用状态",
                Description = "是否启用此触发器",
                Type = ParameterType.Boolean,
                Required = false,
                DefaultValue = true,
                Group = "基本设置",
                Order = 1,
                HelpText = "禁用后，此触发器将不会响应手动触发请求"
            },

            new ParameterDefinition
            {
                Name = "buttonText",
                DisplayName = "按钮文本",
                Description = "触发按钮显示的文本",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = "立即执行",
                Group = "界面设置",
                Order = 2,
                HelpText = "在前端界面中显示的触发按钮文本",
                Placeholder = "请输入按钮文本..."
            },

            new ParameterDefinition
            {
                Name = "confirmMessage",
                DisplayName = "确认消息",
                Description = "触发前显示的确认消息",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = "",
                Group = "界面设置",
                Order = 3,
                HelpText = "如果设置，触发前会显示确认对话框",
                Placeholder = "请输入确认消息..."
            },

            new ParameterDefinition
            {
                Name = "requireConfirmation",
                DisplayName = "需要确认",
                Description = "是否需要用户确认才能触发",
                Type = ParameterType.Boolean,
                Required = false,
                DefaultValue = false,
                Group = "界面设置",
                Order = 4,
                HelpText = "启用后，用户需要在确认对话框中点击确认才能触发"
            },

            new ParameterDefinition
            {
                Name = "buttonStyle",
                DisplayName = "按钮样式",
                Description = "触发按钮的样式",
                Type = ParameterType.Select,
                Required = false,
                DefaultValue = "primary",
                Options = new List<ParameterOption>
                {
                    new ParameterOption { Value = "primary", Label = "主要按钮", Description = "蓝色主要按钮" },
                    new ParameterOption { Value = "success", Label = "成功按钮", Description = "绿色成功按钮" },
                    new ParameterOption { Value = "warning", Label = "警告按钮", Description = "橙色警告按钮" },
                    new ParameterOption { Value = "danger", Label = "危险按钮", Description = "红色危险按钮" },
                    new ParameterOption { Value = "info", Label = "信息按钮", Description = "浅蓝色信息按钮" }
                },
                Group = "界面设置",
                Order = 5
            },

            new ParameterDefinition
            {
                Name = "description",
                DisplayName = "触发器描述",
                Description = "触发器的详细描述",
                Type = ParameterType.Text,
                Required = false,
                DefaultValue = "",
                Group = "高级设置",
                Order = 6,
                HelpText = "详细描述此触发器的用途和触发条件",
                Placeholder = "请输入触发器描述..."
            },

            new ParameterDefinition
            {
                Name = "tags",
                DisplayName = "标签",
                Description = "触发器标签",
                Type = ParameterType.MultiSelect,
                Required = false,
                Options = new List<ParameterOption>
                {
                    new ParameterOption { Value = "manual", Label = "手动", Icon = "👤" },
                    new ParameterOption { Value = "urgent", Label = "紧急", Icon = "🚨" },
                    new ParameterOption { Value = "scheduled", Label = "计划", Icon = "📅" },
                    new ParameterOption { Value = "test", Label = "测试", Icon = "🧪" }
                },
                Group = "高级设置",
                Order = 7
            }
        };
    }

    #endregion

    #region 端点定义

    protected override IEnumerable<EndpointDefinition> GetInputDefinitionsCore()
    {
        // 触发器节点通常没有输入端点
        return Enumerable.Empty<EndpointDefinition>();
    }

    protected override IEnumerable<EndpointDefinition> GetOutputDefinitionsCore()
    {
        return new[]
        {
            new EndpointDefinition
            {
                Id = "triggered",
                Name = "触发输出",
                Description = "手动触发时的输出数据",
                Type = EndpointType.Success,
                DataType = "object",
                Required = false,
                Position = new EndpointPosition { Side = "bottom", Offset = 50 },
                Style = new EndpointStyle
                {
                    Color = "#52c41a",
                    Size = "medium",
                    Shape = "circle"
                }
            }
        };
    }

    #endregion

    #region 验证逻辑

    protected override Task OnValidateAsync(NodeConfiguration configuration,
        List<ValidationError> errors, List<ValidationWarning> warnings,
        CancellationToken cancellationToken = default)
    {
        // 验证按钮文本
        if (configuration.Parameters.TryGetValue("buttonText", out var buttonTextObj))
        {
            var buttonText = buttonTextObj?.ToString();
            if (!string.IsNullOrWhiteSpace(buttonText) && buttonText.Length > 50)
            {
                warnings.Add(new ValidationWarning
                {
                    Code = "LONG_BUTTON_TEXT",
                    Message = "按钮文本过长，建议不超过50个字符",
                    Field = "buttonText"
                });
            }
        }

        // 验证确认消息
        if (configuration.Parameters.TryGetValue("confirmMessage", out var confirmMessageObj))
        {
            var confirmMessage = confirmMessageObj?.ToString();
            if (!string.IsNullOrWhiteSpace(confirmMessage) && confirmMessage.Length > 200)
            {
                warnings.Add(new ValidationWarning
                {
                    Code = "LONG_CONFIRM_MESSAGE",
                    Message = "确认消息过长，建议不超过200个字符",
                    Field = "confirmMessage"
                });
            }
        }

        return Task.CompletedTask;
    }

    #endregion

    #region 执行逻辑

    protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken = default)
    {
        // 获取参数
        var enabled = GetParameter<bool>(context, "enabled", true);
        var buttonText = GetParameter<string>(context, "buttonText", "立即执行");
        var confirmMessage = GetParameter<string>(context, "confirmMessage", "");
        var requireConfirmation = GetParameter<bool>(context, "requireConfirmation", false);
        var buttonStyle = GetParameter<string>(context, "buttonStyle", "primary");
        var description = GetParameter<string>(context, "description", "");
        var tags = GetParameter<string[]>(context, "tags", Array.Empty<string>());

        Log($"手动触发器执行: {buttonText}, 启用: {enabled}");

        // 检查是否启用
        if (!enabled)
        {
            Log("手动触发器已禁用，跳过执行", LogLevel.Warning);
            return NodeExecutionResult.Success(new Dictionary<string, object>
            {
                ["triggered"] = false,
                ["reason"] = "disabled",
                ["timestamp"] = DateTime.UtcNow,
                ["message"] = "触发器已禁用"
            }, "triggered");
        }

        try
        {
            // 模拟异步处理
            await Task.Delay(50, cancellationToken);

            // 构建触发结果
            var result = new Dictionary<string, object>
            {
                ["triggered"] = true,
                ["triggerType"] = "manual",
                ["buttonText"] = buttonText,
                ["confirmMessage"] = confirmMessage,
                ["requireConfirmation"] = requireConfirmation,
                ["buttonStyle"] = buttonStyle,
                ["description"] = description,
                ["tags"] = tags,
                ["timestamp"] = DateTime.UtcNow,
                ["nodeId"] = context.NodeId,
                ["executionId"] = context.ExecutionId,
                ["workflowId"] = context.WorkflowId,
                ["triggeredBy"] = "manual",
                ["metadata"] = new Dictionary<string, object>
                {
                    ["userAgent"] = context.Variables.GetValueOrDefault("userAgent", "unknown"),
                    ["ipAddress"] = context.Variables.GetValueOrDefault("ipAddress", "unknown"),
                    ["sessionId"] = context.Variables.GetValueOrDefault("sessionId", "unknown")
                }
            };

            Log($"手动触发器执行成功: {buttonText}");
            return NodeExecutionResult.Success(result, "triggered");
        }
        catch (OperationCanceledException)
        {
            Log("手动触发器执行被取消", LogLevel.Warning);
            throw;
        }
        catch (Exception ex)
        {
            Log($"手动触发器执行失败: {ex.Message}", LogLevel.Error);
            return NodeExecutionResult.Success(new Dictionary<string, object>
            {
                ["triggered"] = false,
                ["reason"] = "error",
                ["error"] = ex.Message,
                ["timestamp"] = DateTime.UtcNow
            }, "triggered");
        }
    }

    #endregion

    #region 生命周期

    protected override Task OnInitializeAsync(CancellationToken cancellationToken = default)
    {
        Log("手动触发器插件初始化");
        return Task.CompletedTask;
    }

    protected override Task OnDisposeAsync(CancellationToken cancellationToken = default)
    {
        Log("手动触发器插件资源清理");
        return Task.CompletedTask;
    }

    #endregion
}

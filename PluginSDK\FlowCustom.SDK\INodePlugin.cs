using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FlowCustom.SDK
{
    /// <summary>
    /// 节点插件接口
    /// 所有节点插件必须实现此接口
    /// </summary>
    public interface INodePlugin : IDisposable
    {
        /// <summary>
        /// 节点类型标识符（唯一）
        /// </summary>
        string NodeType { get; }

        /// <summary>
        /// 节点显示名称
        /// </summary>
        string DisplayName { get; }

        /// <summary>
        /// 节点描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 节点分类
        /// </summary>
        string Category { get; }

        /// <summary>
        /// 插件版本
        /// </summary>
        string Version { get; }

        /// <summary>
        /// 插件作者
        /// </summary>
        string Author { get; }

        /// <summary>
        /// 是否为触发器节点
        /// </summary>
        bool IsTrigger { get; }

        /// <summary>
        /// 节点图标（可选）
        /// </summary>
        string Icon { get; }

        /// <summary>
        /// 获取节点定义
        /// 包含参数定义、端点配置等
        /// </summary>
        /// <returns>节点定义</returns>
        NodeDefinition GetDefinition();

        /// <summary>
        /// 执行节点逻辑
        /// </summary>
        /// <param name="context">执行上下文</param>
        /// <returns>执行结果</returns>
        Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context);

        /// <summary>
        /// 验证节点配置
        /// </summary>
        /// <param name="configuration">节点配置</param>
        /// <returns>验证结果</returns>
        Task<ValidationResult> ValidateAsync(NodeConfiguration configuration);

        /// <summary>
        /// 初始化插件
        /// 在插件加载时调用
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        void Initialize(IServiceProvider serviceProvider);

        /// <summary>
        /// 获取前端配置组件信息
        /// </summary>
        /// <returns>前端组件信息</returns>
        FrontendComponentInfo GetFrontendComponent();
    }

    /// <summary>
    /// 节点定义
    /// </summary>
    public class NodeDefinition
    {
        /// <summary>
        /// 节点类型
        /// </summary>
        public string NodeType { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 分类
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 图标
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 是否为触发器
        /// </summary>
        public bool IsTrigger { get; set; }

        /// <summary>
        /// 参数定义列表
        /// </summary>
        public List<ParameterDefinition> Parameters { get; set; } = new();

        /// <summary>
        /// 输入端点定义
        /// </summary>
        public List<EndpointDefinition> Inputs { get; set; } = new();

        /// <summary>
        /// 输出端点定义
        /// </summary>
        public List<EndpointDefinition> Outputs { get; set; } = new();

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; } = new();
    }

    /// <summary>
    /// 参数定义
    /// </summary>
    public class ParameterDefinition
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 参数类型
        /// </summary>
        public ParameterType Type { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool Required { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public object? DefaultValue { get; set; }

        /// <summary>
        /// 选项列表（用于下拉框等）
        /// </summary>
        public List<ParameterOption>? Options { get; set; }

        /// <summary>
        /// 验证规则
        /// </summary>
        public Dictionary<string, object> Validation { get; set; } = new();
    }

    /// <summary>
    /// 端点定义
    /// </summary>
    public class EndpointDefinition
    {
        /// <summary>
        /// 端点ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 端点名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 端点描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 端点类型
        /// </summary>
        public EndpointType Type { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool Required { get; set; }

        /// <summary>
        /// 端点位置
        /// </summary>
        public EndpointPosition Position { get; set; } = new();

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; } = "any";
    }

    /// <summary>
    /// 端点位置
    /// </summary>
    public class EndpointPosition
    {
        /// <summary>
        /// 位置边（top, bottom, left, right）
        /// </summary>
        public string Side { get; set; } = "bottom";

        /// <summary>
        /// 偏移百分比 (0-100)
        /// </summary>
        public int Offset { get; set; } = 50;
    }

    /// <summary>
    /// 参数选项
    /// </summary>
    public class ParameterOption
    {
        /// <summary>
        /// 选项值
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 显示文本
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 前端组件信息
    /// </summary>
    public class FrontendComponentInfo
    {
        /// <summary>
        /// 组件名称
        /// </summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 组件文件路径
        /// </summary>
        public string ComponentPath { get; set; } = string.Empty;

        /// <summary>
        /// 图标路径
        /// </summary>
        public string IconPath { get; set; } = string.Empty;

        /// <summary>
        /// 样式文件路径
        /// </summary>
        public string? StylePath { get; set; }

        /// <summary>
        /// 国际化资源路径
        /// </summary>
        public Dictionary<string, string> LocalePaths { get; set; } = new();
    }

    /// <summary>
    /// 参数类型枚举
    /// </summary>
    public enum ParameterType
    {
        String,
        Number,
        Boolean,
        Object,
        Array,
        Select,
        MultiSelect,
        File,
        Credential,
        Expression,
        Json,
        Xml,
        Code
    }

    /// <summary>
    /// 端点类型枚举
    /// </summary>
    public enum EndpointType
    {
        Data,
        Trigger,
        Error,
        Success,
        Condition
    }

    /// <summary>
    /// 节点执行上下文
    /// </summary>
    public class NodeExecutionContext
    {
        /// <summary>
        /// 节点ID
        /// </summary>
        public string NodeId { get; set; } = string.Empty;

        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = string.Empty;

        /// <summary>
        /// 执行ID
        /// </summary>
        public string ExecutionId { get; set; } = string.Empty;

        /// <summary>
        /// 节点配置
        /// </summary>
        public NodeConfiguration Configuration { get; set; } = new();

        /// <summary>
        /// 输入数据
        /// </summary>
        public Dictionary<string, object> InputData { get; set; } = new();

        /// <summary>
        /// 工作流变量
        /// </summary>
        public Dictionary<string, object> Variables { get; set; } = new();

        /// <summary>
        /// 服务提供者
        /// </summary>
        public IServiceProvider ServiceProvider { get; set; } = null!;

        /// <summary>
        /// 取消令牌
        /// </summary>
        public CancellationToken CancellationToken { get; set; }

        /// <summary>
        /// 日志记录器
        /// </summary>
        public ILogger Logger { get; set; } = null!;
    }

    /// <summary>
    /// 节点配置
    /// </summary>
    public class NodeConfiguration
    {
        /// <summary>
        /// 参数值
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();

        /// <summary>
        /// 是否禁用
        /// </summary>
        public bool IsDisabled { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 自定义输入端点
        /// </summary>
        public List<EndpointDefinition>? CustomInputs { get; set; }

        /// <summary>
        /// 自定义输出端点
        /// </summary>
        public List<EndpointDefinition>? CustomOutputs { get; set; }
    }

    /// <summary>
    /// 节点执行结果
    /// </summary>
    public class NodeExecutionResult
    {
        /// <summary>
        /// 执行是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 输出数据
        /// </summary>
        public Dictionary<string, object> OutputData { get; set; } = new();

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 异常详情
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 执行日志
        /// </summary>
        public List<string> Logs { get; set; } = new();

        /// <summary>
        /// 下一步执行的端点
        /// </summary>
        public string? NextEndpoint { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; } = new();

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static NodeExecutionResult Success(Dictionary<string, object>? outputData = null, string? nextEndpoint = null)
        {
            return new NodeExecutionResult
            {
                Success = true,
                OutputData = outputData ?? new(),
                NextEndpoint = nextEndpoint
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static NodeExecutionResult Failure(string errorMessage, Exception? exception = null)
        {
            return new NodeExecutionResult
            {
                Success = false,
                ErrorMessage = errorMessage,
                Exception = exception
            };
        }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 验证是否通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<ValidationError> Errors { get; set; } = new();

        /// <summary>
        /// 警告信息列表
        /// </summary>
        public List<ValidationWarning> Warnings { get; set; } = new();

        /// <summary>
        /// 创建成功验证结果
        /// </summary>
        public static ValidationResult Success()
        {
            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// 创建失败验证结果
        /// </summary>
        public static ValidationResult Failure(params ValidationError[] errors)
        {
            return new ValidationResult
            {
                IsValid = false,
                Errors = errors.ToList()
            };
        }
    }

    /// <summary>
    /// 验证错误
    /// </summary>
    public class ValidationError
    {
        /// <summary>
        /// 错误字段
        /// </summary>
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? Code { get; set; }
    }

    /// <summary>
    /// 验证警告
    /// </summary>
    public class ValidationWarning
    {
        /// <summary>
        /// 警告字段
        /// </summary>
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 警告代码
        /// </summary>
        public string? Code { get; set; }
    }

    /// <summary>
    /// 简单日志接口
    /// </summary>
    public interface ILogger
    {
        void LogDebug(string message);
        void LogInfo(string message);
        void LogWarning(string message);
        void LogError(string message, Exception? exception = null);
    }
}

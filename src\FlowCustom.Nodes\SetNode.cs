using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using System.Text.Json;

namespace FlowCustom.Nodes;

/// <summary>
/// 数据设置节点 - 用于设置和转换数据
/// </summary>
public class SetNode : INode
{
    public string NodeType => "set";
    public string DisplayName => "Set";
    public string Description => "Set values and transform data";
    public string Category => "Data";
    public string Icon => "edit";

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "values",
                DisplayName = "Values",
                Description = "Key-value pairs to set",
                Type = ParameterType.Object,
                Required = true
            },
            new NodeParameterDefinition
            {
                Name = "keepOnlySet",
                DisplayName = "Keep Only Set Values",
                Description = "If true, only the set values will be passed to the next node",
                Type = ParameterType.Boolean,
                Required = false,
                DefaultValue = false
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node,
        Models.ExecutionContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var values = GetParameter<Dictionary<string, object>>(node, "values");
            var keepOnlySet = GetParameter<bool>(node, "keepOnlySet", false);

            if (values == null || !values.Any())
            {
                return NodeExecutionResult.CreateError("Values parameter is required");
            }

            var outputData = new Dictionary<string, object>();

            // 如果不是只保留设置的值，则先复制输入数据
            if (!keepOnlySet)
            {
                // 获取前一个节点的输出数据
                var previousOutputs = context.PreviousNodeOutputs.Values.LastOrDefault();
                if (previousOutputs != null)
                {
                    foreach (var kvp in previousOutputs)
                    {
                        outputData[kvp.Key] = kvp.Value;
                    }
                }
            }

            // 设置新值
            foreach (var kvp in values)
            {
                outputData[kvp.Key] = ProcessValue(kvp.Value, context);
            }

            return NodeExecutionResult.CreateSuccess(outputData);
        }
        catch (Exception ex)
        {
            return NodeExecutionResult.CreateError($"Set node execution failed: {ex.Message}");
        }
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.ContainsKey("values") || parameters["values"] == null)
        {
            errors.Add("Values parameter is required");
        }

        return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                if (typeof(T) == typeof(Dictionary<string, object>) && value is JsonElement jsonElement)
                {
                    var dict = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonElement.GetRawText());
                    return (T)(object)dict!;
                }

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    private object ProcessValue(object value, Models.ExecutionContext context)
    {
        // 如果值是字符串且包含表达式，则处理表达式
        if (value is string stringValue && stringValue.Contains("{{") && stringValue.Contains("}}"))
        {
            return ProcessExpression(stringValue, context);
        }

        return value;
    }

    private object ProcessExpression(string expression, Models.ExecutionContext context)
    {
        // 简单的表达式处理 - 支持 {{variable}} 格式
        var result = expression;

        // 处理变量引用
        foreach (var variable in context.Variables)
        {
            var placeholder = $"{{{{{variable.Key}}}}}";
            if (result.Contains(placeholder))
            {
                result = result.Replace(placeholder, variable.Value?.ToString() ?? "");
            }
        }

        // 处理全局数据引用
        foreach (var data in context.GlobalData)
        {
            var placeholder = $"{{{{{data.Key}}}}}";
            if (result.Contains(placeholder))
            {
                result = result.Replace(placeholder, data.Value?.ToString() ?? "");
            }
        }

        // 处理前置节点输出引用
        foreach (var nodeOutput in context.PreviousNodeOutputs)
        {
            foreach (var output in nodeOutput.Value)
            {
                var placeholder = $"{{{{${nodeOutput.Key}.{output.Key}}}}}";
                if (result.Contains(placeholder))
                {
                    result = result.Replace(placeholder, output.Value?.ToString() ?? "");
                }
            }
        }

        return result;
    }
}

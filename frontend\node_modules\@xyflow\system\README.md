# @xyflow/system

Core system that powers React Flow and Svelte Flow.

## Installation 

```sh 
npm install @xyflow/system
```

## What is this package about?

The @xyflow/system package was created to have a place for vanilla utils for React Flow and Svelte Flow. The package exports helpers for edge creation, pan and zoom, dragging of nodes, general utils and lots of types. All the helpers are specifically built for React Flow and Svelte Flow so it's probably not too interesting to use them with other libraries. 

### XYPanZoom

Adds zoom and pan for the pane. 

### XYDrag

Adds drag for nodes and selection.

### XYHandle 

Adds connection line drawing.

### XYMinimap 

Adds interactive mini map (zoom and pan).

### Edge utils 

Util function for SVG edge path creating.

### Store utils

Helpers for store functions.

### Dom utils

### Marker utils

### Graph utils

### General utils 



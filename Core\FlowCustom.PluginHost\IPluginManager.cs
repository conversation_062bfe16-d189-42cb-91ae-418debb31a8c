using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlowCustom.SDK;
using System.Linq;

namespace FlowCustom.PluginHost
{
    /// <summary>
    /// 插件管理器接口
    /// </summary>
    public interface IPluginManager
    {
        /// <summary>
        /// 加载所有插件
        /// </summary>
        /// <returns>加载的插件数量</returns>
        Task<int> LoadPluginsAsync();

        /// <summary>
        /// 卸载所有插件
        /// </summary>
        Task UnloadPluginsAsync();

        /// <summary>
        /// 获取所有已加载的插件
        /// </summary>
        /// <returns>插件列表</returns>
        IEnumerable<INodePlugin> GetAllPlugins();

        /// <summary>
        /// 根据节点类型获取插件
        /// </summary>
        /// <param name="nodeType">节点类型</param>
        /// <returns>插件实例</returns>
        INodePlugin? GetPlugin(string nodeType);

        /// <summary>
        /// 获取插件元数据
        /// </summary>
        /// <param name="nodeType">节点类型</param>
        /// <returns>插件元数据</returns>
        PluginMetadata? GetPluginMetadata(string nodeType);

        /// <summary>
        /// 安装插件
        /// </summary>
        /// <param name="pluginPath">插件路径</param>
        /// <returns>安装结果</returns>
        Task<PluginInstallResult> InstallPluginAsync(string pluginPath);

        /// <summary>
        /// 卸载插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>卸载结果</returns>
        Task<bool> UninstallPluginAsync(string pluginId);

        /// <summary>
        /// 启用插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>启用结果</returns>
        Task<bool> EnablePluginAsync(string pluginId);

        /// <summary>
        /// 禁用插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>禁用结果</returns>
        Task<bool> DisablePluginAsync(string pluginId);

        /// <summary>
        /// 重新加载插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>重新加载结果</returns>
        Task<bool> ReloadPluginAsync(string pluginId);

        /// <summary>
        /// 获取插件状态
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>插件状态</returns>
        PluginStatus GetPluginStatus(string pluginId);

        /// <summary>
        /// 插件状态变化事件
        /// </summary>
        event EventHandler<PluginStatusChangedEventArgs> PluginStatusChanged;
    }

    /// <summary>
    /// 插件元数据
    /// </summary>
    public class PluginMetadata
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 插件名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 版本
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 作者
        /// </summary>
        public string Author { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 主程序集文件
        /// </summary>
        public string MainAssembly { get; set; } = string.Empty;

        /// <summary>
        /// 依赖项
        /// </summary>
        public List<string> Dependencies { get; set; } = new();

        /// <summary>
        /// 支持的框架
        /// </summary>
        public List<string> SupportedFrameworks { get; set; } = new();

        /// <summary>
        /// 节点定义
        /// </summary>
        public List<NodeTypeDefinition> Nodes { get; set; } = new();

        /// <summary>
        /// 资源路径
        /// </summary>
        public PluginResources Resources { get; set; } = new();

        /// <summary>
        /// 插件路径
        /// </summary>
        public string PluginPath { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 节点类型定义
    /// </summary>
    public class NodeTypeDefinition
    {
        /// <summary>
        /// 节点类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 实现类名
        /// </summary>
        public string ClassName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 插件资源
    /// </summary>
    public class PluginResources
    {
        /// <summary>
        /// 前端资源路径
        /// </summary>
        public string Frontend { get; set; } = string.Empty;

        /// <summary>
        /// 图标路径
        /// </summary>
        public string Icons { get; set; } = string.Empty;

        /// <summary>
        /// 国际化资源路径
        /// </summary>
        public string Localization { get; set; } = string.Empty;
    }

    /// <summary>
    /// 插件安装结果
    /// </summary>
    public class PluginInstallResult
    {
        /// <summary>
        /// 安装是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 插件ID
        /// </summary>
        public string PluginId { get; set; } = string.Empty;

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 安装的节点类型列表
        /// </summary>
        public List<string> InstalledNodeTypes { get; set; } = new();
    }

    /// <summary>
    /// 插件状态
    /// </summary>
    public enum PluginStatus
    {
        /// <summary>
        /// 未知
        /// </summary>
        Unknown,

        /// <summary>
        /// 已安装但未加载
        /// </summary>
        Installed,

        /// <summary>
        /// 已加载
        /// </summary>
        Loaded,

        /// <summary>
        /// 已启用
        /// </summary>
        Enabled,

        /// <summary>
        /// 已禁用
        /// </summary>
        Disabled,

        /// <summary>
        /// 加载失败
        /// </summary>
        LoadFailed,

        /// <summary>
        /// 卸载中
        /// </summary>
        Unloading
    }

    /// <summary>
    /// 插件状态变化事件参数
    /// </summary>
    public class PluginStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string PluginId { get; set; } = string.Empty;

        /// <summary>
        /// 旧状态
        /// </summary>
        public PluginStatus OldStatus { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public PluginStatus NewStatus { get; set; }

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 附加信息
        /// </summary>
        public string? Message { get; set; }
    }
}

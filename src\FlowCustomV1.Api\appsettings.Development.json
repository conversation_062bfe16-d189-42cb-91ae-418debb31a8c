{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.AspNetCore": "Warning", "FlowCustomV1": "Debug"}}}, "FlowCustomV1": {"Plugins": {"EnableHotReload": true, "LoadTimeout": "00:01:00"}, "Execution": {"MaxConcurrentExecutions": 5, "EnableParallelExecution": true}, "Security": {"RequireHttps": false, "EnableRateLimiting": false}, "Features": {"EnableUserRegistration": true, "EnableExecutionHistory": true, "EnableRealTimeMonitoring": true, "EnableMetrics": true}, "Logging": {"LogLevel": "Debug", "EnableSensitiveDataLogging": true}}}
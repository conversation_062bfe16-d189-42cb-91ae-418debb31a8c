using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FlowCustom.SDK;

namespace FlowCustom.Plugins.Core
{
    /// <summary>
    /// 数据设置节点插件
    /// 用于设置和转换数据
    /// </summary>
    public class SetNodePlugin : NodePluginBase
    {
        #region 基本信息

        public override string NodeType => "set";
        public override string DisplayName => "数据设置";
        public override string Description => "设置值和转换数据";
        public override string Category => "数据";
        public override string Author => "FlowCustom Team";
        public override string Version => "1.0.0";
        public override bool IsTrigger => false;
        public override string Icon => "📝";

        #endregion

        #region 参数定义

        protected override List<ParameterDefinition> GetParameterDefinitions()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "values",
                    DisplayName = "设置值",
                    Description = "要设置的键值对 (JSON格式)",
                    Type = ParameterType.Json,
                    Required = true,
                    DefaultValue = "{\"key1\": \"value1\", \"key2\": \"value2\"}"
                },

                new ParameterDefinition
                {
                    Name = "keepOnlySet",
                    DisplayName = "仅保留设置的值",
                    Description = "如果为true，只有设置的值会传递给下一个节点",
                    Type = ParameterType.Boolean,
                    Required = false,
                    DefaultValue = false
                },

                new ParameterDefinition
                {
                    Name = "mergeMode",
                    DisplayName = "合并模式",
                    Description = "如何合并新值与现有数据",
                    Type = ParameterType.Select,
                    Required = false,
                    DefaultValue = "merge",
                    Options = new List<ParameterOption>
                    {
                        new ParameterOption { Value = "merge", Label = "合并", Description = "合并新值到现有数据" },
                        new ParameterOption { Value = "replace", Label = "替换", Description = "完全替换现有数据" },
                        new ParameterOption { Value = "append", Label = "追加", Description = "追加到现有数据" }
                    }
                },

                new ParameterDefinition
                {
                    Name = "enableExpressions",
                    DisplayName = "启用表达式",
                    Description = "是否启用表达式计算（如 {{input.data}}）",
                    Type = ParameterType.Boolean,
                    Required = false,
                    DefaultValue = true
                }
            };
        }

        #endregion

        #region 端点定义

        protected override List<EndpointDefinition> GetInputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "main",
                    Name = "数据输入",
                    Description = "要处理的输入数据",
                    Type = EndpointType.Data,
                    Required = false,
                    Position = new EndpointPosition { Side = "top", Offset = 50 },
                    DataType = "object"
                }
            };
        }

        protected override List<EndpointDefinition> GetOutputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "main",
                    Name = "处理结果",
                    Description = "设置后的数据输出",
                    Type = EndpointType.Data,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 50 },
                    DataType = "object"
                }
            };
        }

        #endregion

        #region 核心逻辑

        protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
        {
            var valuesJson = GetParameter<string>(context, "values", "{}");
            var keepOnlySet = GetParameter<bool>(context, "keepOnlySet", false);
            var mergeMode = GetParameter<string>(context, "mergeMode", "merge");
            var enableExpressions = GetParameter<bool>(context, "enableExpressions", true);

            Log($"开始数据设置，模式: {mergeMode}，仅保留设置值: {keepOnlySet}");

            try
            {
                // 解析设置值
                var values = JsonSerializer.Deserialize<Dictionary<string, object>>(valuesJson);
                if (values == null || !values.Any())
                {
                    return NodeExecutionResult.Failure("设置值不能为空");
                }

                var outputData = new Dictionary<string, object>();

                // 获取输入数据
                var inputData = context.InputData.ContainsKey("main") 
                    ? context.InputData["main"] as Dictionary<string, object> ?? new()
                    : new Dictionary<string, object>();

                // 根据合并模式处理数据
                switch (mergeMode)
                {
                    case "replace":
                        // 完全替换
                        outputData = new Dictionary<string, object>();
                        break;
                    
                    case "append":
                        // 追加模式 - 保留现有数据
                        foreach (var kvp in inputData)
                        {
                            outputData[kvp.Key] = kvp.Value;
                        }
                        break;
                    
                    case "merge":
                    default:
                        // 合并模式 - 如果不是仅保留设置值，则先复制输入数据
                        if (!keepOnlySet)
                        {
                            foreach (var kvp in inputData)
                            {
                                outputData[kvp.Key] = kvp.Value;
                            }
                        }
                        break;
                }

                // 处理设置值
                foreach (var kvp in values)
                {
                    var processedValue = enableExpressions 
                        ? ProcessValue(kvp.Value, context, inputData)
                        : kvp.Value;
                    
                    outputData[kvp.Key] = processedValue;
                }

                // 添加元数据
                outputData["_metadata"] = new Dictionary<string, object>
                {
                    ["processedAt"] = DateTime.UtcNow,
                    ["nodeType"] = NodeType,
                    ["mergeMode"] = mergeMode,
                    ["keepOnlySet"] = keepOnlySet,
                    ["valuesCount"] = values.Count
                };

                Log($"数据设置完成，输出 {outputData.Count} 项数据");

                return NodeExecutionResult.Success(outputData, "main");
            }
            catch (JsonException ex)
            {
                Log($"JSON解析失败: {ex.Message}", LogLevel.Error);
                return NodeExecutionResult.Failure($"设置值JSON格式错误: {ex.Message}");
            }
            catch (Exception ex)
            {
                Log($"数据设置失败: {ex.Message}", LogLevel.Error);
                return NodeExecutionResult.Failure($"数据设置执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理值，支持表达式计算
        /// </summary>
        private object ProcessValue(object value, NodeExecutionContext context, Dictionary<string, object> inputData)
        {
            if (value is string stringValue && stringValue.Contains("{{") && stringValue.Contains("}}"))
            {
                try
                {
                    // 简单的表达式处理
                    var processedValue = stringValue;
                    
                    // 替换 {{input.xxx}} 表达式
                    if (processedValue.Contains("{{input."))
                    {
                        foreach (var kvp in inputData)
                        {
                            var placeholder = $"{{{{input.{kvp.Key}}}}}";
                            if (processedValue.Contains(placeholder))
                            {
                                processedValue = processedValue.Replace(placeholder, kvp.Value?.ToString() ?? "");
                            }
                        }
                    }

                    // 替换 {{workflow.xxx}} 表达式
                    if (processedValue.Contains("{{workflow."))
                    {
                        foreach (var kvp in context.Variables)
                        {
                            var placeholder = $"{{{{workflow.{kvp.Key}}}}}";
                            if (processedValue.Contains(placeholder))
                            {
                                processedValue = processedValue.Replace(placeholder, kvp.Value?.ToString() ?? "");
                            }
                        }
                    }

                    // 替换系统变量
                    processedValue = processedValue.Replace("{{now}}", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"));
                    processedValue = processedValue.Replace("{{timestamp}}", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString());
                    processedValue = processedValue.Replace("{{uuid}}", Guid.NewGuid().ToString());
                    processedValue = processedValue.Replace("{{nodeId}}", context.NodeId);
                    processedValue = processedValue.Replace("{{workflowId}}", context.WorkflowId);
                    processedValue = processedValue.Replace("{{executionId}}", context.ExecutionId);

                    return processedValue;
                }
                catch (Exception ex)
                {
                    Log($"表达式处理失败: {ex.Message}", LogLevel.Warning);
                    return value; // 返回原值
                }
            }

            return value;
        }

        #endregion

        #region 验证逻辑

        protected override void OnValidate(NodeConfiguration configuration, 
            List<ValidationError> errors, List<ValidationWarning> warnings)
        {
            // 验证设置值JSON格式
            if (configuration.Parameters.TryGetValue("values", out var valuesObj))
            {
                var valuesJson = valuesObj?.ToString() ?? "{}";
                try
                {
                    var values = JsonSerializer.Deserialize<Dictionary<string, object>>(valuesJson);
                    if (values == null || !values.Any())
                    {
                        warnings.Add(new ValidationWarning
                        {
                            Field = "values",
                            Message = "设置值为空，节点将不会产生任何输出",
                            Code = "EMPTY_VALUES"
                        });
                    }
                    else if (values.Count > 100)
                    {
                        warnings.Add(new ValidationWarning
                        {
                            Field = "values",
                            Message = "设置值过多，可能影响性能",
                            Code = "TOO_MANY_VALUES"
                        });
                    }
                }
                catch
                {
                    errors.Add(new ValidationError
                    {
                        Field = "values",
                        Message = "设置值必须是有效的JSON格式",
                        Code = "INVALID_JSON"
                    });
                }
            }
        }

        #endregion

        #region 生命周期

        protected override void OnInitialize()
        {
            Log("数据设置插件初始化完成");
        }

        protected override void OnDispose()
        {
            Log("数据设置插件资源清理完成");
        }

        #endregion
    }
}

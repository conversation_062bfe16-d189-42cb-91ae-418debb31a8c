{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.Engine\\FlowCustom.Engine.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.Engine\\FlowCustom.Engine.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.Engine\\FlowCustom.Engine.csproj", "projectName": "FlowCustom.Engine", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.Engine\\FlowCustom.Engine.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.Engine\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\ProgramDevelop\\VisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.PluginHost\\FlowCustom.PluginHost.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.PluginHost\\FlowCustom.PluginHost.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\PluginSDK\\FlowCustom.SDK\\FlowCustom.SDK.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\PluginSDK\\FlowCustom.SDK\\FlowCustom.SDK.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.PluginHost\\FlowCustom.PluginHost.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.PluginHost\\FlowCustom.PluginHost.csproj", "projectName": "FlowCustom.PluginHost", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.PluginHost\\FlowCustom.PluginHost.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\Core\\FlowCustom.PluginHost\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\ProgramDevelop\\VisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\PluginSDK\\FlowCustom.SDK\\FlowCustom.SDK.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\PluginSDK\\FlowCustom.SDK\\FlowCustom.SDK.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\PluginSDK\\FlowCustom.SDK\\FlowCustom.SDK.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\PluginSDK\\FlowCustom.SDK\\FlowCustom.SDK.csproj", "projectName": "FlowCustom.SDK", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\PluginSDK\\FlowCustom.SDK\\FlowCustom.SDK.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\PluginSDK\\FlowCustom.SDK\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\ProgramDevelop\\VisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}
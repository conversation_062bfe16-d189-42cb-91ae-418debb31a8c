using FlowCustom.Core.Models;

namespace FlowCustom.Core.Interfaces;

/// <summary>
/// 工作流引擎接口
/// </summary>
public interface IWorkflowEngine
{
    /// <summary>
    /// 执行工作流
    /// </summary>
    Task<WorkflowExecution> ExecuteAsync(
        Guid workflowId, 
        Dictionary<string, object>? inputData = null,
        string? triggeredBy = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 停止工作流执行
    /// </summary>
    Task StopExecutionAsync(Guid executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    Task CancelExecutionAsync(Guid executionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取执行状态
    /// </summary>
    Task<WorkflowExecution?> GetExecutionAsync(Guid executionId);
    
    /// <summary>
    /// 获取工作流的执行历史
    /// </summary>
    Task<IEnumerable<WorkflowExecution>> GetExecutionHistoryAsync(
        Guid workflowId, 
        int page = 1, 
        int pageSize = 50);
}

/// <summary>
/// 节点注册表接口
/// </summary>
public interface INodeRegistry
{
    /// <summary>
    /// 注册节点
    /// </summary>
    void RegisterNode<T>() where T : class, INode;
    
    /// <summary>
    /// 获取节点实例
    /// </summary>
    INode? GetNode(string nodeType);
    
    /// <summary>
    /// 获取所有已注册的节点类型
    /// </summary>
    IEnumerable<string> GetRegisteredNodeTypes();
    
    /// <summary>
    /// 获取节点定义信息
    /// </summary>
    NodeDefinition? GetNodeDefinition(string nodeType);
    
    /// <summary>
    /// 获取所有节点定义
    /// </summary>
    IEnumerable<NodeDefinition> GetAllNodeDefinitions();
}

/// <summary>
/// 节点定义信息
/// </summary>
public class NodeDefinition
{
    public string NodeType { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public bool IsTrigger { get; set; }
    public NodeParameterDefinition[] Parameters { get; set; } = Array.Empty<NodeParameterDefinition>();
}

/// <summary>
/// 工作流服务接口
/// </summary>
public interface IWorkflowService
{
    /// <summary>
    /// 创建工作流
    /// </summary>
    Task<Workflow> CreateWorkflowAsync(Workflow workflow);
    
    /// <summary>
    /// 更新工作流
    /// </summary>
    Task<Workflow> UpdateWorkflowAsync(Workflow workflow);
    
    /// <summary>
    /// 删除工作流
    /// </summary>
    Task DeleteWorkflowAsync(Guid workflowId);
    
    /// <summary>
    /// 获取工作流
    /// </summary>
    Task<Workflow?> GetWorkflowAsync(Guid workflowId);
    
    /// <summary>
    /// 获取用户的工作流列表
    /// </summary>
    Task<IEnumerable<Workflow>> GetUserWorkflowsAsync(string userId);
    
    /// <summary>
    /// 激活/停用工作流
    /// </summary>
    Task SetWorkflowActiveAsync(Guid workflowId, bool isActive);
    
    /// <summary>
    /// 验证工作流
    /// </summary>
    Task<ValidationResult> ValidateWorkflowAsync(Workflow workflow);

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    Task<IEnumerable<WorkflowExecution>> GetExecutionHistoryAsync(
        Guid workflowId,
        int page = 1,
        int pageSize = 50);
}

/// <summary>
/// 触发器管理器接口
/// </summary>
public interface ITriggerManager
{
    /// <summary>
    /// 启动工作流的所有触发器
    /// </summary>
    Task StartTriggersAsync(Guid workflowId);
    
    /// <summary>
    /// 停止工作流的所有触发器
    /// </summary>
    Task StopTriggersAsync(Guid workflowId);
    
    /// <summary>
    /// 重新加载工作流触发器
    /// </summary>
    Task ReloadTriggersAsync(Guid workflowId);
    
    /// <summary>
    /// 获取活动触发器状态
    /// </summary>
    Task<IEnumerable<TriggerStatus>> GetActiveTriggerStatusAsync();
}

/// <summary>
/// 触发器状态
/// </summary>
public class TriggerStatus
{
    public Guid WorkflowId { get; set; }
    public Guid NodeId { get; set; }
    public string NodeType { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime StartedAt { get; set; }
    public string? LastError { get; set; }
}

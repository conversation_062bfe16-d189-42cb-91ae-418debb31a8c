<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <PropertyGroup>
    <AssemblyTitle>FlowCustomV1 Web API</AssemblyTitle>
    <AssemblyDescription>Web API for FlowCustomV1 workflow automation platform</AssemblyDescription>
    <AssemblyCompany>FlowCustom Team</AssemblyCompany>
    <AssemblyProduct>FlowCustomV1</AssemblyProduct>
    <AssemblyCopyright>Copyright © FlowCustom Team 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>1.0.0</InformationalVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FlowCustomV1.Core\FlowCustomV1.Core.csproj" />
    <ProjectReference Include="..\FlowCustomV1.SDK\FlowCustomV1.SDK.csproj" />
    <ProjectReference Include="..\FlowCustomV1.PluginHost\FlowCustomV1.PluginHost.csproj" />
    <ProjectReference Include="..\FlowCustomV1.Engine\FlowCustomV1.Engine.csproj" />
  </ItemGroup>

</Project>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Handle ID匹配问题的脚本
创建最简单的工作流来定位问题
"""

import requests
import json
import uuid

# API 基础URL
BASE_URL = "http://localhost:5053/api"

def create_simple_debug_workflow():
    """创建最简单的调试工作流"""
    print("🔍 创建简单调试工作流...")
    
    # 1. 创建工作流
    workflow_data = {
        "name": "Handle调试工作流",
        "description": "最简单的工作流用于调试Handle ID问题"
    }
    
    response = requests.post(f"{BASE_URL}/workflow", json=workflow_data)
    if response.status_code != 201:
        print(f"❌ 工作流创建失败: {response.status_code}")
        return None
    
    workflow = response.json()
    workflow_id = workflow["id"]
    print(f"✅ 工作流创建成功: {workflow_id}")
    
    # 2. 创建两个最基本的节点
    trigger_id = str(uuid.uuid4())
    script_id = str(uuid.uuid4())
    
    nodes = [
        # 手动触发器
        {
            "id": trigger_id,
            "workflowId": workflow_id,
            "name": "开始",
            "type": "manual-trigger",
            "position": {"x": 200, "y": 200},
            "parameters": {
                "buttonText": "开始",
                "enabled": True
            },
            "isDisabled": False,
            "notes": "调试触发器"
        },
        # 脚本执行器
        {
            "id": script_id,
            "workflowId": workflow_id,
            "name": "处理",
            "type": "script-executor",
            "position": {"x": 500, "y": 200},
            "parameters": {
                "scriptType": "python",
                "script": "print('Hello Debug')",
                "timeout": 30
            },
            "isDisabled": False,
            "notes": "调试脚本"
        }
    ]
    
    # 3. 创建一条最基本的连线
    connections = [
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": trigger_id,
            "targetNodeId": script_id,
            "sourceOutput": "main",
            "targetInput": "main"
        }
    ]
    
    print(f"📦 创建了 {len(nodes)} 个节点和 {len(connections)} 条连线")
    print(f"🔗 连线详情:")
    for conn in connections:
        print(f"   {conn['sourceNodeId'][:8]}... ({conn['sourceOutput']}) -> {conn['targetNodeId'][:8]}... ({conn['targetInput']})")
    
    # 4. 保存工作流
    workflow["nodes"] = nodes
    workflow["connections"] = connections
    workflow["isActive"] = True
    
    response = requests.put(f"{BASE_URL}/workflow/{workflow_id}", json=workflow)
    if response.status_code != 200:
        print(f"❌ 工作流保存失败: {response.status_code}")
        print(f"   响应: {response.text}")
        return None
    
    print(f"✅ 工作流保存成功")
    return workflow_id

def main():
    print("🔍 Handle ID调试工具")
    print("="*40)
    
    workflow_id = create_simple_debug_workflow()
    
    if workflow_id:
        print("\n" + "="*40)
        print("✅ 调试工作流创建完成!")
        print(f"📝 工作流ID: {workflow_id}")
        print(f"🔗 前端地址: http://localhost:5173")
        
        print("\n🔍 调试步骤:")
        print("   1. 打开前端页面")
        print("   2. 找到'Handle调试工作流'")
        print("   3. 点击'编辑'按钮")
        print("   4. 打开浏览器控制台查看调试信息")
        print("   5. 观察Handle渲染日志")
        print("   6. 检查是否有ReactFlow错误")
        
        print("\n💡 关键调试信息:")
        print("   - 查看'节点 xxx 端点配置'日志")
        print("   - 查看'渲染输入Handle'和'渲染输出Handle'日志")
        print("   - 检查Handle ID是否与连线配置匹配")
        print("   - 确认所有Handle都正确渲染")
    else:
        print("❌ 调试工作流创建失败")

if __name__ == "__main__":
    main()

using FlowCustom.Core.Models;

namespace FlowCustom.Core.Interfaces;

/// <summary>
/// 执行历史服务接口
/// </summary>
public interface IExecutionHistoryService
{
    /// <summary>
    /// 开始执行记录
    /// </summary>
    Task<WorkflowExecution> StartExecutionAsync(Guid workflowId, string triggeredBy, Dictionary<string, object> inputData);

    /// <summary>
    /// 完成执行记录
    /// </summary>
    Task CompleteExecutionAsync(WorkflowExecution execution);

    /// <summary>
    /// 获取执行记录
    /// </summary>
    Task<WorkflowExecution?> GetExecutionAsync(Guid executionId);

    /// <summary>
    /// 获取工作流的执行历史
    /// </summary>
    Task<IEnumerable<WorkflowExecution>> GetWorkflowExecutionsAsync(Guid workflowId, int limit = 100, int skip = 0);

    /// <summary>
    /// 获取执行统计信息
    /// </summary>
    Task<ExecutionStatistics> GetExecutionStatisticsAsync(Guid? workflowId = null, DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 清理旧的执行记录
    /// </summary>
    Task CleanupOldExecutionsAsync(TimeSpan maxAge);

    /// <summary>
    /// 添加系统日志
    /// </summary>
    Task AddSystemLogAsync(string level, string message, string? source = null, 
        Guid? workflowId = null, Guid? nodeId = null, Guid? executionId = null, 
        Dictionary<string, object>? details = null);

    /// <summary>
    /// 获取系统日志
    /// </summary>
    Task<IEnumerable<SystemLogEntry>> GetLogsAsync(
        Guid? workflowId = null,
        Guid? executionId = null,
        string? level = null,
        int limit = 1000,
        int skip = 0);

    /// <summary>
    /// 获取执行记录（新方法签名）
    /// </summary>
    Task<IEnumerable<WorkflowExecution>> GetExecutionsAsync(
        Guid? workflowId = null,
        int limit = 100,
        int skip = 0);

    /// <summary>
    /// 清空日志
    /// </summary>
    Task ClearLogsAsync(Guid? workflowId = null, string? level = null);
}

<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

  <PropertyGroup>
    <AssemblyTitle>FlowCustomV1 Core Plugins</AssemblyTitle>
    <AssemblyDescription>Core plugins for FlowCustomV1 workflow automation platform</AssemblyDescription>
    <AssemblyCompany>FlowCustom Team</AssemblyCompany>
    <AssemblyProduct>FlowCustomV1</AssemblyProduct>
    <AssemblyCopyright>Copyright © FlowCustom Team 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>1.0.0</InformationalVersion>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\FlowCustomV1.SDK\FlowCustomV1.SDK.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

</Project>

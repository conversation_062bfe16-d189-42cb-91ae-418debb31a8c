using FlowCustom.Core.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustom.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class NodesController : ControllerBase
{
    private readonly INodeRegistry _nodeRegistry;

    public NodesController(INodeRegistry nodeRegistry)
    {
        _nodeRegistry = nodeRegistry;
    }

    /// <summary>
    /// 获取所有可用的节点定义
    /// </summary>
    [HttpGet]
    public ActionResult<IEnumerable<NodeDefinition>> GetAllNodes()
    {
        var nodes = _nodeRegistry.GetAllNodeDefinitions();
        return Ok(nodes);
    }

    /// <summary>
    /// 获取指定节点的定义
    /// </summary>
    [HttpGet("{nodeType}")]
    public ActionResult<NodeDefinition> GetNode(string nodeType)
    {
        var node = _nodeRegistry.GetNodeDefinition(nodeType);
        if (node == null)
        {
            return NotFound();
        }
        return Ok(node);
    }

    /// <summary>
    /// 获取节点分类
    /// </summary>
    [HttpGet("categories")]
    public ActionResult<IEnumerable<string>> GetCategories()
    {
        var categories = _nodeRegistry.GetAllNodeDefinitions()
            .Select(n => n.Category)
            .Distinct()
            .OrderBy(c => c);
        
        return Ok(categories);
    }
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终功能测试脚本 - 展示所有新功能
"""

import requests
import json
import uuid
import time

# API 基础URL
BASE_URL = "http://localhost:5053/api"

def test_all_features():
    """测试所有新功能"""
    print("🚀 FlowCustom 最终功能展示测试")
    print("="*80)
    
    # 1. 测试节点类型
    print("\n📋 1. 节点类型统计")
    response = requests.get(f"{BASE_URL}/nodes")
    if response.status_code == 200:
        nodes = response.json()
        categories = {}
        for node in nodes:
            category = node.get('category', '未分类')
            if category not in categories:
                categories[category] = []
            categories[category].append(node)
        
        print(f"✅ 总计 {len(nodes)} 个节点类型，分为 {len(categories)} 个分类：")
        for category, category_nodes in categories.items():
            print(f"   📂 {category}: {len(category_nodes)} 个节点")
            for node in category_nodes:
                icon_map = {
                    'globe': '🌐', 'edit': '⚙️', 'webhook': '🔗', 'clock': '⏰',
                    'api': '🌐', 'folder': '📁', 'hand-pointer': '👆', 'code': '💻',
                    'condition': '❓', 'loop': '🔁', 'transform': '🔄'
                }
                icon = icon_map.get(node.get('icon', ''), '📦')
                print(f"      {icon} {node.get('displayName', 'N/A')}")
    
    # 2. 创建展示工作流
    print(f"\n🔧 2. 创建功能展示工作流")
    workflow_data = {
        "name": "FlowCustom 功能展示",
        "description": "展示连线箭头、执行状态、并行处理、逻辑节点等所有新功能"
    }
    
    response = requests.post(f"{BASE_URL}/workflow", json=workflow_data)
    if response.status_code != 201:
        print(f"❌ 工作流创建失败: {response.status_code}")
        return None
    
    workflow = response.json()
    workflow_id = workflow["id"]
    print(f"✅ 工作流创建成功: {workflow['name']}")
    print(f"   ID: {workflow_id}")
    
    # 3. 设置复杂工作流
    print(f"\n🏗️ 3. 构建复杂工作流结构")
    
    # 创建节点
    nodes = []
    connections = []
    
    # 手动触发器
    trigger_id = str(uuid.uuid4())
    nodes.append({
        "id": trigger_id,
        "workflowId": workflow_id,
        "name": "🚀 启动展示",
        "type": "manual-trigger",
        "position": {"x": 100, "y": 200},
        "parameters": {
            "buttonText": "开始功能展示",
            "confirmMessage": "准备好体验 FlowCustom 的强大功能了吗？",
            "enabled": True
        },
        "isDisabled": False,
        "notes": "展示手动触发功能"
    })
    
    # 条件判断节点
    condition_id = str(uuid.uuid4())
    nodes.append({
        "id": condition_id,
        "workflowId": workflow_id,
        "name": "🤔 智能判断",
        "type": "conditional",
        "position": {"x": 350, "y": 200},
        "parameters": {
            "condition": "data.mode == \"demo\"",
            "inputData": {"mode": "demo", "level": "advanced"},
            "trueOutput": {"path": "demo_mode", "message": "进入演示模式"},
            "falseOutput": {"path": "normal_mode", "message": "进入普通模式"}
        },
        "isDisabled": False,
        "notes": "展示条件判断逻辑"
    })
    
    # 并行分支1：数据处理
    script1_id = str(uuid.uuid4())
    nodes.append({
        "id": script1_id,
        "workflowId": workflow_id,
        "name": "📊 数据分析",
        "type": "script-executor",
        "position": {"x": 600, "y": 100},
        "parameters": {
            "scriptType": "python",
            "script": """
print("🔍 开始数据分析...")
import json
import time
from datetime import datetime

# 模拟数据分析
data = {
    "analysis_type": "performance_metrics",
    "timestamp": datetime.now().isoformat(),
    "metrics": {
        "cpu_usage": 45.2,
        "memory_usage": 67.8,
        "disk_io": 23.1,
        "network_io": 12.5
    },
    "status": "healthy",
    "recommendations": [
        "系统运行正常",
        "内存使用率适中",
        "建议定期清理临时文件"
    ]
}

print(f"📈 分析完成: {data['status']}")
print(f"💾 内存使用: {data['metrics']['memory_usage']}%")
print(f"🔧 建议数量: {len(data['recommendations'])}")
print("✅ 数据分析完成")
""",
            "timeout": 30
        },
        "isDisabled": False,
        "notes": "并行分支1：数据分析"
    })
    
    # 并行分支2：系统监控
    script2_id = str(uuid.uuid4())
    nodes.append({
        "id": script2_id,
        "workflowId": workflow_id,
        "name": "🖥️ 系统监控",
        "type": "script-executor",
        "position": {"x": 600, "y": 300},
        "parameters": {
            "scriptType": "javascript",
            "script": """
console.log("🔍 开始系统监控...");

// 模拟系统监控
const monitoring = {
    timestamp: new Date().toISOString(),
    services: [
        { name: "Web Server", status: "running", uptime: "99.9%" },
        { name: "Database", status: "running", uptime: "99.8%" },
        { name: "Cache", status: "running", uptime: "99.7%" },
        { name: "Queue", status: "running", uptime: "99.6%" }
    ],
    alerts: [],
    overall_health: "excellent"
};

console.log(`🟢 服务状态: ${monitoring.services.length} 个服务运行中`);
console.log(`⚡ 整体健康度: ${monitoring.overall_health}`);
console.log(`🚨 告警数量: ${monitoring.alerts.length}`);
console.log("✅ 系统监控完成");
""",
            "timeout": 30
        },
        "isDisabled": False,
        "notes": "并行分支2：系统监控"
    })
    
    # 循环处理节点
    loop_id = str(uuid.uuid4())
    nodes.append({
        "id": loop_id,
        "workflowId": workflow_id,
        "name": "🔁 批量处理",
        "type": "loop",
        "position": {"x": 850, "y": 200},
        "parameters": {
            "loopType": "array",
            "arrayData": ["任务1", "任务2", "任务3", "任务4", "任务5"],
            "maxIterations": 10,
            "batchSize": 2
        },
        "isDisabled": False,
        "notes": "展示循环批处理功能"
    })
    
    # 数据转换节点
    transform_id = str(uuid.uuid4())
    nodes.append({
        "id": transform_id,
        "workflowId": workflow_id,
        "name": "🔄 数据转换",
        "type": "data-transform",
        "position": {"x": 1100, "y": 200},
        "parameters": {
            "transformType": "map",
            "inputData": [
                {"name": "Alice", "score": 95, "grade": "A"},
                {"name": "Bob", "score": 87, "grade": "B"},
                {"name": "Charlie", "score": 92, "grade": "A"}
            ],
            "transformExpression": "item.name + ' 获得 ' + item.grade + ' 等级 (分数: ' + item.score + ')'"
        },
        "isDisabled": False,
        "notes": "展示数据转换功能"
    })
    
    # 最终汇总节点
    final_id = str(uuid.uuid4())
    nodes.append({
        "id": final_id,
        "workflowId": workflow_id,
        "name": "🎯 功能展示完成",
        "type": "script-executor",
        "position": {"x": 1350, "y": 200},
        "parameters": {
            "scriptType": "python",
            "script": """
print("🎉 FlowCustom 功能展示完成！")
print("="*50)

features = [
    "✅ 连线箭头显示",
    "✅ 实时执行状态",
    "✅ 并行分支处理", 
    "✅ 条件判断逻辑",
    "✅ 循环批处理",
    "✅ 数据转换",
    "✅ 多语言脚本支持",
    "✅ 执行历史记录",
    "✅ 节点状态指示器",
    "✅ 工作流保存功能"
]

print("🚀 已展示的功能:")
for feature in features:
    print(f"   {feature}")

print("\\n🎊 FlowCustom - 强大的工作流自动化平台！")
print("📝 支持复杂业务逻辑和数据处理")
print("🔧 可视化编辑，简单易用")
print("⚡ 高性能并行执行")
""",
            "timeout": 30
        },
        "isDisabled": False,
        "notes": "展示完成总结"
    })
    
    # 创建连接（展示连线箭头）
    connections = [
        # 主流程
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": trigger_id, "targetNodeId": condition_id, "sourceOutput": "main", "targetInput": "main"},
        
        # 并行分支
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": condition_id, "targetNodeId": script1_id, "sourceOutput": "true", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": condition_id, "targetNodeId": script2_id, "sourceOutput": "true", "targetInput": "main"},
        
        # 汇聚到循环
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": script1_id, "targetNodeId": loop_id, "sourceOutput": "main", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": script2_id, "targetNodeId": loop_id, "sourceOutput": "main", "targetInput": "main"},
        
        # 后续处理
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": loop_id, "targetNodeId": transform_id, "sourceOutput": "main", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": transform_id, "targetNodeId": final_id, "sourceOutput": "main", "targetInput": "main"},
    ]
    
    # 更新工作流
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 获取工作流失败: {response.status_code}")
        return None
    
    workflow = response.json()
    workflow["nodes"] = nodes
    workflow["connections"] = connections
    workflow["isActive"] = True
    
    response = requests.put(f"{BASE_URL}/workflow/{workflow_id}", json=workflow)
    if response.status_code != 200:
        print(f"❌ 工作流更新失败: {response.status_code}")
        return None
    
    print(f"✅ 工作流构建完成")
    print(f"   📦 节点数量: {len(nodes)}")
    print(f"   🔗 连接数量: {len(connections)}")
    print(f"   🌟 特色功能: 并行执行、条件判断、循环处理、数据转换")
    
    # 4. 执行展示
    print(f"\n🚀 4. 执行功能展示")
    response = requests.post(f"{BASE_URL}/trigger/manual/{workflow_id}")
    
    if response.status_code != 200:
        print(f"❌ 工作流触发失败: {response.status_code}")
        return None
    
    result = response.json()
    execution_id = result.get("executionId")
    print(f"✅ 工作流触发成功")
    print(f"   🆔 执行ID: {execution_id}")
    print(f"   ⏱️ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 等待执行完成
    print(f"\n⏳ 等待执行完成...")
    time.sleep(8)
    
    # 获取执行结果
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}/history")
    if response.status_code == 200:
        executions = response.json()
        if executions:
            latest = executions[0]
            print(f"\n📊 5. 执行结果统计")
            print(f"   🎯 状态: {'成功' if latest.get('status') == 2 else '失败'}")
            print(f"   ⏱️ 总耗时: {(time.mktime(time.strptime(latest.get('finishedAt', '')[:19], '%Y-%m-%dT%H:%M:%S')) - time.mktime(time.strptime(latest.get('startedAt', '')[:19], '%Y-%m-%dT%H:%M:%S'))):.2f}秒" if latest.get('finishedAt') else "执行中...")
            
            node_executions = latest.get("nodeExecutions", [])
            if node_executions:
                print(f"   📋 节点执行详情:")
                for i, node_exec in enumerate(node_executions, 1):
                    status_icon = "✅" if node_exec.get('status') == 2 else "❌"
                    duration = node_exec.get('duration', '00:00:00')
                    print(f"      {i}. {status_icon} {node_exec.get('nodeName', 'N/A')} ({duration})")
    
    print(f"\n🎊 6. 功能展示总结")
    print(f"✅ 所有核心功能已成功展示！")
    print(f"🔗 前端地址: http://localhost:5173")
    print(f"📝 工作流ID: {workflow_id}")
    print(f"\n🌟 FlowCustom 特色功能:")
    print(f"   🎨 可视化工作流编辑器")
    print(f"   ⚡ 多分支并行执行引擎")
    print(f"   🧠 智能条件判断和循环处理")
    print(f"   🔄 强大的数据转换能力")
    print(f"   💻 多语言脚本支持 (Python/JavaScript/Bash)")
    print(f"   📊 实时执行状态监控")
    print(f"   🔗 带箭头的连线显示")
    print(f"   📈 完整的执行历史记录")
    
    return workflow_id

if __name__ == "__main__":
    test_all_features()

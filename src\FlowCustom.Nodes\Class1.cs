using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using System.Text;
using System.Text.Json;

namespace FlowCustom.Nodes;

/// <summary>
/// HTTP 请求节点
/// </summary>
public class HttpRequestNode : INode
{
    private readonly HttpClient _httpClient;

    public HttpRequestNode(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public string NodeType => "http-request";
    public string DisplayName => "HTTP Request";
    public string Description => "Make HTTP requests to external APIs";
    public string Category => "Network";
    public string Icon => "globe";

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "method",
                DisplayName = "HTTP Method",
                Description = "The HTTP method to use",
                Type = ParameterType.Select,
                Required = true,
                DefaultValue = "GET",
                Options = new[] { "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS" }
            },
            new NodeParameterDefinition
            {
                Name = "url",
                DisplayName = "URL",
                Description = "The URL to make the request to",
                Type = ParameterType.String,
                Required = true
            },
            new NodeParameterDefinition
            {
                Name = "headers",
                DisplayName = "Headers",
                Description = "HTTP headers to include",
                Type = ParameterType.Object,
                Required = false
            },
            new NodeParameterDefinition
            {
                Name = "body",
                DisplayName = "Request Body",
                Description = "The request body (for POST, PUT, PATCH)",
                Type = ParameterType.Object,
                Required = false
            },
            new NodeParameterDefinition
            {
                Name = "timeout",
                DisplayName = "Timeout (seconds)",
                Description = "Request timeout in seconds",
                Type = ParameterType.Number,
                Required = false,
                DefaultValue = 30
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node,
        Models.ExecutionContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var method = GetParameter<string>(node, "method", "GET");
            var url = GetParameter<string>(node, "url");
            var headers = GetParameter<Dictionary<string, string>>(node, "headers");
            var body = GetParameter<object>(node, "body");
            var timeoutSeconds = GetParameter<int>(node, "timeout", 30);

            if (string.IsNullOrEmpty(url))
            {
                return NodeExecutionResult.CreateError("URL is required");
            }

            using var request = new HttpRequestMessage(new HttpMethod(method), url);

            // 设置超时
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, timeoutCts.Token);

            // 添加头部
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
            }

            // 添加请求体
            if (body != null && (method == "POST" || method == "PUT" || method == "PATCH"))
            {
                var json = JsonSerializer.Serialize(body);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            }

            var response = await _httpClient.SendAsync(request, combinedCts.Token);
            var responseContent = await response.Content.ReadAsStringAsync(combinedCts.Token);

            var result = new Dictionary<string, object>
            {
                ["statusCode"] = (int)response.StatusCode,
                ["headers"] = response.Headers.ToDictionary(h => h.Key, h => string.Join(", ", h.Value)),
                ["body"] = responseContent,
                ["isSuccess"] = response.IsSuccessStatusCode
            };

            // 尝试解析 JSON 响应
            try
            {
                if (response.Content.Headers.ContentType?.MediaType == "application/json")
                {
                    var jsonData = JsonSerializer.Deserialize<object>(responseContent);
                    result["json"] = jsonData;
                }
            }
            catch
            {
                // 忽略 JSON 解析错误
            }

            return NodeExecutionResult.CreateSuccess(result);
        }
        catch (Exception ex)
        {
            return NodeExecutionResult.CreateError($"HTTP request failed: {ex.Message}");
        }
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.ContainsKey("url") || string.IsNullOrEmpty(parameters["url"]?.ToString()))
        {
            errors.Add("URL is required");
        }

        if (parameters.ContainsKey("method"))
        {
            var method = parameters["method"]?.ToString()?.ToUpper();
            var validMethods = new[] { "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS" };
            if (!validMethods.Contains(method))
            {
                errors.Add("Invalid HTTP method");
            }
        }

        return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                if (typeof(T) == typeof(Dictionary<string, string>) && value is JsonElement jsonElement)
                {
                    var dict = JsonSerializer.Deserialize<Dictionary<string, string>>(jsonElement.GetRawText());
                    return (T)(object)dict!;
                }

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

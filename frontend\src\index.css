@tailwind base;
@tailwind components;
@tailwind utilities;

/* React Flow styles */
@import '@xyflow/react/dist/style.css';

/* Custom styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

/* Custom node styles */
.custom-node {
  @apply bg-white border-2 border-gray-300 rounded-lg shadow-md p-3 min-w-[200px];
}

.custom-node.selected {
  @apply border-primary-500;
}

.custom-node .node-header {
  @apply flex items-center gap-2 mb-2;
}

.custom-node .node-title {
  @apply font-semibold text-gray-800;
}

.custom-node .node-description {
  @apply text-sm text-gray-600;
}

/* Handle styles */
.react-flow__handle {
  @apply w-3 h-3 border-2 border-gray-400 bg-white;
}

.react-flow__handle-top {
  @apply -top-1.5;
}

.react-flow__handle-bottom {
  @apply -bottom-1.5;
}

.react-flow__handle-left {
  @apply -left-1.5;
}

.react-flow__handle-right {
  @apply -right-1.5;
}

/* Sidebar styles */
.node-sidebar {
  @apply w-80 bg-gray-50 border-l border-gray-200 p-4 overflow-y-auto;
}

.node-category {
  @apply mb-6;
}

.node-category-title {
  @apply text-sm font-semibold text-gray-700 uppercase tracking-wide mb-3;
}

.node-item {
  @apply flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 cursor-pointer hover:border-primary-300 hover:shadow-sm transition-all duration-200 mb-2;
}

.node-item:hover {
  @apply transform translate-x-1;
}

/* React Flow styles */
@import '@xyflow/react/dist/style.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 节点执行状态动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 连线执行动画 */
@keyframes flow {
  0% {
    stroke-dashoffset: 20;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.executing-edge {
  stroke-dasharray: 5 5;
  animation: flow 1s linear infinite;
}

/* 节点执行状态样式 */
.node-executing {
  animation: pulse 2s infinite;
}

.node-success {
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

.node-error {
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

/* Coze风格的全局样式 */
:root {
  --coze-primary: #6366f1;
  --coze-primary-hover: #5855eb;
  --coze-secondary: #f1f5f9;
  --coze-accent: #10b981;
  --coze-warning: #f59e0b;
  --coze-error: #ef4444;
  --coze-text-primary: #1e293b;
  --coze-text-secondary: #64748b;
  --coze-text-muted: #94a3b8;
  --coze-border: #e2e8f0;
  --coze-border-light: #f1f5f9;
  --coze-bg-primary: #ffffff;
  --coze-bg-secondary: #f8fafc;
  --coze-bg-tertiary: #f1f5f9;
  --coze-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --coze-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --coze-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --coze-radius-sm: 6px;
  --coze-radius-md: 8px;
  --coze-radius-lg: 12px;
  --coze-radius-xl: 16px;
}

* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--coze-bg-secondary);
  color: var(--coze-text-primary);
  line-height: 1.5;
}

/* Coze风格的按钮 */
.coze-btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium transition-all duration-200 ease-in-out;
  border-radius: var(--coze-radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  outline: none;
  user-select: none;
}

.coze-btn-primary {
  background-color: var(--coze-primary);
  color: white;
  box-shadow: var(--coze-shadow-sm);
}

.coze-btn-primary:hover {
  background-color: var(--coze-primary-hover);
  box-shadow: var(--coze-shadow-md);
  transform: translateY(-1px);
}

.coze-btn-secondary {
  background-color: var(--coze-bg-primary);
  color: var(--coze-text-primary);
  border-color: var(--coze-border);
  box-shadow: var(--coze-shadow-sm);
}

.coze-btn-secondary:hover {
  background-color: var(--coze-bg-tertiary);
  border-color: var(--coze-primary);
  box-shadow: var(--coze-shadow-md);
}

.coze-btn-success {
  background-color: var(--coze-accent);
  color: white;
  box-shadow: var(--coze-shadow-sm);
}

.coze-btn-success:hover {
  background-color: #059669;
  box-shadow: var(--coze-shadow-md);
  transform: translateY(-1px);
}

.coze-btn-warning {
  background-color: var(--coze-warning);
  color: white;
  box-shadow: var(--coze-shadow-sm);
}

.coze-btn-warning:hover {
  background-color: #d97706;
  box-shadow: var(--coze-shadow-md);
  transform: translateY(-1px);
}

.coze-btn-danger {
  background-color: var(--coze-error);
  color: white;
  box-shadow: var(--coze-shadow-sm);
}

.coze-btn-danger:hover {
  background-color: #dc2626;
  box-shadow: var(--coze-shadow-md);
  transform: translateY(-1px);
}

/* Coze风格的卡片 */
.coze-card {
  background-color: var(--coze-bg-primary);
  border: 1px solid var(--coze-border);
  border-radius: var(--coze-radius-lg);
  box-shadow: var(--coze-shadow-sm);
  transition: all 0.2s ease-in-out;
}

.coze-card:hover {
  box-shadow: var(--coze-shadow-md);
  border-color: var(--coze-primary);
}

.coze-card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--coze-border-light);
  border-radius: var(--coze-radius-lg) var(--coze-radius-lg) 0 0;
}

.coze-card-body {
  padding: 20px;
}

.coze-card-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--coze-border-light);
  border-radius: 0 0 var(--coze-radius-lg) var(--coze-radius-lg);
  background-color: var(--coze-bg-secondary);
}

/* Coze风格的输入框 */
.coze-input {
  @apply w-full px-3 py-2 text-sm transition-all duration-200;
  background-color: var(--coze-bg-primary);
  border: 1px solid var(--coze-border);
  border-radius: var(--coze-radius-md);
  color: var(--coze-text-primary);
  outline: none;
}

.coze-input:focus {
  border-color: var(--coze-primary);
  box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

.coze-input::placeholder {
  color: var(--coze-text-muted);
}

/* Coze风格的标签 */
.coze-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium;
  border-radius: var(--coze-radius-sm);
  background-color: var(--coze-bg-tertiary);
  color: var(--coze-text-secondary);
}

.coze-badge-primary {
  background-color: rgb(99 102 241 / 0.1);
  color: var(--coze-primary);
}

.coze-badge-success {
  background-color: rgb(16 185 129 / 0.1);
  color: var(--coze-accent);
}

.coze-badge-warning {
  background-color: rgb(245 158 11 / 0.1);
  color: var(--coze-warning);
}

.coze-badge-error {
  background-color: rgb(239 68 68 / 0.1);
  color: var(--coze-error);
}

/* Custom styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

/* Custom node styles */
.custom-node {
  @apply bg-white border-2 border-gray-300 rounded-lg shadow-md p-3 min-w-[200px];
}

.custom-node.selected {
  @apply border-blue-500;
}

.custom-node .node-header {
  @apply flex items-center gap-2 mb-2;
}

.custom-node .node-title {
  @apply font-semibold text-gray-800;
}

.custom-node .node-description {
  @apply text-sm text-gray-600;
}

/* Handle styles */
.react-flow__handle {
  @apply w-3 h-3 border-2 border-gray-400 bg-white;
}

.react-flow__handle-top {
  @apply -top-1.5;
}

.react-flow__handle-bottom {
  @apply -bottom-1.5;
}

.react-flow__handle-left {
  @apply -left-1.5;
}

.react-flow__handle-right {
  @apply -right-1.5;
}

/* Sidebar styles */
.node-sidebar {
  @apply w-80 bg-gray-50 border-l border-gray-200 p-4 overflow-y-auto;
}

.node-category {
  @apply mb-6;
}

.node-category-title {
  @apply text-sm font-semibold text-gray-700 uppercase tracking-wide mb-3;
}

.node-item {
  @apply flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200 mb-2;
}

.node-item:hover {
  @apply transform translate-x-1;
}

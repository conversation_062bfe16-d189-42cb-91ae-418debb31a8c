import axios from 'axios';
import type { Workflow, NodeDefinition, WorkflowExecution } from '../types/workflow';

const API_BASE_URL = 'http://localhost:5053/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Workflow API
export const workflowApi = {
  // Get all workflows
  getWorkflows: async (userId = 'default'): Promise<Workflow[]> => {
    const response = await api.get(`/workflow?userId=${userId}`);
    return response.data;
  },

  // Get workflow by ID
  getWorkflow: async (id: string): Promise<Workflow> => {
    const response = await api.get(`/workflow/${id}`);
    return response.data;
  },

  // Create workflow
  createWorkflow: async (workflow: Partial<Workflow>): Promise<Workflow> => {
    const response = await api.post('/workflow', workflow);
    return response.data;
  },

  // Update workflow
  updateWorkflow: async (id: string, workflow: Workflow): Promise<Workflow> => {
    const response = await api.put(`/workflow/${id}`, workflow);
    return response.data;
  },

  // Delete workflow
  deleteWorkflow: async (id: string): Promise<void> => {
    await api.delete(`/workflow/${id}`);
  },

  // Toggle workflow active status
  toggleWorkflow: async (id: string, isActive: boolean): Promise<void> => {
    await api.post(`/workflow/${id}/toggle`, isActive);
  },

  // Execute workflow
  executeWorkflow: async (id: string, inputData?: Record<string, unknown>): Promise<WorkflowExecution> => {
    const response = await api.post(`/workflow/${id}/execute`, inputData || {});
    return response.data;
  },

  // Get execution history
  getExecutionHistory: async (id: string, page = 1, pageSize = 50): Promise<WorkflowExecution[]> => {
    const response = await api.get(`/workflow/${id}/executions?page=${page}&pageSize=${pageSize}`);
    return response.data;
  },

  // Get execution details
  getExecution: async (executionId: string): Promise<WorkflowExecution> => {
    const response = await api.get(`/workflow/executions/${executionId}`);
    return response.data;
  },

  // Stop execution
  stopExecution: async (executionId: string): Promise<void> => {
    await api.post(`/workflow/executions/${executionId}/stop`);
  },
};

// Nodes API
export const nodesApi = {
  // Get all available nodes
  getNodes: async (): Promise<NodeDefinition[]> => {
    const response = await api.get('/nodes');
    return response.data;
  },

  // Get node definition
  getNode: async (nodeType: string): Promise<NodeDefinition> => {
    const response = await api.get(`/nodes/${nodeType}`);
    return response.data;
  },

  // Get node categories
  getCategories: async (): Promise<string[]> => {
    const response = await api.get('/nodes/categories');
    return response.data;
  },
};

export default api;

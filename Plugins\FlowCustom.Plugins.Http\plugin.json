{"id": "FlowCustom.Plugins.Http", "name": "HTTP插件包", "version": "1.0.0", "author": "FlowCustom Team", "description": "提供HTTP相关功能的插件包，包含Webhook触发器、HTTP请求等节点", "main": "FlowCustom.Plugins.Http.dll", "dependencies": ["FlowCustom.SDK@^1.0.0"], "supportedFrameworks": ["net8.0"], "nodes": [{"type": "webhook-trigger", "class": "FlowCustom.Plugins.Http.WebhookTriggerPlugin", "displayName": "Webhook触发器", "category": "触发器", "description": "接收HTTP请求触发工作流执行"}, {"type": "http-request", "class": "FlowCustom.Plugins.Http.HttpRequestPlugin", "displayName": "HTTP请求", "category": "网络", "description": "发送HTTP请求并获取响应"}], "resources": {"frontend": "wwwroot/", "icons": "icons/", "localization": "locales/"}, "permissions": ["network.listen", "network.access", "http.server"], "configuration": {"defaultPort": 5053, "maxWebhooks": 100, "defaultTimeout": 30, "enableHttps": false, "corsEnabled": true, "authenticationMethods": ["none", "token", "basic", "signature"]}, "endpoints": {"webhooks": "/api/webhooks", "health": "/api/health"}, "metadata": {"homepage": "https://flowcustom.com/plugins/http", "repository": "https://github.com/flowcustom/plugins-http", "license": "MIT", "keywords": ["webhook", "http", "trigger", "api", "rest"], "screenshots": ["screenshots/webhook-trigger.png"], "documentation": "docs/README.md"}, "compatibility": {"minEngineVersion": "1.0.0", "maxEngineVersion": "2.0.0", "operatingSystems": ["windows", "linux", "macos"]}, "installation": {"preInstallScript": "scripts/pre-install.ps1", "postInstallScript": "scripts/post-install.ps1", "uninstallScript": "scripts/uninstall.ps1"}}
#!/bin/bash

# FlowCustom 部署脚本
set -e

echo "🚀 Starting FlowCustom deployment..."

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# 创建必要的目录
echo "📁 Creating directories..."
mkdir -p data/logs
mkdir -p data/uploads
mkdir -p nginx/ssl

# 设置权限
chmod 755 data/logs
chmod 755 data/uploads

# 构建前端
echo "🔨 Building frontend..."
if [ -d "frontend" ]; then
    cd frontend
    if [ -f "package.json" ]; then
        echo "Installing frontend dependencies..."
        npm install
        echo "Building frontend..."
        npm run build
        
        # 复制构建结果到 nginx 目录
        if [ -d "dist" ]; then
            echo "Copying frontend build to nginx..."
            mkdir -p ../nginx/html
            cp -r dist/* ../nginx/html/
        fi
    fi
    cd ..
else
    echo "⚠️  Frontend directory not found, skipping frontend build"
fi

# 停止现有容器
echo "🛑 Stopping existing containers..."
docker-compose down

# 构建镜像
echo "🔨 Building Docker images..."
docker-compose build

# 启动服务
echo "🚀 Starting services..."
docker-compose up -d

# 等待服务启动
echo "⏳ Waiting for services to start..."
sleep 30

# 健康检查
echo "🔍 Performing health check..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost/health > /dev/null 2>&1; then
        echo "✅ Health check passed!"
        break
    else
        echo "⏳ Attempt $attempt/$max_attempts: Service not ready yet..."
        sleep 10
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ Health check failed after $max_attempts attempts"
    echo "📋 Container logs:"
    docker-compose logs --tail=50
    exit 1
fi

# 显示服务状态
echo "📊 Service status:"
docker-compose ps

echo ""
echo "🎉 FlowCustom deployment completed successfully!"
echo ""
echo "📍 Access URLs:"
echo "   Frontend: http://localhost"
echo "   API: http://localhost/api"
echo "   Swagger: http://localhost/swagger"
echo "   Health: http://localhost/health"
echo ""
echo "🔧 Management commands:"
echo "   View logs: docker-compose logs -f"
echo "   Stop services: docker-compose down"
echo "   Restart services: docker-compose restart"
echo "   Update services: docker-compose pull && docker-compose up -d"
echo ""
echo "📚 Default credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""

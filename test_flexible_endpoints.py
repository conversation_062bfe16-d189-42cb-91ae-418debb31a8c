#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
灵活端点配置功能测试脚本
验证动态端点管理和Handle修复
"""

import requests
import json
import uuid

# API 基础URL
BASE_URL = "http://localhost:5053/api"

def create_flexible_endpoint_test():
    """创建灵活端点测试工作流"""
    print("🔗 创建灵活端点配置测试工作流...")
    
    # 1. 创建工作流
    workflow_data = {
        "name": "灵活端点配置测试",
        "description": "测试动态端点管理和自定义端点配置功能"
    }
    
    response = requests.post(f"{BASE_URL}/workflow", json=workflow_data)
    if response.status_code != 201:
        print(f"❌ 工作流创建失败: {response.status_code}")
        return None
    
    workflow = response.json()
    workflow_id = workflow["id"]
    print(f"✅ 工作流创建成功: {workflow_id}")
    
    # 2. 创建带自定义端点的节点
    trigger_id = str(uuid.uuid4())
    processor_id = str(uuid.uuid4())
    merger_id = str(uuid.uuid4())
    
    nodes = [
        # 手动触发器 - 使用默认端点
        {
            "id": trigger_id,
            "workflowId": workflow_id,
            "name": "🚀 开始触发",
            "type": "manual-trigger",
            "position": {"x": 100, "y": 200},
            "parameters": {
                "buttonText": "开始测试",
                "enabled": True
            },
            "isDisabled": False,
            "notes": "使用默认端点配置的触发器"
        },
        # 处理器 - 自定义多个输出端点
        {
            "id": processor_id,
            "workflowId": workflow_id,
            "name": "🔄 多输出处理器",
            "type": "script-executor",
            "position": {"x": 400, "y": 200},
            "parameters": {
                "scriptType": "python",
                "script": "# 多输出处理逻辑\nprint('处理数据')\nresult = {'status': 'success', 'data': 'processed'}",
                "timeout": 30
            },
            "isDisabled": False,
            "notes": "自定义多个输出端点的处理器",
            # 自定义输出端点
            "customOutputs": [
                {
                    "id": "success_data",
                    "name": "成功数据",
                    "description": "处理成功时的数据输出",
                    "type": "success",
                    "position": {"side": "bottom", "offset": 25}
                },
                {
                    "id": "error_info",
                    "name": "错误信息",
                    "description": "处理失败时的错误信息",
                    "type": "error",
                    "position": {"side": "bottom", "offset": 50}
                },
                {
                    "id": "debug_log",
                    "name": "调试日志",
                    "description": "调试信息输出",
                    "type": "data",
                    "position": {"side": "bottom", "offset": 75}
                }
            ]
        },
        # 合并器 - 自定义多个输入端点
        {
            "id": merger_id,
            "workflowId": workflow_id,
            "name": "🔗 多输入合并器",
            "type": "script-executor",
            "position": {"x": 700, "y": 200},
            "parameters": {
                "scriptType": "javascript",
                "script": "// 合并多个输入\nconsole.log('合并数据');",
                "timeout": 30
            },
            "isDisabled": False,
            "notes": "自定义多个输入端点的合并器",
            # 自定义输入端点
            "customInputs": [
                {
                    "id": "data_input",
                    "name": "数据输入",
                    "description": "主要数据输入",
                    "type": "data",
                    "position": {"side": "left", "offset": 25}
                },
                {
                    "id": "config_input",
                    "name": "配置输入",
                    "description": "配置参数输入",
                    "type": "data",
                    "position": {"side": "left", "offset": 50}
                },
                {
                    "id": "metadata_input",
                    "name": "元数据输入",
                    "description": "元数据信息输入",
                    "type": "data",
                    "position": {"side": "left", "offset": 75}
                }
            ],
            # 自定义输出端点
            "customOutputs": [
                {
                    "id": "merged_result",
                    "name": "合并结果",
                    "description": "合并后的最终结果",
                    "type": "data",
                    "position": {"side": "right", "offset": 50}
                }
            ]
        }
    ]
    
    # 3. 创建连线 - 测试自定义端点连接
    connections = [
        # 触发器 -> 处理器
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": trigger_id,
            "targetNodeId": processor_id,
            "sourceOutput": "main",
            "targetInput": "main"
        },
        # 处理器成功输出 -> 合并器数据输入
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": processor_id,
            "targetNodeId": merger_id,
            "sourceOutput": "success_data",  # 自定义端点
            "targetInput": "data_input"      # 自定义端点
        },
        # 处理器调试日志 -> 合并器元数据输入
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": processor_id,
            "targetNodeId": merger_id,
            "sourceOutput": "debug_log",     # 自定义端点
            "targetInput": "metadata_input"  # 自定义端点
        }
    ]
    
    print(f"📦 创建了 {len(nodes)} 个节点和 {len(connections)} 条连线")
    print(f"🔗 自定义端点测试:")
    print(f"   ✅ 处理器: 3个自定义输出端点")
    print(f"   ✅ 合并器: 3个自定义输入端点 + 1个自定义输出端点")
    print(f"   ✅ 连线: 使用自定义端点ID进行连接")
    
    # 4. 保存工作流
    workflow["nodes"] = nodes
    workflow["connections"] = connections
    workflow["isActive"] = True
    
    response = requests.put(f"{BASE_URL}/workflow/{workflow_id}", json=workflow)
    if response.status_code != 200:
        print(f"❌ 工作流保存失败: {response.status_code}")
        print(f"   响应: {response.text}")
        return None
    
    print(f"✅ 工作流保存成功")
    return workflow_id

def verify_endpoint_data(workflow_id):
    """验证端点数据"""
    print(f"\n🔍 验证端点数据...")
    
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 获取工作流失败: {response.status_code}")
        return False
    
    workflow = response.json()
    nodes = workflow.get("nodes", [])
    
    print(f"📊 端点配置验证:")
    for node in nodes:
        node_name = node.get('name', 'Unknown')
        custom_inputs = node.get('customInputs', [])
        custom_outputs = node.get('customOutputs', [])
        
        print(f"   节点: {node_name}")
        if custom_inputs:
            print(f"     自定义输入: {len(custom_inputs)}个")
            for inp in custom_inputs:
                print(f"       - {inp.get('name')} ({inp.get('id')})")
        if custom_outputs:
            print(f"     自定义输出: {len(custom_outputs)}个")
            for out in custom_outputs:
                print(f"       - {out.get('name')} ({out.get('id')})")
    
    return True

def main():
    print("🔗 灵活端点配置功能测试")
    print("="*50)
    
    try:
        # 1. 创建测试工作流
        workflow_id = create_flexible_endpoint_test()
        if not workflow_id:
            print("❌ 测试失败：无法创建工作流")
            return
        
        # 2. 验证端点数据
        if not verify_endpoint_data(workflow_id):
            print("❌ 测试失败：端点数据验证不通过")
            return
        
        print("\n" + "="*50)
        print("✅ 灵活端点配置功能测试准备完成!")
        print(f"📝 测试工作流ID: {workflow_id}")
        print(f"🔗 前端地址: http://localhost:5173")
        
        print("\n🎯 新功能特性:")
        print("   ✅ 自定义端点配置 - 节点可以定义自己的输入输出端点")
        print("   ✅ 端点管理界面 - 在配置面板中动态管理端点")
        print("   ✅ 灵活连线支持 - 支持任意自定义端点之间的连接")
        print("   ✅ 端点类型颜色 - 不同类型端点有不同颜色标识")
        print("   ✅ 位置自定义 - 端点可以放置在节点的任意边和位置")
        
        print("\n💡 前端测试指南:")
        print("   1. 打开前端页面并找到'灵活端点配置测试'工作流")
        print("   2. 点击'编辑'按钮进入编辑器")
        print("   3. 观察节点的自定义端点配置：")
        print("      - 处理器节点：底部3个不同颜色的输出端点")
        print("      - 合并器节点：左侧3个输入端点，右侧1个输出端点")
        print("   4. 双击节点打开配置面板")
        print("   5. 查看'端点配置'部分：")
        print("      - 可以添加新端点")
        print("      - 可以编辑现有端点")
        print("      - 可以删除端点")
        print("      - 可以调整端点位置和类型")
        print("   6. 测试连线功能：")
        print("      - 尝试连接不同的自定义端点")
        print("      - 观察连线颜色是否正确")
        print("      - 验证Handle错误是否已解决")
        
        print("\n🎉 如果所有功能都正常，说明灵活端点系统实现成功！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlowCustom.SDK;

namespace FlowCustom.Plugins.Core
{
    /// <summary>
    /// 手动触发器插件
    /// 提供手动触发工作流的功能
    /// </summary>
    public class ManualTriggerPlugin : NodePluginBase
    {
        public override string NodeType => "manual-trigger";
        public override string DisplayName => "手动触发器";
        public override string Description => "手动触发工作流执行";
        public override string Category => "触发器";
        public override string Author => "FlowCustom Team";
        public override string Version => "1.0.0";
        public override bool IsTrigger => true;
        public override string Icon => "🚀";

        /// <summary>
        /// 获取参数定义
        /// </summary>
        protected override List<ParameterDefinition> GetParameterDefinitions()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "buttonText",
                    DisplayName = "按钮文本",
                    Description = "触发按钮显示的文本",
                    Type = ParameterType.String,
                    Required = false,
                    DefaultValue = "开始执行"
                },
                new ParameterDefinition
                {
                    Name = "enabled",
                    DisplayName = "启用状态",
                    Description = "是否启用此触发器",
                    Type = ParameterType.Boolean,
                    Required = false,
                    DefaultValue = true
                },
                new ParameterDefinition
                {
                    Name = "description",
                    DisplayName = "触发器描述",
                    Description = "触发器的详细描述",
                    Type = ParameterType.String,
                    Required = false,
                    DefaultValue = ""
                }
            };
        }

        /// <summary>
        /// 获取输出端点定义
        /// </summary>
        protected override List<EndpointDefinition> GetOutputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "main",
                    Name = "触发输出",
                    Description = "触发器激活时的输出",
                    Type = EndpointType.Trigger,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 50 },
                    DataType = "trigger"
                }
            };
        }

        /// <summary>
        /// 执行触发器逻辑
        /// </summary>
        protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
        {
            var buttonText = GetParameter<string>(context, "buttonText", "开始执行");
            var enabled = GetParameter<bool>(context, "enabled", true);
            var description = GetParameter<string>(context, "description", "");

            Log($"手动触发器被激活: {buttonText}");

            if (!enabled)
            {
                Log("触发器已禁用，跳过执行", LogLevel.Warning);
                return NodeExecutionResult.Failure("触发器已禁用");
            }

            // 模拟触发器处理
            await Task.Delay(100, context.CancellationToken);

            var outputData = new Dictionary<string, object>
            {
                ["triggerTime"] = DateTime.UtcNow,
                ["triggerType"] = "manual",
                ["buttonText"] = buttonText,
                ["description"] = description,
                ["executionId"] = context.ExecutionId,
                ["workflowId"] = context.WorkflowId,
                ["triggeredBy"] = "manual",
                ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
            };

            Log($"手动触发器执行完成，输出数据: {outputData.Count} 项");

            return NodeExecutionResult.Success(outputData, "main");
        }

        /// <summary>
        /// 自定义验证逻辑
        /// </summary>
        protected override void OnValidate(NodeConfiguration configuration, 
            List<ValidationError> errors, List<ValidationWarning> warnings)
        {
            // 验证按钮文本
            if (configuration.Parameters.TryGetValue("buttonText", out var buttonTextObj))
            {
                var buttonText = buttonTextObj?.ToString() ?? "";
                if (string.IsNullOrWhiteSpace(buttonText))
                {
                    warnings.Add(new ValidationWarning
                    {
                        Field = "buttonText",
                        Message = "建议设置按钮文本以提供更好的用户体验",
                        Code = "EMPTY_BUTTON_TEXT"
                    });
                }
                else if (buttonText.Length > 50)
                {
                    errors.Add(new ValidationError
                    {
                        Field = "buttonText",
                        Message = "按钮文本长度不能超过50个字符",
                        Code = "BUTTON_TEXT_TOO_LONG"
                    });
                }
            }

            // 验证描述长度
            if (configuration.Parameters.TryGetValue("description", out var descObj))
            {
                var description = descObj?.ToString() ?? "";
                if (description.Length > 500)
                {
                    errors.Add(new ValidationError
                    {
                        Field = "description",
                        Message = "描述长度不能超过500个字符",
                        Code = "DESCRIPTION_TOO_LONG"
                    });
                }
            }
        }

        /// <summary>
        /// 获取前端组件信息
        /// </summary>
        public override FrontendComponentInfo GetFrontendComponent()
        {
            return new FrontendComponentInfo
            {
                ComponentName = "ManualTriggerConfig",
                ComponentPath = "components/ManualTriggerConfig.tsx",
                IconPath = "icons/manual-trigger.svg",
                LocalePaths = new Dictionary<string, string>
                {
                    ["zh-CN"] = "locales/zh-CN/manual-trigger.json",
                    ["en-US"] = "locales/en-US/manual-trigger.json"
                }
            };
        }

        /// <summary>
        /// 插件初始化
        /// </summary>
        protected override void OnInitialize()
        {
            Log("手动触发器插件初始化完成");
        }

        /// <summary>
        /// 资源清理
        /// </summary>
        protected override void OnDispose()
        {
            Log("手动触发器插件资源清理完成");
        }
    }
}

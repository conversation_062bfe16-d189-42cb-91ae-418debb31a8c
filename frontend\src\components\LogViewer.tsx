import React, { useState, useEffect, useRef } from 'react';

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'Info' | 'Warning' | 'Error' | 'Debug';
  message: string;
  source?: string;
  workflowId?: string;
  nodeId?: string;
  executionId?: string;
  details?: Record<string, unknown>;
}

interface LogViewerProps {
  workflowId?: string;
  executionId?: string;
  autoRefresh?: boolean;
  maxLines?: number;
}

const LogViewer: React.FC<LogViewerProps> = ({
  workflowId,
  executionId,
  autoRefresh = true,
  maxLines = 1000,
}) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('');
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [autoScroll, setAutoScroll] = useState(true);
  const logContainerRef = useRef<HTMLDivElement>(null);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let url = '/api/logs';
      const params = new URLSearchParams();
      
      if (workflowId) params.append('workflowId', workflowId);
      if (executionId) params.append('executionId', executionId);
      if (levelFilter !== 'all') params.append('level', levelFilter);
      params.append('limit', maxLines.toString());
      
      if (params.toString()) {
        url += '?' + params.toString();
      }

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`获取日志失败: ${response.statusText}`);
      }
      
      const newLogs = await response.json();
      setLogs(newLogs);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取日志失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [workflowId, executionId, levelFilter]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchLogs, 2000); // 每2秒刷新一次
    return () => clearInterval(interval);
  }, [autoRefresh, workflowId, executionId, levelFilter]);

  useEffect(() => {
    if (autoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs, autoScroll]);

  const filteredLogs = logs.filter(log => {
    if (filter && !log.message.toLowerCase().includes(filter.toLowerCase())) {
      return false;
    }
    return true;
  });

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Error': return 'text-red-600 bg-red-50';
      case 'Warning': return 'text-yellow-600 bg-yellow-50';
      case 'Info': return 'text-blue-600 bg-blue-50';
      case 'Debug': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'Error': return '❌';
      case 'Warning': return '⚠️';
      case 'Info': return 'ℹ️';
      case 'Debug': return '🔍';
      default: return '📝';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3,
    });
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="flex flex-col h-full bg-white border border-gray-200 rounded-lg shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-gray-800 flex items-center">
            <span className="mr-2">📋</span>
            运行日志
          </h3>
          {workflowId && (
            <span className="text-sm text-gray-600">
              工作流: {workflowId.substring(0, 8)}...
            </span>
          )}
          {executionId && (
            <span className="text-sm text-gray-600">
              执行: {executionId.substring(0, 8)}...
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={levelFilter}
            onChange={(e) => setLevelFilter(e.target.value)}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">所有级别</option>
            <option value="Error">错误</option>
            <option value="Warning">警告</option>
            <option value="Info">信息</option>
            <option value="Debug">调试</option>
          </select>
          
          <label className="flex items-center text-sm text-gray-600">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-1"
            />
            自动刷新
          </label>
          
          <label className="flex items-center text-sm text-gray-600">
            <input
              type="checkbox"
              checked={autoScroll}
              onChange={(e) => setAutoScroll(e.target.checked)}
              className="mr-1"
            />
            自动滚动
          </label>
          
          <button
            onClick={fetchLogs}
            disabled={loading}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? '刷新中...' : '刷新'}
          </button>
          
          <button
            onClick={clearLogs}
            className="px-3 py-1 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            清空
          </button>
        </div>
      </div>

      {/* Filter */}
      <div className="p-3 border-b border-gray-200">
        <input
          type="text"
          placeholder="搜索日志内容..."
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border-b border-red-200">
          <p className="text-red-600 text-sm">❌ {error}</p>
        </div>
      )}

      {/* Log Content */}
      <div
        ref={logContainerRef}
        className="flex-1 overflow-y-auto p-2 space-y-1 font-mono text-sm"
        style={{ maxHeight: '400px' }}
      >
        {filteredLogs.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            {loading ? '加载日志中...' : '暂无日志数据'}
          </div>
        ) : (
          filteredLogs.map((log) => (
            <div
              key={log.id}
              className={`p-2 rounded border-l-4 ${getLevelColor(log.level)} border-l-current`}
            >
              <div className="flex items-start space-x-2">
                <span className="text-lg">{getLevelIcon(log.level)}</span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 text-xs text-gray-500 mb-1">
                    <span>{formatTimestamp(log.timestamp)}</span>
                    <span className={`px-2 py-0.5 rounded text-xs font-medium ${getLevelColor(log.level)}`}>
                      {log.level}
                    </span>
                    {log.source && <span>[{log.source}]</span>}
                    {log.nodeId && <span>节点: {log.nodeId.substring(0, 8)}...</span>}
                  </div>
                  <div className="text-gray-800 break-words">
                    {log.message}
                  </div>
                  {log.details && Object.keys(log.details).length > 0 && (
                    <details className="mt-1">
                      <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                        详细信息
                      </summary>
                      <pre className="mt-1 text-xs text-gray-600 bg-gray-100 p-2 rounded overflow-x-auto">
                        {JSON.stringify(log.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Footer */}
      <div className="p-2 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>共 {filteredLogs.length} 条日志</span>
          <span>最后更新: {new Date().toLocaleTimeString('zh-CN')}</span>
        </div>
      </div>
    </div>
  );
};

export default LogViewer;

# FlowCustomV1 API 测试脚本
param(
    [string]$BaseUrl = "http://localhost:5279",
    [switch]$Verbose = $false
)

Write-Host "🧪 FlowCustomV1 API 测试脚本" -ForegroundColor Green
Write-Host "=" * 50

$ErrorActionPreference = "Continue"

# 设置详细输出
if ($Verbose) {
    $VerbosePreference = "Continue"
}

Write-Host "🌐 测试基础URL: $BaseUrl" -ForegroundColor Cyan

# 测试健康检查端点
Write-Host "🔍 测试健康检查端点..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/health" -Method Get -ErrorAction Stop
    Write-Host "✅ 健康检查成功" -ForegroundColor Green
    Write-Host "   状态: $($response.Status)" -ForegroundColor Gray
    Write-Host "   版本: $($response.Version)" -ForegroundColor Gray
    Write-Host "   环境: $($response.Environment)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试系统信息端点
Write-Host "🔍 测试系统信息端点..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/system/info" -Method Get -ErrorAction Stop
    Write-Host "✅ 系统信息获取成功" -ForegroundColor Green
    Write-Host "   系统名称: $($response.System.Name)" -ForegroundColor Gray
    Write-Host "   系统版本: $($response.System.Version)" -ForegroundColor Gray
    Write-Host "   插件数量: $($response.Statistics.TotalPlugins)" -ForegroundColor Gray
    Write-Host "   触发器插件: $($response.Statistics.TriggerPlugins)" -ForegroundColor Gray
    Write-Host "   动作插件: $($response.Statistics.ActionPlugins)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 系统信息获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试插件列表端点
Write-Host "🔍 测试插件列表端点..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/plugins" -Method Get -ErrorAction Stop
    Write-Host "✅ 插件列表获取成功" -ForegroundColor Green
    Write-Host "   插件数量: $($response.Count)" -ForegroundColor Gray
    
    foreach ($plugin in $response) {
        Write-Host "   🔌 $($plugin.displayName) ($($plugin.nodeType))" -ForegroundColor Gray
        Write-Host "      分类: $($plugin.category)" -ForegroundColor DarkGray
        Write-Host "      版本: $($plugin.version)" -ForegroundColor DarkGray
        Write-Host "      作者: $($plugin.author)" -ForegroundColor DarkGray
        Write-Host "      触发器: $(if ($plugin.isTrigger) { '是' } else { '否' })" -ForegroundColor DarkGray
    }
} catch {
    Write-Host "❌ 插件列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试插件分类端点
Write-Host "🔍 测试插件分类端点..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/plugins/categories" -Method Get -ErrorAction Stop
    Write-Host "✅ 插件分类获取成功" -ForegroundColor Green
    Write-Host "   分类数量: $($response.Count)" -ForegroundColor Gray
    
    foreach ($category in $response) {
        Write-Host "   📁 $($category.category) ($($category.count) 个插件)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 插件分类获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试Swagger文档端点
Write-Host "🔍 测试Swagger文档端点..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/swagger" -Method Get -UseBasicParsing -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Swagger文档可访问" -ForegroundColor Green
        Write-Host "   状态码: $($response.StatusCode)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ Swagger文档访问失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 API测试完成!" -ForegroundColor Green
Write-Host "💡 提示:"
Write-Host "   - 访问Swagger文档: $BaseUrl/swagger"
Write-Host "   - 查看健康状态: $BaseUrl/health"
Write-Host "   - 查看系统信息: $BaseUrl/api/system/info"
Write-Host "   - 查看插件列表: $BaseUrl/api/plugins"

Write-Host ""
Write-Host "⏱️  测试完成时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray

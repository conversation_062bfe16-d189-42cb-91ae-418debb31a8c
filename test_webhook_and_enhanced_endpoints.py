#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Webhook节点和增强端点配置功能测试脚本
验证新的插件化架构和灵活端点配置
"""

import requests
import json
import uuid
import time

# API 基础URL
BASE_URL = "http://localhost:5053/api"

def create_webhook_test_workflow():
    """创建Webhook测试工作流"""
    print("🌐 创建Webhook节点测试工作流...")
    
    # 1. 创建工作流
    workflow_data = {
        "name": "Webhook节点测试",
        "description": "测试新的Webhook触发器节点和增强的端点配置功能"
    }
    
    response = requests.post(f"{BASE_URL}/workflow", json=workflow_data)
    if response.status_code != 201:
        print(f"❌ 工作流创建失败: {response.status_code}")
        return None
    
    workflow = response.json()
    workflow_id = workflow["id"]
    print(f"✅ 工作流创建成功: {workflow_id}")
    
    # 2. 创建节点 - 使用新的Webhook触发器
    webhook_id = str(uuid.uuid4())
    processor_id = str(uuid.uuid4())
    response_id = str(uuid.uuid4())
    
    nodes = [
        # Webhook触发器节点
        {
            "id": webhook_id,
            "workflowId": workflow_id,
            "name": "🌐 Webhook接收器",
            "type": "webhook-trigger",
            "position": {"x": 100, "y": 200},
            "parameters": {
                "path": "/webhook/test-trigger",
                "methods": ["POST", "PUT"],
                "authentication": "token",
                "authToken": "test-secret-token-123",
                "responseMode": "immediate",
                "responseBody": "{\"status\": \"received\", \"message\": \"Webhook处理成功\"}",
                "maxPayloadSize": 2048,
                "enableCors": True,
                "corsOrigins": "*"
            },
            "isDisabled": False,
            "notes": "接收外部系统的Webhook请求",
            # 自定义输出端点
            "customOutputs": [
                {
                    "id": "webhook_data",
                    "name": "Webhook数据",
                    "description": "接收到的Webhook请求数据",
                    "type": "data",
                    "dataType": "object",
                    "required": False,
                    "position": {"side": "bottom", "offset": 30}
                },
                {
                    "id": "webhook_headers",
                    "name": "请求头",
                    "description": "HTTP请求头信息",
                    "type": "data",
                    "dataType": "object",
                    "required": False,
                    "position": {"side": "bottom", "offset": 50}
                },
                {
                    "id": "webhook_metadata",
                    "name": "元数据",
                    "description": "请求的元数据信息",
                    "type": "data",
                    "dataType": "object",
                    "required": False,
                    "position": {"side": "bottom", "offset": 70}
                }
            ]
        },
        
        # 数据处理器 - 多输入多输出
        {
            "id": processor_id,
            "workflowId": workflow_id,
            "name": "🔄 数据处理器",
            "type": "script-executor",
            "position": {"x": 400, "y": 200},
            "parameters": {
                "scriptType": "python",
                "script": """
# 处理Webhook数据
import json

# 获取输入数据
webhook_data = input_data.get('webhook_data', {})
headers = input_data.get('headers', {})
metadata = input_data.get('metadata', {})

print(f"处理Webhook数据: {len(webhook_data)} 项")
print(f"请求头: {len(headers)} 项")
print(f"元数据: {len(metadata)} 项")

# 处理逻辑
processed_data = {
    'original_data': webhook_data,
    'processed_at': '2024-01-01T00:00:00Z',
    'data_size': len(str(webhook_data)),
    'has_headers': len(headers) > 0
}

# 生成不同类型的输出
result = {
    'processed_data': processed_data,
    'summary': f"处理了 {len(webhook_data)} 项数据",
    'status': 'success'
}
""",
                "timeout": 30
            },
            "isDisabled": False,
            "notes": "处理接收到的Webhook数据",
            # 自定义输入端点
            "customInputs": [
                {
                    "id": "webhook_data",
                    "name": "Webhook数据",
                    "description": "来自Webhook的数据",
                    "type": "data",
                    "dataType": "object",
                    "required": True,
                    "position": {"side": "top", "offset": 30}
                },
                {
                    "id": "headers",
                    "name": "请求头",
                    "description": "HTTP请求头",
                    "type": "data",
                    "dataType": "object",
                    "required": False,
                    "position": {"side": "left", "offset": 30}
                },
                {
                    "id": "metadata",
                    "name": "元数据",
                    "description": "请求元数据",
                    "type": "data",
                    "dataType": "object",
                    "required": False,
                    "position": {"side": "left", "offset": 70}
                }
            ],
            # 自定义输出端点
            "customOutputs": [
                {
                    "id": "processed_data",
                    "name": "处理结果",
                    "description": "处理后的数据",
                    "type": "success",
                    "dataType": "object",
                    "required": False,
                    "position": {"side": "bottom", "offset": 25}
                },
                {
                    "id": "summary",
                    "name": "处理摘要",
                    "description": "处理结果摘要",
                    "type": "text",
                    "dataType": "string",
                    "required": False,
                    "position": {"side": "bottom", "offset": 50}
                },
                {
                    "id": "status",
                    "name": "状态信息",
                    "description": "处理状态",
                    "type": "data",
                    "dataType": "string",
                    "required": False,
                    "position": {"side": "bottom", "offset": 75}
                }
            ]
        },
        
        # 响应生成器
        {
            "id": response_id,
            "workflowId": workflow_id,
            "name": "📤 响应生成器",
            "type": "script-executor",
            "position": {"x": 700, "y": 200},
            "parameters": {
                "scriptType": "javascript",
                "script": """
// 生成响应数据
const processedData = input_data.processed_data || {};
const summary = input_data.summary || '';
const status = input_data.status || 'unknown';

console.log('生成响应:', summary);

const response = {
    success: status === 'success',
    message: summary,
    timestamp: new Date().toISOString(),
    data: processedData
};

console.log('响应生成完成');
""",
                "timeout": 10
            },
            "isDisabled": False,
            "notes": "生成最终响应",
            # 自定义输入端点 - 灵活的数据类型
            "customInputs": [
                {
                    "id": "processed_data",
                    "name": "处理结果",
                    "description": "来自处理器的结果",
                    "type": "success",
                    "dataType": "object",
                    "required": True,
                    "position": {"side": "top", "offset": 25}
                },
                {
                    "id": "summary",
                    "name": "摘要信息",
                    "description": "处理摘要文本",
                    "type": "text",
                    "dataType": "string",
                    "required": False,
                    "position": {"side": "top", "offset": 50}
                },
                {
                    "id": "status",
                    "name": "状态",
                    "description": "处理状态",
                    "type": "data",
                    "dataType": "string",
                    "required": False,
                    "position": {"side": "top", "offset": 75}
                }
            ]
        }
    ]
    
    # 3. 创建连线 - 使用自定义端点
    connections = [
        # Webhook数据 -> 处理器
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": webhook_id,
            "targetNodeId": processor_id,
            "sourceOutput": "webhook_data",
            "targetInput": "webhook_data"
        },
        # Webhook请求头 -> 处理器
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": webhook_id,
            "targetNodeId": processor_id,
            "sourceOutput": "webhook_headers",
            "targetInput": "headers"
        },
        # Webhook元数据 -> 处理器
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": webhook_id,
            "targetNodeId": processor_id,
            "sourceOutput": "webhook_metadata",
            "targetInput": "metadata"
        },
        # 处理结果 -> 响应生成器
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": processor_id,
            "targetNodeId": response_id,
            "sourceOutput": "processed_data",
            "targetInput": "processed_data"
        },
        # 处理摘要 -> 响应生成器
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": processor_id,
            "targetNodeId": response_id,
            "sourceOutput": "summary",
            "targetInput": "summary"
        },
        # 状态信息 -> 响应生成器
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": processor_id,
            "targetNodeId": response_id,
            "sourceOutput": "status",
            "targetInput": "status"
        }
    ]
    
    print(f"📦 创建了 {len(nodes)} 个节点和 {len(connections)} 条连线")
    print(f"🔗 端点配置特性:")
    print(f"   ✅ Webhook触发器: 3个自定义输出端点")
    print(f"   ✅ 数据处理器: 3个自定义输入 + 3个自定义输出端点")
    print(f"   ✅ 响应生成器: 3个自定义输入端点")
    print(f"   ✅ 多种数据类型: object, string, success等")
    print(f"   ✅ 灵活端点位置: top, bottom, left等不同位置")
    
    # 4. 保存工作流
    workflow["nodes"] = nodes
    workflow["connections"] = connections
    workflow["isActive"] = True
    
    response = requests.put(f"{BASE_URL}/workflow/{workflow_id}", json=workflow)
    if response.status_code != 200:
        print(f"❌ 工作流保存失败: {response.status_code}")
        print(f"   响应: {response.text}")
        return None
    
    print(f"✅ 工作流保存成功")
    return workflow_id

def verify_enhanced_features(workflow_id):
    """验证增强功能"""
    print(f"\n🔍 验证增强功能...")
    
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 获取工作流失败: {response.status_code}")
        return False
    
    workflow = response.json()
    nodes = workflow.get("nodes", [])
    connections = workflow.get("connections", [])
    
    print(f"📊 增强功能验证:")
    
    # 验证自定义端点
    total_custom_inputs = 0
    total_custom_outputs = 0
    
    for node in nodes:
        node_name = node.get('name', 'Unknown')
        custom_inputs = node.get('customInputs', [])
        custom_outputs = node.get('customOutputs', [])
        
        if custom_inputs or custom_outputs:
            print(f"   节点: {node_name}")
            if custom_inputs:
                total_custom_inputs += len(custom_inputs)
                print(f"     自定义输入: {len(custom_inputs)}个")
                for inp in custom_inputs:
                    print(f"       - {inp.get('name')} ({inp.get('dataType', 'any')})")
            if custom_outputs:
                total_custom_outputs += len(custom_outputs)
                print(f"     自定义输出: {len(custom_outputs)}个")
                for out in custom_outputs:
                    print(f"       - {out.get('name')} ({out.get('dataType', 'any')})")
    
    print(f"   总计自定义输入端点: {total_custom_inputs}个")
    print(f"   总计自定义输出端点: {total_custom_outputs}个")
    print(f"   连线数量: {len(connections)}条")
    
    # 验证数据类型多样性
    data_types = set()
    endpoint_types = set()
    
    for node in nodes:
        for endpoints in [node.get('customInputs', []), node.get('customOutputs', [])]:
            for endpoint in endpoints:
                data_types.add(endpoint.get('dataType', 'any'))
                endpoint_types.add(endpoint.get('type', 'data'))
    
    print(f"   数据类型种类: {len(data_types)}种 - {', '.join(sorted(data_types))}")
    print(f"   端点类型种类: {len(endpoint_types)}种 - {', '.join(sorted(endpoint_types))}")
    
    return True

def main():
    print("🌐 Webhook节点和增强端点配置功能测试")
    print("="*60)
    
    try:
        # 1. 创建测试工作流
        workflow_id = create_webhook_test_workflow()
        if not workflow_id:
            print("❌ 测试失败：无法创建工作流")
            return
        
        # 2. 验证增强功能
        if not verify_enhanced_features(workflow_id):
            print("❌ 测试失败：功能验证不通过")
            return
        
        print("\n" + "="*60)
        print("✅ Webhook节点和增强端点配置功能测试完成!")
        print(f"📝 测试工作流ID: {workflow_id}")
        print(f"🔗 前端地址: http://localhost:5173")
        
        print("\n🎯 新功能特性:")
        print("   ✅ Webhook触发器节点 - 支持HTTP请求触发")
        print("   ✅ 灵活端点配置 - 自定义输入输出端点")
        print("   ✅ 多种数据类型 - object, string, success等")
        print("   ✅ 端点位置配置 - top, bottom, left, right")
        print("   ✅ 端点属性配置 - 名称、描述、类型、必需性")
        print("   ✅ 端点模板系统 - 快速创建常用端点")
        
        print("\n💡 前端测试指南:")
        print("   1. 打开前端页面并找到'Webhook节点测试'工作流")
        print("   2. 点击'编辑'按钮进入编辑器")
        print("   3. 观察新的Webhook触发器节点")
        print("   4. 双击节点查看端点配置功能：")
        print("      - 端点管理界面")
        print("      - 端点模板选择")
        print("      - 数据类型配置")
        print("      - 位置和属性设置")
        print("   5. 验证连线功能：")
        print("      - 不同类型端点的颜色")
        print("      - 自定义端点连接")
        print("      - 端点位置显示")
        
        print("\n🌐 Webhook测试:")
        print("   Webhook URL: http://localhost:5053/webhook/test-trigger")
        print("   支持方法: POST, PUT")
        print("   认证Token: test-secret-token-123")
        print("   测试命令:")
        print("   curl -X POST http://localhost:5053/webhook/test-trigger \\")
        print("        -H 'Authorization: Bearer test-secret-token-123' \\")
        print("        -H 'Content-Type: application/json' \\")
        print("        -d '{\"test\": \"data\", \"message\": \"Hello Webhook\"}'")
        
        print("\n🎉 如果所有功能都正常，说明插件化架构和增强端点配置成功！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

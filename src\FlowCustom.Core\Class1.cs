﻿namespace FlowCustom.Core.Models;

/// <summary>
/// 工作流定义
/// </summary>
public class Workflow
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 工作流节点列表
    /// </summary>
    public List<WorkflowNode> Nodes { get; set; } = new();

    /// <summary>
    /// 节点间连接关系
    /// </summary>
    public List<NodeConnection> Connections { get; set; } = new();

    /// <summary>
    /// 工作流设置
    /// </summary>
    public WorkflowSettings Settings { get; set; } = new();
}

/// <summary>
/// 工作流节点
/// </summary>
public class WorkflowNode
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid WorkflowId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public NodePosition Position { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool IsDisabled { get; set; } = false;
    public string Notes { get; set; } = string.Empty;
}

/// <summary>
/// 节点连接
/// </summary>
public class NodeConnection
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid WorkflowId { get; set; }
    public Guid SourceNodeId { get; set; }
    public Guid TargetNodeId { get; set; }
    public string SourceOutput { get; set; } = "main";
    public string TargetInput { get; set; } = "main";
}

/// <summary>
/// 节点位置信息
/// </summary>
public class NodePosition
{
    public double X { get; set; }
    public double Y { get; set; }
}

/// <summary>
/// 工作流设置
/// </summary>
public class WorkflowSettings
{
    public TimeSpan? Timeout { get; set; }
    public int MaxRetries { get; set; } = 0;
    public bool SaveExecutionProgress { get; set; } = true;
    public Dictionary<string, object> Variables { get; set; } = new();
}

using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Nodes;

/// <summary>
/// 定时任务触发器节点
/// </summary>
public class CronTriggerNode : ITriggerNode
{
    private readonly ILogger<CronTriggerNode> _logger;
    private static readonly ConcurrentDictionary<Guid, Timer> _activeTimers = new();

    public CronTriggerNode(ILogger<CronTriggerNode> logger)
    {
        _logger = logger;
    }

    public string NodeType => "cron-trigger";
    public string DisplayName => "定时触发器";
    public string Description => "使用cron表达式按计划触发工作流";
    public string Category => "触发器";
    public string Icon => "clock";

    public event EventHandler<TriggerEventArgs>? Triggered;

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "cronExpression",
                DisplayName = "Cron表达式",
                Description = "用于调度的Cron表达式（例如：'0 */5 * * * *' 表示每5分钟）",
                Type = ParameterType.String,
                Required = true,
                DefaultValue = "0 */5 * * * *"
            },
            new NodeParameterDefinition
            {
                Name = "timezone",
                DisplayName = "时区",
                Description = "调度的时区",
                Type = ParameterType.Select,
                Required = false,
                DefaultValue = "UTC",
                Options = new[] { "UTC", "Local", "America/New_York", "Europe/London", "Asia/Shanghai" }
            },
            new NodeParameterDefinition
            {
                Name = "enabled",
                DisplayName = "启用",
                Description = "是否启用此触发器",
                Type = ParameterType.Boolean,
                Required = false,
                DefaultValue = true
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node, 
        FlowCustom.Core.Models.ExecutionContext context, 
        CancellationToken cancellationToken = default)
    {
        // 触发器节点不直接执行，而是在触发时提供数据
        await Task.CompletedTask;
        return NodeExecutionResult.CreateSuccess(new Dictionary<string, object>
        {
            ["message"] = "Cron trigger is active",
            ["timestamp"] = DateTime.UtcNow,
            ["cronExpression"] = GetParameter<string>(node, "cronExpression", "0 */5 * * * *")
        });
    }

    public async Task StartAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        var cronExpression = GetParameter<string>(node, "cronExpression", "0 */5 * * * *");
        var enabled = GetParameter<bool>(node, "enabled", true);

        if (!enabled)
        {
            _logger.LogInformation("Cron trigger is disabled for node: {NodeId}", node.Id);
            return;
        }

        try
        {
            // 简化的 cron 解析 - 这里使用固定间隔作为示例
            var interval = ParseCronToInterval(cronExpression);
            
            var timer = new Timer(async _ =>
            {
                try
                {
                    var triggerArgs = new TriggerEventArgs
                    {
                        WorkflowId = node.WorkflowId,
                        NodeId = node.Id,
                        Data = new Dictionary<string, object>
                        {
                            ["timestamp"] = DateTime.UtcNow,
                            ["cronExpression"] = cronExpression,
                            ["triggeredBy"] = "cron"
                        },
                        Source = "cron"
                    };

                    Triggered?.Invoke(this, triggerArgs);
                    _logger.LogInformation("Cron trigger fired for node: {NodeId}", node.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error firing cron trigger for node: {NodeId}", node.Id);
                }
            }, null, interval, interval);

            _activeTimers[node.Id] = timer;
            _logger.LogInformation("Started cron trigger: {CronExpression} for node: {NodeId}", 
                cronExpression, node.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start cron trigger for node: {NodeId}", node.Id);
            throw;
        }

        await Task.CompletedTask;
    }

    public async Task StopAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        var nodeId = node.Id;
        
        if (_activeTimers.TryRemove(nodeId, out var timer))
        {
            timer.Dispose();
            _logger.LogInformation("Stopped cron trigger for node: {NodeId}", node.Id);
        }

        await Task.CompletedTask;
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.ContainsKey("cronExpression") || 
            string.IsNullOrEmpty(parameters["cronExpression"]?.ToString()))
        {
            errors.Add("Cron expression is required");
        }
        else
        {
            var cronExpression = parameters["cronExpression"].ToString();
            if (!IsValidCronExpression(cronExpression))
            {
                errors.Add("Invalid cron expression format");
            }
        }

        return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    private TimeSpan ParseCronToInterval(string cronExpression)
    {
        // 简化的 cron 解析 - 实际项目中应该使用专业的 cron 库如 NCrontab
        // 这里只是示例实现
        var parts = cronExpression.Split(' ');
        if (parts.Length >= 6)
        {
            // 假设格式是: 秒 分 时 日 月 年
            if (parts[1] == "*/5") return TimeSpan.FromMinutes(5);
            if (parts[1] == "*/10") return TimeSpan.FromMinutes(10);
            if (parts[1] == "*/15") return TimeSpan.FromMinutes(15);
            if (parts[1] == "*/30") return TimeSpan.FromMinutes(30);
            if (parts[2] == "*/1") return TimeSpan.FromHours(1);
        }
        
        // 默认每5分钟
        return TimeSpan.FromMinutes(5);
    }

    private bool IsValidCronExpression(string? cronExpression)
    {
        if (string.IsNullOrEmpty(cronExpression))
            return false;

        var parts = cronExpression.Split(' ');
        return parts.Length >= 5; // 简化验证
    }
}

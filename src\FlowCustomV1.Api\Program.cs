using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Engine.Services;
using FlowCustomV1.PluginHost.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;

// 设置控制台编码为UTF-8
Console.OutputEncoding = Encoding.UTF8;
Console.InputEncoding = Encoding.UTF8;

// 配置Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/flowcustomv1-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

var builder = WebApplication.CreateBuilder(args);

// 使用Serilog
builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        options.JsonSerializerOptions.WriteIndented = true;
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
    });

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { 
        Title = "FlowCustomV1 API", 
        Version = "v1.0.0",
        Description = "下一代插件化工作流自动化平台 API",
        Contact = new() { Name = "FlowCustom Team", Email = "<EMAIL>" }
    });
    
    // 包含XML注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 注册插件管理器
var pluginDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plugins");
builder.Services.AddSingleton<IPluginManager>(serviceProvider =>
{
    var logger = serviceProvider.GetRequiredService<ILogger<PluginManager>>();
    return new PluginManager(logger, serviceProvider, pluginDirectory);
});

// 注册工作流执行引擎
builder.Services.AddSingleton<IWorkflowEngine, WorkflowEngine>();

// 注册HTTP客户端
builder.Services.AddHttpClient();

// Add JWT Authentication
var jwtSecret = builder.Configuration["JWT:Secret"] ?? "FlowCustomV1JwtSecretKey123456789";
var key = Encoding.ASCII.GetBytes(jwtSecret);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddAuthorization();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "FlowCustomV1 API v1.0.0");
        c.RoutePrefix = "swagger";
        c.DocumentTitle = "FlowCustomV1 API Documentation";
    });
}

// app.UseHttpsRedirection(); // 开发环境禁用HTTPS重定向
app.UseCors("AllowAll");

// 使用Serilog请求日志
app.UseSerilogRequestLogging();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// 健康检查端点
app.MapGet("/health", () => new
{
    Status = "Healthy",
    Timestamp = DateTime.UtcNow,
    Version = "1.0.0",
    Architecture = "Plugin-based",
    Environment = app.Environment.EnvironmentName
});

// 系统信息端点
app.MapGet("/api/system/info", (IPluginManager pluginManager) =>
{
    var plugins = pluginManager.GetAllPlugins();
    return new
    {
        System = new
        {
            Name = "FlowCustomV1",
            Version = "1.0.0",
            Architecture = "Plugin-based",
            Environment = app.Environment.EnvironmentName,
            StartTime = DateTime.UtcNow,
            DotNetVersion = Environment.Version.ToString(),
            MachineName = Environment.MachineName,
            ProcessorCount = Environment.ProcessorCount
        },
        Plugins = plugins.Select(p => new
        {
            p.NodeType,
            p.DisplayName,
            p.Description,
            p.Category,
            p.Version,
            p.Author,
            p.IsTrigger,
            p.Icon
        }).ToList(),
        Statistics = new
        {
            TotalPlugins = plugins.Count(),
            TriggerPlugins = plugins.Count(p => p.IsTrigger),
            ActionPlugins = plugins.Count(p => !p.IsTrigger),
            Categories = plugins.GroupBy(p => p.Category).Select(g => new { Category = g.Key, Count = g.Count() }).ToList()
        }
    };
});

// 初始化插件系统
Task.Run(async () =>
{
    try
    {
        var logger = app.Services.GetRequiredService<ILogger<Program>>();
        logger.LogInformation("🚀 FlowCustomV1 启动中...");
        logger.LogInformation("📁 插件目录: {PluginDirectory}", pluginDirectory);
        
        var pluginManager = app.Services.GetRequiredService<IPluginManager>();
        var loadedCount = await pluginManager.LoadPluginsAsync();
        
        logger.LogInformation("✅ 插件加载完成，共加载 {LoadedCount} 个插件", loadedCount);
        
        // 记录加载的插件
        var plugins = pluginManager.GetAllPlugins();
        foreach (var plugin in plugins)
        {
            logger.LogInformation("🔌 已加载插件: {DisplayName} ({NodeType}) v{Version} by {Author}", 
                plugin.DisplayName, plugin.NodeType, plugin.Version, plugin.Author);
        }
        
        logger.LogInformation("🎉 FlowCustomV1 启动完成!");
        logger.LogInformation("🌐 API地址: http://localhost:5000");
        logger.LogInformation("📚 API文档: http://localhost:5000/swagger");
    }
    catch (Exception ex)
    {
        var logger = app.Services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "❌ 插件加载失败");
    }
});

// 优雅关闭
var lifetime = app.Services.GetRequiredService<IHostApplicationLifetime>();
lifetime.ApplicationStopping.Register(() =>
{
    Log.Information("🛑 FlowCustomV1 正在关闭...");
});

lifetime.ApplicationStopped.Register(() =>
{
    Log.Information("✅ FlowCustomV1 已关闭");
    Log.CloseAndFlush();
});

Console.WriteLine("🚀 FlowCustomV1 - 下一代插件化工作流自动化平台");
Console.WriteLine("📖 访问 http://localhost:5000/swagger 查看API文档");

app.Run();

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using System.Text.Json;
using System.Threading.Tasks;
using FlowCustom.SDK;
using Microsoft.Extensions.Logging;

namespace FlowCustom.PluginHost
{
    /// <summary>
    /// 插件管理器实现
    /// </summary>
    public class PluginManager : IPluginManager, IDisposable
    {
        private readonly ILogger<PluginManager> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly string _pluginDirectory;
        private readonly ConcurrentDictionary<string, PluginContainer> _loadedPlugins;
        private readonly ConcurrentDictionary<string, PluginMetadata> _pluginMetadata;
        private readonly object _lockObject = new();

        public event EventHandler<PluginStatusChangedEventArgs>? PluginStatusChanged;

        public PluginManager(ILogger<PluginManager> logger, IServiceProvider serviceProvider, string? pluginDirectory = null)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _pluginDirectory = pluginDirectory ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plugins");
            _loadedPlugins = new ConcurrentDictionary<string, PluginContainer>();
            _pluginMetadata = new ConcurrentDictionary<string, PluginMetadata>();

            // 确保插件目录存在
            Directory.CreateDirectory(_pluginDirectory);
        }

        /// <summary>
        /// 加载所有插件
        /// </summary>
        public async Task<int> LoadPluginsAsync()
        {
            _logger.LogInformation("开始加载插件...");
            
            var loadedCount = 0;
            var pluginDirectories = Directory.GetDirectories(_pluginDirectory);

            foreach (var pluginDir in pluginDirectories)
            {
                try
                {
                    var manifestPath = Path.Combine(pluginDir, "plugin.json");
                    if (!File.Exists(manifestPath))
                    {
                        _logger.LogWarning($"插件目录 {pluginDir} 缺少 plugin.json 文件");
                        continue;
                    }

                    var metadata = await LoadPluginMetadataAsync(manifestPath);
                    if (metadata == null)
                    {
                        continue;
                    }

                    metadata.PluginPath = pluginDir;
                    _pluginMetadata[metadata.Id] = metadata;

                    if (await LoadPluginAsync(metadata))
                    {
                        loadedCount++;
                        _logger.LogInformation($"成功加载插件: {metadata.Name} v{metadata.Version}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"加载插件失败: {pluginDir}");
                }
            }

            _logger.LogInformation($"插件加载完成，共加载 {loadedCount} 个插件");
            return loadedCount;
        }

        /// <summary>
        /// 加载插件元数据
        /// </summary>
        private async Task<PluginMetadata?> LoadPluginMetadataAsync(string manifestPath)
        {
            try
            {
                var jsonContent = await File.ReadAllTextAsync(manifestPath);
                var metadata = JsonSerializer.Deserialize<PluginMetadata>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return metadata;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解析插件清单文件失败: {manifestPath}");
                return null;
            }
        }

        /// <summary>
        /// 加载单个插件
        /// </summary>
        private async Task<bool> LoadPluginAsync(PluginMetadata metadata)
        {
            try
            {
                var assemblyPath = Path.Combine(metadata.PluginPath, metadata.MainAssembly);
                if (!File.Exists(assemblyPath))
                {
                    _logger.LogError($"插件程序集文件不存在: {assemblyPath}");
                    return false;
                }

                // 创建插件加载上下文
                var loadContext = new PluginLoadContext(assemblyPath);
                var assembly = loadContext.LoadFromAssemblyPath(assemblyPath);

                // 查找插件类型
                var pluginTypes = assembly.GetTypes()
                    .Where(t => typeof(INodePlugin).IsAssignableFrom(t) && !t.IsAbstract)
                    .ToList();

                if (!pluginTypes.Any())
                {
                    _logger.LogWarning($"插件程序集中未找到 INodePlugin 实现: {assemblyPath}");
                    return false;
                }

                var container = new PluginContainer
                {
                    Metadata = metadata,
                    LoadContext = loadContext,
                    Assembly = assembly,
                    Plugins = new Dictionary<string, INodePlugin>()
                };

                // 创建插件实例
                foreach (var pluginType in pluginTypes)
                {
                    try
                    {
                        var plugin = Activator.CreateInstance(pluginType) as INodePlugin;
                        if (plugin != null)
                        {
                            plugin.Initialize(_serviceProvider);
                            container.Plugins[plugin.NodeType] = plugin;
                            
                            OnPluginStatusChanged(metadata.Id, PluginStatus.Unknown, PluginStatus.Loaded);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"创建插件实例失败: {pluginType.FullName}");
                    }
                }

                _loadedPlugins[metadata.Id] = container;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"加载插件失败: {metadata.Id}");
                OnPluginStatusChanged(metadata.Id, PluginStatus.Unknown, PluginStatus.LoadFailed);
                return false;
            }
        }

        /// <summary>
        /// 卸载所有插件
        /// </summary>
        public async Task UnloadPluginsAsync()
        {
            _logger.LogInformation("开始卸载所有插件...");

            var tasks = _loadedPlugins.Values.Select(container => Task.Run(() =>
            {
                try
                {
                    OnPluginStatusChanged(container.Metadata.Id, PluginStatus.Loaded, PluginStatus.Unloading);
                    
                    // 释放插件资源
                    foreach (var plugin in container.Plugins.Values)
                    {
                        plugin.Dispose();
                    }

                    // 卸载程序集
                    container.LoadContext?.Unload();
                    
                    _logger.LogInformation($"成功卸载插件: {container.Metadata.Name}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"卸载插件失败: {container.Metadata.Id}");
                }
            }));

            await Task.WhenAll(tasks);
            
            _loadedPlugins.Clear();
            _pluginMetadata.Clear();
            
            _logger.LogInformation("所有插件卸载完成");
        }

        /// <summary>
        /// 获取所有已加载的插件
        /// </summary>
        public IEnumerable<INodePlugin> GetAllPlugins()
        {
            return _loadedPlugins.Values
                .SelectMany(container => container.Plugins.Values)
                .ToList();
        }

        /// <summary>
        /// 根据节点类型获取插件
        /// </summary>
        public INodePlugin? GetPlugin(string nodeType)
        {
            foreach (var container in _loadedPlugins.Values)
            {
                if (container.Plugins.TryGetValue(nodeType, out var plugin))
                {
                    return plugin;
                }
            }
            return null;
        }

        /// <summary>
        /// 获取插件元数据
        /// </summary>
        public PluginMetadata? GetPluginMetadata(string nodeType)
        {
            foreach (var container in _loadedPlugins.Values)
            {
                if (container.Plugins.ContainsKey(nodeType))
                {
                    return container.Metadata;
                }
            }
            return null;
        }

        /// <summary>
        /// 安装插件
        /// </summary>
        public async Task<PluginInstallResult> InstallPluginAsync(string pluginPath)
        {
            try
            {
                // TODO: 实现插件安装逻辑
                // 1. 验证插件包
                // 2. 解压到插件目录
                // 3. 验证依赖
                // 4. 加载插件
                
                await Task.CompletedTask;
                return new PluginInstallResult { Success = false, ErrorMessage = "功能尚未实现" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"安装插件失败: {pluginPath}");
                return new PluginInstallResult { Success = false, ErrorMessage = ex.Message };
            }
        }

        /// <summary>
        /// 卸载插件
        /// </summary>
        public async Task<bool> UninstallPluginAsync(string pluginId)
        {
            try
            {
                // TODO: 实现插件卸载逻辑
                await Task.CompletedTask;
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"卸载插件失败: {pluginId}");
                return false;
            }
        }

        /// <summary>
        /// 启用插件
        /// </summary>
        public async Task<bool> EnablePluginAsync(string pluginId)
        {
            try
            {
                // TODO: 实现插件启用逻辑
                await Task.CompletedTask;
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"启用插件失败: {pluginId}");
                return false;
            }
        }

        /// <summary>
        /// 禁用插件
        /// </summary>
        public async Task<bool> DisablePluginAsync(string pluginId)
        {
            try
            {
                // TODO: 实现插件禁用逻辑
                await Task.CompletedTask;
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"禁用插件失败: {pluginId}");
                return false;
            }
        }

        /// <summary>
        /// 重新加载插件
        /// </summary>
        public async Task<bool> ReloadPluginAsync(string pluginId)
        {
            try
            {
                // TODO: 实现插件重新加载逻辑
                await Task.CompletedTask;
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"重新加载插件失败: {pluginId}");
                return false;
            }
        }

        /// <summary>
        /// 获取插件状态
        /// </summary>
        public PluginStatus GetPluginStatus(string pluginId)
        {
            if (_loadedPlugins.ContainsKey(pluginId))
            {
                return PluginStatus.Loaded;
            }
            
            if (_pluginMetadata.ContainsKey(pluginId))
            {
                return PluginStatus.Installed;
            }
            
            return PluginStatus.Unknown;
        }

        /// <summary>
        /// 触发插件状态变化事件
        /// </summary>
        private void OnPluginStatusChanged(string pluginId, PluginStatus oldStatus, PluginStatus newStatus)
        {
            PluginStatusChanged?.Invoke(this, new PluginStatusChangedEventArgs
            {
                PluginId = pluginId,
                OldStatus = oldStatus,
                NewStatus = newStatus
            });
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            UnloadPluginsAsync().Wait();
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// 插件容器
    /// </summary>
    internal class PluginContainer
    {
        public PluginMetadata Metadata { get; set; } = null!;
        public PluginLoadContext? LoadContext { get; set; }
        public Assembly? Assembly { get; set; }
        public Dictionary<string, INodePlugin> Plugins { get; set; } = new();
    }

    /// <summary>
    /// 插件加载上下文
    /// </summary>
    internal class PluginLoadContext : AssemblyLoadContext
    {
        private readonly AssemblyDependencyResolver _resolver;

        public PluginLoadContext(string pluginPath) : base(isCollectible: true)
        {
            _resolver = new AssemblyDependencyResolver(pluginPath);
        }

        protected override Assembly? Load(AssemblyName assemblyName)
        {
            var assemblyPath = _resolver.ResolveAssemblyToPath(assemblyName);
            return assemblyPath != null ? LoadFromAssemblyPath(assemblyPath) : null;
        }

        protected override IntPtr LoadUnmanagedDll(string unmanagedDllName)
        {
            var libraryPath = _resolver.ResolveUnmanagedDllToPath(unmanagedDllName);
            return libraryPath != null ? LoadUnmanagedDllFromPath(libraryPath) : IntPtr.Zero;
        }
    }
}

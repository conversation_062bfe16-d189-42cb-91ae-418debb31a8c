using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlowCustom.SDK;

namespace FlowCustom.Plugins.Http
{
    /// <summary>
    /// Webhook触发器插件
    /// 提供HTTP Webhook端点，接收外部系统的HTTP请求来触发工作流
    /// </summary>
    public class WebhookTriggerPlugin : NodePluginBase
    {
        #region 基本信息

        public override string NodeType => "webhook-trigger";
        public override string DisplayName => "Webhook触发器";
        public override string Description => "接收HTTP请求触发工作流执行";
        public override string Category => "触发器";
        public override string Author => "FlowCustom Team";
        public override string Version => "1.0.0";
        public override bool IsTrigger => true;
        public override string Icon => "🌐";

        #endregion

        #region 参数定义

        protected override List<ParameterDefinition> GetParameterDefinitions()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "path",
                    DisplayName = "Webhook路径",
                    Description = "Webhook的URL路径，例如：/webhook/my-trigger",
                    Type = ParameterType.String,
                    Required = true,
                    DefaultValue = "/webhook/trigger",
                    Validation = new Dictionary<string, object>
                    {
                        ["pattern"] = "^/[a-zA-Z0-9/_-]+$",
                        ["minLength"] = 2,
                        ["maxLength"] = 100
                    }
                },

                new ParameterDefinition
                {
                    Name = "methods",
                    DisplayName = "允许的HTTP方法",
                    Description = "允许触发的HTTP方法",
                    Type = ParameterType.MultiSelect,
                    Required = true,
                    DefaultValue = new[] { "POST" },
                    Options = new List<ParameterOption>
                    {
                        new ParameterOption { Value = "GET", Label = "GET", Description = "GET请求" },
                        new ParameterOption { Value = "POST", Label = "POST", Description = "POST请求" },
                        new ParameterOption { Value = "PUT", Label = "PUT", Description = "PUT请求" },
                        new ParameterOption { Value = "PATCH", Label = "PATCH", Description = "PATCH请求" },
                        new ParameterOption { Value = "DELETE", Label = "DELETE", Description = "DELETE请求" }
                    }
                },

                new ParameterDefinition
                {
                    Name = "authentication",
                    DisplayName = "身份验证",
                    Description = "Webhook的身份验证方式",
                    Type = ParameterType.Select,
                    Required = false,
                    DefaultValue = "none",
                    Options = new List<ParameterOption>
                    {
                        new ParameterOption { Value = "none", Label = "无验证", Description = "不进行身份验证" },
                        new ParameterOption { Value = "token", Label = "Token验证", Description = "使用Token进行验证" },
                        new ParameterOption { Value = "basic", Label = "Basic认证", Description = "使用用户名密码认证" },
                        new ParameterOption { Value = "signature", Label = "签名验证", Description = "使用HMAC签名验证" }
                    }
                },

                new ParameterDefinition
                {
                    Name = "authToken",
                    DisplayName = "验证Token",
                    Description = "用于Token验证的密钥",
                    Type = ParameterType.String,
                    Required = false,
                    DefaultValue = ""
                },

                new ParameterDefinition
                {
                    Name = "responseMode",
                    DisplayName = "响应模式",
                    Description = "Webhook的响应方式",
                    Type = ParameterType.Select,
                    Required = false,
                    DefaultValue = "immediate",
                    Options = new List<ParameterOption>
                    {
                        new ParameterOption { Value = "immediate", Label = "立即响应", Description = "立即返回200状态码" },
                        new ParameterOption { Value = "wait", Label = "等待完成", Description = "等待工作流执行完成后响应" },
                        new ParameterOption { Value = "async", Label = "异步响应", Description = "异步执行，返回执行ID" }
                    }
                },

                new ParameterDefinition
                {
                    Name = "responseBody",
                    DisplayName = "响应内容",
                    Description = "自定义响应内容（JSON格式）",
                    Type = ParameterType.Json,
                    Required = false,
                    DefaultValue = "{\"status\": \"received\", \"message\": \"Webhook received successfully\"}"
                },

                new ParameterDefinition
                {
                    Name = "maxPayloadSize",
                    DisplayName = "最大负载大小(KB)",
                    Description = "允许的最大请求体大小",
                    Type = ParameterType.Number,
                    Required = false,
                    DefaultValue = 1024,
                    Validation = new Dictionary<string, object>
                    {
                        ["min"] = 1,
                        ["max"] = 10240
                    }
                },

                new ParameterDefinition
                {
                    Name = "enableCors",
                    DisplayName = "启用CORS",
                    Description = "是否启用跨域资源共享",
                    Type = ParameterType.Boolean,
                    Required = false,
                    DefaultValue = true
                },

                new ParameterDefinition
                {
                    Name = "corsOrigins",
                    DisplayName = "允许的来源",
                    Description = "CORS允许的来源域名，多个用逗号分隔",
                    Type = ParameterType.String,
                    Required = false,
                    DefaultValue = "*"
                }
            };
        }

        #endregion

        #region 端点定义

        protected override List<EndpointDefinition> GetInputDefinitions()
        {
            // 触发器节点没有输入端点
            return new List<EndpointDefinition>();
        }

        protected override List<EndpointDefinition> GetOutputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "webhook",
                    Name = "Webhook触发",
                    Description = "接收到Webhook请求时的输出",
                    Type = EndpointType.Trigger,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 30 },
                    DataType = "webhook"
                },

                new EndpointDefinition
                {
                    Id = "error",
                    Name = "错误输出",
                    Description = "Webhook处理错误时的输出",
                    Type = EndpointType.Error,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 70 },
                    DataType = "error"
                }
            };
        }

        #endregion

        #region 核心逻辑

        protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
        {
            var path = GetParameter<string>(context, "path", "/webhook/trigger");
            var methods = GetParameter<string[]>(context, "methods", new[] { "POST" });
            var authentication = GetParameter<string>(context, "authentication", "none");
            var authToken = GetParameter<string>(context, "authToken", "");
            var responseMode = GetParameter<string>(context, "responseMode", "immediate");
            var responseBodyJson = GetParameter<string>(context, "responseBody", "{}");
            var maxPayloadSize = GetParameter<int>(context, "maxPayloadSize", 1024);
            var enableCors = GetParameter<bool>(context, "enableCors", true);
            var corsOrigins = GetParameter<string>(context, "corsOrigins", "*");

            Log($"配置Webhook触发器: {path}");

            try
            {
                // 解析响应内容
                var responseBody = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseBodyJson);

                // 注册Webhook端点（这里是模拟，实际需要与Web服务器集成）
                var webhookConfig = new WebhookConfiguration
                {
                    Path = path,
                    Methods = methods,
                    Authentication = authentication,
                    AuthToken = authToken,
                    ResponseMode = responseMode,
                    ResponseBody = responseBody,
                    MaxPayloadSize = maxPayloadSize * 1024, // 转换为字节
                    EnableCors = enableCors,
                    CorsOrigins = corsOrigins.Split(',', StringSplitOptions.RemoveEmptyEntries)
                };

                // 模拟注册Webhook
                await RegisterWebhookAsync(webhookConfig, context);

                var outputData = new Dictionary<string, object>
                {
                    ["webhookUrl"] = $"http://localhost:5053{path}",
                    ["methods"] = methods,
                    ["authentication"] = authentication,
                    ["responseMode"] = responseMode,
                    ["registeredAt"] = DateTime.UtcNow,
                    ["nodeId"] = context.NodeId,
                    ["workflowId"] = context.WorkflowId
                };

                Log($"Webhook触发器配置完成: {path}");

                return NodeExecutionResult.Success(outputData, "webhook");
            }
            catch (Exception ex)
            {
                Log($"Webhook配置失败: {ex.Message}", LogLevel.Error);
                
                var errorData = new Dictionary<string, object>
                {
                    ["error"] = ex.Message,
                    ["errorType"] = ex.GetType().Name,
                    ["timestamp"] = DateTime.UtcNow,
                    ["path"] = path
                };

                return NodeExecutionResult.Success(errorData, "error");
            }
        }

        /// <summary>
        /// 注册Webhook端点
        /// </summary>
        private async Task RegisterWebhookAsync(WebhookConfiguration config, NodeExecutionContext context)
        {
            // 这里应该与实际的Web服务器集成
            // 例如：动态注册路由、配置中间件等
            
            Log($"注册Webhook端点: {config.Path}");
            Log($"支持的HTTP方法: {string.Join(", ", config.Methods)}");
            Log($"身份验证方式: {config.Authentication}");
            Log($"响应模式: {config.ResponseMode}");
            
            // 模拟异步注册过程
            await Task.Delay(100, context.CancellationToken);
            
            // 在实际实现中，这里应该：
            // 1. 向Web服务器注册新的路由
            // 2. 配置身份验证中间件
            // 3. 设置CORS策略
            // 4. 配置请求大小限制
            // 5. 存储Webhook配置到数据库
        }

        #endregion

        #region 验证逻辑

        protected override void OnValidate(NodeConfiguration configuration, 
            List<ValidationError> errors, List<ValidationWarning> warnings)
        {
            // 验证Webhook路径
            if (configuration.Parameters.TryGetValue("path", out var pathObj))
            {
                var path = pathObj?.ToString() ?? "";
                if (string.IsNullOrWhiteSpace(path))
                {
                    errors.Add(new ValidationError
                    {
                        Field = "path",
                        Message = "Webhook路径不能为空",
                        Code = "EMPTY_PATH"
                    });
                }
                else if (!path.StartsWith("/"))
                {
                    errors.Add(new ValidationError
                    {
                        Field = "path",
                        Message = "Webhook路径必须以 '/' 开头",
                        Code = "INVALID_PATH_FORMAT"
                    });
                }
                else if (path.Length > 100)
                {
                    errors.Add(new ValidationError
                    {
                        Field = "path",
                        Message = "Webhook路径长度不能超过100个字符",
                        Code = "PATH_TOO_LONG"
                    });
                }
            }

            // 验证HTTP方法
            if (configuration.Parameters.TryGetValue("methods", out var methodsObj))
            {
                if (methodsObj is string[] methods && methods.Length == 0)
                {
                    errors.Add(new ValidationError
                    {
                        Field = "methods",
                        Message = "至少需要选择一个HTTP方法",
                        Code = "NO_METHODS_SELECTED"
                    });
                }
            }

            // 验证身份验证配置
            if (configuration.Parameters.TryGetValue("authentication", out var authObj))
            {
                var auth = authObj?.ToString() ?? "none";
                if (auth == "token" && 
                    (!configuration.Parameters.TryGetValue("authToken", out var tokenObj) ||
                     string.IsNullOrWhiteSpace(tokenObj?.ToString())))
                {
                    errors.Add(new ValidationError
                    {
                        Field = "authToken",
                        Message = "选择Token验证时必须提供验证Token",
                        Code = "MISSING_AUTH_TOKEN"
                    });
                }
            }

            // 验证响应内容JSON格式
            if (configuration.Parameters.TryGetValue("responseBody", out var responseObj))
            {
                var responseJson = responseObj?.ToString() ?? "{}";
                try
                {
                    System.Text.Json.JsonSerializer.Deserialize<object>(responseJson);
                }
                catch
                {
                    errors.Add(new ValidationError
                    {
                        Field = "responseBody",
                        Message = "响应内容不是有效的JSON格式",
                        Code = "INVALID_JSON"
                    });
                }
            }

            // 验证负载大小
            if (configuration.Parameters.TryGetValue("maxPayloadSize", out var sizeObj))
            {
                if (int.TryParse(sizeObj?.ToString(), out var size))
                {
                    if (size < 1)
                    {
                        errors.Add(new ValidationError
                        {
                            Field = "maxPayloadSize",
                            Message = "最大负载大小必须大于0",
                            Code = "INVALID_PAYLOAD_SIZE"
                        });
                    }
                    else if (size > 10240)
                    {
                        warnings.Add(new ValidationWarning
                        {
                            Field = "maxPayloadSize",
                            Message = "负载大小过大可能影响性能",
                            Code = "LARGE_PAYLOAD_SIZE"
                        });
                    }
                }
            }
        }

        #endregion

        #region 生命周期

        protected override void OnInitialize()
        {
            Log("Webhook触发器插件初始化完成");
        }

        protected override void OnDispose()
        {
            Log("Webhook触发器插件资源清理完成");
            // 在实际实现中，这里应该清理注册的Webhook端点
        }

        #endregion
    }

    /// <summary>
    /// Webhook配置
    /// </summary>
    public class WebhookConfiguration
    {
        public string Path { get; set; } = string.Empty;
        public string[] Methods { get; set; } = Array.Empty<string>();
        public string Authentication { get; set; } = "none";
        public string AuthToken { get; set; } = string.Empty;
        public string ResponseMode { get; set; } = "immediate";
        public Dictionary<string, object>? ResponseBody { get; set; }
        public int MaxPayloadSize { get; set; } = 1024 * 1024; // 1MB
        public bool EnableCors { get; set; } = true;
        public string[] CorsOrigins { get; set; } = Array.Empty<string>();
    }
}

<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>FlowCustom.SDK</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>FlowCustom Team</Authors>
    <Company>FlowCustom</Company>
    <Product>FlowCustom Plugin SDK</Product>
    <Description>FlowCustom插件开发SDK，提供插件开发所需的接口和基础类</Description>
    <PackageTags>workflow;automation;plugin;sdk</PackageTags>
    <PackageProjectUrl>https://github.com/flowcustom/sdk</PackageProjectUrl>
    <RepositoryUrl>https://github.com/flowcustom/sdk</RepositoryUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

</Project>

using FlowCustom.Core.Models;

namespace FlowCustom.Core.Interfaces;

/// <summary>
/// 节点接口
/// </summary>
public interface INode
{
    /// <summary>
    /// 节点类型标识
    /// </summary>
    string NodeType { get; }
    
    /// <summary>
    /// 节点显示名称
    /// </summary>
    string DisplayName { get; }
    
    /// <summary>
    /// 节点描述
    /// </summary>
    string Description { get; }
    
    /// <summary>
    /// 节点分类
    /// </summary>
    string Category { get; }
    
    /// <summary>
    /// 节点图标
    /// </summary>
    string Icon { get; }
    
    /// <summary>
    /// 获取节点参数定义
    /// </summary>
    NodeParameterDefinition[] GetParameterDefinitions();
    
    /// <summary>
    /// 执行节点
    /// </summary>
    Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node,
        Models.ExecutionContext context,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证节点参数
    /// </summary>
    ValidationResult ValidateParameters(Dictionary<string, object> parameters);
}

/// <summary>
/// 触发器节点接口
/// </summary>
public interface ITriggerNode : INode
{
    /// <summary>
    /// 启动触发器监听
    /// </summary>
    Task StartAsync(WorkflowNode node, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 停止触发器监听
    /// </summary>
    Task StopAsync(WorkflowNode node, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 触发器事件
    /// </summary>
    event EventHandler<TriggerEventArgs> Triggered;
}

/// <summary>
/// 节点参数定义
/// </summary>
public class NodeParameterDefinition
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ParameterType Type { get; set; }
    public bool Required { get; set; }
    public object? DefaultValue { get; set; }
    public string[]? Options { get; set; }
    public Dictionary<string, object> Validation { get; set; } = new();
}

/// <summary>
/// 参数类型枚举
/// </summary>
public enum ParameterType
{
    String,
    Number,
    Boolean,
    Object,
    Array,
    Select,
    MultiSelect,
    File,
    Credential,
    Expression
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    
    public static ValidationResult Success() => new() { IsValid = true };
    
    public static ValidationResult Failure(params string[] errors) => new()
    {
        IsValid = false,
        Errors = errors.ToList()
    };
}

/// <summary>
/// 触发器事件参数
/// </summary>
public class TriggerEventArgs : EventArgs
{
    public Guid WorkflowId { get; set; }
    public Guid NodeId { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public string? Source { get; set; }
}

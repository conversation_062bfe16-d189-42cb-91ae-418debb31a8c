using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace FlowCustom.Api.Controllers;

[ApiController]
[Route("[controller]")]
public class HealthController : ControllerBase
{
    /// <summary>
    /// 基本健康检查
    /// </summary>
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(new
        {
            status = "Healthy",
            timestamp = DateTime.UtcNow,
            version = GetVersion(),
            environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
        });
    }

    /// <summary>
    /// 详细健康检查
    /// </summary>
    [HttpGet("detailed")]
    public async Task<IActionResult> GetDetailed()
    {
        var healthChecks = new Dictionary<string, object>();

        // 检查内存使用情况
        var memoryUsage = GC.GetTotalMemory(false);
        healthChecks["memory"] = new
        {
            status = memoryUsage < 500 * 1024 * 1024 ? "Healthy" : "Warning", // 500MB 阈值
            usageBytes = memoryUsage,
            usageMB = memoryUsage / 1024 / 1024
        };

        // 检查磁盘空间
        try
        {
            var drives = DriveInfo.GetDrives().Where(d => d.IsReady);
            var diskInfo = drives.Select(d => new
            {
                name = d.Name,
                totalSizeGB = d.TotalSize / 1024 / 1024 / 1024,
                availableSpaceGB = d.AvailableFreeSpace / 1024 / 1024 / 1024,
                usagePercent = (double)(d.TotalSize - d.AvailableFreeSpace) / d.TotalSize * 100
            }).ToList();

            healthChecks["disk"] = new
            {
                status = diskInfo.All(d => d.usagePercent < 90) ? "Healthy" : "Warning",
                drives = diskInfo
            };
        }
        catch (Exception ex)
        {
            healthChecks["disk"] = new
            {
                status = "Error",
                error = ex.Message
            };
        }

        // 检查系统运行时间
        var uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime.ToUniversalTime();
        healthChecks["uptime"] = new
        {
            status = "Healthy",
            uptimeSeconds = uptime.TotalSeconds,
            uptimeFormatted = $"{uptime.Days}d {uptime.Hours}h {uptime.Minutes}m {uptime.Seconds}s"
        };

        // 检查线程池状态
        ThreadPool.GetAvailableThreads(out int workerThreads, out int completionPortThreads);
        ThreadPool.GetMaxThreads(out int maxWorkerThreads, out int maxCompletionPortThreads);
        
        healthChecks["threadPool"] = new
        {
            status = workerThreads > 10 ? "Healthy" : "Warning",
            availableWorkerThreads = workerThreads,
            maxWorkerThreads = maxWorkerThreads,
            availableCompletionPortThreads = completionPortThreads,
            maxCompletionPortThreads = maxCompletionPortThreads
        };

        var overallStatus = healthChecks.Values
            .Select(v => v.GetType().GetProperty("status")?.GetValue(v)?.ToString())
            .Any(s => s == "Error") ? "Unhealthy" :
            healthChecks.Values
            .Select(v => v.GetType().GetProperty("status")?.GetValue(v)?.ToString())
            .Any(s => s == "Warning") ? "Degraded" : "Healthy";

        return Ok(new
        {
            status = overallStatus,
            timestamp = DateTime.UtcNow,
            version = GetVersion(),
            environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
            checks = healthChecks
        });
    }

    /// <summary>
    /// 就绪检查
    /// </summary>
    [HttpGet("ready")]
    public async Task<IActionResult> Ready()
    {
        // 检查关键服务是否就绪
        var readinessChecks = new Dictionary<string, object>();

        // 检查数据库连接（模拟）
        try
        {
            // TODO: 实际检查数据库连接
            await Task.Delay(10); // 模拟数据库检查
            readinessChecks["database"] = new { status = "Ready" };
        }
        catch (Exception ex)
        {
            readinessChecks["database"] = new { status = "NotReady", error = ex.Message };
        }

        // 检查缓存连接（模拟）
        try
        {
            // TODO: 实际检查 Redis 连接
            await Task.Delay(10); // 模拟缓存检查
            readinessChecks["cache"] = new { status = "Ready" };
        }
        catch (Exception ex)
        {
            readinessChecks["cache"] = new { status = "NotReady", error = ex.Message };
        }

        var isReady = readinessChecks.Values
            .All(v => v.GetType().GetProperty("status")?.GetValue(v)?.ToString() == "Ready");

        if (isReady)
        {
            return Ok(new
            {
                status = "Ready",
                timestamp = DateTime.UtcNow,
                checks = readinessChecks
            });
        }
        else
        {
            return StatusCode(503, new
            {
                status = "NotReady",
                timestamp = DateTime.UtcNow,
                checks = readinessChecks
            });
        }
    }

    /// <summary>
    /// 存活检查
    /// </summary>
    [HttpGet("live")]
    public IActionResult Live()
    {
        // 简单的存活检查
        return Ok(new
        {
            status = "Alive",
            timestamp = DateTime.UtcNow
        });
    }

    private string GetVersion()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;
            return version?.ToString() ?? "Unknown";
        }
        catch
        {
            return "Unknown";
        }
    }
}

version: '3.8'

services:
  # FlowCustom API 服务
  flowcustom-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: flowcustom-api
    ports:
      - "5000:80"
      - "5001:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=postgres;Database=FlowCustom;User Id=flowcustom;Password=flowcustom123;
      - Redis__ConnectionString=redis:6379
      - JWT__Secret=FlowCustomJwtSecretKey123456789
      - JWT__ExpiryHours=24
    depends_on:
      - postgres
      - redis
    networks:
      - flowcustom-network
    restart: unless-stopped
    volumes:
      - ./data/logs:/app/logs
      - ./data/uploads:/app/uploads

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: flowcustom-postgres
    environment:
      - POSTGRES_DB=FlowCustom
      - POSTGRES_USER=flowcustom
      - POSTGRES_PASSWORD=flowcustom123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - flowcustom-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: flowcustom-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - flowcustom-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: flowcustom-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - flowcustom-api
    networks:
      - flowcustom-network
    restart: unless-stopped

  # 前端构建服务（仅用于构建）
  frontend-build:
    image: node:18-alpine
    container_name: flowcustom-frontend-build
    working_dir: /app
    volumes:
      - ./frontend:/app
      - frontend_node_modules:/app/node_modules
    command: sh -c "npm install && npm run build"
    profiles:
      - build

volumes:
  postgres_data:
  redis_data:
  frontend_node_modules:

networks:
  flowcustom-network:
    driver: bridge

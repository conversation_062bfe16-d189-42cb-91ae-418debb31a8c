# 使用官方 .NET 8 运行时作为基础镜像
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# 使用 .NET 8 SDK 进行构建
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制项目文件
COPY ["src/FlowCustom.Api/FlowCustom.Api.csproj", "src/FlowCustom.Api/"]
COPY ["src/FlowCustom.Core/FlowCustom.Core.csproj", "src/FlowCustom.Core/"]
COPY ["src/FlowCustom.Engine/FlowCustom.Engine.csproj", "src/FlowCustom.Engine/"]
COPY ["src/FlowCustom.Data/FlowCustom.Data.csproj", "src/FlowCustom.Data/"]
COPY ["src/FlowCustom.Nodes/FlowCustom.Nodes.csproj", "src/FlowCustom.Nodes/"]

# 还原依赖项
RUN dotnet restore "src/FlowCustom.Api/FlowCustom.Api.csproj"

# 复制所有源代码
COPY . .

# 构建应用程序
WORKDIR "/src/src/FlowCustom.Api"
RUN dotnet build "FlowCustom.Api.csproj" -c Release -o /app/build

# 发布应用程序
FROM build AS publish
RUN dotnet publish "FlowCustom.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# 最终镜像
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 创建非 root 用户
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

ENTRYPOINT ["dotnet", "FlowCustom.Api.dll"]

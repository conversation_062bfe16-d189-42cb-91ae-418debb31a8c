using FlowCustom.Nodes;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;

namespace FlowCustom.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TriggerController : ControllerBase
{
    private readonly ILogger<TriggerController> _logger;

    public TriggerController(ILogger<TriggerController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理 Webhook 触发器请求
    /// </summary>
    [HttpPost("webhook/{*path}")]
    [HttpGet("webhook/{*path}")]
    [HttpPut("webhook/{*path}")]
    [HttpDelete("webhook/{*path}")]
    [HttpPatch("webhook/{*path}")]
    public async Task<IActionResult> HandleWebhook(string path)
    {
        try
        {
            var method = Request.Method;
            var headers = Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString());
            
            // 读取请求体
            var body = new Dictionary<string, object>();
            if (Request.ContentLength > 0)
            {
                using var reader = new StreamReader(Request.Body);
                var bodyContent = await reader.ReadToEndAsync();
                
                if (!string.IsNullOrEmpty(bodyContent))
                {
                    try
                    {
                        var jsonData = JsonSerializer.Deserialize<Dictionary<string, object>>(bodyContent);
                        if (jsonData != null)
                        {
                            body = jsonData;
                        }
                    }
                    catch
                    {
                        body["raw"] = bodyContent;
                    }
                }
            }

            // 添加查询参数
            foreach (var param in Request.Query)
            {
                body[$"query_{param.Key}"] = param.Value.ToString();
            }

            var webhookPath = "/" + path;
            var success = await WebhookTriggerNode.HandleWebhookRequest(webhookPath, method, body, headers);

            if (success)
            {
                return Ok(new { message = "Webhook processed successfully", timestamp = DateTime.UtcNow });
            }
            else
            {
                return NotFound(new { message = "No active webhook found for this path" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing webhook: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    /// <summary>
    /// 处理 API 触发器请求
    /// </summary>
    [HttpPost("api/{*path}")]
    [HttpGet("api/{*path}")]
    [HttpPut("api/{*path}")]
    [HttpDelete("api/{*path}")]
    [HttpPatch("api/{*path}")]
    public async Task<IActionResult> HandleApiTrigger(string path)
    {
        try
        {
            var method = Request.Method;
            var headers = Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString());
            var apiKey = Request.Headers["X-API-Key"].FirstOrDefault();
            
            // 读取请求体
            var body = new Dictionary<string, object>();
            if (Request.ContentLength > 0)
            {
                using var reader = new StreamReader(Request.Body);
                var bodyContent = await reader.ReadToEndAsync();
                
                if (!string.IsNullOrEmpty(bodyContent))
                {
                    try
                    {
                        var jsonData = JsonSerializer.Deserialize<Dictionary<string, object>>(bodyContent);
                        if (jsonData != null)
                        {
                            body = jsonData;
                        }
                    }
                    catch
                    {
                        body["raw"] = bodyContent;
                    }
                }
            }

            // 添加查询参数
            foreach (var param in Request.Query)
            {
                body[$"query_{param.Key}"] = param.Value.ToString();
            }

            var apiPath = "/api/trigger/" + path;
            var (success, response) = await ApiTriggerNode.HandleApiRequest(apiPath, method, body, headers, apiKey);

            if (success)
            {
                return Ok(new { message = response, timestamp = DateTime.UtcNow });
            }
            else
            {
                return NotFound(new { message = response });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing API trigger: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取活动触发器状态
    /// </summary>
    [HttpGet("status")]
    public async Task<IActionResult> GetTriggerStatus()
    {
        try
        {
            // TODO: 从触发器管理器获取状态
            var status = new
            {
                webhooks = new { active = 0, total = 0 },
                apis = new { active = 0, total = 0 },
                crons = new { active = 0, total = 0 },
                fileWatchers = new { active = 0, total = 0 },
                timestamp = DateTime.UtcNow
            };

            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trigger status");
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    /// <summary>
    /// 手动触发工作流（用于测试）
    /// </summary>
    [HttpPost("manual/{workflowId}")]
    public async Task<IActionResult> ManualTrigger(Guid workflowId, [FromBody] Dictionary<string, object>? data = null)
    {
        try
        {
            // TODO: 直接调用工作流引擎执行
            _logger.LogInformation("Manual trigger requested for workflow: {WorkflowId}", workflowId);
            
            return Ok(new 
            { 
                message = "Manual trigger executed", 
                workflowId, 
                data = data ?? new Dictionary<string, object>(),
                timestamp = DateTime.UtcNow 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing manual trigger for workflow: {WorkflowId}", workflowId);
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }
}

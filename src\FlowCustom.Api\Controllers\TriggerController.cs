using FlowCustom.Nodes;
using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using FlowCustom.Data;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;

namespace FlowCustom.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TriggerController : ControllerBase
{
    private readonly ILogger<TriggerController> _logger;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly IWorkflowService _workflowService;

    public TriggerController(
        ILogger<TriggerController> logger,
        IWorkflowEngine workflowEngine,
        IWorkflowService workflowService)
    {
        _logger = logger;
        _workflowEngine = workflowEngine;
        _workflowService = workflowService;
    }

    /// <summary>
    /// 处理 Webhook 触发器请求
    /// </summary>
    [HttpPost("webhook/{*path}")]
    [HttpGet("webhook/{*path}")]
    [HttpPut("webhook/{*path}")]
    [HttpDelete("webhook/{*path}")]
    [HttpPatch("webhook/{*path}")]
    public async Task<IActionResult> HandleWebhook(string path)
    {
        try
        {
            var method = Request.Method;
            var headers = Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString());
            
            // 读取请求体
            var body = new Dictionary<string, object>();
            if (Request.ContentLength > 0)
            {
                using var reader = new StreamReader(Request.Body);
                var bodyContent = await reader.ReadToEndAsync();
                
                if (!string.IsNullOrEmpty(bodyContent))
                {
                    try
                    {
                        var jsonData = JsonSerializer.Deserialize<Dictionary<string, object>>(bodyContent);
                        if (jsonData != null)
                        {
                            body = jsonData;
                        }
                    }
                    catch
                    {
                        body["raw"] = bodyContent;
                    }
                }
            }

            // 添加查询参数
            foreach (var param in Request.Query)
            {
                body[$"query_{param.Key}"] = param.Value.ToString();
            }

            var webhookPath = "/" + path;
            var success = await WebhookTriggerNode.HandleWebhookRequest(webhookPath, method, body, headers);

            if (success)
            {
                return Ok(new { message = "Webhook processed successfully", timestamp = DateTime.UtcNow });
            }
            else
            {
                return NotFound(new { message = "No active webhook found for this path" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing webhook: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    /// <summary>
    /// 处理 API 触发器请求
    /// </summary>
    [HttpPost("api/{*path}")]
    [HttpGet("api/{*path}")]
    [HttpPut("api/{*path}")]
    [HttpDelete("api/{*path}")]
    [HttpPatch("api/{*path}")]
    public async Task<IActionResult> HandleApiTrigger(string path)
    {
        try
        {
            var method = Request.Method;
            var headers = Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString());
            var apiKey = Request.Headers["X-API-Key"].FirstOrDefault();
            
            // 读取请求体
            var body = new Dictionary<string, object>();
            if (Request.ContentLength > 0)
            {
                using var reader = new StreamReader(Request.Body);
                var bodyContent = await reader.ReadToEndAsync();
                
                if (!string.IsNullOrEmpty(bodyContent))
                {
                    try
                    {
                        var jsonData = JsonSerializer.Deserialize<Dictionary<string, object>>(bodyContent);
                        if (jsonData != null)
                        {
                            body = jsonData;
                        }
                    }
                    catch
                    {
                        body["raw"] = bodyContent;
                    }
                }
            }

            // 添加查询参数
            foreach (var param in Request.Query)
            {
                body[$"query_{param.Key}"] = param.Value.ToString();
            }

            var apiPath = "/api/trigger/" + path;
            var (success, response) = await ApiTriggerNode.HandleApiRequest(apiPath, method, body, headers, apiKey);

            if (success)
            {
                return Ok(new { message = response, timestamp = DateTime.UtcNow });
            }
            else
            {
                return NotFound(new { message = response });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing API trigger: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取活动触发器状态
    /// </summary>
    [HttpGet("status")]
    public async Task<IActionResult> GetTriggerStatus()
    {
        try
        {
            // TODO: 从触发器管理器获取状态
            var status = new
            {
                webhooks = new { active = 0, total = 0 },
                apis = new { active = 0, total = 0 },
                crons = new { active = 0, total = 0 },
                fileWatchers = new { active = 0, total = 0 },
                timestamp = DateTime.UtcNow
            };

            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trigger status");
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    /// <summary>
    /// 手动触发工作流
    /// </summary>
    [HttpPost("manual/{workflowId}")]
    public async Task<IActionResult> ManualTrigger(string workflowId, [FromQuery] string? nodeId = null, [FromBody] Dictionary<string, object>? data = null)
    {
        try
        {
            _logger.LogInformation("收到手动触发请求: WorkflowId={WorkflowId}, NodeId={NodeId}", workflowId, nodeId);

            // 解析工作流ID
            if (!Guid.TryParse(workflowId, out var workflowGuid))
            {
                return BadRequest("无效的工作流ID格式");
            }

            // 获取工作流
            var workflow = await _workflowService.GetWorkflowAsync(workflowGuid);
            if (workflow == null)
            {
                return NotFound($"工作流 {workflowId} 不存在");
            }

            if (!workflow.IsActive)
            {
                return BadRequest("工作流未激活，无法触发");
            }

            // 查找手动触发器节点
            WorkflowNode? triggerNode = null;
            if (!string.IsNullOrEmpty(nodeId))
            {
                triggerNode = workflow.Nodes.FirstOrDefault(n => n.Id.ToString() == nodeId && n.Type == "manual-trigger");
            }
            else
            {
                // 如果没有指定节点ID，查找第一个手动触发器节点
                triggerNode = workflow.Nodes.FirstOrDefault(n => n.Type == "manual-trigger");
            }

            if (triggerNode == null)
            {
                return BadRequest("工作流中没有找到手动触发器节点");
            }

            // 检查触发器是否启用
            var enabled = GetParameter<bool>(triggerNode, "enabled", true);
            if (!enabled)
            {
                return BadRequest("手动触发器已禁用");
            }

            // 创建触发数据
            var triggerData = new Dictionary<string, object>
            {
                ["triggerType"] = "manual",
                ["triggeredAt"] = DateTime.UtcNow,
                ["triggeredBy"] = "user",
                ["nodeId"] = triggerNode.Id,
                ["workflowId"] = workflowId,
                ["buttonText"] = GetParameter<string>(triggerNode, "buttonText", "立即执行"),
                ["confirmMessage"] = GetParameter<string>(triggerNode, "confirmMessage", "")
            };

            // 合并用户提供的数据
            if (data != null)
            {
                foreach (var kvp in data)
                {
                    triggerData[kvp.Key] = kvp.Value;
                }
            }

            _logger.LogInformation("开始执行手动触发的工作流: {WorkflowId}", workflowId);

            // 执行工作流
            var executionResult = await _workflowEngine.ExecuteAsync(workflowGuid, triggerData, "manual-trigger");

            _logger.LogInformation("手动触发工作流执行完成: {WorkflowId}, 结果: {Success}",
                workflowId, executionResult.Status);

            return Ok(new
            {
                success = executionResult.Status == ExecutionStatus.Success,
                executionId = executionResult.Id,
                message = executionResult.Status == ExecutionStatus.Success ? "工作流触发成功" : "工作流执行失败",
                error = executionResult.Status == ExecutionStatus.Error ? executionResult.Error : null,
                triggerData = triggerData
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动触发工作流时发生错误: {WorkflowId}", workflowId);
            return StatusCode(500, new
            {
                success = false,
                message = "手动触发工作流时发生错误",
                error = ex.Message
            });
        }
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default)
    {
        if (node.Parameters != null && node.Parameters.ContainsKey(parameterName))
        {
            try
            {
                var value = node.Parameters[parameterName];
                if (value is T directValue)
                {
                    return directValue;
                }
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法转换参数 {ParameterName} 的值，使用默认值", parameterName);
            }
        }
        return defaultValue;
    }
}

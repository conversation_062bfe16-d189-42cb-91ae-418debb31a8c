using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustom.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WorkflowController : ControllerBase
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly ITriggerManager _triggerManager;

    public WorkflowController(
        IWorkflowService workflowService,
        IWorkflowEngine workflowEngine,
        ITriggerManager triggerManager)
    {
        _workflowService = workflowService;
        _workflowEngine = workflowEngine;
        _triggerManager = triggerManager;
    }

    /// <summary>
    /// 获取用户的工作流列表
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Workflow>>> GetWorkflows([FromQuery] string? userId = "default")
    {
        var workflows = await _workflowService.GetUserWorkflowsAsync(userId ?? "default");
        return Ok(workflows);
    }

    /// <summary>
    /// 获取指定工作流
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<Workflow>> GetWorkflow(Guid id)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }
        return Ok(workflow);
    }

    /// <summary>
    /// 创建新工作流
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<Workflow>> CreateWorkflow([FromBody] Workflow workflow)
    {
        var validation = await _workflowService.ValidateWorkflowAsync(workflow);
        if (!validation.IsValid)
        {
            return BadRequest(validation.Errors);
        }

        var createdWorkflow = await _workflowService.CreateWorkflowAsync(workflow);
        
        // 如果工作流是激活状态，启动触发器
        if (createdWorkflow.IsActive)
        {
            await _triggerManager.StartTriggersAsync(createdWorkflow.Id);
        }

        return CreatedAtAction(nameof(GetWorkflow), new { id = createdWorkflow.Id }, createdWorkflow);
    }

    /// <summary>
    /// 更新工作流
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<Workflow>> UpdateWorkflow(Guid id, [FromBody] Workflow workflow)
    {
        if (id != workflow.Id)
        {
            return BadRequest("ID mismatch");
        }

        var existingWorkflow = await _workflowService.GetWorkflowAsync(id);
        if (existingWorkflow == null)
        {
            return NotFound();
        }

        var validation = await _workflowService.ValidateWorkflowAsync(workflow);
        if (!validation.IsValid)
        {
            return BadRequest(validation.Errors);
        }

        // 重新加载触发器
        await _triggerManager.ReloadTriggersAsync(id);

        var updatedWorkflow = await _workflowService.UpdateWorkflowAsync(workflow);
        return Ok(updatedWorkflow);
    }

    /// <summary>
    /// 删除工作流
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteWorkflow(Guid id)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }

        // 停止触发器
        await _triggerManager.StopTriggersAsync(id);

        await _workflowService.DeleteWorkflowAsync(id);
        return NoContent();
    }

    /// <summary>
    /// 激活/停用工作流
    /// </summary>
    [HttpPost("{id}/toggle")]
    public async Task<IActionResult> ToggleWorkflow(Guid id, [FromBody] bool isActive)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }

        await _workflowService.SetWorkflowActiveAsync(id, isActive);

        if (isActive)
        {
            await _triggerManager.StartTriggersAsync(id);
        }
        else
        {
            await _triggerManager.StopTriggersAsync(id);
        }

        return Ok();
    }

    /// <summary>
    /// 手动执行工作流
    /// </summary>
    [HttpPost("{id}/execute")]
    public async Task<ActionResult<WorkflowExecution>> ExecuteWorkflow(
        Guid id, 
        [FromBody] Dictionary<string, object>? inputData = null)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }

        var execution = await _workflowEngine.ExecuteAsync(id, inputData, "manual");
        return Ok(execution);
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    [HttpGet("{id}/executions")]
    public async Task<ActionResult<IEnumerable<WorkflowExecution>>> GetExecutionHistory(
        Guid id,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50)
    {
        var executions = await _workflowEngine.GetExecutionHistoryAsync(id, page, pageSize);
        return Ok(executions);
    }

    /// <summary>
    /// 获取执行详情
    /// </summary>
    [HttpGet("executions/{executionId}")]
    public async Task<ActionResult<WorkflowExecution>> GetExecution(Guid executionId)
    {
        var execution = await _workflowEngine.GetExecutionAsync(executionId);
        if (execution == null)
        {
            return NotFound();
        }
        return Ok(execution);
    }

    /// <summary>
    /// 停止工作流执行
    /// </summary>
    [HttpPost("executions/{executionId}/stop")]
    public async Task<IActionResult> StopExecution(Guid executionId)
    {
        await _workflowEngine.StopExecutionAsync(executionId);
        return Ok();
    }
}

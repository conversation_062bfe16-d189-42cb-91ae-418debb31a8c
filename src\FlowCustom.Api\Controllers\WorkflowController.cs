using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustom.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WorkflowController : ControllerBase
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly ITriggerManager _triggerManager;

    public WorkflowController(
        IWorkflowService workflowService,
        IWorkflowEngine workflowEngine,
        ITriggerManager triggerManager)
    {
        _workflowService = workflowService;
        _workflowEngine = workflowEngine;
        _triggerManager = triggerManager;
    }

    /// <summary>
    /// 获取用户的工作流列表
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Workflow>>> GetWorkflows([FromQuery] string? userId = "default")
    {
        var workflows = await _workflowService.GetUserWorkflowsAsync(userId ?? "default");
        return Ok(workflows);
    }

    /// <summary>
    /// 获取指定工作流
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<Workflow>> GetWorkflow(Guid id)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }
        return Ok(workflow);
    }

    /// <summary>
    /// 创建新工作流
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<Workflow>> CreateWorkflow([FromBody] CreateWorkflowRequest request)
    {
        // 创建完整的工作流对象
        var workflow = new Workflow
        {
            Id = Guid.NewGuid(),
            Name = request.Name ?? "New Workflow",
            Description = request.Description ?? "",
            IsActive = request.IsActive ?? false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = "system", // TODO: 从认证上下文获取用户信息
            Nodes = request.Nodes ?? new List<WorkflowNode>(),
            Connections = request.Connections ?? new List<NodeConnection>(),
            Settings = request.Settings ?? new WorkflowSettings()
        };

        var validation = await _workflowService.ValidateWorkflowAsync(workflow);
        if (!validation.IsValid)
        {
            return BadRequest(validation.Errors);
        }

        var createdWorkflow = await _workflowService.CreateWorkflowAsync(workflow);

        // 如果工作流是激活状态，启动触发器
        if (createdWorkflow.IsActive)
        {
            await _triggerManager.StartTriggersAsync(createdWorkflow.Id);
        }

        return CreatedAtAction(nameof(GetWorkflow), new { id = createdWorkflow.Id }, createdWorkflow);
    }

    /// <summary>
    /// 更新工作流
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<Workflow>> UpdateWorkflow(Guid id, [FromBody] Workflow workflow)
    {
        try
        {
            _logger.LogInformation("Updating workflow {WorkflowId} with name: {WorkflowName}", id, workflow.Name);

            if (id != workflow.Id)
            {
                _logger.LogWarning("ID mismatch: URL ID {UrlId} != Body ID {BodyId}", id, workflow.Id);
                return BadRequest("ID mismatch");
            }

            var existingWorkflow = await _workflowService.GetWorkflowAsync(id);
            if (existingWorkflow == null)
            {
                _logger.LogWarning("Workflow not found: {WorkflowId}", id);
                return NotFound();
            }

            var validation = await _workflowService.ValidateWorkflowAsync(workflow);
            if (!validation.IsValid)
            {
                _logger.LogWarning("Workflow validation failed for {WorkflowId}: {Errors}", id, string.Join(", ", validation.Errors));
                return BadRequest(validation.Errors);
            }

            // 重新加载触发器
            await _triggerManager.ReloadTriggersAsync(id);

            var updatedWorkflow = await _workflowService.UpdateWorkflowAsync(workflow);
            _logger.LogInformation("Successfully updated workflow: {WorkflowId}", id);
            return Ok(updatedWorkflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating workflow {WorkflowId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// 删除工作流
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteWorkflow(Guid id)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }

        // 停止触发器
        await _triggerManager.StopTriggersAsync(id);

        await _workflowService.DeleteWorkflowAsync(id);
        return NoContent();
    }

    /// <summary>
    /// 激活/停用工作流
    /// </summary>
    [HttpPost("{id}/toggle")]
    public async Task<IActionResult> ToggleWorkflow(Guid id, [FromBody] bool isActive)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }

        await _workflowService.SetWorkflowActiveAsync(id, isActive);

        if (isActive)
        {
            await _triggerManager.StartTriggersAsync(id);
        }
        else
        {
            await _triggerManager.StopTriggersAsync(id);
        }

        return Ok();
    }

    /// <summary>
    /// 手动执行工作流
    /// </summary>
    [HttpPost("{id}/execute")]
    public async Task<ActionResult<WorkflowExecution>> ExecuteWorkflow(
        Guid id, 
        [FromBody] Dictionary<string, object>? inputData = null)
    {
        var workflow = await _workflowService.GetWorkflowAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }

        var execution = await _workflowEngine.ExecuteAsync(id, inputData, "manual");
        return Ok(execution);
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    [HttpGet("{id}/executions")]
    public async Task<ActionResult<IEnumerable<WorkflowExecution>>> GetExecutionHistory(
        Guid id,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50)
    {
        var executions = await _workflowEngine.GetExecutionHistoryAsync(id, page, pageSize);
        return Ok(executions);
    }

    /// <summary>
    /// 获取执行详情
    /// </summary>
    [HttpGet("executions/{executionId}")]
    public async Task<ActionResult<WorkflowExecution>> GetExecution(Guid executionId)
    {
        var execution = await _workflowEngine.GetExecutionAsync(executionId);
        if (execution == null)
        {
            return NotFound();
        }
        return Ok(execution);
    }

    /// <summary>
    /// 停止工作流执行
    /// </summary>
    [HttpPost("executions/{executionId}/stop")]
    public async Task<IActionResult> StopExecution(Guid executionId)
    {
        await _workflowEngine.StopExecutionAsync(executionId);
        return Ok();
    }
}

/// <summary>
/// 创建工作流请求模型
/// </summary>
public class CreateWorkflowRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public List<WorkflowNode>? Nodes { get; set; }
    public List<NodeConnection>? Connections { get; set; }
    public WorkflowSettings? Settings { get; set; }
}

import React, { useState, useEffect } from 'react';

interface DebugLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  nodeId?: string;
  nodeName?: string;
  data?: any;
}

interface DebugPanelProps {
  isOpen: boolean;
  onClose: () => void;
  workflowId?: string;
  executionId?: string;
}

const DebugPanel: React.FC<DebugPanelProps> = ({ 
  isOpen, 
  onClose, 
  workflowId, 
  executionId 
}) => {
  const [logs, setLogs] = useState<DebugLog[]>([]);
  const [filter, setFilter] = useState<string>('all');
  const [autoScroll, setAutoScroll] = useState(true);

  // 模拟调试日志数据
  useEffect(() => {
    if (isOpen && executionId) {
      // 模拟实时调试日志
      const mockLogs: DebugLog[] = [
        {
          id: '1',
          timestamp: new Date().toISOString(),
          level: 'info',
          message: '开始执行工作流',
          data: { workflowId, executionId }
        },
        {
          id: '2',
          timestamp: new Date(Date.now() + 1000).toISOString(),
          level: 'debug',
          message: '节点输入数据验证',
          nodeId: 'node-1',
          nodeName: '定时触发器',
          data: { cronExpression: '0 */1 * * * *', enabled: true }
        },
        {
          id: '3',
          timestamp: new Date(Date.now() + 2000).toISOString(),
          level: 'info',
          message: '节点执行成功',
          nodeId: 'node-1',
          nodeName: '定时触发器',
          data: { duration: 150, status: 'success' }
        }
      ];
      setLogs(mockLogs);
    }
  }, [isOpen, executionId, workflowId]);

  const filteredLogs = logs.filter(log => 
    filter === 'all' || log.level === filter
  );

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      case 'debug':
        return '🔍';
      default:
        return '📝';
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'text-red-600 bg-red-50';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50';
      case 'info':
        return 'text-blue-600 bg-blue-50';
      case 'debug':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="coze-card w-4/5 h-4/5 flex flex-col max-w-6xl">
        {/* Header */}
        <div className="coze-card-header flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-lg flex items-center justify-center text-lg" 
                 style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
              🐛
            </div>
            <div>
              <h3 className="text-lg font-semibold" style={{ color: 'var(--coze-text-primary)' }}>
                流程调试
              </h3>
              <p className="text-sm" style={{ color: 'var(--coze-text-secondary)' }}>
                实时查看工作流执行过程和调试信息
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="coze-input text-sm"
            >
              <option value="all">所有日志</option>
              <option value="error">错误</option>
              <option value="warning">警告</option>
              <option value="info">信息</option>
              <option value="debug">调试</option>
            </select>
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={autoScroll}
                onChange={(e) => setAutoScroll(e.target.checked)}
                className="rounded"
              />
              <span style={{ color: 'var(--coze-text-secondary)' }}>自动滚动</span>
            </label>
            <button
              onClick={onClose}
              className="coze-btn coze-btn-secondary p-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="coze-card-body flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto space-y-2">
            {filteredLogs.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" 
                     style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
                  <svg className="w-8 h-8" style={{ color: 'var(--coze-text-muted)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-sm" style={{ color: 'var(--coze-text-secondary)' }}>
                  暂无调试日志
                </p>
              </div>
            ) : (
              filteredLogs.map((log) => (
                <div
                  key={log.id}
                  className={`coze-card p-3 ${getLevelColor(log.level)}`}
                >
                  <div className="flex items-start space-x-3">
                    <span className="text-lg">{getLevelIcon(log.level)}</span>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-xs font-mono" style={{ color: 'var(--coze-text-muted)' }}>
                          {new Date(log.timestamp).toLocaleTimeString()}
                        </span>
                        {log.nodeId && (
                          <span className="coze-badge coze-badge-primary text-xs">
                            {log.nodeName || log.nodeId}
                          </span>
                        )}
                      </div>
                      <p className="text-sm font-medium mb-2" style={{ color: 'var(--coze-text-primary)' }}>
                        {log.message}
                      </p>
                      {log.data && (
                        <details className="text-xs">
                          <summary className="cursor-pointer" style={{ color: 'var(--coze-text-secondary)' }}>
                            查看详细数据
                          </summary>
                          <pre className="mt-2 p-2 rounded" style={{ backgroundColor: 'var(--coze-bg-secondary)' }}>
                            {JSON.stringify(log.data, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="coze-card-footer flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm" style={{ color: 'var(--coze-text-secondary)' }}>
              共 {filteredLogs.length} 条日志
            </span>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <span className="text-sm" style={{ color: 'var(--coze-text-secondary)' }}>
                实时监控中
              </span>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setLogs([])}
              className="coze-btn coze-btn-secondary text-sm"
            >
              清空日志
            </button>
            <button
              onClick={() => {
                const logText = filteredLogs.map(log => 
                  `[${new Date(log.timestamp).toLocaleString()}] ${log.level.toUpperCase()}: ${log.message}`
                ).join('\n');
                navigator.clipboard.writeText(logText);
              }}
              className="coze-btn coze-btn-secondary text-sm"
            >
              复制日志
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugPanel;

﻿using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FlowCustom.Engine;

/// <summary>
/// 节点注册表实现
/// </summary>
public class NodeRegistry : INodeRegistry
{
    private readonly Dictionary<string, Type> _nodeTypes = new();
    private readonly Dictionary<string, NodeDefinition> _nodeDefinitions = new();
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NodeRegistry> _logger;

    public NodeRegistry(IServiceProvider serviceProvider, ILogger<NodeRegistry> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public void RegisterNode<T>() where T : class, INode
    {
        var nodeType = typeof(T);
        var instance = ActivatorUtilities.CreateInstance<T>(_serviceProvider);

        _nodeTypes[instance.NodeType] = nodeType;
        _nodeDefinitions[instance.NodeType] = new NodeDefinition
        {
            NodeType = instance.NodeType,
            DisplayName = instance.DisplayName,
            Description = instance.Description,
            Category = instance.Category,
            Icon = instance.Icon,
            IsTrigger = instance is ITriggerNode,
            Parameters = instance.GetParameterDefinitions()
        };

        _logger.LogInformation("Registered node: {NodeType}", instance.NodeType);
    }

    public INode? GetNode(string nodeType)
    {
        if (!_nodeTypes.TryGetValue(nodeType, out var type))
        {
            _logger.LogWarning("Node type not found: {NodeType}", nodeType);
            return null;
        }

        try
        {
            return (INode)ActivatorUtilities.CreateInstance(_serviceProvider, type);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create node instance: {NodeType}", nodeType);
            return null;
        }
    }

    public IEnumerable<string> GetRegisteredNodeTypes()
    {
        return _nodeTypes.Keys;
    }

    public NodeDefinition? GetNodeDefinition(string nodeType)
    {
        return _nodeDefinitions.TryGetValue(nodeType, out var definition) ? definition : null;
    }

    public IEnumerable<NodeDefinition> GetAllNodeDefinitions()
    {
        return _nodeDefinitions.Values;
    }
}

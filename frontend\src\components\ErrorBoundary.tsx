import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--coze-bg-secondary)' }}>
          <div className="coze-card max-w-2xl w-full mx-4">
            <div className="coze-card-header">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-lg bg-red-100 flex items-center justify-center">
                  <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-red-600">
                    应用程序错误
                  </h3>
                  <p className="text-sm" style={{ color: 'var(--coze-text-secondary)' }}>
                    很抱歉，应用程序遇到了一个错误
                  </p>
                </div>
              </div>
            </div>
            
            <div className="coze-card-body">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2" style={{ color: 'var(--coze-text-primary)' }}>
                    错误信息:
                  </h4>
                  <div className="p-3 rounded-lg" style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
                    <code className="text-sm text-red-600">
                      {this.state.error?.message || '未知错误'}
                    </code>
                  </div>
                </div>
                
                {this.state.error?.stack && (
                  <details className="space-y-2">
                    <summary className="cursor-pointer font-medium" style={{ color: 'var(--coze-text-primary)' }}>
                      详细堆栈信息
                    </summary>
                    <div className="p-3 rounded-lg overflow-auto max-h-64" style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
                      <pre className="text-xs" style={{ color: 'var(--coze-text-secondary)' }}>
                        {this.state.error.stack}
                      </pre>
                    </div>
                  </details>
                )}
                
                {this.state.errorInfo?.componentStack && (
                  <details className="space-y-2">
                    <summary className="cursor-pointer font-medium" style={{ color: 'var(--coze-text-primary)' }}>
                      组件堆栈信息
                    </summary>
                    <div className="p-3 rounded-lg overflow-auto max-h-64" style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
                      <pre className="text-xs" style={{ color: 'var(--coze-text-secondary)' }}>
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  </details>
                )}
              </div>
            </div>
            
            <div className="coze-card-footer flex space-x-3">
              <button
                onClick={() => window.location.reload()}
                className="coze-btn coze-btn-primary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                重新加载页面
              </button>
              <button
                onClick={() => this.setState({ hasError: false, error: undefined, errorInfo: undefined })}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                </svg>
                重试
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

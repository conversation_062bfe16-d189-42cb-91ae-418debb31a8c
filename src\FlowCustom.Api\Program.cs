using FlowCustom.Core.Interfaces;
using FlowCustom.Data;
using FlowCustom.Engine;
using FlowCustom.Nodes;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Register application services
builder.Services.AddSingleton<IWorkflowService, WorkflowRepository>();
builder.Services.AddSingleton<INodeRegistry, NodeRegistry>();
builder.Services.AddSingleton<IWorkflowEngine, WorkflowEngine>();
builder.Services.AddSingleton<ITriggerManager, TriggerManager>();
builder.Services.AddSingleton<IUserService, UserService>();
builder.Services.AddSingleton<IAuthService, AuthService>();
builder.Services.AddSingleton<ExecutionHistoryService>();
builder.Services.AddSingleton<MonitoringService>();

// Register HTTP client for nodes
builder.Services.AddHttpClient();

// Add JWT Authentication
var jwtSecret = builder.Configuration["JWT:Secret"] ?? "FlowCustomJwtSecretKey123456789";
var key = Encoding.ASCII.GetBytes(jwtSecret);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddAuthorization();

// Register nodes
builder.Services.AddTransient<HttpRequestNode>();
builder.Services.AddTransient<SetNode>();
builder.Services.AddTransient<WebhookTriggerNode>();
builder.Services.AddTransient<CronTriggerNode>();
builder.Services.AddTransient<FileWatcherTriggerNode>();
builder.Services.AddTransient<ApiTriggerNode>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Register nodes with the registry
var nodeRegistry = app.Services.GetRequiredService<INodeRegistry>();
nodeRegistry.RegisterNode<HttpRequestNode>();
nodeRegistry.RegisterNode<SetNode>();
nodeRegistry.RegisterNode<WebhookTriggerNode>();
nodeRegistry.RegisterNode<CronTriggerNode>();
nodeRegistry.RegisterNode<FileWatcherTriggerNode>();
nodeRegistry.RegisterNode<ApiTriggerNode>();

app.Run();

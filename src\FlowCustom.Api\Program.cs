using FlowCustom.Core.Interfaces;
using FlowCustom.Data;
using FlowCustom.Engine;
using FlowCustom.Nodes;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Register application services
builder.Services.AddSingleton<IWorkflowService, WorkflowRepository>();
builder.Services.AddSingleton<INodeRegistry, NodeRegistry>();
builder.Services.AddSingleton<IWorkflowEngine, WorkflowEngine>();
builder.Services.AddSingleton<ITriggerManager, TriggerManager>();

// Register HTTP client for nodes
builder.Services.AddHttpClient();

// Register nodes
builder.Services.AddTransient<HttpRequestNode>();
builder.Services.AddTransient<SetNode>();
builder.Services.AddTransient<WebhookTriggerNode>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthorization();

app.MapControllers();

// Register nodes with the registry
var nodeRegistry = app.Services.GetRequiredService<INodeRegistry>();
nodeRegistry.RegisterNode<HttpRequestNode>();
nodeRegistry.RegisterNode<SetNode>();
nodeRegistry.RegisterNode<WebhookTriggerNode>();

app.Run();

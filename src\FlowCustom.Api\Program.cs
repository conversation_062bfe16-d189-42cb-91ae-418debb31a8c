using FlowCustom.Core.Interfaces;
using FlowCustom.Data;
using FlowCustom.Engine;
using FlowCustom.Nodes;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Unicode;

// 设置控制台编码为UTF-8
Console.OutputEncoding = Encoding.UTF8;
Console.InputEncoding = Encoding.UTF8;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        options.JsonSerializerOptions.WriteIndented = true;
        options.JsonSerializerOptions.PropertyNamingPolicy = null; // 保持原始属性名
    });
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Register application services
builder.Services.AddSingleton<IWorkflowService, WorkflowRepository>();
builder.Services.AddSingleton<INodeRegistry, NodeRegistry>();
builder.Services.AddSingleton<IWorkflowEngine, WorkflowEngine>();
builder.Services.AddSingleton<ITriggerManager, TriggerManager>();
builder.Services.AddSingleton<IUserService, UserService>();
builder.Services.AddSingleton<IAuthService, AuthService>();
builder.Services.AddSingleton<ExecutionHistoryService>();
builder.Services.AddSingleton<IExecutionHistoryService, ExecutionHistoryService>();
builder.Services.AddSingleton<MonitoringService>();

// Register HTTP client for nodes
builder.Services.AddHttpClient();

// Add JWT Authentication
var jwtSecret = builder.Configuration["JWT:Secret"] ?? "FlowCustomJwtSecretKey123456789";
var key = Encoding.ASCII.GetBytes(jwtSecret);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddAuthorization();

// Register nodes
builder.Services.AddTransient<HttpRequestNode>();
builder.Services.AddTransient<SetNode>();
builder.Services.AddTransient<WebhookTriggerNode>();
builder.Services.AddTransient<CronTriggerNode>();
builder.Services.AddTransient<FileWatcherTriggerNode>();
builder.Services.AddTransient<ApiTriggerNode>();
builder.Services.AddTransient<ManualTriggerNode>();
builder.Services.AddTransient<ScriptExecutorNode>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Register nodes with the registry
var nodeRegistry = app.Services.GetRequiredService<INodeRegistry>();
nodeRegistry.RegisterNode<HttpRequestNode>();
nodeRegistry.RegisterNode<SetNode>();
nodeRegistry.RegisterNode<WebhookTriggerNode>();
nodeRegistry.RegisterNode<CronTriggerNode>();
nodeRegistry.RegisterNode<FileWatcherTriggerNode>();
nodeRegistry.RegisterNode<ApiTriggerNode>();
nodeRegistry.RegisterNode<ManualTriggerNode>();
nodeRegistry.RegisterNode<ScriptExecutorNode>();

// 添加一些示例日志用于测试
Task.Run(async () =>
{
    await Task.Delay(1000); // 等待1秒确保服务完全启动
    var executionHistoryService = app.Services.GetRequiredService<ExecutionHistoryService>();
    await executionHistoryService.AddSystemLogAsync("Info", "系统启动完成", "System");
    await executionHistoryService.AddSystemLogAsync("Info", "节点注册完成，共注册8个节点", "NodeRegistry");
    await executionHistoryService.AddSystemLogAsync("Warning", "这是一个警告日志示例", "TestService");
    await executionHistoryService.AddSystemLogAsync("Error", "这是一个错误日志示例", "TestService");
    await executionHistoryService.AddSystemLogAsync("Debug", "这是一个调试日志示例", "TestService");
});

app.Run();

# FlowCustom 插件开发指南

## 概述

FlowCustom 支持插件化的节点系统，允许开发者创建自定义节点来扩展工作流功能。本指南将详细介绍如何开发、测试和部署插件。

## 快速开始

### 1. 环境准备

- .NET 8.0 SDK
- Visual Studio 2022 或 VS Code
- FlowCustom.SDK NuGet 包

### 2. 创建插件项目

```bash
# 创建新的类库项目
dotnet new classlib -n MyAwesome.Plugin

# 添加 SDK 引用
dotnet add package FlowCustom.SDK
```

### 3. 实现插件类

```csharp
using FlowCustom.SDK;

public class MyAwesomePlugin : NodePluginBase
{
    public override string NodeType => "my-awesome-node";
    public override string DisplayName => "我的超棒节点";
    public override string Description => "这是一个超棒的自定义节点";
    
    protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
    {
        // 实现您的业务逻辑
        return NodeExecutionResult.Success(new Dictionary<string, object>
        {
            ["result"] = "Hello from my awesome plugin!"
        });
    }
}
```

## 详细开发指南

### 插件基础结构

#### 1. 继承 NodePluginBase

所有插件都必须继承 `NodePluginBase` 类：

```csharp
public class MyPlugin : NodePluginBase
{
    // 必须实现的属性
    public override string NodeType => "unique-node-type";
    public override string DisplayName => "显示名称";
    public override string Description => "节点描述";
    
    // 必须实现的方法
    protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
    {
        // 核心业务逻辑
    }
}
```

#### 2. 基本属性配置

```csharp
public override string NodeType => "company-category-name";  // 唯一标识符
public override string DisplayName => "用户友好的名称";
public override string Description => "详细的功能描述";
public override string Category => "分类名称";              // 用于分组
public override string Version => "1.0.0";                 // 版本号
public override string Author => "作者名称";
public override bool IsTrigger => false;                   // 是否为触发器
public override string Icon => "🔧";                       // 图标
```

### 参数定义

#### 1. 基本参数类型

```csharp
protected override List<ParameterDefinition> GetParameterDefinitions()
{
    return new List<ParameterDefinition>
    {
        // 文本参数
        new ParameterDefinition
        {
            Name = "text",
            DisplayName = "文本内容",
            Description = "输入文本内容",
            Type = ParameterType.String,
            Required = true,
            DefaultValue = ""
        },
        
        // 数字参数
        new ParameterDefinition
        {
            Name = "number",
            DisplayName = "数字",
            Type = ParameterType.Number,
            Required = false,
            DefaultValue = 0,
            Validation = new Dictionary<string, object>
            {
                ["min"] = 0,
                ["max"] = 100
            }
        },
        
        // 布尔参数
        new ParameterDefinition
        {
            Name = "enabled",
            DisplayName = "启用",
            Type = ParameterType.Boolean,
            DefaultValue = true
        },
        
        // 选择参数
        new ParameterDefinition
        {
            Name = "mode",
            DisplayName = "模式",
            Type = ParameterType.Select,
            Options = new List<ParameterOption>
            {
                new ParameterOption { Value = "fast", Label = "快速" },
                new ParameterOption { Value = "normal", Label = "普通" },
                new ParameterOption { Value = "detailed", Label = "详细" }
            }
        }
    };
}
```

#### 2. 高级参数类型

```csharp
// JSON 参数
new ParameterDefinition
{
    Name = "config",
    DisplayName = "配置",
    Type = ParameterType.Json,
    DefaultValue = "{}"
},

// 文件参数
new ParameterDefinition
{
    Name = "file",
    DisplayName = "文件",
    Type = ParameterType.File
},

// 多选参数
new ParameterDefinition
{
    Name = "tags",
    DisplayName = "标签",
    Type = ParameterType.MultiSelect,
    Options = new List<ParameterOption>
    {
        new ParameterOption { Value = "tag1", Label = "标签1" },
        new ParameterOption { Value = "tag2", Label = "标签2" }
    }
}
```

### 端点定义

#### 1. 输入端点

```csharp
protected override List<EndpointDefinition> GetInputDefinitions()
{
    return new List<EndpointDefinition>
    {
        new EndpointDefinition
        {
            Id = "main",
            Name = "主输入",
            Description = "主要数据输入",
            Type = EndpointType.Data,
            Required = true,
            Position = new EndpointPosition { Side = "top", Offset = 50 }
        },
        
        new EndpointDefinition
        {
            Id = "config",
            Name = "配置输入",
            Description = "配置参数输入",
            Type = EndpointType.Data,
            Required = false,
            Position = new EndpointPosition { Side = "left", Offset = 30 }
        }
    };
}
```

#### 2. 输出端点

```csharp
protected override List<EndpointDefinition> GetOutputDefinitions()
{
    return new List<EndpointDefinition>
    {
        new EndpointDefinition
        {
            Id = "success",
            Name = "成功",
            Type = EndpointType.Success,
            Position = new EndpointPosition { Side = "bottom", Offset = 30 }
        },
        
        new EndpointDefinition
        {
            Id = "error",
            Name = "错误",
            Type = EndpointType.Error,
            Position = new EndpointPosition { Side = "bottom", Offset = 70 }
        }
    };
}
```

### 核心执行逻辑

#### 1. 基本执行模式

```csharp
protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
{
    try
    {
        // 获取参数
        var text = GetParameter<string>(context, "text", "");
        var number = GetParameter<int>(context, "number", 0);
        var enabled = GetParameter<bool>(context, "enabled", true);
        
        // 记录日志
        Log($"开始处理: {text}");
        
        // 检查取消
        context.CancellationToken.ThrowIfCancellationRequested();
        
        // 执行业务逻辑
        var result = await ProcessDataAsync(text, number, enabled);
        
        // 返回成功结果
        return NodeExecutionResult.Success(result, "success");
    }
    catch (OperationCanceledException)
    {
        return NodeExecutionResult.Failure("操作被取消");
    }
    catch (Exception ex)
    {
        Log($"执行失败: {ex.Message}", LogLevel.Error);
        return NodeExecutionResult.Success(new Dictionary<string, object>
        {
            ["error"] = ex.Message
        }, "error");
    }
}
```

#### 2. 获取输入数据

```csharp
// 获取输入数据
var inputData = context.InputData;

// 获取特定输入
if (inputData.TryGetValue("main", out var mainInput))
{
    // 处理主输入数据
}

// 获取工作流变量
var variables = context.Variables;
```

### 验证逻辑

```csharp
protected override void OnValidate(NodeConfiguration configuration, 
    List<ValidationError> errors, List<ValidationWarning> warnings)
{
    // 验证必需参数
    if (!configuration.Parameters.ContainsKey("text") || 
        string.IsNullOrEmpty(configuration.Parameters["text"]?.ToString()))
    {
        errors.Add(new ValidationError
        {
            Field = "text",
            Message = "文本内容不能为空",
            Code = "REQUIRED_FIELD"
        });
    }
    
    // 添加警告
    if (configuration.Parameters.TryGetValue("number", out var numberObj) &&
        int.TryParse(numberObj?.ToString(), out var number) && number > 50)
    {
        warnings.Add(new ValidationWarning
        {
            Field = "number",
            Message = "数值较大，可能影响性能",
            Code = "PERFORMANCE_WARNING"
        });
    }
}
```

### 生命周期管理

```csharp
// 初始化
protected override void OnInitialize()
{
    Log("插件初始化完成");
    // 初始化资源
}

// 执行前
protected override Task OnBeforeExecute(NodeExecutionContext context)
{
    Log("准备执行");
    return Task.CompletedTask;
}

// 执行后
protected override Task OnAfterExecute(NodeExecutionContext context, NodeExecutionResult result)
{
    Log($"执行完成，成功: {result.Success}");
    return Task.CompletedTask;
}

// 资源清理
protected override void OnDispose()
{
    Log("清理插件资源");
    // 释放资源
}
```

## 插件清单文件

创建 `plugin.json` 文件：

```json
{
  "id": "MyCompany.MyPlugin",
  "name": "我的插件",
  "version": "1.0.0",
  "author": "我的公司",
  "description": "插件描述",
  "main": "MyCompany.MyPlugin.dll",
  "dependencies": [
    "FlowCustom.SDK@^1.0.0"
  ],
  "supportedFrameworks": ["net8.0"],
  "nodes": [
    {
      "type": "my-awesome-node",
      "class": "MyCompany.MyPlugin.MyAwesomePlugin"
    }
  ],
  "resources": {
    "frontend": "wwwroot/",
    "icons": "icons/",
    "localization": "locales/"
  }
}
```

## 前端组件开发

### 1. React 配置组件

```typescript
// components/MyAwesomeNodeConfig.tsx
import React from 'react';

interface MyAwesomeNodeConfigProps {
  node: WorkflowNode;
  onUpdate: (updates: Partial<WorkflowNode>) => void;
}

const MyAwesomeNodeConfig: React.FC<MyAwesomeNodeConfigProps> = ({ node, onUpdate }) => {
  const handleParameterChange = (name: string, value: any) => {
    onUpdate({
      parameters: {
        ...node.parameters,
        [name]: value
      }
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium">文本内容</label>
        <input
          type="text"
          value={node.parameters?.text || ''}
          onChange={(e) => handleParameterChange('text', e.target.value)}
          className="w-full px-3 py-2 border rounded"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium">数字</label>
        <input
          type="number"
          value={node.parameters?.number || 0}
          onChange={(e) => handleParameterChange('number', parseInt(e.target.value))}
          className="w-full px-3 py-2 border rounded"
        />
      </div>
    </div>
  );
};

export default MyAwesomeNodeConfig;
```

## 测试和调试

### 1. 单元测试

```csharp
[Test]
public async Task TestPluginExecution()
{
    var plugin = new MyAwesomePlugin();
    plugin.Initialize(serviceProvider);
    
    var context = new NodeExecutionContext
    {
        NodeId = "test-node",
        Configuration = new NodeConfiguration
        {
            Parameters = new Dictionary<string, object>
            {
                ["text"] = "test message"
            }
        },
        InputData = new Dictionary<string, object>(),
        CancellationToken = CancellationToken.None
    };
    
    var result = await plugin.ExecuteAsync(context);
    
    Assert.IsTrue(result.Success);
    Assert.IsNotNull(result.OutputData);
}
```

### 2. 集成测试

```csharp
[Test]
public async Task TestPluginInWorkflow()
{
    // 创建包含插件的工作流
    // 执行工作流
    // 验证结果
}
```

## 部署和发布

### 1. 编译插件

```bash
dotnet build -c Release
```

### 2. 创建插件包

```
MyPlugin/
├── MyPlugin.dll
├── plugin.json
├── wwwroot/
│   └── components/
├── icons/
└── locales/
```

### 3. 安装插件

1. 将插件文件夹复制到 `Runtime/plugins/` 目录
2. 重启应用或调用重新加载 API
3. 验证插件加载成功

## 最佳实践

1. **错误处理**: 始终实现适当的错误处理和日志记录
2. **性能**: 避免阻塞操作，使用异步方法
3. **取消支持**: 检查 CancellationToken 以支持操作取消
4. **资源管理**: 正确释放资源，实现 IDisposable
5. **版本兼容**: 遵循语义化版本规范
6. **文档**: 提供详细的参数和功能说明
7. **测试**: 编写全面的单元测试和集成测试

## 常见问题

### Q: 如何调试插件？
A: 可以在开发环境中直接调试，或者使用日志记录来跟踪执行过程。

### Q: 插件如何访问外部服务？
A: 通过依赖注入获取服务，或者在插件中直接创建 HTTP 客户端等。

### Q: 如何处理插件依赖？
A: 在 plugin.json 中声明依赖，插件加载器会自动处理。

### Q: 插件可以访问文件系统吗？
A: 可以，但建议在插件清单中声明所需权限。

## 示例插件

查看 `Templates/` 目录中的完整示例插件，了解更多实现细节。

using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace FlowCustom.Nodes;

/// <summary>
/// 循环节点
/// </summary>
public class LoopNode : INode
{
    private readonly ILogger<LoopNode> _logger;

    public LoopNode(ILogger<LoopNode> logger)
    {
        _logger = logger;
    }

    public string NodeType => "loop";

    public string DisplayName => "循环";

    public string Description => "对数组或指定次数进行循环处理";

    public string Category => "逻辑";

    public string Icon => "loop";

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "loopType",
                DisplayName = "循环类型",
                Description = "选择循环类型",
                Type = ParameterType.Select,
                Required = true,
                DefaultValue = "array",
                Options = new[] { "array", "count" }
            },
            new NodeParameterDefinition
            {
                Name = "arrayData",
                DisplayName = "数组数据",
                Description = "要循环处理的数组数据（JSON格式）",
                Type = ParameterType.Object,
                Required = false,
                DefaultValue = new object[] { 1, 2, 3, 4, 5 }
            },
            new NodeParameterDefinition
            {
                Name = "loopCount",
                DisplayName = "循环次数",
                Description = "指定循环的次数（当循环类型为count时使用）",
                Type = ParameterType.Number,
                Required = false,
                DefaultValue = 5
            },
            new NodeParameterDefinition
            {
                Name = "maxIterations",
                DisplayName = "最大迭代次数",
                Description = "防止无限循环的安全限制",
                Type = ParameterType.Number,
                Required = false,
                DefaultValue = 1000
            },
            new NodeParameterDefinition
            {
                Name = "batchSize",
                DisplayName = "批处理大小",
                Description = "每批处理的项目数量（0表示不分批）",
                Type = ParameterType.Number,
                Required = false,
                DefaultValue = 0
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node,
        FlowCustom.Core.Models.ExecutionContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var loopType = GetParameter<string>(node, "loopType", "array");
            var arrayData = GetParameter<object[]>(node, "arrayData", new object[] { 1, 2, 3, 4, 5 });
            var loopCount = GetParameter<int>(node, "loopCount", 5);
            var maxIterations = GetParameter<int>(node, "maxIterations", 1000);
            var batchSize = GetParameter<int>(node, "batchSize", 0);

            _logger.LogInformation("开始执行循环节点: {NodeId}, 类型: {LoopType}", node.Id, loopType);

            var results = new List<object>();
            var iterations = 0;

            if (loopType == "array")
            {
                if (arrayData == null || arrayData.Length == 0)
                {
                    _logger.LogWarning("数组数据为空，跳过循环: {NodeId}", node.Id);
                    return NodeExecutionResult.CreateSuccess(new Dictionary<string, object>
                    {
                        ["results"] = results,
                        ["totalIterations"] = 0,
                        ["loopType"] = loopType
                    });
                }

                // 数组循环
                if (batchSize > 0)
                {
                    // 批处理模式
                    for (int i = 0; i < arrayData.Length; i += batchSize)
                    {
                        if (iterations >= maxIterations)
                        {
                            _logger.LogWarning("达到最大迭代次数限制: {MaxIterations}", maxIterations);
                            break;
                        }

                        var batch = arrayData.Skip(i).Take(batchSize).ToArray();
                        var batchResult = await ProcessBatch(batch, iterations, context, cancellationToken);
                        results.Add(batchResult);
                        iterations++;
                    }
                }
                else
                {
                    // 逐项处理模式
                    for (int i = 0; i < arrayData.Length; i++)
                    {
                        if (iterations >= maxIterations)
                        {
                            _logger.LogWarning("达到最大迭代次数限制: {MaxIterations}", maxIterations);
                            break;
                        }

                        var item = arrayData[i];
                        var itemResult = await ProcessItem(item, i, context, cancellationToken);
                        results.Add(itemResult);
                        iterations++;
                    }
                }
            }
            else if (loopType == "count")
            {
                // 计数循环
                var actualCount = Math.Min(loopCount, maxIterations);
                
                for (int i = 0; i < actualCount; i++)
                {
                    var itemResult = await ProcessIteration(i, context, cancellationToken);
                    results.Add(itemResult);
                    iterations++;
                }
            }

            _logger.LogInformation("循环节点执行完成: {NodeId}, 迭代次数: {Iterations}", node.Id, iterations);

            var outputData = new Dictionary<string, object>
            {
                ["results"] = results,
                ["totalIterations"] = iterations,
                ["loopType"] = loopType,
                ["completedAt"] = DateTime.UtcNow,
                ["inputData"] = loopType == "array" ? arrayData : loopCount
            };

            return NodeExecutionResult.CreateSuccess(outputData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "循环节点执行失败: {NodeId}", node.Id);
            return NodeExecutionResult.CreateError($"循环节点执行失败: {ex.Message}");
        }
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.TryGetValue("loopType", out var loopTypeObj) || 
            string.IsNullOrWhiteSpace(loopTypeObj?.ToString()))
        {
            errors.Add("循环类型不能为空");
        }
        else
        {
            var loopType = loopTypeObj.ToString();
            if (!new[] { "array", "count" }.Contains(loopType))
            {
                errors.Add("不支持的循环类型");
            }
        }

        if (parameters.TryGetValue("maxIterations", out var maxIterObj))
        {
            if (!int.TryParse(maxIterObj?.ToString(), out var maxIter) || maxIter <= 0)
            {
                errors.Add("最大迭代次数必须是大于0的整数");
            }
        }

        if (parameters.TryGetValue("loopCount", out var countObj))
        {
            if (!int.TryParse(countObj?.ToString(), out var count) || count < 0)
            {
                errors.Add("循环次数必须是非负整数");
            }
        }

        return errors.Count == 0 ? ValidationResult.Success() : ValidationResult.Failure(errors.ToArray());
    }

    private async Task<object> ProcessBatch(object[] batch, int batchIndex, FlowCustom.Core.Models.ExecutionContext context, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        return new
        {
            batchIndex,
            batchSize = batch.Length,
            items = batch,
            processedAt = DateTime.UtcNow
        };
    }

    private async Task<object> ProcessItem(object item, int index, FlowCustom.Core.Models.ExecutionContext context, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        return new
        {
            index,
            item,
            processedAt = DateTime.UtcNow,
            contextData = context.GlobalData.Count
        };
    }

    private async Task<object> ProcessIteration(int iteration, FlowCustom.Core.Models.ExecutionContext context, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        return new
        {
            iteration,
            processedAt = DateTime.UtcNow,
            contextData = context.GlobalData.Count
        };
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                // 处理字符串类型
                if (typeof(T) == typeof(string))
                {
                    return (T)(object)(value?.ToString() ?? string.Empty);
                }

                // 处理整数类型
                if (typeof(T) == typeof(int))
                {
                    if (value is int intValue)
                        return (T)(object)intValue;
                    if (int.TryParse(value?.ToString(), out var parsedInt))
                        return (T)(object)parsedInt;
                }

                // 处理数组类型
                if (typeof(T) == typeof(object[]))
                {
                    if (value is object[] arrayValue)
                        return (T)(object)arrayValue;
                    if (value is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.Array)
                    {
                        var array = JsonSerializer.Deserialize<object[]>(jsonElement.GetRawText());
                        return (T)(object)(array ?? new object[0]);
                    }
                    if (value is IEnumerable<object> enumerable)
                        return (T)(object)enumerable.ToArray();
                    return (T)(object)new object[0];
                }

                // 其他类型尝试直接转换
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法转换参数 {ParameterName} 的值 '{Value}'，使用默认值", parameterName, value);
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

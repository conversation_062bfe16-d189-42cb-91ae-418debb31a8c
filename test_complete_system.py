#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统测试脚本
"""

import requests
import json
import uuid
import time

# API 基础URL
BASE_URL = "http://localhost:5053/api"

def test_nodes_api():
    """测试节点API"""
    print("🔍 测试节点API...")
    
    response = requests.get(f"{BASE_URL}/nodes")
    
    if response.status_code == 200:
        nodes = response.json()
        print(f"✅ 成功获取 {len(nodes)} 个节点类型")
        
        # 按分类显示节点
        categories = {}
        for node in nodes:
            category = node.get('category', '未分类')
            if category not in categories:
                categories[category] = []
            categories[category].append(node)
        
        for category, category_nodes in categories.items():
            print(f"\n📂 {category} ({len(category_nodes)}个节点):")
            for node in category_nodes:
                print(f"   - {node.get('nodeType', 'N/A')}: {node.get('displayName', 'N/A')}")
        
        # 检查新的逻辑节点
        logic_nodes = ['conditional', 'loop', 'data-transform']
        found_logic_nodes = []
        for node in nodes:
            if node.get('nodeType') in logic_nodes:
                found_logic_nodes.append(node.get('nodeType'))
        
        print(f"\n🧠 逻辑节点检查:")
        for node_type in logic_nodes:
            if node_type in found_logic_nodes:
                print(f"   ✅ {node_type}")
            else:
                print(f"   ❌ {node_type}")
        
        return len(nodes)
    else:
        print(f"❌ 获取节点失败: {response.status_code}")
        return 0

def test_workflow_creation():
    """测试工作流创建"""
    print("\n🔧 测试工作流创建...")
    
    workflow_data = {
        "name": "完整系统测试工作流",
        "description": "测试所有功能：连线箭头、保存、逻辑节点等"
    }
    
    response = requests.post(f"{BASE_URL}/workflow", json=workflow_data)
    
    if response.status_code == 201:
        workflow = response.json()
        workflow_id = workflow["id"]
        print(f"✅ 工作流创建成功，ID: {workflow_id}")
        print(f"   名称: {workflow['name']}")
        return workflow_id
    else:
        print(f"❌ 工作流创建失败: {response.status_code}")
        print(f"   响应: {response.text}")
        return None

def test_complex_workflow(workflow_id):
    """测试复杂工作流（包含逻辑节点）"""
    print(f"\n🚀 测试复杂工作流: {workflow_id}")
    
    # 创建节点
    manual_trigger_id = str(uuid.uuid4())
    conditional_id = str(uuid.uuid4())
    script_true_id = str(uuid.uuid4())
    script_false_id = str(uuid.uuid4())
    loop_id = str(uuid.uuid4())
    transform_id = str(uuid.uuid4())
    
    # 手动触发器
    manual_trigger = {
        "id": manual_trigger_id,
        "workflowId": workflow_id,
        "name": "手动触发器",
        "type": "manual-trigger",
        "position": {"x": 100, "y": 100},
        "parameters": {
            "buttonText": "开始复杂流程",
            "enabled": True
        },
        "isDisabled": False,
        "notes": ""
    }
    
    # 条件判断节点
    conditional = {
        "id": conditional_id,
        "workflowId": workflow_id,
        "name": "条件判断",
        "type": "conditional",
        "position": {"x": 300, "y": 100},
        "parameters": {
            "condition": "data.value > 5",
            "inputData": {"value": 8},
            "trueOutput": {"result": "大于5", "path": "true_branch"},
            "falseOutput": {"result": "小于等于5", "path": "false_branch"}
        },
        "isDisabled": False,
        "notes": "判断数值大小"
    }
    
    # True分支脚本
    script_true = {
        "id": script_true_id,
        "workflowId": workflow_id,
        "name": "True分支处理",
        "type": "script-executor",
        "position": {"x": 500, "y": 50},
        "parameters": {
            "scriptType": "python",
            "script": "print('执行True分支')\nprint('数值大于5，执行特殊处理')\nresult = {'branch': 'true', 'processed': True}",
            "timeout": 30,
            "captureOutput": True
        },
        "isDisabled": False,
        "notes": "True分支的处理逻辑"
    }
    
    # False分支脚本
    script_false = {
        "id": script_false_id,
        "workflowId": workflow_id,
        "name": "False分支处理",
        "type": "script-executor",
        "position": {"x": 500, "y": 150},
        "parameters": {
            "scriptType": "javascript",
            "script": "console.log('执行False分支'); console.log('数值小于等于5，执行默认处理'); const result = {branch: 'false', processed: true};",
            "timeout": 30,
            "captureOutput": True
        },
        "isDisabled": False,
        "notes": "False分支的处理逻辑"
    }
    
    # 循环节点
    loop = {
        "id": loop_id,
        "workflowId": workflow_id,
        "name": "数据循环处理",
        "type": "loop",
        "position": {"x": 700, "y": 100},
        "parameters": {
            "loopType": "array",
            "arrayData": [1, 2, 3, 4, 5],
            "maxIterations": 10,
            "batchSize": 0
        },
        "isDisabled": False,
        "notes": "循环处理数组数据"
    }
    
    # 数据转换节点
    transform = {
        "id": transform_id,
        "workflowId": workflow_id,
        "name": "数据转换",
        "type": "data-transform",
        "position": {"x": 900, "y": 100},
        "parameters": {
            "transformType": "map",
            "inputData": [
                {"name": "张三", "age": 25, "score": 85},
                {"name": "李四", "age": 30, "score": 92},
                {"name": "王五", "age": 28, "score": 78}
            ],
            "transformExpression": "item.name + ' (年龄:' + item.age + ', 分数:' + item.score + ')'"
        },
        "isDisabled": False,
        "notes": "转换用户数据格式"
    }
    
    # 创建连接（带箭头）
    connections = [
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": manual_trigger_id,
            "targetNodeId": conditional_id,
            "sourceOutput": "main",
            "targetInput": "main"
        },
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": conditional_id,
            "targetNodeId": script_true_id,
            "sourceOutput": "true",
            "targetInput": "main"
        },
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": conditional_id,
            "targetNodeId": script_false_id,
            "sourceOutput": "false",
            "targetInput": "main"
        },
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": script_true_id,
            "targetNodeId": loop_id,
            "sourceOutput": "main",
            "targetInput": "main"
        },
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": script_false_id,
            "targetNodeId": loop_id,
            "sourceOutput": "main",
            "targetInput": "main"
        },
        {
            "id": str(uuid.uuid4()),
            "workflowId": workflow_id,
            "sourceNodeId": loop_id,
            "targetNodeId": transform_id,
            "sourceOutput": "main",
            "targetInput": "main"
        }
    ]
    
    # 获取当前工作流
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 获取工作流失败: {response.status_code}")
        return False
    
    workflow = response.json()
    
    # 更新工作流
    workflow["nodes"] = [manual_trigger, conditional, script_true, script_false, loop, transform]
    workflow["connections"] = connections
    workflow["isActive"] = True
    
    print("更新工作流，添加复杂节点和连接...")
    update_response = requests.put(f"{BASE_URL}/workflow/{workflow_id}", json=workflow)
    
    if update_response.status_code == 200:
        print("✅ 复杂工作流更新成功")
        print(f"   节点数量: {len(workflow['nodes'])}")
        print(f"   连接数量: {len(workflow['connections'])}")
        return True
    else:
        print(f"❌ 工作流更新失败: {update_response.status_code}")
        print(f"   响应: {update_response.text}")
        return False

def test_workflow_execution(workflow_id):
    """测试工作流执行"""
    print(f"\n🚀 测试工作流执行: {workflow_id}")
    
    response = requests.post(f"{BASE_URL}/trigger/manual/{workflow_id}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 工作流触发成功")
        execution_id = result.get("executionId")
        print(f"   执行ID: {execution_id}")
        
        # 等待执行完成
        print("等待执行完成...")
        time.sleep(8)
        
        # 获取执行结果
        history_response = requests.get(f"{BASE_URL}/workflow/{workflow_id}/history")
        if history_response.status_code == 200:
            executions = history_response.json()
            if executions:
                latest_execution = executions[0]
                print("✅ 执行结果:")
                print(f"   状态: {latest_execution.get('status', 'N/A')}")
                print(f"   开始时间: {latest_execution.get('startedAt', 'N/A')}")
                print(f"   结束时间: {latest_execution.get('completedAt', 'N/A')}")
                
                # 显示节点执行结果
                node_results = latest_execution.get("nodeResults", {})
                if node_results:
                    print(f"\n📊 节点执行详情 ({len(node_results)}个节点):")
                    for node_id, result in node_results.items():
                        print(f"   节点 {node_id[:8]}...")
                        print(f"     状态: {result.get('status', 'N/A')}")
                        if "outputData" in result and result["outputData"]:
                            output_data = result["outputData"]
                            if "exitCode" in output_data:
                                print(f"     退出码: {output_data['exitCode']}")
                return True
        
        return False
    else:
        print(f"❌ 工作流触发失败: {response.status_code}")
        return False

def main():
    print("🚀 开始完整系统测试...")
    print("="*60)
    
    try:
        # 1. 测试节点API
        node_count = test_nodes_api()
        
        # 2. 测试工作流创建
        workflow_id = test_workflow_creation()
        if not workflow_id:
            return
        
        # 3. 测试复杂工作流
        if test_complex_workflow(workflow_id):
            # 4. 测试工作流执行
            test_workflow_execution(workflow_id)
        
        print("\n" + "="*60)
        print("✅ 完整系统测试完成！")
        print(f"📝 测试工作流ID: {workflow_id}")
        print(f"🔗 前端地址: http://localhost:5173")
        print(f"🔗 后端地址: http://localhost:5053")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

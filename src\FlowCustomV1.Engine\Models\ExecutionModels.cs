using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;

namespace FlowCustomV1.Engine.Models;

/// <summary>
/// 执行图
/// 表示工作流的执行结构和依赖关系
/// </summary>
public class ExecutionGraph
{
    /// <summary>
    /// 执行节点字典
    /// </summary>
    public Dictionary<string, ExecutionNode> Nodes { get; set; } = new();

    /// <summary>
    /// 执行连接列表
    /// </summary>
    public List<ExecutionConnection> Connections { get; set; } = new();
}

/// <summary>
/// 执行节点
/// 包含节点的执行信息和依赖关系
/// </summary>
public class ExecutionNode
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 节点名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 节点插件
    /// </summary>
    public INodePlugin? Plugin { get; set; }

    /// <summary>
    /// 节点配置
    /// </summary>
    public NodeConfiguration Configuration { get; set; } = new();

    /// <summary>
    /// 依赖的节点ID列表
    /// </summary>
    public List<string> Dependencies { get; set; } = new();

    /// <summary>
    /// 依赖此节点的节点ID列表
    /// </summary>
    public List<string> Dependents { get; set; } = new();

    /// <summary>
    /// 执行级别（用于层级执行）
    /// </summary>
    public int Level { get; set; } = 0;

    /// <summary>
    /// 是否已执行
    /// </summary>
    public bool IsExecuted { get; set; } = false;

    /// <summary>
    /// 执行状态
    /// </summary>
    public NodeExecutionStatus Status { get; set; } = NodeExecutionStatus.Pending;

    /// <summary>
    /// 输出数据
    /// </summary>
    public Dictionary<string, object> OutputData { get; set; } = new();
}

/// <summary>
/// 执行连接
/// 表示节点间的数据流连接
/// </summary>
public class ExecutionConnection
{
    /// <summary>
    /// 源节点ID
    /// </summary>
    public string SourceNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string TargetNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 源输出端点
    /// </summary>
    public string SourceOutput { get; set; } = string.Empty;

    /// <summary>
    /// 目标输入端点
    /// </summary>
    public string TargetInput { get; set; } = string.Empty;

    /// <summary>
    /// 连接条件
    /// </summary>
    public string? Condition { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 执行层级
/// 用于层级并行执行
/// </summary>
public class ExecutionLevel
{
    /// <summary>
    /// 层级编号
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// 该层级的节点列表
    /// </summary>
    public List<ExecutionNode> Nodes { get; set; } = new();

    /// <summary>
    /// 是否可以并行执行
    /// </summary>
    public bool CanExecuteInParallel { get; set; } = true;

    /// <summary>
    /// 执行状态
    /// </summary>
    public ExecutionLevelStatus Status { get; set; } = ExecutionLevelStatus.Pending;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan? Duration => EndTime?.Subtract(StartTime ?? DateTime.UtcNow);
}

/// <summary>
/// 执行上下文
/// 包含执行过程中的全局信息
/// </summary>
public class WorkflowExecutionContext
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流定义
    /// </summary>
    public WorkflowDefinition Workflow { get; set; } = new();

    /// <summary>
    /// 执行图
    /// </summary>
    public ExecutionGraph Graph { get; set; } = new();

    /// <summary>
    /// 全局变量
    /// </summary>
    public Dictionary<string, object> Variables { get; set; } = new();

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 触发数据
    /// </summary>
    public Dictionary<string, object> TriggerData { get; set; } = new();

    /// <summary>
    /// 节点输出数据
    /// </summary>
    public Dictionary<string, Dictionary<string, object>> NodeOutputs { get; set; } = new();

    /// <summary>
    /// 已执行的节点
    /// </summary>
    public HashSet<string> ExecutedNodes { get; set; } = new();

    /// <summary>
    /// 执行选项
    /// </summary>
    public ExecutionOptions Options { get; set; } = new();

    /// <summary>
    /// 取消令牌
    /// </summary>
    public CancellationToken CancellationToken { get; set; } = default;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 触发者
    /// </summary>
    public string? TriggeredBy { get; set; }

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionStatus Status { get; set; } = WorkflowExecutionStatus.Pending;
}

/// <summary>
/// 节点执行统计
/// </summary>
public class NodeExecutionStats
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 执行次数
    /// </summary>
    public int ExecutionCount { get; set; } = 0;

    /// <summary>
    /// 成功次数
    /// </summary>
    public int SuccessCount { get; set; } = 0;

    /// <summary>
    /// 失败次数
    /// </summary>
    public int FailureCount { get; set; } = 0;

    /// <summary>
    /// 平均执行时间
    /// </summary>
    public TimeSpan AverageExecutionTime { get; set; } = TimeSpan.Zero;

    /// <summary>
    /// 最长执行时间
    /// </summary>
    public TimeSpan MaxExecutionTime { get; set; } = TimeSpan.Zero;

    /// <summary>
    /// 最短执行时间
    /// </summary>
    public TimeSpan MinExecutionTime { get; set; } = TimeSpan.MaxValue;

    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime? LastExecutionTime { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => ExecutionCount > 0 ? (double)SuccessCount / ExecutionCount : 0;
}

/// <summary>
/// 工作流执行统计
/// </summary>
public class WorkflowExecutionStats
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 总执行次数
    /// </summary>
    public int TotalExecutions { get; set; } = 0;

    /// <summary>
    /// 成功执行次数
    /// </summary>
    public int SuccessfulExecutions { get; set; } = 0;

    /// <summary>
    /// 失败执行次数
    /// </summary>
    public int FailedExecutions { get; set; } = 0;

    /// <summary>
    /// 平均执行时间
    /// </summary>
    public TimeSpan AverageExecutionTime { get; set; } = TimeSpan.Zero;

    /// <summary>
    /// 节点执行统计
    /// </summary>
    public List<NodeExecutionStats> NodeStats { get; set; } = new();

    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime? LastExecutionTime { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions : 0;
}

/// <summary>
/// 执行层级状态
/// </summary>
public enum ExecutionLevelStatus
{
    /// <summary>
    /// 等待中
    /// </summary>
    Pending = 0,

    /// <summary>
    /// 运行中
    /// </summary>
    Running = 1,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 2,

    /// <summary>
    /// 失败
    /// </summary>
    Failed = 3,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 4
}

namespace FlowCustom.Core.Models;

/// <summary>
/// 工作流执行实例
/// </summary>
public class WorkflowExecution
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid WorkflowId { get; set; }
    public ExecutionStatus Status { get; set; } = ExecutionStatus.Running;
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    public DateTime? FinishedAt { get; set; }
    public string? Error { get; set; }
    public string? TriggeredBy { get; set; }
    public Dictionary<string, object> InputData { get; set; } = new();
    public Dictionary<string, object> OutputData { get; set; } = new();
    
    /// <summary>
    /// 节点执行记录
    /// </summary>
    public List<NodeExecution> NodeExecutions { get; set; } = new();
}

/// <summary>
/// 节点执行记录
/// </summary>
public class NodeExecution
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid ExecutionId { get; set; }
    public Guid NodeId { get; set; }
    public string NodeName { get; set; } = string.Empty;
    public string NodeType { get; set; } = string.Empty;
    public ExecutionStatus Status { get; set; } = ExecutionStatus.Running;
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    public DateTime? FinishedAt { get; set; }
    public TimeSpan? Duration => FinishedAt?.Subtract(StartedAt);
    public string? Error { get; set; }
    public Dictionary<string, object> InputData { get; set; } = new();
    public Dictionary<string, object> OutputData { get; set; } = new();
    public int RetryCount { get; set; } = 0;
}

/// <summary>
/// 执行状态枚举
/// </summary>
public enum ExecutionStatus
{
    Waiting,
    Running,
    Success,
    Error,
    Cancelled,
    Timeout
}

/// <summary>
/// 执行上下文
/// </summary>
public class ExecutionContext
{
    public Guid ExecutionId { get; set; }
    public Guid WorkflowId { get; set; }
    public Dictionary<string, object> Variables { get; set; } = new();
    public Dictionary<string, object> GlobalData { get; set; } = new();
    public CancellationToken CancellationToken { get; set; }
    
    /// <summary>
    /// 获取前置节点的输出数据
    /// </summary>
    public Dictionary<Guid, Dictionary<string, object>> PreviousNodeOutputs { get; set; } = new();
}

/// <summary>
/// 节点执行结果
/// </summary>
public class NodeExecutionResult
{
    public bool Success { get; set; }
    public Dictionary<string, object> OutputData { get; set; } = new();
    public string? Error { get; set; }
    public bool ShouldContinue { get; set; } = true;
    
    public static NodeExecutionResult CreateSuccess(Dictionary<string, object>? outputData = null)
    {
        return new NodeExecutionResult
        {
            Success = true,
            OutputData = outputData ?? new Dictionary<string, object>()
        };
    }
    
    public static NodeExecutionResult CreateError(string error)
    {
        return new NodeExecutionResult
        {
            Success = false,
            Error = error,
            ShouldContinue = false
        };
    }
}

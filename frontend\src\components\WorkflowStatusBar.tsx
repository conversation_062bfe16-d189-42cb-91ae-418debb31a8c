import React from 'react';

interface WorkflowStatusBarProps {
  nodeCount: number;
  connectionCount: number;
  selectedNodeId?: string;
  isTestRunning?: boolean;
  lastSaved?: Date;
}

const WorkflowStatusBar: React.FC<WorkflowStatusBarProps> = ({
  nodeCount,
  connectionCount,
  selectedNodeId,
  isTestRunning,
  lastSaved
}) => {
  return (
    <div className="coze-card border-0 rounded-none border-t" style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
      <div className="px-6 py-2 flex items-center justify-between text-sm">
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4" style={{ color: 'var(--coze-text-muted)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <span style={{ color: 'var(--coze-text-secondary)' }}>
              {nodeCount} 个节点
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4" style={{ color: 'var(--coze-text-muted)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            <span style={{ color: 'var(--coze-text-secondary)' }}>
              {connectionCount} 个连接
            </span>
          </div>

          {selectedNodeId && (
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4" style={{ color: 'var(--coze-primary)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              <span style={{ color: 'var(--coze-primary)' }}>
                已选择节点
              </span>
            </div>
          )}

          {isTestRunning && (
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4 animate-spin" style={{ color: 'var(--coze-warning)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span style={{ color: 'var(--coze-warning)' }}>
                试运行中...
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-6">
          {lastSaved && (
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4" style={{ color: 'var(--coze-text-muted)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span style={{ color: 'var(--coze-text-secondary)' }}>
                上次保存: {lastSaved.toLocaleTimeString()}
              </span>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <span style={{ color: 'var(--coze-text-secondary)' }}>
              已连接
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowStatusBar;

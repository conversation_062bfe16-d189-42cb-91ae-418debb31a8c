using FlowCustom.Core.Models;
using FlowCustom.Data;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Engine;

/// <summary>
/// 实时监控服务
/// </summary>
public class MonitoringService
{
    private readonly ExecutionHistoryService _executionHistoryService;
    private readonly ILogger<MonitoringService> _logger;
    private readonly ConcurrentDictionary<Guid, WorkflowExecution> _activeExecutions = new();
    private readonly Timer _metricsTimer;

    public MonitoringService(
        ExecutionHistoryService executionHistoryService,
        ILogger<MonitoringService> logger)
    {
        _executionHistoryService = executionHistoryService;
        _logger = logger;
        
        // 每分钟收集一次指标
        _metricsTimer = new Timer(CollectMetrics, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    /// <summary>
    /// 开始监控执行
    /// </summary>
    public async Task StartMonitoringAsync(WorkflowExecution execution)
    {
        _activeExecutions[execution.Id] = execution;
        
        await LogExecutionEventAsync(execution.Id, "ExecutionStarted", new Dictionary<string, object>
        {
            ["workflowId"] = execution.WorkflowId,
            ["triggeredBy"] = execution.TriggeredBy ?? "unknown",
            ["inputDataSize"] = execution.InputData.Count
        });

        _logger.LogInformation("Started monitoring execution: {ExecutionId}", execution.Id);
    }

    /// <summary>
    /// 更新执行状态
    /// </summary>
    public async Task UpdateExecutionAsync(WorkflowExecution execution)
    {
        _activeExecutions[execution.Id] = execution;
        
        await LogExecutionEventAsync(execution.Id, "ExecutionUpdated", new Dictionary<string, object>
        {
            ["status"] = execution.Status.ToString(),
            ["nodeExecutions"] = execution.NodeExecutions.Count
        });

        // 如果执行完成，保存到历史记录
        if (execution.Status != ExecutionStatus.Running && execution.Status != ExecutionStatus.Waiting)
        {
            await CompleteMonitoringAsync(execution);
        }
    }

    /// <summary>
    /// 完成监控
    /// </summary>
    public async Task CompleteMonitoringAsync(WorkflowExecution execution)
    {
        _activeExecutions.TryRemove(execution.Id, out _);
        await _executionHistoryService.SaveExecutionAsync(execution);
        
        await LogExecutionEventAsync(execution.Id, "ExecutionCompleted", new Dictionary<string, object>
        {
            ["status"] = execution.Status.ToString(),
            ["duration"] = execution.FinishedAt?.Subtract(execution.StartedAt).TotalMilliseconds ?? 0,
            ["nodeCount"] = execution.NodeExecutions.Count,
            ["outputDataSize"] = execution.OutputData.Count
        });

        _logger.LogInformation("Completed monitoring execution: {ExecutionId} with status: {Status}", 
            execution.Id, execution.Status);
    }

    /// <summary>
    /// 记录节点执行事件
    /// </summary>
    public async Task LogNodeExecutionAsync(Guid executionId, NodeExecution nodeExecution)
    {
        await LogExecutionEventAsync(executionId, "NodeExecution", new Dictionary<string, object>
        {
            ["nodeId"] = nodeExecution.NodeId,
            ["nodeName"] = nodeExecution.NodeName,
            ["nodeType"] = nodeExecution.NodeType,
            ["status"] = nodeExecution.Status.ToString(),
            ["duration"] = nodeExecution.Duration?.TotalMilliseconds ?? 0,
            ["retryCount"] = nodeExecution.RetryCount
        }, nodeExecution.NodeId);
    }

    /// <summary>
    /// 记录错误
    /// </summary>
    public async Task LogErrorAsync(Guid executionId, string error, Guid? nodeId = null, Dictionary<string, object>? context = null)
    {
        await LogExecutionEventAsync(executionId, "Error", new Dictionary<string, object>
        {
            ["error"] = error,
            ["context"] = context ?? new Dictionary<string, object>()
        }, nodeId, "Error");

        _logger.LogError("Execution error in {ExecutionId}: {Error}", executionId, error);
    }

    /// <summary>
    /// 记录警告
    /// </summary>
    public async Task LogWarningAsync(Guid executionId, string warning, Guid? nodeId = null, Dictionary<string, object>? context = null)
    {
        await LogExecutionEventAsync(executionId, "Warning", new Dictionary<string, object>
        {
            ["warning"] = warning,
            ["context"] = context ?? new Dictionary<string, object>()
        }, nodeId, "Warning");

        _logger.LogWarning("Execution warning in {ExecutionId}: {Warning}", executionId, warning);
    }

    /// <summary>
    /// 获取活动执行列表
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetActiveExecutionsAsync()
    {
        await Task.CompletedTask;
        return _activeExecutions.Values.ToList();
    }

    /// <summary>
    /// 获取系统指标
    /// </summary>
    public async Task<SystemMetrics> GetSystemMetricsAsync()
    {
        var activeExecutions = _activeExecutions.Values.ToList();
        var statistics = await _executionHistoryService.GetExecutionStatisticsAsync();

        return new SystemMetrics
        {
            ActiveExecutions = activeExecutions.Count,
            TotalExecutionsToday = statistics.TotalExecutions,
            SuccessRate = statistics.SuccessRate,
            FailureRate = statistics.FailureRate,
            AverageExecutionTime = statistics.AverageExecutionTime,
            MemoryUsage = GC.GetTotalMemory(false),
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 记录执行事件
    /// </summary>
    private async Task LogExecutionEventAsync(
        Guid executionId, 
        string eventType, 
        Dictionary<string, object> data,
        Guid? nodeId = null,
        string level = "Info")
    {
        var log = new ExecutionLog
        {
            ExecutionId = executionId,
            NodeId = nodeId,
            Level = level,
            Message = eventType,
            Details = System.Text.Json.JsonSerializer.Serialize(data),
            Metadata = data
        };

        await _executionHistoryService.LogExecutionAsync(executionId, log);
    }

    /// <summary>
    /// 收集系统指标
    /// </summary>
    private async void CollectMetrics(object? state)
    {
        try
        {
            var metrics = await GetSystemMetricsAsync();
            _logger.LogInformation("System metrics: Active={ActiveExecutions}, Memory={MemoryUsage}MB", 
                metrics.ActiveExecutions, metrics.MemoryUsage / 1024 / 1024);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting system metrics");
        }
    }

    public void Dispose()
    {
        _metricsTimer?.Dispose();
    }
}

/// <summary>
/// 系统指标
/// </summary>
public class SystemMetrics
{
    public int ActiveExecutions { get; set; }
    public int TotalExecutionsToday { get; set; }
    public double SuccessRate { get; set; }
    public double FailureRate { get; set; }
    public double AverageExecutionTime { get; set; }
    public long MemoryUsage { get; set; }
    public DateTime Timestamp { get; set; }
}

using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustomV1.Api.Controllers;

/// <summary>
/// 插件管理控制器
/// 提供插件的查询、管理和配置功能
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class PluginsController : ControllerBase
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<PluginsController> _logger;

    public PluginsController(
        IPluginManager pluginManager,
        ILogger<PluginsController> logger)
    {
        _pluginManager = pluginManager ?? throw new ArgumentNullException(nameof(pluginManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 获取所有已加载的插件
    /// </summary>
    /// <returns>插件列表</returns>
    [HttpGet]
    public ActionResult<IEnumerable<object>> GetPlugins()
    {
        try
        {
            var plugins = _pluginManager.GetAllPlugins();
            var result = plugins.Select(plugin => new
            {
                nodeType = plugin.NodeType,
                displayName = plugin.DisplayName,
                description = plugin.Description,
                category = plugin.Category,
                version = plugin.Version,
                author = plugin.Author,
                isTrigger = plugin.IsTrigger,
                icon = plugin.Icon,
                parameters = plugin.GetParameterDefinitions().Select(p => new
                {
                    name = p.Name,
                    displayName = p.DisplayName,
                    description = p.Description,
                    type = p.Type.ToString(),
                    required = p.Required,
                    defaultValue = p.DefaultValue,
                    options = p.Options.Select(o => new
                    {
                        value = o.Value,
                        label = o.Label,
                        description = o.Description
                    }).ToList()
                }).ToList(),
                inputs = plugin.GetInputDefinitions().Select(i => new
                {
                    id = i.Id,
                    name = i.Name,
                    description = i.Description,
                    type = i.Type.ToString(),
                    dataType = i.DataType,
                    required = i.Required,
                    position = new { side = i.Position.Side, offset = i.Position.Offset }
                }).ToList(),
                outputs = plugin.GetOutputDefinitions().Select(o => new
                {
                    id = o.Id,
                    name = o.Name,
                    description = o.Description,
                    type = o.Type.ToString(),
                    dataType = o.DataType,
                    required = o.Required,
                    position = new { side = o.Position.Side, offset = o.Position.Offset }
                }).ToList()
            }).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取插件列表失败");
            return StatusCode(500, new { message = "获取插件列表失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取特定插件信息
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>插件信息</returns>
    [HttpGet("{nodeType}")]
    public ActionResult<object> GetPlugin(string nodeType)
    {
        try
        {
            var plugin = _pluginManager.GetPlugin(nodeType);
            if (plugin == null)
            {
                return NotFound(new { message = $"插件 '{nodeType}' 不存在" });
            }

            var result = new
            {
                nodeType = plugin.NodeType,
                displayName = plugin.DisplayName,
                description = plugin.Description,
                category = plugin.Category,
                version = plugin.Version,
                author = plugin.Author,
                isTrigger = plugin.IsTrigger,
                icon = plugin.Icon,
                parameters = plugin.GetParameterDefinitions().Select(p => new
                {
                    name = p.Name,
                    displayName = p.DisplayName,
                    description = p.Description,
                    type = p.Type.ToString(),
                    required = p.Required,
                    defaultValue = p.DefaultValue,
                    group = p.Group,
                    order = p.Order,
                    hidden = p.Hidden,
                    helpText = p.HelpText,
                    placeholder = p.Placeholder,
                    options = p.Options.Select(o => new
                    {
                        value = o.Value,
                        label = o.Label,
                        description = o.Description,
                        icon = o.Icon,
                        disabled = o.Disabled,
                        group = o.Group
                    }).ToList(),
                    validation = p.Validation
                }).ToList(),
                inputs = plugin.GetInputDefinitions().Select(i => new
                {
                    id = i.Id,
                    name = i.Name,
                    description = i.Description,
                    type = i.Type.ToString(),
                    dataType = i.DataType,
                    required = i.Required,
                    position = new { side = i.Position.Side, offset = i.Position.Offset },
                    defaultValue = i.DefaultValue,
                    validation = i.Validation,
                    style = new
                    {
                        color = i.Style.Color,
                        size = i.Style.Size,
                        shape = i.Style.Shape,
                        borderWidth = i.Style.BorderWidth,
                        borderColor = i.Style.BorderColor
                    }
                }).ToList(),
                outputs = plugin.GetOutputDefinitions().Select(o => new
                {
                    id = o.Id,
                    name = o.Name,
                    description = o.Description,
                    type = o.Type.ToString(),
                    dataType = o.DataType,
                    required = o.Required,
                    position = new { side = o.Position.Side, offset = o.Position.Offset },
                    defaultValue = o.DefaultValue,
                    validation = o.Validation,
                    style = new
                    {
                        color = o.Style.Color,
                        size = o.Style.Size,
                        shape = o.Style.Shape,
                        borderWidth = o.Style.BorderWidth,
                        borderColor = o.Style.BorderColor
                    }
                }).ToList()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取插件信息失败: {NodeType}", nodeType);
            return StatusCode(500, new { message = "获取插件信息失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取插件的节点定义
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>节点定义</returns>
    [HttpGet("{nodeType}/definition")]
    public ActionResult<object> GetPluginDefinition(string nodeType)
    {
        try
        {
            var plugin = _pluginManager.GetPlugin(nodeType);
            if (plugin == null)
            {
                return NotFound(new { message = $"插件 '{nodeType}' 不存在" });
            }

            var definition = new
            {
                nodeType = plugin.NodeType,
                displayName = plugin.DisplayName,
                description = plugin.Description,
                category = plugin.Category,
                version = plugin.Version,
                author = plugin.Author,
                isTrigger = plugin.IsTrigger,
                icon = plugin.Icon,
                parameters = plugin.GetParameterDefinitions(),
                inputs = plugin.GetInputDefinitions(),
                outputs = plugin.GetOutputDefinitions()
            };

            return Ok(definition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取插件定义失败: {NodeType}", nodeType);
            return StatusCode(500, new { message = "获取插件定义失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 验证节点配置
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="configuration">节点配置</param>
    /// <returns>验证结果</returns>
    [HttpPost("{nodeType}/validate")]
    public async Task<ActionResult<object>> ValidateNodeConfiguration(
        string nodeType, 
        [FromBody] NodeConfiguration configuration)
    {
        try
        {
            var plugin = _pluginManager.GetPlugin(nodeType);
            if (plugin == null)
            {
                return NotFound(new { message = $"插件 '{nodeType}' 不存在" });
            }

            var validationResult = await plugin.ValidateAsync(configuration);

            var result = new
            {
                isValid = validationResult.IsValid,
                nodeType = validationResult.NodeType,
                validatedParameters = validationResult.ValidatedParameters,
                errors = validationResult.Errors.Select(e => new
                {
                    code = e.Code,
                    message = e.Message,
                    field = e.Field,
                    value = e.Value,
                    severity = e.Severity.ToString()
                }).ToList(),
                warnings = validationResult.Warnings.Select(w => new
                {
                    code = w.Code,
                    message = w.Message,
                    field = w.Field,
                    value = w.Value
                }).ToList()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证节点配置失败: {NodeType}", nodeType);
            return StatusCode(500, new { message = "验证节点配置失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取插件信息
    /// </summary>
    /// <returns>插件信息列表</returns>
    [HttpGet("info")]
    public ActionResult<IEnumerable<object>> GetPluginInfos()
    {
        try
        {
            var pluginInfos = _pluginManager.GetPluginInfos();
            var result = pluginInfos.Select(info => new
            {
                id = info.Id,
                name = info.Name,
                description = info.Description,
                version = info.Version,
                author = info.Author,
                path = info.Path,
                assemblyName = info.AssemblyName,
                isLoaded = info.IsLoaded,
                loadedAt = info.LoadedAt,
                nodeTypes = info.NodeTypes,
                dependencies = info.Dependencies,
                tags = info.Tags,
                metadata = info.Metadata
            }).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取插件信息失败");
            return StatusCode(500, new { message = "获取插件信息失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 重新加载插件
    /// </summary>
    /// <returns>加载结果</returns>
    [HttpPost("reload")]
    public async Task<ActionResult<object>> ReloadPlugins()
    {
        try
        {
            _logger.LogInformation("开始重新加载插件...");

            var loadedCount = await _pluginManager.LoadPluginsAsync();

            var result = new
            {
                success = true,
                loadedCount = loadedCount,
                message = $"成功重新加载 {loadedCount} 个插件",
                timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("插件重新加载完成: {LoadedCount} 个插件", loadedCount);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载插件失败");
            return StatusCode(500, new
            {
                success = false,
                message = "重新加载插件失败",
                error = ex.Message,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 获取插件分类
    /// </summary>
    /// <returns>分类列表</returns>
    [HttpGet("categories")]
    public ActionResult<IEnumerable<object>> GetCategories()
    {
        try
        {
            var plugins = _pluginManager.GetAllPlugins();
            var categories = plugins
                .GroupBy(p => p.Category)
                .Select(g => new
                {
                    category = g.Key,
                    count = g.Count(),
                    plugins = g.Select(p => new
                    {
                        nodeType = p.NodeType,
                        displayName = p.DisplayName,
                        isTrigger = p.IsTrigger,
                        icon = p.Icon
                    }).ToList()
                })
                .OrderBy(c => c.category)
                .ToList();

            return Ok(categories);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取插件分类失败");
            return StatusCode(500, new { message = "获取插件分类失败", error = ex.Message });
        }
    }
}

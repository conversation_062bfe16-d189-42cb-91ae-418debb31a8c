{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "FlowCustom": "Debug"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=FlowCustom;Trusted_Connection=true;TrustServerCertificate=true;", "Redis": "localhost:6379"}, "JWT": {"Secret": "FlowCustomJwtSecretKey123456789", "ExpiryHours": 24, "Issuer": "FlowCustom", "Audience": "FlowCustom"}, "FlowCustom": {"MaxWorkflowExecutionTime": "01:00:00", "MaxConcurrentExecutions": 10, "EnableMetrics": true, "EnableAuditLogging": true, "DataRetentionDays": 30, "FileUpload": {"MaxFileSize": 104857600, "AllowedExtensions": [".txt", ".json", ".csv", ".xml", ".yaml", ".yml"], "UploadPath": "./uploads"}, "Security": {"RequireHttps": false, "EnableCors": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:5173"], "EnableRateLimiting": true, "RateLimitRequests": 100, "RateLimitWindow": "00:01:00"}, "Features": {"EnableUserRegistration": true, "EnableApiKeys": true, "EnableTeams": true, "EnableWorkflowSharing": true, "EnableExecutionHistory": true, "EnableRealTimeMonitoring": true}}}
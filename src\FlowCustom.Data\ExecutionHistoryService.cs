using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Data;

/// <summary>
/// 执行历史服务
/// </summary>
public class ExecutionHistoryService
{
    private readonly ILogger<ExecutionHistoryService> _logger;
    private readonly ConcurrentDictionary<Guid, WorkflowExecution> _executions = new();
    private readonly ConcurrentDictionary<Guid, List<ExecutionLog>> _executionLogs = new();
    private readonly ConcurrentQueue<SystemLogEntry> _systemLogs = new();

    public ExecutionHistoryService(ILogger<ExecutionHistoryService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 保存执行记录
    /// </summary>
    public async Task SaveExecutionAsync(WorkflowExecution execution)
    {
        _executions[execution.Id] = execution;
        _logger.LogInformation("Saved execution: {ExecutionId} for workflow: {WorkflowId}", 
            execution.Id, execution.WorkflowId);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取执行记录
    /// </summary>
    public async Task<WorkflowExecution?> GetExecutionAsync(Guid executionId)
    {
        await Task.CompletedTask;
        return _executions.TryGetValue(executionId, out var execution) ? execution : null;
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetExecutionHistoryAsync(
        Guid workflowId, 
        int page = 1, 
        int pageSize = 50)
    {
        await Task.CompletedTask;
        
        var workflowExecutions = _executions.Values
            .Where(e => e.WorkflowId == workflowId)
            .OrderByDescending(e => e.StartedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize);

        return workflowExecutions;
    }

    /// <summary>
    /// 获取所有执行历史
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetAllExecutionsAsync(
        int page = 1, 
        int pageSize = 50,
        ExecutionStatus? status = null)
    {
        await Task.CompletedTask;
        
        var query = _executions.Values.AsEnumerable();
        
        if (status.HasValue)
        {
            query = query.Where(e => e.Status == status.Value);
        }

        return query
            .OrderByDescending(e => e.StartedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize);
    }

    /// <summary>
    /// 记录执行日志
    /// </summary>
    public async Task LogExecutionAsync(Guid executionId, ExecutionLog log)
    {
        if (!_executionLogs.ContainsKey(executionId))
        {
            _executionLogs[executionId] = new List<ExecutionLog>();
        }

        _executionLogs[executionId].Add(log);
        _logger.LogDebug("Added execution log: {LogLevel} for execution: {ExecutionId}", 
            log.Level, executionId);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取执行日志
    /// </summary>
    public async Task<IEnumerable<ExecutionLog>> GetExecutionLogsAsync(Guid executionId)
    {
        await Task.CompletedTask;
        return _executionLogs.TryGetValue(executionId, out var logs) 
            ? logs.OrderBy(l => l.Timestamp) 
            : Enumerable.Empty<ExecutionLog>();
    }

    /// <summary>
    /// 获取执行统计信息
    /// </summary>
    public async Task<ExecutionStatistics> GetExecutionStatisticsAsync(
        Guid? workflowId = null,
        DateTime? startDate = null,
        DateTime? endDate = null)
    {
        await Task.CompletedTask;
        
        var query = _executions.Values.AsEnumerable();
        
        if (workflowId.HasValue)
        {
            query = query.Where(e => e.WorkflowId == workflowId.Value);
        }

        if (startDate.HasValue)
        {
            query = query.Where(e => e.StartedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(e => e.StartedAt <= endDate.Value);
        }

        var executions = query.ToList();
        
        return new ExecutionStatistics
        {
            TotalExecutions = executions.Count,
            SuccessfulExecutions = executions.Count(e => e.Status == ExecutionStatus.Success),
            FailedExecutions = executions.Count(e => e.Status == ExecutionStatus.Error),
            RunningExecutions = executions.Count(e => e.Status == ExecutionStatus.Running),
            CancelledExecutions = executions.Count(e => e.Status == ExecutionStatus.Cancelled),
            AverageExecutionTime = executions
                .Where(e => e.FinishedAt.HasValue)
                .Select(e => e.FinishedAt!.Value.Subtract(e.StartedAt))
                .DefaultIfEmpty()
                .Average(ts => ts.TotalMilliseconds),
            LastExecutionTime = executions.OrderByDescending(e => e.StartedAt).FirstOrDefault()?.StartedAt
        };
    }

    /// <summary>
    /// 清理旧的执行记录
    /// </summary>
    public async Task CleanupOldExecutionsAsync(TimeSpan retentionPeriod)
    {
        var cutoffDate = DateTime.UtcNow.Subtract(retentionPeriod);
        var oldExecutions = _executions.Values
            .Where(e => e.StartedAt < cutoffDate)
            .ToList();

        foreach (var execution in oldExecutions)
        {
            _executions.TryRemove(execution.Id, out _);
            _executionLogs.TryRemove(execution.Id, out _);
        }

        _logger.LogInformation("Cleaned up {Count} old executions", oldExecutions.Count);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 添加系统日志
    /// </summary>
    public async Task AddSystemLogAsync(string level, string message, string? source = null,
        Guid? workflowId = null, Guid? nodeId = null, Guid? executionId = null,
        Dictionary<string, object>? details = null)
    {
        var logEntry = new SystemLogEntry
        {
            Id = Guid.NewGuid(),
            Timestamp = DateTime.UtcNow,
            Level = level,
            Message = message,
            Source = source,
            WorkflowId = workflowId,
            NodeId = nodeId,
            ExecutionId = executionId,
            Details = details ?? new Dictionary<string, object>()
        };

        _systemLogs.Enqueue(logEntry);

        // 保持最多10000条日志
        while (_systemLogs.Count > 10000)
        {
            _systemLogs.TryDequeue(out _);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取系统日志
    /// </summary>
    public async Task<IEnumerable<SystemLogEntry>> GetLogsAsync(
        Guid? workflowId = null,
        Guid? executionId = null,
        string? level = null,
        int limit = 1000,
        int skip = 0)
    {
        await Task.CompletedTask;

        var query = _systemLogs.AsEnumerable();

        if (workflowId.HasValue)
        {
            query = query.Where(log => log.WorkflowId == workflowId.Value);
        }

        if (executionId.HasValue)
        {
            query = query.Where(log => log.ExecutionId == executionId.Value);
        }

        if (!string.IsNullOrEmpty(level))
        {
            query = query.Where(log => log.Level.Equals(level, StringComparison.OrdinalIgnoreCase));
        }

        return query
            .OrderByDescending(log => log.Timestamp)
            .Skip(skip)
            .Take(limit);
    }

    /// <summary>
    /// 获取执行记录（新方法签名）
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetExecutionsAsync(
        Guid? workflowId = null,
        int limit = 100,
        int skip = 0)
    {
        await Task.CompletedTask;

        var query = _executions.Values.AsEnumerable();

        if (workflowId.HasValue)
        {
            query = query.Where(e => e.WorkflowId == workflowId.Value);
        }

        return query
            .OrderByDescending(e => e.StartedAt)
            .Skip(skip)
            .Take(limit);
    }

    /// <summary>
    /// 清空日志
    /// </summary>
    public async Task ClearLogsAsync(Guid? workflowId = null, string? level = null)
    {
        var logsToRemove = new List<SystemLogEntry>();

        foreach (var log in _systemLogs)
        {
            bool shouldRemove = true;

            if (workflowId.HasValue && log.WorkflowId != workflowId.Value)
            {
                shouldRemove = false;
            }

            if (!string.IsNullOrEmpty(level) && !log.Level.Equals(level, StringComparison.OrdinalIgnoreCase))
            {
                shouldRemove = false;
            }

            if (shouldRemove)
            {
                logsToRemove.Add(log);
            }
        }

        // 重新构建队列，排除要删除的日志
        var remainingLogs = _systemLogs.Except(logsToRemove);
        _systemLogs.Clear();
        foreach (var log in remainingLogs)
        {
            _systemLogs.Enqueue(log);
        }

        _logger.LogInformation("清空了 {Count} 条日志", logsToRemove.Count);
        await Task.CompletedTask;
    }
}

/// <summary>
/// 执行日志
/// </summary>
public class ExecutionLog
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid ExecutionId { get; set; }
    public Guid? NodeId { get; set; }
    public string Level { get; set; } = "Info";
    public string Message { get; set; } = string.Empty;
    public string? Details { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Metadata { get; set; } = new();
}



using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Data;

/// <summary>
/// 执行历史服务
/// </summary>
public class ExecutionHistoryService
{
    private readonly ILogger<ExecutionHistoryService> _logger;
    private readonly ConcurrentDictionary<Guid, WorkflowExecution> _executions = new();
    private readonly ConcurrentDictionary<Guid, List<ExecutionLog>> _executionLogs = new();

    public ExecutionHistoryService(ILogger<ExecutionHistoryService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 保存执行记录
    /// </summary>
    public async Task SaveExecutionAsync(WorkflowExecution execution)
    {
        _executions[execution.Id] = execution;
        _logger.LogInformation("Saved execution: {ExecutionId} for workflow: {WorkflowId}", 
            execution.Id, execution.WorkflowId);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取执行记录
    /// </summary>
    public async Task<WorkflowExecution?> GetExecutionAsync(Guid executionId)
    {
        await Task.CompletedTask;
        return _executions.TryGetValue(executionId, out var execution) ? execution : null;
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetExecutionHistoryAsync(
        Guid workflowId, 
        int page = 1, 
        int pageSize = 50)
    {
        await Task.CompletedTask;
        
        var workflowExecutions = _executions.Values
            .Where(e => e.WorkflowId == workflowId)
            .OrderByDescending(e => e.StartedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize);

        return workflowExecutions;
    }

    /// <summary>
    /// 获取所有执行历史
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetAllExecutionsAsync(
        int page = 1, 
        int pageSize = 50,
        ExecutionStatus? status = null)
    {
        await Task.CompletedTask;
        
        var query = _executions.Values.AsEnumerable();
        
        if (status.HasValue)
        {
            query = query.Where(e => e.Status == status.Value);
        }

        return query
            .OrderByDescending(e => e.StartedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize);
    }

    /// <summary>
    /// 记录执行日志
    /// </summary>
    public async Task LogExecutionAsync(Guid executionId, ExecutionLog log)
    {
        if (!_executionLogs.ContainsKey(executionId))
        {
            _executionLogs[executionId] = new List<ExecutionLog>();
        }

        _executionLogs[executionId].Add(log);
        _logger.LogDebug("Added execution log: {LogLevel} for execution: {ExecutionId}", 
            log.Level, executionId);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取执行日志
    /// </summary>
    public async Task<IEnumerable<ExecutionLog>> GetExecutionLogsAsync(Guid executionId)
    {
        await Task.CompletedTask;
        return _executionLogs.TryGetValue(executionId, out var logs) 
            ? logs.OrderBy(l => l.Timestamp) 
            : Enumerable.Empty<ExecutionLog>();
    }

    /// <summary>
    /// 获取执行统计信息
    /// </summary>
    public async Task<ExecutionStatistics> GetExecutionStatisticsAsync(
        Guid? workflowId = null,
        DateTime? startDate = null,
        DateTime? endDate = null)
    {
        await Task.CompletedTask;
        
        var query = _executions.Values.AsEnumerable();
        
        if (workflowId.HasValue)
        {
            query = query.Where(e => e.WorkflowId == workflowId.Value);
        }

        if (startDate.HasValue)
        {
            query = query.Where(e => e.StartedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(e => e.StartedAt <= endDate.Value);
        }

        var executions = query.ToList();
        
        return new ExecutionStatistics
        {
            TotalExecutions = executions.Count,
            SuccessfulExecutions = executions.Count(e => e.Status == ExecutionStatus.Success),
            FailedExecutions = executions.Count(e => e.Status == ExecutionStatus.Error),
            RunningExecutions = executions.Count(e => e.Status == ExecutionStatus.Running),
            CancelledExecutions = executions.Count(e => e.Status == ExecutionStatus.Cancelled),
            AverageExecutionTime = executions
                .Where(e => e.FinishedAt.HasValue)
                .Select(e => e.FinishedAt!.Value.Subtract(e.StartedAt))
                .DefaultIfEmpty()
                .Average(ts => ts.TotalMilliseconds),
            LastExecutionTime = executions.OrderByDescending(e => e.StartedAt).FirstOrDefault()?.StartedAt
        };
    }

    /// <summary>
    /// 清理旧的执行记录
    /// </summary>
    public async Task CleanupOldExecutionsAsync(TimeSpan retentionPeriod)
    {
        var cutoffDate = DateTime.UtcNow.Subtract(retentionPeriod);
        var oldExecutions = _executions.Values
            .Where(e => e.StartedAt < cutoffDate)
            .ToList();

        foreach (var execution in oldExecutions)
        {
            _executions.TryRemove(execution.Id, out _);
            _executionLogs.TryRemove(execution.Id, out _);
        }

        _logger.LogInformation("Cleaned up {Count} old executions", oldExecutions.Count);
        await Task.CompletedTask;
    }
}

/// <summary>
/// 执行日志
/// </summary>
public class ExecutionLog
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid ExecutionId { get; set; }
    public Guid? NodeId { get; set; }
    public string Level { get; set; } = "Info";
    public string Message { get; set; } = string.Empty;
    public string? Details { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 执行统计信息
/// </summary>
public class ExecutionStatistics
{
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public int RunningExecutions { get; set; }
    public int CancelledExecutions { get; set; }
    public double AverageExecutionTime { get; set; }
    public DateTime? LastExecutionTime { get; set; }
    
    public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions * 100 : 0;
    public double FailureRate => TotalExecutions > 0 ? (double)FailedExecutions / TotalExecutions * 100 : 0;
}

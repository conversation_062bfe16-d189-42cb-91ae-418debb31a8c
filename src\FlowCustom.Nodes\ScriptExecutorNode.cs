using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text;

namespace FlowCustom.Nodes;

/// <summary>
/// 脚本执行器节点
/// </summary>
public class ScriptExecutorNode : INode
{
    private readonly ILogger<ScriptExecutorNode> _logger;

    public ScriptExecutorNode(ILogger<ScriptExecutorNode> logger)
    {
        _logger = logger;
    }

    public string NodeType => "script-executor";

    public string DisplayName => "脚本执行器";

    public string Description => "执行Python、Bash或JavaScript脚本";

    public string Category => "工具";

    public string Icon => "code";

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "scriptType",
                DisplayName = "脚本类型",
                Description = "选择要执行的脚本类型",
                Type = ParameterType.Select,
                Required = true,
                DefaultValue = "python",
                Options = new[] { "python", "bash", "javascript" }
            },
            new NodeParameterDefinition
            {
                Name = "script",
                DisplayName = "脚本内容",
                Description = "要执行的脚本代码",
                Type = ParameterType.String,
                Required = true,
                DefaultValue = "# Python示例\nprint('Hello, World!')\nresult = 1 + 1\nprint(f'1 + 1 = {result}')"
            },
            new NodeParameterDefinition
            {
                Name = "timeout",
                DisplayName = "超时时间（秒）",
                Description = "脚本执行的最大时间限制",
                Type = ParameterType.Number,
                Required = false,
                DefaultValue = 30
            },
            new NodeParameterDefinition
            {
                Name = "workingDirectory",
                DisplayName = "工作目录",
                Description = "脚本执行的工作目录（可选）",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = ""
            },
            new NodeParameterDefinition
            {
                Name = "environmentVariables",
                DisplayName = "环境变量",
                Description = "设置脚本执行时的环境变量（JSON格式）",
                Type = ParameterType.Object,
                Required = false,
                DefaultValue = new Dictionary<string, string>()
            },
            new NodeParameterDefinition
            {
                Name = "captureOutput",
                DisplayName = "捕获输出",
                Description = "是否捕获脚本的标准输出和错误输出",
                Type = ParameterType.Boolean,
                Required = false,
                DefaultValue = true
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node,
        FlowCustom.Core.Models.ExecutionContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var scriptType = GetParameter<string>(node, "scriptType", "python");
            var script = GetParameter<string>(node, "script", "");
            var timeout = GetParameter<int>(node, "timeout", 30);
            var workingDirectory = GetParameter<string>(node, "workingDirectory", "");
            var environmentVariables = GetParameter<Dictionary<string, string>>(node, "environmentVariables", new Dictionary<string, string>());
            var captureOutput = GetParameter<bool>(node, "captureOutput", true);

            if (string.IsNullOrWhiteSpace(script))
            {
                return NodeExecutionResult.CreateError("脚本内容不能为空");
            }

            _logger.LogInformation("开始执行{ScriptType}脚本: {NodeId}", scriptType, node.Id);

            var result = await ExecuteScriptAsync(scriptType, script, timeout, workingDirectory, environmentVariables, captureOutput, cancellationToken);

            _logger.LogInformation("脚本执行完成: {NodeId}, 退出码: {ExitCode}", node.Id, result.ExitCode);

            var outputData = new Dictionary<string, object>
            {
                ["exitCode"] = result.ExitCode,
                ["stdout"] = result.StandardOutput,
                ["stderr"] = result.StandardError,
                ["success"] = result.ExitCode == 0,
                ["executionTime"] = result.ExecutionTime,
                ["scriptType"] = scriptType
            };

            // 如果脚本执行失败，返回错误
            if (result.ExitCode != 0)
            {
                var errorMessage = $"脚本执行失败，退出码: {result.ExitCode}";
                if (!string.IsNullOrEmpty(result.StandardError))
                {
                    errorMessage += $"\n错误输出: {result.StandardError}";
                }
                return NodeExecutionResult.CreateError(errorMessage);
            }

            return NodeExecutionResult.CreateSuccess(outputData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "脚本执行节点执行失败: {NodeId}", node.Id);
            return NodeExecutionResult.CreateError($"脚本执行节点执行失败: {ex.Message}");
        }
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.TryGetValue("scriptType", out var scriptTypeObj) || 
            string.IsNullOrWhiteSpace(scriptTypeObj?.ToString()))
        {
            errors.Add("脚本类型不能为空");
        }
        else
        {
            var scriptType = scriptTypeObj.ToString();
            if (!new[] { "python", "bash", "javascript" }.Contains(scriptType))
            {
                errors.Add("不支持的脚本类型");
            }
        }

        if (!parameters.TryGetValue("script", out var scriptObj) || 
            string.IsNullOrWhiteSpace(scriptObj?.ToString()))
        {
            errors.Add("脚本内容不能为空");
        }

        if (parameters.TryGetValue("timeout", out var timeoutObj))
        {
            if (!int.TryParse(timeoutObj?.ToString(), out var timeout) || timeout <= 0)
            {
                errors.Add("超时时间必须是大于0的整数");
            }
        }

        return errors.Count == 0 ? ValidationResult.Success() : ValidationResult.Failure(errors.ToArray());
    }

    private async Task<ScriptExecutionResult> ExecuteScriptAsync(
        string scriptType,
        string script,
        int timeoutSeconds,
        string workingDirectory,
        Dictionary<string, string> environmentVariables,
        bool captureOutput,
        CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;
        var processStartInfo = CreateProcessStartInfo(scriptType, script, workingDirectory, environmentVariables, captureOutput);

        using var process = new Process { StartInfo = processStartInfo };
        var outputBuilder = new StringBuilder();
        var errorBuilder = new StringBuilder();

        if (captureOutput)
        {
            process.OutputDataReceived += (sender, e) =>
            {
                if (e.Data != null)
                {
                    outputBuilder.AppendLine(e.Data);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (e.Data != null)
                {
                    errorBuilder.AppendLine(e.Data);
                }
            };
        }

        process.Start();

        if (captureOutput)
        {
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();
        }

        // 等待进程完成或超时
        var completed = await WaitForProcessAsync(process, timeoutSeconds * 1000, cancellationToken);

        if (!completed)
        {
            try
            {
                process.Kill(true);
                await process.WaitForExitAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "强制终止进程时发生错误");
            }
        }

        var executionTime = DateTime.UtcNow - startTime;

        return new ScriptExecutionResult
        {
            ExitCode = completed ? process.ExitCode : -1,
            StandardOutput = outputBuilder.ToString(),
            StandardError = completed ? errorBuilder.ToString() : "脚本执行超时",
            ExecutionTime = executionTime
        };
    }

    private ProcessStartInfo CreateProcessStartInfo(
        string scriptType,
        string script,
        string workingDirectory,
        Dictionary<string, string> environmentVariables,
        bool captureOutput)
    {
        var processStartInfo = new ProcessStartInfo
        {
            UseShellExecute = false,
            CreateNoWindow = true,
            RedirectStandardInput = true,
            RedirectStandardOutput = captureOutput,
            RedirectStandardError = captureOutput,
            StandardInputEncoding = Encoding.UTF8,
            StandardOutputEncoding = Encoding.UTF8,
            StandardErrorEncoding = Encoding.UTF8
        };

        // 设置工作目录
        if (!string.IsNullOrWhiteSpace(workingDirectory) && Directory.Exists(workingDirectory))
        {
            processStartInfo.WorkingDirectory = workingDirectory;
        }

        // 设置环境变量
        foreach (var envVar in environmentVariables)
        {
            processStartInfo.Environment[envVar.Key] = envVar.Value;
        }

        // 根据脚本类型设置执行命令
        switch (scriptType.ToLower())
        {
            case "python":
                processStartInfo.FileName = "python";
                processStartInfo.Arguments = "-c \"" + script.Replace("\"", "\\\"") + "\"";
                break;

            case "bash":
                if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                {
                    // Windows上使用PowerShell
                    processStartInfo.FileName = "powershell";
                    processStartInfo.Arguments = "-Command \"" + script.Replace("\"", "`\"") + "\"";
                }
                else
                {
                    // Linux/Mac上使用bash
                    processStartInfo.FileName = "/bin/bash";
                    processStartInfo.Arguments = "-c \"" + script.Replace("\"", "\\\"") + "\"";
                }
                break;

            case "javascript":
                processStartInfo.FileName = "node";
                processStartInfo.Arguments = "-e \"" + script.Replace("\"", "\\\"") + "\"";
                break;

            default:
                throw new ArgumentException($"不支持的脚本类型: {scriptType}");
        }

        return processStartInfo;
    }

    private async Task<bool> WaitForProcessAsync(Process process, int timeoutMs, CancellationToken cancellationToken)
    {
        try
        {
            using var timeoutCts = new CancellationTokenSource(timeoutMs);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);
            
            await process.WaitForExitAsync(combinedCts.Token);
            return true;
        }
        catch (OperationCanceledException)
        {
            return false;
        }
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;
                
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法转换参数 {ParameterName} 的值，使用默认值", parameterName);
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

/// <summary>
/// 脚本执行结果
/// </summary>
public class ScriptExecutionResult
{
    public int ExitCode { get; set; }
    public string StandardOutput { get; set; } = string.Empty;
    public string StandardError { get; set; } = string.Empty;
    public TimeSpan ExecutionTime { get; set; }
}

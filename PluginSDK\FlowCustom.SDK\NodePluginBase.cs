using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FlowCustom.SDK
{
    /// <summary>
    /// 节点插件基础类
    /// 提供插件开发的基础实现
    /// </summary>
    public abstract class NodePluginBase : INodePlugin
    {
        protected IServiceProvider? _serviceProvider;
        protected ILogger? _logger;

        /// <summary>
        /// 节点类型标识符
        /// </summary>
        public abstract string NodeType { get; }

        /// <summary>
        /// 节点显示名称
        /// </summary>
        public abstract string DisplayName { get; }

        /// <summary>
        /// 节点描述
        /// </summary>
        public abstract string Description { get; }

        /// <summary>
        /// 节点分类
        /// </summary>
        public virtual string Category => "General";

        /// <summary>
        /// 插件版本
        /// </summary>
        public virtual string Version => "1.0.0";

        /// <summary>
        /// 插件作者
        /// </summary>
        public virtual string Author => "Unknown";

        /// <summary>
        /// 是否为触发器节点
        /// </summary>
        public virtual bool IsTrigger => false;

        /// <summary>
        /// 节点图标
        /// </summary>
        public virtual string Icon => "⚙️";

        /// <summary>
        /// 初始化插件
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        public virtual void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService(typeof(ILogger)) as ILogger;
            OnInitialize();
        }

        /// <summary>
        /// 插件初始化回调
        /// 子类可以重写此方法进行自定义初始化
        /// </summary>
        protected virtual void OnInitialize()
        {
        }

        /// <summary>
        /// 获取节点定义
        /// </summary>
        /// <returns>节点定义</returns>
        public virtual NodeDefinition GetDefinition()
        {
            var definition = new NodeDefinition
            {
                NodeType = NodeType,
                DisplayName = DisplayName,
                Description = Description,
                Category = Category,
                Icon = Icon,
                IsTrigger = IsTrigger,
                Parameters = GetParameterDefinitions(),
                Inputs = GetInputDefinitions(),
                Outputs = GetOutputDefinitions()
            };

            OnBuildDefinition(definition);
            return definition;
        }

        /// <summary>
        /// 获取参数定义列表
        /// 子类重写此方法定义节点参数
        /// </summary>
        /// <returns>参数定义列表</returns>
        protected virtual List<ParameterDefinition> GetParameterDefinitions()
        {
            return new List<ParameterDefinition>();
        }

        /// <summary>
        /// 获取输入端点定义
        /// 子类重写此方法定义输入端点
        /// </summary>
        /// <returns>输入端点定义列表</returns>
        protected virtual List<EndpointDefinition> GetInputDefinitions()
        {
            if (IsTrigger)
            {
                return new List<EndpointDefinition>(); // 触发器没有输入
            }

            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "main",
                    Name = "输入",
                    Description = "主要输入端点",
                    Type = EndpointType.Data,
                    Required = true,
                    Position = new EndpointPosition { Side = "top", Offset = 50 }
                }
            };
        }

        /// <summary>
        /// 获取输出端点定义
        /// 子类重写此方法定义输出端点
        /// </summary>
        /// <returns>输出端点定义列表</returns>
        protected virtual List<EndpointDefinition> GetOutputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "main",
                    Name = "输出",
                    Description = "主要输出端点",
                    Type = EndpointType.Data,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 50 }
                }
            };
        }

        /// <summary>
        /// 构建定义回调
        /// 子类可以重写此方法进行自定义定义构建
        /// </summary>
        /// <param name="definition">节点定义</param>
        protected virtual void OnBuildDefinition(NodeDefinition definition)
        {
        }

        /// <summary>
        /// 执行节点逻辑
        /// </summary>
        /// <param name="context">执行上下文</param>
        /// <returns>执行结果</returns>
        public async Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context)
        {
            try
            {
                // 前置处理
                await OnBeforeExecute(context);

                // 验证输入
                var validationResult = await ValidateAsync(context.Configuration);
                if (!validationResult.IsValid)
                {
                    var errorMessage = string.Join("; ", validationResult.Errors.Select(e => e.Message));
                    return NodeExecutionResult.Failure($"配置验证失败: {errorMessage}");
                }

                // 执行核心逻辑
                var result = await OnExecuteAsync(context);

                // 后置处理
                await OnAfterExecute(context, result);

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"节点执行失败: {ex.Message}", ex);
                return NodeExecutionResult.Failure(ex.Message, ex);
            }
        }

        /// <summary>
        /// 执行前回调
        /// </summary>
        /// <param name="context">执行上下文</param>
        protected virtual Task OnBeforeExecute(NodeExecutionContext context)
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// 核心执行逻辑
        /// 子类必须实现此方法
        /// </summary>
        /// <param name="context">执行上下文</param>
        /// <returns>执行结果</returns>
        protected abstract Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context);

        /// <summary>
        /// 执行后回调
        /// </summary>
        /// <param name="context">执行上下文</param>
        /// <param name="result">执行结果</param>
        protected virtual Task OnAfterExecute(NodeExecutionContext context, NodeExecutionResult result)
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// 验证节点配置
        /// </summary>
        /// <param name="configuration">节点配置</param>
        /// <returns>验证结果</returns>
        public virtual Task<ValidationResult> ValidateAsync(NodeConfiguration configuration)
        {
            var errors = new List<ValidationError>();
            var warnings = new List<ValidationWarning>();

            // 验证必需参数
            var parameterDefinitions = GetParameterDefinitions();
            foreach (var paramDef in parameterDefinitions.Where(p => p.Required))
            {
                if (!configuration.Parameters.ContainsKey(paramDef.Name) ||
                    configuration.Parameters[paramDef.Name] == null)
                {
                    errors.Add(new ValidationError
                    {
                        Field = paramDef.Name,
                        Message = $"必需参数 '{paramDef.DisplayName}' 不能为空",
                        Code = "REQUIRED_PARAMETER_MISSING"
                    });
                }
            }

            // 自定义验证
            OnValidate(configuration, errors, warnings);

            return Task.FromResult(new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors,
                Warnings = warnings
            });
        }

        /// <summary>
        /// 自定义验证逻辑
        /// 子类可以重写此方法添加自定义验证
        /// </summary>
        /// <param name="configuration">节点配置</param>
        /// <param name="errors">错误列表</param>
        /// <param name="warnings">警告列表</param>
        protected virtual void OnValidate(NodeConfiguration configuration, 
            List<ValidationError> errors, List<ValidationWarning> warnings)
        {
        }

        /// <summary>
        /// 获取前端配置组件信息
        /// </summary>
        /// <returns>前端组件信息</returns>
        public virtual FrontendComponentInfo GetFrontendComponent()
        {
            return new FrontendComponentInfo
            {
                ComponentName = $"{NodeType}Config",
                ComponentPath = $"components/{NodeType}Config.tsx",
                IconPath = $"icons/{NodeType}.svg"
            };
        }

        /// <summary>
        /// 获取参数值
        /// </summary>
        /// <typeparam name="T">参数类型</typeparam>
        /// <param name="context">执行上下文</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>参数值</returns>
        protected T GetParameter<T>(NodeExecutionContext context, string parameterName, T defaultValue = default!)
        {
            if (context.Configuration.Parameters.TryGetValue(parameterName, out var value))
            {
                try
                {
                    if (value is T directValue)
                        return directValue;

                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch
                {
                    _logger?.LogWarning($"参数 '{parameterName}' 类型转换失败，使用默认值");
                }
            }

            return defaultValue;
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="level">日志级别</param>
        protected void Log(string message, LogLevel level = LogLevel.Info)
        {
            switch (level)
            {
                case LogLevel.Debug:
                    _logger?.LogDebug(message);
                    break;
                case LogLevel.Info:
                    _logger?.LogInfo(message);
                    break;
                case LogLevel.Warning:
                    _logger?.LogWarning(message);
                    break;
                case LogLevel.Error:
                    _logger?.LogError(message);
                    break;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public virtual void Dispose()
        {
            OnDispose();
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源回调
        /// 子类可以重写此方法进行资源清理
        /// </summary>
        protected virtual void OnDispose()
        {
        }
    }

    /// <summary>
    /// 日志级别
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error
    }
}

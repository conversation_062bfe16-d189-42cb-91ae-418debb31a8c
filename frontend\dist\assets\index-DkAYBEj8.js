(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))s(l);new MutationObserver(l=>{for(const a of l)if(a.type==="childList")for(const c of a.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&s(c)}).observe(document,{childList:!0,subtree:!0});function o(l){const a={};return l.integrity&&(a.integrity=l.integrity),l.referrerPolicy&&(a.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?a.credentials="include":l.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function s(l){if(l.ep)return;l.ep=!0;const a=o(l);fetch(l.href,a)}})();function Qp(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Va={exports:{}},ci={},Ua={exports:{}},Ce={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wh;function By(){if(wh)return Ce;wh=1;var t=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),c=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),v=Symbol.iterator;function y(T){return T===null||typeof T!="object"?null:(T=v&&T[v]||T["@@iterator"],typeof T=="function"?T:null)}var w={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_=Object.assign,S={};function k(T,j,oe){this.props=T,this.context=j,this.refs=S,this.updater=oe||w}k.prototype.isReactComponent={},k.prototype.setState=function(T,j){if(typeof T!="object"&&typeof T!="function"&&T!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,T,j,"setState")},k.prototype.forceUpdate=function(T){this.updater.enqueueForceUpdate(this,T,"forceUpdate")};function C(){}C.prototype=k.prototype;function z(T,j,oe){this.props=T,this.context=j,this.refs=S,this.updater=oe||w}var E=z.prototype=new C;E.constructor=z,_(E,k.prototype),E.isPureReactComponent=!0;var N=Array.isArray,A=Object.prototype.hasOwnProperty,$={current:null},F={key:!0,ref:!0,__self:!0,__source:!0};function X(T,j,oe){var ie,ue={},ae=null,fe=null;if(j!=null)for(ie in j.ref!==void 0&&(fe=j.ref),j.key!==void 0&&(ae=""+j.key),j)A.call(j,ie)&&!F.hasOwnProperty(ie)&&(ue[ie]=j[ie]);var re=arguments.length-2;if(re===1)ue.children=oe;else if(1<re){for(var ce=Array(re),Se=0;Se<re;Se++)ce[Se]=arguments[Se+2];ue.children=ce}if(T&&T.defaultProps)for(ie in re=T.defaultProps,re)ue[ie]===void 0&&(ue[ie]=re[ie]);return{$$typeof:t,type:T,key:ae,ref:fe,props:ue,_owner:$.current}}function K(T,j){return{$$typeof:t,type:T.type,key:j,ref:T.ref,props:T.props,_owner:T._owner}}function Z(T){return typeof T=="object"&&T!==null&&T.$$typeof===t}function J(T){var j={"=":"=0",":":"=2"};return"$"+T.replace(/[=:]/g,function(oe){return j[oe]})}var ee=/\/+/g;function G(T,j){return typeof T=="object"&&T!==null&&T.key!=null?J(""+T.key):j.toString(36)}function P(T,j,oe,ie,ue){var ae=typeof T;(ae==="undefined"||ae==="boolean")&&(T=null);var fe=!1;if(T===null)fe=!0;else switch(ae){case"string":case"number":fe=!0;break;case"object":switch(T.$$typeof){case t:case r:fe=!0}}if(fe)return fe=T,ue=ue(fe),T=ie===""?"."+G(fe,0):ie,N(ue)?(oe="",T!=null&&(oe=T.replace(ee,"$&/")+"/"),P(ue,j,oe,"",function(Se){return Se})):ue!=null&&(Z(ue)&&(ue=K(ue,oe+(!ue.key||fe&&fe.key===ue.key?"":(""+ue.key).replace(ee,"$&/")+"/")+T)),j.push(ue)),1;if(fe=0,ie=ie===""?".":ie+":",N(T))for(var re=0;re<T.length;re++){ae=T[re];var ce=ie+G(ae,re);fe+=P(ae,j,oe,ce,ue)}else if(ce=y(T),typeof ce=="function")for(T=ce.call(T),re=0;!(ae=T.next()).done;)ae=ae.value,ce=ie+G(ae,re++),fe+=P(ae,j,oe,ce,ue);else if(ae==="object")throw j=String(T),Error("Objects are not valid as a React child (found: "+(j==="[object Object]"?"object with keys {"+Object.keys(T).join(", ")+"}":j)+"). If you meant to render a collection of children, use an array instead.");return fe}function W(T,j,oe){if(T==null)return T;var ie=[],ue=0;return P(T,ie,"","",function(ae){return j.call(oe,ae,ue++)}),ie}function b(T){if(T._status===-1){var j=T._result;j=j(),j.then(function(oe){(T._status===0||T._status===-1)&&(T._status=1,T._result=oe)},function(oe){(T._status===0||T._status===-1)&&(T._status=2,T._result=oe)}),T._status===-1&&(T._status=0,T._result=j)}if(T._status===1)return T._result.default;throw T._result}var H={current:null},D={transition:null},I={ReactCurrentDispatcher:H,ReactCurrentBatchConfig:D,ReactCurrentOwner:$};function B(){throw Error("act(...) is not supported in production builds of React.")}return Ce.Children={map:W,forEach:function(T,j,oe){W(T,function(){j.apply(this,arguments)},oe)},count:function(T){var j=0;return W(T,function(){j++}),j},toArray:function(T){return W(T,function(j){return j})||[]},only:function(T){if(!Z(T))throw Error("React.Children.only expected to receive a single React element child.");return T}},Ce.Component=k,Ce.Fragment=o,Ce.Profiler=l,Ce.PureComponent=z,Ce.StrictMode=s,Ce.Suspense=p,Ce.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I,Ce.act=B,Ce.cloneElement=function(T,j,oe){if(T==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+T+".");var ie=_({},T.props),ue=T.key,ae=T.ref,fe=T._owner;if(j!=null){if(j.ref!==void 0&&(ae=j.ref,fe=$.current),j.key!==void 0&&(ue=""+j.key),T.type&&T.type.defaultProps)var re=T.type.defaultProps;for(ce in j)A.call(j,ce)&&!F.hasOwnProperty(ce)&&(ie[ce]=j[ce]===void 0&&re!==void 0?re[ce]:j[ce])}var ce=arguments.length-2;if(ce===1)ie.children=oe;else if(1<ce){re=Array(ce);for(var Se=0;Se<ce;Se++)re[Se]=arguments[Se+2];ie.children=re}return{$$typeof:t,type:T.type,key:ue,ref:ae,props:ie,_owner:fe}},Ce.createContext=function(T){return T={$$typeof:c,_currentValue:T,_currentValue2:T,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},T.Provider={$$typeof:a,_context:T},T.Consumer=T},Ce.createElement=X,Ce.createFactory=function(T){var j=X.bind(null,T);return j.type=T,j},Ce.createRef=function(){return{current:null}},Ce.forwardRef=function(T){return{$$typeof:d,render:T}},Ce.isValidElement=Z,Ce.lazy=function(T){return{$$typeof:m,_payload:{_status:-1,_result:T},_init:b}},Ce.memo=function(T,j){return{$$typeof:g,type:T,compare:j===void 0?null:j}},Ce.startTransition=function(T){var j=D.transition;D.transition={};try{T()}finally{D.transition=j}},Ce.unstable_act=B,Ce.useCallback=function(T,j){return H.current.useCallback(T,j)},Ce.useContext=function(T){return H.current.useContext(T)},Ce.useDebugValue=function(){},Ce.useDeferredValue=function(T){return H.current.useDeferredValue(T)},Ce.useEffect=function(T,j){return H.current.useEffect(T,j)},Ce.useId=function(){return H.current.useId()},Ce.useImperativeHandle=function(T,j,oe){return H.current.useImperativeHandle(T,j,oe)},Ce.useInsertionEffect=function(T,j){return H.current.useInsertionEffect(T,j)},Ce.useLayoutEffect=function(T,j){return H.current.useLayoutEffect(T,j)},Ce.useMemo=function(T,j){return H.current.useMemo(T,j)},Ce.useReducer=function(T,j,oe){return H.current.useReducer(T,j,oe)},Ce.useRef=function(T){return H.current.useRef(T)},Ce.useState=function(T){return H.current.useState(T)},Ce.useSyncExternalStore=function(T,j,oe){return H.current.useSyncExternalStore(T,j,oe)},Ce.useTransition=function(){return H.current.useTransition()},Ce.version="18.3.1",Ce}var xh;function Pi(){return xh||(xh=1,Ua.exports=By()),Ua.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sh;function Vy(){if(Sh)return ci;Sh=1;var t=Pi(),r=Symbol.for("react.element"),o=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,l=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function c(d,p,g){var m,v={},y=null,w=null;g!==void 0&&(y=""+g),p.key!==void 0&&(y=""+p.key),p.ref!==void 0&&(w=p.ref);for(m in p)s.call(p,m)&&!a.hasOwnProperty(m)&&(v[m]=p[m]);if(d&&d.defaultProps)for(m in p=d.defaultProps,p)v[m]===void 0&&(v[m]=p[m]);return{$$typeof:r,type:d,key:y,ref:w,props:v,_owner:l.current}}return ci.Fragment=o,ci.jsx=c,ci.jsxs=c,ci}var Eh;function Uy(){return Eh||(Eh=1,Va.exports=Vy()),Va.exports}var M=Uy(),q=Pi();const Wy=Qp(q);var Zs={},Wa={exports:{}},pt={},Xa={exports:{}},Ya={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _h;function Xy(){return _h||(_h=1,function(t){function r(D,I){var B=D.length;D.push(I);e:for(;0<B;){var T=B-1>>>1,j=D[T];if(0<l(j,I))D[T]=I,D[B]=j,B=T;else break e}}function o(D){return D.length===0?null:D[0]}function s(D){if(D.length===0)return null;var I=D[0],B=D.pop();if(B!==I){D[0]=B;e:for(var T=0,j=D.length,oe=j>>>1;T<oe;){var ie=2*(T+1)-1,ue=D[ie],ae=ie+1,fe=D[ae];if(0>l(ue,B))ae<j&&0>l(fe,ue)?(D[T]=fe,D[ae]=B,T=ae):(D[T]=ue,D[ie]=B,T=ie);else if(ae<j&&0>l(fe,B))D[T]=fe,D[ae]=B,T=ae;else break e}}return I}function l(D,I){var B=D.sortIndex-I.sortIndex;return B!==0?B:D.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var a=performance;t.unstable_now=function(){return a.now()}}else{var c=Date,d=c.now();t.unstable_now=function(){return c.now()-d}}var p=[],g=[],m=1,v=null,y=3,w=!1,_=!1,S=!1,k=typeof setTimeout=="function"?setTimeout:null,C=typeof clearTimeout=="function"?clearTimeout:null,z=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function E(D){for(var I=o(g);I!==null;){if(I.callback===null)s(g);else if(I.startTime<=D)s(g),I.sortIndex=I.expirationTime,r(p,I);else break;I=o(g)}}function N(D){if(S=!1,E(D),!_)if(o(p)!==null)_=!0,b(A);else{var I=o(g);I!==null&&H(N,I.startTime-D)}}function A(D,I){_=!1,S&&(S=!1,C(X),X=-1),w=!0;var B=y;try{for(E(I),v=o(p);v!==null&&(!(v.expirationTime>I)||D&&!J());){var T=v.callback;if(typeof T=="function"){v.callback=null,y=v.priorityLevel;var j=T(v.expirationTime<=I);I=t.unstable_now(),typeof j=="function"?v.callback=j:v===o(p)&&s(p),E(I)}else s(p);v=o(p)}if(v!==null)var oe=!0;else{var ie=o(g);ie!==null&&H(N,ie.startTime-I),oe=!1}return oe}finally{v=null,y=B,w=!1}}var $=!1,F=null,X=-1,K=5,Z=-1;function J(){return!(t.unstable_now()-Z<K)}function ee(){if(F!==null){var D=t.unstable_now();Z=D;var I=!0;try{I=F(!0,D)}finally{I?G():($=!1,F=null)}}else $=!1}var G;if(typeof z=="function")G=function(){z(ee)};else if(typeof MessageChannel<"u"){var P=new MessageChannel,W=P.port2;P.port1.onmessage=ee,G=function(){W.postMessage(null)}}else G=function(){k(ee,0)};function b(D){F=D,$||($=!0,G())}function H(D,I){X=k(function(){D(t.unstable_now())},I)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(D){D.callback=null},t.unstable_continueExecution=function(){_||w||(_=!0,b(A))},t.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<D?Math.floor(1e3/D):5},t.unstable_getCurrentPriorityLevel=function(){return y},t.unstable_getFirstCallbackNode=function(){return o(p)},t.unstable_next=function(D){switch(y){case 1:case 2:case 3:var I=3;break;default:I=y}var B=y;y=I;try{return D()}finally{y=B}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(D,I){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var B=y;y=D;try{return I()}finally{y=B}},t.unstable_scheduleCallback=function(D,I,B){var T=t.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?T+B:T):B=T,D){case 1:var j=-1;break;case 2:j=250;break;case 5:j=**********;break;case 4:j=1e4;break;default:j=5e3}return j=B+j,D={id:m++,callback:I,priorityLevel:D,startTime:B,expirationTime:j,sortIndex:-1},B>T?(D.sortIndex=B,r(g,D),o(p)===null&&D===o(g)&&(S?(C(X),X=-1):S=!0,H(N,B-T))):(D.sortIndex=j,r(p,D),_||w||(_=!0,b(A))),D},t.unstable_shouldYield=J,t.unstable_wrapCallback=function(D){var I=y;return function(){var B=y;y=I;try{return D.apply(this,arguments)}finally{y=B}}}}(Ya)),Ya}var kh;function Yy(){return kh||(kh=1,Xa.exports=Xy()),Xa.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nh;function qy(){if(Nh)return pt;Nh=1;var t=Pi(),r=Yy();function o(e){for(var n="https://reactjs.org/docs/error-decoder.html?invariant="+e,i=1;i<arguments.length;i++)n+="&args[]="+encodeURIComponent(arguments[i]);return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s=new Set,l={};function a(e,n){c(e,n),c(e+"Capture",n)}function c(e,n){for(l[e]=n,e=0;e<n.length;e++)s.add(n[e])}var d=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),p=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,m={},v={};function y(e){return p.call(v,e)?!0:p.call(m,e)?!1:g.test(e)?v[e]=!0:(m[e]=!0,!1)}function w(e,n,i,u){if(i!==null&&i.type===0)return!1;switch(typeof n){case"function":case"symbol":return!0;case"boolean":return u?!1:i!==null?!i.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function _(e,n,i,u){if(n===null||typeof n>"u"||w(e,n,i,u))return!0;if(u)return!1;if(i!==null)switch(i.type){case 3:return!n;case 4:return n===!1;case 5:return isNaN(n);case 6:return isNaN(n)||1>n}return!1}function S(e,n,i,u,f,h,x){this.acceptsBooleans=n===2||n===3||n===4,this.attributeName=u,this.attributeNamespace=f,this.mustUseProperty=i,this.propertyName=e,this.type=n,this.sanitizeURL=h,this.removeEmptyString=x}var k={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){k[e]=new S(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var n=e[0];k[n]=new S(n,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){k[e]=new S(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){k[e]=new S(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){k[e]=new S(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){k[e]=new S(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){k[e]=new S(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){k[e]=new S(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){k[e]=new S(e,5,!1,e.toLowerCase(),null,!1,!1)});var C=/[\-:]([a-z])/g;function z(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var n=e.replace(C,z);k[n]=new S(n,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var n=e.replace(C,z);k[n]=new S(n,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var n=e.replace(C,z);k[n]=new S(n,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){k[e]=new S(e,1,!1,e.toLowerCase(),null,!1,!1)}),k.xlinkHref=new S("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){k[e]=new S(e,1,!1,e.toLowerCase(),null,!0,!0)});function E(e,n,i,u){var f=k.hasOwnProperty(n)?k[n]:null;(f!==null?f.type!==0:u||!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(_(n,i,f,u)&&(i=null),u||f===null?y(n)&&(i===null?e.removeAttribute(n):e.setAttribute(n,""+i)):f.mustUseProperty?e[f.propertyName]=i===null?f.type===3?!1:"":i:(n=f.attributeName,u=f.attributeNamespace,i===null?e.removeAttribute(n):(f=f.type,i=f===3||f===4&&i===!0?"":""+i,u?e.setAttributeNS(u,n,i):e.setAttribute(n,i))))}var N=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,A=Symbol.for("react.element"),$=Symbol.for("react.portal"),F=Symbol.for("react.fragment"),X=Symbol.for("react.strict_mode"),K=Symbol.for("react.profiler"),Z=Symbol.for("react.provider"),J=Symbol.for("react.context"),ee=Symbol.for("react.forward_ref"),G=Symbol.for("react.suspense"),P=Symbol.for("react.suspense_list"),W=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),H=Symbol.for("react.offscreen"),D=Symbol.iterator;function I(e){return e===null||typeof e!="object"?null:(e=D&&e[D]||e["@@iterator"],typeof e=="function"?e:null)}var B=Object.assign,T;function j(e){if(T===void 0)try{throw Error()}catch(i){var n=i.stack.trim().match(/\n( *(at )?)/);T=n&&n[1]||""}return`
`+T+e}var oe=!1;function ie(e,n){if(!e||oe)return"";oe=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(n)if(n=function(){throw Error()},Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(n,[])}catch(Y){var u=Y}Reflect.construct(e,[],n)}else{try{n.call()}catch(Y){u=Y}e.call(n.prototype)}else{try{throw Error()}catch(Y){u=Y}e()}}catch(Y){if(Y&&u&&typeof Y.stack=="string"){for(var f=Y.stack.split(`
`),h=u.stack.split(`
`),x=f.length-1,R=h.length-1;1<=x&&0<=R&&f[x]!==h[R];)R--;for(;1<=x&&0<=R;x--,R--)if(f[x]!==h[R]){if(x!==1||R!==1)do if(x--,R--,0>R||f[x]!==h[R]){var L=`
`+f[x].replace(" at new "," at ");return e.displayName&&L.includes("<anonymous>")&&(L=L.replace("<anonymous>",e.displayName)),L}while(1<=x&&0<=R);break}}}finally{oe=!1,Error.prepareStackTrace=i}return(e=e?e.displayName||e.name:"")?j(e):""}function ue(e){switch(e.tag){case 5:return j(e.type);case 16:return j("Lazy");case 13:return j("Suspense");case 19:return j("SuspenseList");case 0:case 2:case 15:return e=ie(e.type,!1),e;case 11:return e=ie(e.type.render,!1),e;case 1:return e=ie(e.type,!0),e;default:return""}}function ae(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case F:return"Fragment";case $:return"Portal";case K:return"Profiler";case X:return"StrictMode";case G:return"Suspense";case P:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case J:return(e.displayName||"Context")+".Consumer";case Z:return(e._context.displayName||"Context")+".Provider";case ee:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case W:return n=e.displayName||null,n!==null?n:ae(e.type)||"Memo";case b:n=e._payload,e=e._init;try{return ae(e(n))}catch{}}return null}function fe(e){var n=e.type;switch(e.tag){case 24:return"Cache";case 9:return(n.displayName||"Context")+".Consumer";case 10:return(n._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=n.render,e=e.displayName||e.name||"",n.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ae(n);case 8:return n===X?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n}return null}function re(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ce(e){var n=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function Se(e){var n=ce(e)?"checked":"value",i=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),u=""+e[n];if(!e.hasOwnProperty(n)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var f=i.get,h=i.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return f.call(this)},set:function(x){u=""+x,h.call(this,x)}}),Object.defineProperty(e,n,{enumerable:i.enumerable}),{getValue:function(){return u},setValue:function(x){u=""+x},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}function _e(e){e._valueTracker||(e._valueTracker=Se(e))}function ye(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var i=n.getValue(),u="";return e&&(u=ce(e)?e.checked?"true":"false":e.value),e=u,e!==i?(n.setValue(e),!0):!1}function Pe(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ne(e,n){var i=n.checked;return B({},n,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:i??e._wrapperState.initialChecked})}function Ie(e,n){var i=n.defaultValue==null?"":n.defaultValue,u=n.checked!=null?n.checked:n.defaultChecked;i=re(n.value!=null?n.value:i),e._wrapperState={initialChecked:u,initialValue:i,controlled:n.type==="checkbox"||n.type==="radio"?n.checked!=null:n.value!=null}}function Be(e,n){n=n.checked,n!=null&&E(e,"checked",n,!1)}function Wt(e,n){Be(e,n);var i=re(n.value),u=n.type;if(i!=null)u==="number"?(i===0&&e.value===""||e.value!=i)&&(e.value=""+i):e.value!==""+i&&(e.value=""+i);else if(u==="submit"||u==="reset"){e.removeAttribute("value");return}n.hasOwnProperty("value")?qn(e,n.type,i):n.hasOwnProperty("defaultValue")&&qn(e,n.type,re(n.defaultValue)),n.checked==null&&n.defaultChecked!=null&&(e.defaultChecked=!!n.defaultChecked)}function Nr(e,n,i){if(n.hasOwnProperty("value")||n.hasOwnProperty("defaultValue")){var u=n.type;if(!(u!=="submit"&&u!=="reset"||n.value!==void 0&&n.value!==null))return;n=""+e._wrapperState.initialValue,i||n===e.value||(e.value=n),e.defaultValue=n}i=e.name,i!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,i!==""&&(e.name=i)}function qn(e,n,i){(n!=="number"||Pe(e.ownerDocument)!==e)&&(i==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+i&&(e.defaultValue=""+i))}var Xt=Array.isArray;function It(e,n,i,u){if(e=e.options,n){n={};for(var f=0;f<i.length;f++)n["$"+i[f]]=!0;for(i=0;i<e.length;i++)f=n.hasOwnProperty("$"+e[i].value),e[i].selected!==f&&(e[i].selected=f),f&&u&&(e[i].defaultSelected=!0)}else{for(i=""+re(i),n=null,f=0;f<e.length;f++){if(e[f].value===i){e[f].selected=!0,u&&(e[f].defaultSelected=!0);return}n!==null||e[f].disabled||(n=e[f])}n!==null&&(n.selected=!0)}}function rn(e,n){if(n.dangerouslySetInnerHTML!=null)throw Error(o(91));return B({},n,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function _n(e,n){var i=n.value;if(i==null){if(i=n.children,n=n.defaultValue,i!=null){if(n!=null)throw Error(o(92));if(Xt(i)){if(1<i.length)throw Error(o(93));i=i[0]}n=i}n==null&&(n=""),i=n}e._wrapperState={initialValue:re(i)}}function Cr(e,n){var i=re(n.value),u=re(n.defaultValue);i!=null&&(i=""+i,i!==e.value&&(e.value=i),n.defaultValue==null&&e.defaultValue!==i&&(e.defaultValue=i)),u!=null&&(e.defaultValue=""+u)}function Qn(e){var n=e.textContent;n===e._wrapperState.initialValue&&n!==""&&n!==null&&(e.value=n)}function on(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function sn(e,n){return e==null||e==="http://www.w3.org/1999/xhtml"?on(n):e==="http://www.w3.org/2000/svg"&&n==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Kn,Di=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(n,i,u,f){MSApp.execUnsafeLocalFunction(function(){return e(n,i,u,f)})}:e}(function(e,n){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=n;else{for(Kn=Kn||document.createElement("div"),Kn.innerHTML="<svg>"+n.valueOf().toString()+"</svg>",n=Kn.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;n.firstChild;)e.appendChild(n.firstChild)}});function ln(e,n){if(n){var i=e.firstChild;if(i&&i===e.lastChild&&i.nodeType===3){i.nodeValue=n;return}}e.textContent=n}var Gn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Gl=["Webkit","ms","Moz","O"];Object.keys(Gn).forEach(function(e){Gl.forEach(function(n){n=n+e.charAt(0).toUpperCase()+e.substring(1),Gn[n]=Gn[e]})});function $i(e,n,i){return n==null||typeof n=="boolean"||n===""?"":i||typeof n!="number"||n===0||Gn.hasOwnProperty(e)&&Gn[e]?(""+n).trim():n+"px"}function ji(e,n){e=e.style;for(var i in n)if(n.hasOwnProperty(i)){var u=i.indexOf("--")===0,f=$i(i,n[i],u);i==="float"&&(i="cssFloat"),u?e.setProperty(i,f):e[i]=f}}var Zl=B({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function vo(e,n){if(n){if(Zl[e]&&(n.children!=null||n.dangerouslySetInnerHTML!=null))throw Error(o(137,e));if(n.dangerouslySetInnerHTML!=null){if(n.children!=null)throw Error(o(60));if(typeof n.dangerouslySetInnerHTML!="object"||!("__html"in n.dangerouslySetInnerHTML))throw Error(o(61))}if(n.style!=null&&typeof n.style!="object")throw Error(o(62))}}function wo(e,n){if(e.indexOf("-")===-1)return typeof n.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xo=null;function So(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Eo=null,kn=null,Nn=null;function Fi(e){if(e=Qo(e)){if(typeof Eo!="function")throw Error(o(280));var n=e.stateNode;n&&(n=hs(n),Eo(e.stateNode,e.type,n))}}function bi(e){kn?Nn?Nn.push(e):Nn=[e]:kn=e}function _o(){if(kn){var e=kn,n=Nn;if(Nn=kn=null,Fi(e),n)for(e=0;e<n.length;e++)Fi(n[e])}}function Hi(e,n){return e(n)}function Bi(){}var ko=!1;function Vi(e,n,i){if(ko)return e(n,i);ko=!0;try{return Hi(e,n,i)}finally{ko=!1,(kn!==null||Nn!==null)&&(Bi(),_o())}}function Zn(e,n){var i=e.stateNode;if(i===null)return null;var u=hs(i);if(u===null)return null;i=u[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(u=!u.disabled)||(e=e.type,u=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!u;break e;default:e=!1}if(e)return null;if(i&&typeof i!="function")throw Error(o(231,n,typeof i));return i}var No=!1;if(d)try{var Jn={};Object.defineProperty(Jn,"passive",{get:function(){No=!0}}),window.addEventListener("test",Jn,Jn),window.removeEventListener("test",Jn,Jn)}catch{No=!1}function Jl(e,n,i,u,f,h,x,R,L){var Y=Array.prototype.slice.call(arguments,3);try{n.apply(i,Y)}catch(ne){this.onError(ne)}}var er=!1,Tr=null,Pr=!1,Co=null,eu={onError:function(e){er=!0,Tr=e}};function tu(e,n,i,u,f,h,x,R,L){er=!1,Tr=null,Jl.apply(eu,arguments)}function nu(e,n,i,u,f,h,x,R,L){if(tu.apply(this,arguments),er){if(er){var Y=Tr;er=!1,Tr=null}else throw Error(o(198));Pr||(Pr=!0,Co=Y)}}function Yt(e){var n=e,i=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do n=e,(n.flags&4098)!==0&&(i=n.return),e=n.return;while(e)}return n.tag===3?i:null}function To(e){if(e.tag===13){var n=e.memoizedState;if(n===null&&(e=e.alternate,e!==null&&(n=e.memoizedState)),n!==null)return n.dehydrated}return null}function Po(e){if(Yt(e)!==e)throw Error(o(188))}function ru(e){var n=e.alternate;if(!n){if(n=Yt(e),n===null)throw Error(o(188));return n!==e?null:e}for(var i=e,u=n;;){var f=i.return;if(f===null)break;var h=f.alternate;if(h===null){if(u=f.return,u!==null){i=u;continue}break}if(f.child===h.child){for(h=f.child;h;){if(h===i)return Po(f),e;if(h===u)return Po(f),n;h=h.sibling}throw Error(o(188))}if(i.return!==u.return)i=f,u=h;else{for(var x=!1,R=f.child;R;){if(R===i){x=!0,i=f,u=h;break}if(R===u){x=!0,u=f,i=h;break}R=R.sibling}if(!x){for(R=h.child;R;){if(R===i){x=!0,i=h,u=f;break}if(R===u){x=!0,u=h,i=f;break}R=R.sibling}if(!x)throw Error(o(189))}}if(i.alternate!==u)throw Error(o(190))}if(i.tag!==3)throw Error(o(188));return i.stateNode.current===i?e:n}function Ui(e){return e=ru(e),e!==null?Wi(e):null}function Wi(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var n=Wi(e);if(n!==null)return n;e=e.sibling}return null}var Xi=r.unstable_scheduleCallback,Yi=r.unstable_cancelCallback,ou=r.unstable_shouldYield,qi=r.unstable_requestPaint,je=r.unstable_now,iu=r.unstable_getCurrentPriorityLevel,Ro=r.unstable_ImmediatePriority,Qi=r.unstable_UserBlockingPriority,Rr=r.unstable_NormalPriority,su=r.unstable_LowPriority,Ki=r.unstable_IdlePriority,tr=null,Ct=null;function lu(e){if(Ct&&typeof Ct.onCommitFiberRoot=="function")try{Ct.onCommitFiberRoot(tr,e,void 0,(e.current.flags&128)===128)}catch{}}var vt=Math.clz32?Math.clz32:cu,uu=Math.log,au=Math.LN2;function cu(e){return e>>>=0,e===0?32:31-(uu(e)/au|0)|0}var Mr=64,un=4194304;function nr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ar(e,n){var i=e.pendingLanes;if(i===0)return 0;var u=0,f=e.suspendedLanes,h=e.pingedLanes,x=i&268435455;if(x!==0){var R=x&~f;R!==0?u=nr(R):(h&=x,h!==0&&(u=nr(h)))}else x=i&~f,x!==0?u=nr(x):h!==0&&(u=nr(h));if(u===0)return 0;if(n!==0&&n!==u&&(n&f)===0&&(f=u&-u,h=n&-n,f>=h||f===16&&(h&4194240)!==0))return n;if((u&4)!==0&&(u|=i&16),n=e.entangledLanes,n!==0)for(e=e.entanglements,n&=u;0<n;)i=31-vt(n),f=1<<i,u|=e[i],n&=~f;return u}function fu(e,n){switch(e){case 1:case 2:case 4:return n+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Mo(e,n){for(var i=e.suspendedLanes,u=e.pingedLanes,f=e.expirationTimes,h=e.pendingLanes;0<h;){var x=31-vt(h),R=1<<x,L=f[x];L===-1?((R&i)===0||(R&u)!==0)&&(f[x]=fu(R,n)):L<=n&&(e.expiredLanes|=R),h&=~R}}function Ao(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Gi(){var e=Mr;return Mr<<=1,(Mr&4194240)===0&&(Mr=64),e}function Lr(e){for(var n=[],i=0;31>i;i++)n.push(e);return n}function Lo(e,n,i){e.pendingLanes|=n,n!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,n=31-vt(n),e[n]=i}function a0(e,n){var i=e.pendingLanes&~n;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=n,e.mutableReadLanes&=n,e.entangledLanes&=n,n=e.entanglements;var u=e.eventTimes;for(e=e.expirationTimes;0<i;){var f=31-vt(i),h=1<<f;n[f]=0,u[f]=-1,e[f]=-1,i&=~h}}function du(e,n){var i=e.entangledLanes|=n;for(e=e.entanglements;i;){var u=31-vt(i),f=1<<u;f&n|e[u]&n&&(e[u]|=n),i&=~f}}var Ae=0;function Kc(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Gc,hu,Zc,Jc,ef,pu=!1,Zi=[],Cn=null,Tn=null,Pn=null,Io=new Map,zo=new Map,Rn=[],c0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function tf(e,n){switch(e){case"focusin":case"focusout":Cn=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":Pn=null;break;case"pointerover":case"pointerout":Io.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":zo.delete(n.pointerId)}}function Oo(e,n,i,u,f,h){return e===null||e.nativeEvent!==h?(e={blockedOn:n,domEventName:i,eventSystemFlags:u,nativeEvent:h,targetContainers:[f]},n!==null&&(n=Qo(n),n!==null&&hu(n)),e):(e.eventSystemFlags|=u,n=e.targetContainers,f!==null&&n.indexOf(f)===-1&&n.push(f),e)}function f0(e,n,i,u,f){switch(n){case"focusin":return Cn=Oo(Cn,e,n,i,u,f),!0;case"dragenter":return Tn=Oo(Tn,e,n,i,u,f),!0;case"mouseover":return Pn=Oo(Pn,e,n,i,u,f),!0;case"pointerover":var h=f.pointerId;return Io.set(h,Oo(Io.get(h)||null,e,n,i,u,f)),!0;case"gotpointercapture":return h=f.pointerId,zo.set(h,Oo(zo.get(h)||null,e,n,i,u,f)),!0}return!1}function nf(e){var n=rr(e.target);if(n!==null){var i=Yt(n);if(i!==null){if(n=i.tag,n===13){if(n=To(i),n!==null){e.blockedOn=n,ef(e.priority,function(){Zc(i)});return}}else if(n===3&&i.stateNode.current.memoizedState.isDehydrated){e.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ji(e){if(e.blockedOn!==null)return!1;for(var n=e.targetContainers;0<n.length;){var i=gu(e.domEventName,e.eventSystemFlags,n[0],e.nativeEvent);if(i===null){i=e.nativeEvent;var u=new i.constructor(i.type,i);xo=u,i.target.dispatchEvent(u),xo=null}else return n=Qo(i),n!==null&&hu(n),e.blockedOn=i,!1;n.shift()}return!0}function rf(e,n,i){Ji(e)&&i.delete(n)}function d0(){pu=!1,Cn!==null&&Ji(Cn)&&(Cn=null),Tn!==null&&Ji(Tn)&&(Tn=null),Pn!==null&&Ji(Pn)&&(Pn=null),Io.forEach(rf),zo.forEach(rf)}function Do(e,n){e.blockedOn===n&&(e.blockedOn=null,pu||(pu=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,d0)))}function $o(e){function n(f){return Do(f,e)}if(0<Zi.length){Do(Zi[0],e);for(var i=1;i<Zi.length;i++){var u=Zi[i];u.blockedOn===e&&(u.blockedOn=null)}}for(Cn!==null&&Do(Cn,e),Tn!==null&&Do(Tn,e),Pn!==null&&Do(Pn,e),Io.forEach(n),zo.forEach(n),i=0;i<Rn.length;i++)u=Rn[i],u.blockedOn===e&&(u.blockedOn=null);for(;0<Rn.length&&(i=Rn[0],i.blockedOn===null);)nf(i),i.blockedOn===null&&Rn.shift()}var Ir=N.ReactCurrentBatchConfig,es=!0;function h0(e,n,i,u){var f=Ae,h=Ir.transition;Ir.transition=null;try{Ae=1,mu(e,n,i,u)}finally{Ae=f,Ir.transition=h}}function p0(e,n,i,u){var f=Ae,h=Ir.transition;Ir.transition=null;try{Ae=4,mu(e,n,i,u)}finally{Ae=f,Ir.transition=h}}function mu(e,n,i,u){if(es){var f=gu(e,n,i,u);if(f===null)Iu(e,n,u,ts,i),tf(e,u);else if(f0(f,e,n,i,u))u.stopPropagation();else if(tf(e,u),n&4&&-1<c0.indexOf(e)){for(;f!==null;){var h=Qo(f);if(h!==null&&Gc(h),h=gu(e,n,i,u),h===null&&Iu(e,n,u,ts,i),h===f)break;f=h}f!==null&&u.stopPropagation()}else Iu(e,n,u,null,i)}}var ts=null;function gu(e,n,i,u){if(ts=null,e=So(u),e=rr(e),e!==null)if(n=Yt(e),n===null)e=null;else if(i=n.tag,i===13){if(e=To(n),e!==null)return e;e=null}else if(i===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null);return ts=e,null}function of(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(iu()){case Ro:return 1;case Qi:return 4;case Rr:case su:return 16;case Ki:return 536870912;default:return 16}default:return 16}}var Mn=null,yu=null,ns=null;function sf(){if(ns)return ns;var e,n=yu,i=n.length,u,f="value"in Mn?Mn.value:Mn.textContent,h=f.length;for(e=0;e<i&&n[e]===f[e];e++);var x=i-e;for(u=1;u<=x&&n[i-u]===f[h-u];u++);return ns=f.slice(e,1<u?1-u:void 0)}function rs(e){var n=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&n===13&&(e=13)):e=n,e===10&&(e=13),32<=e||e===13?e:0}function os(){return!0}function lf(){return!1}function wt(e){function n(i,u,f,h,x){this._reactName=i,this._targetInst=f,this.type=u,this.nativeEvent=h,this.target=x,this.currentTarget=null;for(var R in e)e.hasOwnProperty(R)&&(i=e[R],this[R]=i?i(h):h[R]);return this.isDefaultPrevented=(h.defaultPrevented!=null?h.defaultPrevented:h.returnValue===!1)?os:lf,this.isPropagationStopped=lf,this}return B(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=os)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=os)},persist:function(){},isPersistent:os}),n}var zr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vu=wt(zr),jo=B({},zr,{view:0,detail:0}),m0=wt(jo),wu,xu,Fo,is=B({},jo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Eu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Fo&&(Fo&&e.type==="mousemove"?(wu=e.screenX-Fo.screenX,xu=e.screenY-Fo.screenY):xu=wu=0,Fo=e),wu)},movementY:function(e){return"movementY"in e?e.movementY:xu}}),uf=wt(is),g0=B({},is,{dataTransfer:0}),y0=wt(g0),v0=B({},jo,{relatedTarget:0}),Su=wt(v0),w0=B({},zr,{animationName:0,elapsedTime:0,pseudoElement:0}),x0=wt(w0),S0=B({},zr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),E0=wt(S0),_0=B({},zr,{data:0}),af=wt(_0),k0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},N0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},C0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function T0(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):(e=C0[e])?!!n[e]:!1}function Eu(){return T0}var P0=B({},jo,{key:function(e){if(e.key){var n=k0[e.key]||e.key;if(n!=="Unidentified")return n}return e.type==="keypress"?(e=rs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?N0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Eu,charCode:function(e){return e.type==="keypress"?rs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?rs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),R0=wt(P0),M0=B({},is,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),cf=wt(M0),A0=B({},jo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Eu}),L0=wt(A0),I0=B({},zr,{propertyName:0,elapsedTime:0,pseudoElement:0}),z0=wt(I0),O0=B({},is,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),D0=wt(O0),$0=[9,13,27,32],_u=d&&"CompositionEvent"in window,bo=null;d&&"documentMode"in document&&(bo=document.documentMode);var j0=d&&"TextEvent"in window&&!bo,ff=d&&(!_u||bo&&8<bo&&11>=bo),df=" ",hf=!1;function pf(e,n){switch(e){case"keyup":return $0.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function mf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Or=!1;function F0(e,n){switch(e){case"compositionend":return mf(n);case"keypress":return n.which!==32?null:(hf=!0,df);case"textInput":return e=n.data,e===df&&hf?null:e;default:return null}}function b0(e,n){if(Or)return e==="compositionend"||!_u&&pf(e,n)?(e=sf(),ns=yu=Mn=null,Or=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return ff&&n.locale!=="ko"?null:n.data;default:return null}}var H0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gf(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n==="input"?!!H0[e.type]:n==="textarea"}function yf(e,n,i,u){bi(u),n=cs(n,"onChange"),0<n.length&&(i=new vu("onChange","change",null,i,u),e.push({event:i,listeners:n}))}var Ho=null,Bo=null;function B0(e){Of(e,0)}function ss(e){var n=br(e);if(ye(n))return e}function V0(e,n){if(e==="change")return n}var vf=!1;if(d){var ku;if(d){var Nu="oninput"in document;if(!Nu){var wf=document.createElement("div");wf.setAttribute("oninput","return;"),Nu=typeof wf.oninput=="function"}ku=Nu}else ku=!1;vf=ku&&(!document.documentMode||9<document.documentMode)}function xf(){Ho&&(Ho.detachEvent("onpropertychange",Sf),Bo=Ho=null)}function Sf(e){if(e.propertyName==="value"&&ss(Bo)){var n=[];yf(n,Bo,e,So(e)),Vi(B0,n)}}function U0(e,n,i){e==="focusin"?(xf(),Ho=n,Bo=i,Ho.attachEvent("onpropertychange",Sf)):e==="focusout"&&xf()}function W0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ss(Bo)}function X0(e,n){if(e==="click")return ss(n)}function Y0(e,n){if(e==="input"||e==="change")return ss(n)}function q0(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var zt=typeof Object.is=="function"?Object.is:q0;function Vo(e,n){if(zt(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;var i=Object.keys(e),u=Object.keys(n);if(i.length!==u.length)return!1;for(u=0;u<i.length;u++){var f=i[u];if(!p.call(n,f)||!zt(e[f],n[f]))return!1}return!0}function Ef(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function _f(e,n){var i=Ef(e);e=0;for(var u;i;){if(i.nodeType===3){if(u=e+i.textContent.length,e<=n&&u>=n)return{node:i,offset:n-e};e=u}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=Ef(i)}}function kf(e,n){return e&&n?e===n?!0:e&&e.nodeType===3?!1:n&&n.nodeType===3?kf(e,n.parentNode):"contains"in e?e.contains(n):e.compareDocumentPosition?!!(e.compareDocumentPosition(n)&16):!1:!1}function Nf(){for(var e=window,n=Pe();n instanceof e.HTMLIFrameElement;){try{var i=typeof n.contentWindow.location.href=="string"}catch{i=!1}if(i)e=n.contentWindow;else break;n=Pe(e.document)}return n}function Cu(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&(n==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||n==="textarea"||e.contentEditable==="true")}function Q0(e){var n=Nf(),i=e.focusedElem,u=e.selectionRange;if(n!==i&&i&&i.ownerDocument&&kf(i.ownerDocument.documentElement,i)){if(u!==null&&Cu(i)){if(n=u.start,e=u.end,e===void 0&&(e=n),"selectionStart"in i)i.selectionStart=n,i.selectionEnd=Math.min(e,i.value.length);else if(e=(n=i.ownerDocument||document)&&n.defaultView||window,e.getSelection){e=e.getSelection();var f=i.textContent.length,h=Math.min(u.start,f);u=u.end===void 0?h:Math.min(u.end,f),!e.extend&&h>u&&(f=u,u=h,h=f),f=_f(i,h);var x=_f(i,u);f&&x&&(e.rangeCount!==1||e.anchorNode!==f.node||e.anchorOffset!==f.offset||e.focusNode!==x.node||e.focusOffset!==x.offset)&&(n=n.createRange(),n.setStart(f.node,f.offset),e.removeAllRanges(),h>u?(e.addRange(n),e.extend(x.node,x.offset)):(n.setEnd(x.node,x.offset),e.addRange(n)))}}for(n=[],e=i;e=e.parentNode;)e.nodeType===1&&n.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof i.focus=="function"&&i.focus(),i=0;i<n.length;i++)e=n[i],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var K0=d&&"documentMode"in document&&11>=document.documentMode,Dr=null,Tu=null,Uo=null,Pu=!1;function Cf(e,n,i){var u=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;Pu||Dr==null||Dr!==Pe(u)||(u=Dr,"selectionStart"in u&&Cu(u)?u={start:u.selectionStart,end:u.selectionEnd}:(u=(u.ownerDocument&&u.ownerDocument.defaultView||window).getSelection(),u={anchorNode:u.anchorNode,anchorOffset:u.anchorOffset,focusNode:u.focusNode,focusOffset:u.focusOffset}),Uo&&Vo(Uo,u)||(Uo=u,u=cs(Tu,"onSelect"),0<u.length&&(n=new vu("onSelect","select",null,n,i),e.push({event:n,listeners:u}),n.target=Dr)))}function ls(e,n){var i={};return i[e.toLowerCase()]=n.toLowerCase(),i["Webkit"+e]="webkit"+n,i["Moz"+e]="moz"+n,i}var $r={animationend:ls("Animation","AnimationEnd"),animationiteration:ls("Animation","AnimationIteration"),animationstart:ls("Animation","AnimationStart"),transitionend:ls("Transition","TransitionEnd")},Ru={},Tf={};d&&(Tf=document.createElement("div").style,"AnimationEvent"in window||(delete $r.animationend.animation,delete $r.animationiteration.animation,delete $r.animationstart.animation),"TransitionEvent"in window||delete $r.transitionend.transition);function us(e){if(Ru[e])return Ru[e];if(!$r[e])return e;var n=$r[e],i;for(i in n)if(n.hasOwnProperty(i)&&i in Tf)return Ru[e]=n[i];return e}var Pf=us("animationend"),Rf=us("animationiteration"),Mf=us("animationstart"),Af=us("transitionend"),Lf=new Map,If="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function An(e,n){Lf.set(e,n),a(n,[e])}for(var Mu=0;Mu<If.length;Mu++){var Au=If[Mu],G0=Au.toLowerCase(),Z0=Au[0].toUpperCase()+Au.slice(1);An(G0,"on"+Z0)}An(Pf,"onAnimationEnd"),An(Rf,"onAnimationIteration"),An(Mf,"onAnimationStart"),An("dblclick","onDoubleClick"),An("focusin","onFocus"),An("focusout","onBlur"),An(Af,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),a("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),a("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),a("onBeforeInput",["compositionend","keypress","textInput","paste"]),a("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),a("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),a("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Wo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),J0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Wo));function zf(e,n,i){var u=e.type||"unknown-event";e.currentTarget=i,nu(u,n,void 0,e),e.currentTarget=null}function Of(e,n){n=(n&4)!==0;for(var i=0;i<e.length;i++){var u=e[i],f=u.event;u=u.listeners;e:{var h=void 0;if(n)for(var x=u.length-1;0<=x;x--){var R=u[x],L=R.instance,Y=R.currentTarget;if(R=R.listener,L!==h&&f.isPropagationStopped())break e;zf(f,R,Y),h=L}else for(x=0;x<u.length;x++){if(R=u[x],L=R.instance,Y=R.currentTarget,R=R.listener,L!==h&&f.isPropagationStopped())break e;zf(f,R,Y),h=L}}}if(Pr)throw e=Co,Pr=!1,Co=null,e}function ze(e,n){var i=n[Fu];i===void 0&&(i=n[Fu]=new Set);var u=e+"__bubble";i.has(u)||(Df(n,e,2,!1),i.add(u))}function Lu(e,n,i){var u=0;n&&(u|=4),Df(i,e,u,n)}var as="_reactListening"+Math.random().toString(36).slice(2);function Xo(e){if(!e[as]){e[as]=!0,s.forEach(function(i){i!=="selectionchange"&&(J0.has(i)||Lu(i,!1,e),Lu(i,!0,e))});var n=e.nodeType===9?e:e.ownerDocument;n===null||n[as]||(n[as]=!0,Lu("selectionchange",!1,n))}}function Df(e,n,i,u){switch(of(n)){case 1:var f=h0;break;case 4:f=p0;break;default:f=mu}i=f.bind(null,n,i,e),f=void 0,!No||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(f=!0),u?f!==void 0?e.addEventListener(n,i,{capture:!0,passive:f}):e.addEventListener(n,i,!0):f!==void 0?e.addEventListener(n,i,{passive:f}):e.addEventListener(n,i,!1)}function Iu(e,n,i,u,f){var h=u;if((n&1)===0&&(n&2)===0&&u!==null)e:for(;;){if(u===null)return;var x=u.tag;if(x===3||x===4){var R=u.stateNode.containerInfo;if(R===f||R.nodeType===8&&R.parentNode===f)break;if(x===4)for(x=u.return;x!==null;){var L=x.tag;if((L===3||L===4)&&(L=x.stateNode.containerInfo,L===f||L.nodeType===8&&L.parentNode===f))return;x=x.return}for(;R!==null;){if(x=rr(R),x===null)return;if(L=x.tag,L===5||L===6){u=h=x;continue e}R=R.parentNode}}u=u.return}Vi(function(){var Y=h,ne=So(i),se=[];e:{var te=Lf.get(e);if(te!==void 0){var de=vu,pe=e;switch(e){case"keypress":if(rs(i)===0)break e;case"keydown":case"keyup":de=R0;break;case"focusin":pe="focus",de=Su;break;case"focusout":pe="blur",de=Su;break;case"beforeblur":case"afterblur":de=Su;break;case"click":if(i.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":de=uf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":de=y0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":de=L0;break;case Pf:case Rf:case Mf:de=x0;break;case Af:de=z0;break;case"scroll":de=m0;break;case"wheel":de=D0;break;case"copy":case"cut":case"paste":de=E0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":de=cf}var me=(n&4)!==0,Ue=!me&&e==="scroll",V=me?te!==null?te+"Capture":null:te;me=[];for(var O=Y,U;O!==null;){U=O;var le=U.stateNode;if(U.tag===5&&le!==null&&(U=le,V!==null&&(le=Zn(O,V),le!=null&&me.push(Yo(O,le,U)))),Ue)break;O=O.return}0<me.length&&(te=new de(te,pe,null,i,ne),se.push({event:te,listeners:me}))}}if((n&7)===0){e:{if(te=e==="mouseover"||e==="pointerover",de=e==="mouseout"||e==="pointerout",te&&i!==xo&&(pe=i.relatedTarget||i.fromElement)&&(rr(pe)||pe[an]))break e;if((de||te)&&(te=ne.window===ne?ne:(te=ne.ownerDocument)?te.defaultView||te.parentWindow:window,de?(pe=i.relatedTarget||i.toElement,de=Y,pe=pe?rr(pe):null,pe!==null&&(Ue=Yt(pe),pe!==Ue||pe.tag!==5&&pe.tag!==6)&&(pe=null)):(de=null,pe=Y),de!==pe)){if(me=uf,le="onMouseLeave",V="onMouseEnter",O="mouse",(e==="pointerout"||e==="pointerover")&&(me=cf,le="onPointerLeave",V="onPointerEnter",O="pointer"),Ue=de==null?te:br(de),U=pe==null?te:br(pe),te=new me(le,O+"leave",de,i,ne),te.target=Ue,te.relatedTarget=U,le=null,rr(ne)===Y&&(me=new me(V,O+"enter",pe,i,ne),me.target=U,me.relatedTarget=Ue,le=me),Ue=le,de&&pe)t:{for(me=de,V=pe,O=0,U=me;U;U=jr(U))O++;for(U=0,le=V;le;le=jr(le))U++;for(;0<O-U;)me=jr(me),O--;for(;0<U-O;)V=jr(V),U--;for(;O--;){if(me===V||V!==null&&me===V.alternate)break t;me=jr(me),V=jr(V)}me=null}else me=null;de!==null&&$f(se,te,de,me,!1),pe!==null&&Ue!==null&&$f(se,Ue,pe,me,!0)}}e:{if(te=Y?br(Y):window,de=te.nodeName&&te.nodeName.toLowerCase(),de==="select"||de==="input"&&te.type==="file")var ge=V0;else if(gf(te))if(vf)ge=Y0;else{ge=W0;var we=U0}else(de=te.nodeName)&&de.toLowerCase()==="input"&&(te.type==="checkbox"||te.type==="radio")&&(ge=X0);if(ge&&(ge=ge(e,Y))){yf(se,ge,i,ne);break e}we&&we(e,te,Y),e==="focusout"&&(we=te._wrapperState)&&we.controlled&&te.type==="number"&&qn(te,"number",te.value)}switch(we=Y?br(Y):window,e){case"focusin":(gf(we)||we.contentEditable==="true")&&(Dr=we,Tu=Y,Uo=null);break;case"focusout":Uo=Tu=Dr=null;break;case"mousedown":Pu=!0;break;case"contextmenu":case"mouseup":case"dragend":Pu=!1,Cf(se,i,ne);break;case"selectionchange":if(K0)break;case"keydown":case"keyup":Cf(se,i,ne)}var xe;if(_u)e:{switch(e){case"compositionstart":var Ee="onCompositionStart";break e;case"compositionend":Ee="onCompositionEnd";break e;case"compositionupdate":Ee="onCompositionUpdate";break e}Ee=void 0}else Or?pf(e,i)&&(Ee="onCompositionEnd"):e==="keydown"&&i.keyCode===229&&(Ee="onCompositionStart");Ee&&(ff&&i.locale!=="ko"&&(Or||Ee!=="onCompositionStart"?Ee==="onCompositionEnd"&&Or&&(xe=sf()):(Mn=ne,yu="value"in Mn?Mn.value:Mn.textContent,Or=!0)),we=cs(Y,Ee),0<we.length&&(Ee=new af(Ee,e,null,i,ne),se.push({event:Ee,listeners:we}),xe?Ee.data=xe:(xe=mf(i),xe!==null&&(Ee.data=xe)))),(xe=j0?F0(e,i):b0(e,i))&&(Y=cs(Y,"onBeforeInput"),0<Y.length&&(ne=new af("onBeforeInput","beforeinput",null,i,ne),se.push({event:ne,listeners:Y}),ne.data=xe))}Of(se,n)})}function Yo(e,n,i){return{instance:e,listener:n,currentTarget:i}}function cs(e,n){for(var i=n+"Capture",u=[];e!==null;){var f=e,h=f.stateNode;f.tag===5&&h!==null&&(f=h,h=Zn(e,i),h!=null&&u.unshift(Yo(e,h,f)),h=Zn(e,n),h!=null&&u.push(Yo(e,h,f))),e=e.return}return u}function jr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function $f(e,n,i,u,f){for(var h=n._reactName,x=[];i!==null&&i!==u;){var R=i,L=R.alternate,Y=R.stateNode;if(L!==null&&L===u)break;R.tag===5&&Y!==null&&(R=Y,f?(L=Zn(i,h),L!=null&&x.unshift(Yo(i,L,R))):f||(L=Zn(i,h),L!=null&&x.push(Yo(i,L,R)))),i=i.return}x.length!==0&&e.push({event:n,listeners:x})}var ey=/\r\n?/g,ty=/\u0000|\uFFFD/g;function jf(e){return(typeof e=="string"?e:""+e).replace(ey,`
`).replace(ty,"")}function fs(e,n,i){if(n=jf(n),jf(e)!==n&&i)throw Error(o(425))}function ds(){}var zu=null,Ou=null;function Du(e,n){return e==="textarea"||e==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var $u=typeof setTimeout=="function"?setTimeout:void 0,ny=typeof clearTimeout=="function"?clearTimeout:void 0,Ff=typeof Promise=="function"?Promise:void 0,ry=typeof queueMicrotask=="function"?queueMicrotask:typeof Ff<"u"?function(e){return Ff.resolve(null).then(e).catch(oy)}:$u;function oy(e){setTimeout(function(){throw e})}function ju(e,n){var i=n,u=0;do{var f=i.nextSibling;if(e.removeChild(i),f&&f.nodeType===8)if(i=f.data,i==="/$"){if(u===0){e.removeChild(f),$o(n);return}u--}else i!=="$"&&i!=="$?"&&i!=="$!"||u++;i=f}while(i);$o(n)}function Ln(e){for(;e!=null;e=e.nextSibling){var n=e.nodeType;if(n===1||n===3)break;if(n===8){if(n=e.data,n==="$"||n==="$!"||n==="$?")break;if(n==="/$")return null}}return e}function bf(e){e=e.previousSibling;for(var n=0;e;){if(e.nodeType===8){var i=e.data;if(i==="$"||i==="$!"||i==="$?"){if(n===0)return e;n--}else i==="/$"&&n++}e=e.previousSibling}return null}var Fr=Math.random().toString(36).slice(2),qt="__reactFiber$"+Fr,qo="__reactProps$"+Fr,an="__reactContainer$"+Fr,Fu="__reactEvents$"+Fr,iy="__reactListeners$"+Fr,sy="__reactHandles$"+Fr;function rr(e){var n=e[qt];if(n)return n;for(var i=e.parentNode;i;){if(n=i[an]||i[qt]){if(i=n.alternate,n.child!==null||i!==null&&i.child!==null)for(e=bf(e);e!==null;){if(i=e[qt])return i;e=bf(e)}return n}e=i,i=e.parentNode}return null}function Qo(e){return e=e[qt]||e[an],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function br(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(o(33))}function hs(e){return e[qo]||null}var bu=[],Hr=-1;function In(e){return{current:e}}function Oe(e){0>Hr||(e.current=bu[Hr],bu[Hr]=null,Hr--)}function Le(e,n){Hr++,bu[Hr]=e.current,e.current=n}var zn={},nt=In(zn),at=In(!1),or=zn;function Br(e,n){var i=e.type.contextTypes;if(!i)return zn;var u=e.stateNode;if(u&&u.__reactInternalMemoizedUnmaskedChildContext===n)return u.__reactInternalMemoizedMaskedChildContext;var f={},h;for(h in i)f[h]=n[h];return u&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=n,e.__reactInternalMemoizedMaskedChildContext=f),f}function ct(e){return e=e.childContextTypes,e!=null}function ps(){Oe(at),Oe(nt)}function Hf(e,n,i){if(nt.current!==zn)throw Error(o(168));Le(nt,n),Le(at,i)}function Bf(e,n,i){var u=e.stateNode;if(n=n.childContextTypes,typeof u.getChildContext!="function")return i;u=u.getChildContext();for(var f in u)if(!(f in n))throw Error(o(108,fe(e)||"Unknown",f));return B({},i,u)}function ms(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||zn,or=nt.current,Le(nt,e),Le(at,at.current),!0}function Vf(e,n,i){var u=e.stateNode;if(!u)throw Error(o(169));i?(e=Bf(e,n,or),u.__reactInternalMemoizedMergedChildContext=e,Oe(at),Oe(nt),Le(nt,e)):Oe(at),Le(at,i)}var cn=null,gs=!1,Hu=!1;function Uf(e){cn===null?cn=[e]:cn.push(e)}function ly(e){gs=!0,Uf(e)}function On(){if(!Hu&&cn!==null){Hu=!0;var e=0,n=Ae;try{var i=cn;for(Ae=1;e<i.length;e++){var u=i[e];do u=u(!0);while(u!==null)}cn=null,gs=!1}catch(f){throw cn!==null&&(cn=cn.slice(e+1)),Xi(Ro,On),f}finally{Ae=n,Hu=!1}}return null}var Vr=[],Ur=0,ys=null,vs=0,Tt=[],Pt=0,ir=null,fn=1,dn="";function sr(e,n){Vr[Ur++]=vs,Vr[Ur++]=ys,ys=e,vs=n}function Wf(e,n,i){Tt[Pt++]=fn,Tt[Pt++]=dn,Tt[Pt++]=ir,ir=e;var u=fn;e=dn;var f=32-vt(u)-1;u&=~(1<<f),i+=1;var h=32-vt(n)+f;if(30<h){var x=f-f%5;h=(u&(1<<x)-1).toString(32),u>>=x,f-=x,fn=1<<32-vt(n)+f|i<<f|u,dn=h+e}else fn=1<<h|i<<f|u,dn=e}function Bu(e){e.return!==null&&(sr(e,1),Wf(e,1,0))}function Vu(e){for(;e===ys;)ys=Vr[--Ur],Vr[Ur]=null,vs=Vr[--Ur],Vr[Ur]=null;for(;e===ir;)ir=Tt[--Pt],Tt[Pt]=null,dn=Tt[--Pt],Tt[Pt]=null,fn=Tt[--Pt],Tt[Pt]=null}var xt=null,St=null,De=!1,Ot=null;function Xf(e,n){var i=Lt(5,null,null,0);i.elementType="DELETED",i.stateNode=n,i.return=e,n=e.deletions,n===null?(e.deletions=[i],e.flags|=16):n.push(i)}function Yf(e,n){switch(e.tag){case 5:var i=e.type;return n=n.nodeType!==1||i.toLowerCase()!==n.nodeName.toLowerCase()?null:n,n!==null?(e.stateNode=n,xt=e,St=Ln(n.firstChild),!0):!1;case 6:return n=e.pendingProps===""||n.nodeType!==3?null:n,n!==null?(e.stateNode=n,xt=e,St=null,!0):!1;case 13:return n=n.nodeType!==8?null:n,n!==null?(i=ir!==null?{id:fn,overflow:dn}:null,e.memoizedState={dehydrated:n,treeContext:i,retryLane:1073741824},i=Lt(18,null,null,0),i.stateNode=n,i.return=e,e.child=i,xt=e,St=null,!0):!1;default:return!1}}function Uu(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Wu(e){if(De){var n=St;if(n){var i=n;if(!Yf(e,n)){if(Uu(e))throw Error(o(418));n=Ln(i.nextSibling);var u=xt;n&&Yf(e,n)?Xf(u,i):(e.flags=e.flags&-4097|2,De=!1,xt=e)}}else{if(Uu(e))throw Error(o(418));e.flags=e.flags&-4097|2,De=!1,xt=e}}}function qf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;xt=e}function ws(e){if(e!==xt)return!1;if(!De)return qf(e),De=!0,!1;var n;if((n=e.tag!==3)&&!(n=e.tag!==5)&&(n=e.type,n=n!=="head"&&n!=="body"&&!Du(e.type,e.memoizedProps)),n&&(n=St)){if(Uu(e))throw Qf(),Error(o(418));for(;n;)Xf(e,n),n=Ln(n.nextSibling)}if(qf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(e.nodeType===8){var i=e.data;if(i==="/$"){if(n===0){St=Ln(e.nextSibling);break e}n--}else i!=="$"&&i!=="$!"&&i!=="$?"||n++}e=e.nextSibling}St=null}}else St=xt?Ln(e.stateNode.nextSibling):null;return!0}function Qf(){for(var e=St;e;)e=Ln(e.nextSibling)}function Wr(){St=xt=null,De=!1}function Xu(e){Ot===null?Ot=[e]:Ot.push(e)}var uy=N.ReactCurrentBatchConfig;function Ko(e,n,i){if(e=i.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(i._owner){if(i=i._owner,i){if(i.tag!==1)throw Error(o(309));var u=i.stateNode}if(!u)throw Error(o(147,e));var f=u,h=""+e;return n!==null&&n.ref!==null&&typeof n.ref=="function"&&n.ref._stringRef===h?n.ref:(n=function(x){var R=f.refs;x===null?delete R[h]:R[h]=x},n._stringRef=h,n)}if(typeof e!="string")throw Error(o(284));if(!i._owner)throw Error(o(290,e))}return e}function xs(e,n){throw e=Object.prototype.toString.call(n),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e))}function Kf(e){var n=e._init;return n(e._payload)}function Gf(e){function n(V,O){if(e){var U=V.deletions;U===null?(V.deletions=[O],V.flags|=16):U.push(O)}}function i(V,O){if(!e)return null;for(;O!==null;)n(V,O),O=O.sibling;return null}function u(V,O){for(V=new Map;O!==null;)O.key!==null?V.set(O.key,O):V.set(O.index,O),O=O.sibling;return V}function f(V,O){return V=Vn(V,O),V.index=0,V.sibling=null,V}function h(V,O,U){return V.index=U,e?(U=V.alternate,U!==null?(U=U.index,U<O?(V.flags|=2,O):U):(V.flags|=2,O)):(V.flags|=1048576,O)}function x(V){return e&&V.alternate===null&&(V.flags|=2),V}function R(V,O,U,le){return O===null||O.tag!==6?(O=$a(U,V.mode,le),O.return=V,O):(O=f(O,U),O.return=V,O)}function L(V,O,U,le){var ge=U.type;return ge===F?ne(V,O,U.props.children,le,U.key):O!==null&&(O.elementType===ge||typeof ge=="object"&&ge!==null&&ge.$$typeof===b&&Kf(ge)===O.type)?(le=f(O,U.props),le.ref=Ko(V,O,U),le.return=V,le):(le=Us(U.type,U.key,U.props,null,V.mode,le),le.ref=Ko(V,O,U),le.return=V,le)}function Y(V,O,U,le){return O===null||O.tag!==4||O.stateNode.containerInfo!==U.containerInfo||O.stateNode.implementation!==U.implementation?(O=ja(U,V.mode,le),O.return=V,O):(O=f(O,U.children||[]),O.return=V,O)}function ne(V,O,U,le,ge){return O===null||O.tag!==7?(O=pr(U,V.mode,le,ge),O.return=V,O):(O=f(O,U),O.return=V,O)}function se(V,O,U){if(typeof O=="string"&&O!==""||typeof O=="number")return O=$a(""+O,V.mode,U),O.return=V,O;if(typeof O=="object"&&O!==null){switch(O.$$typeof){case A:return U=Us(O.type,O.key,O.props,null,V.mode,U),U.ref=Ko(V,null,O),U.return=V,U;case $:return O=ja(O,V.mode,U),O.return=V,O;case b:var le=O._init;return se(V,le(O._payload),U)}if(Xt(O)||I(O))return O=pr(O,V.mode,U,null),O.return=V,O;xs(V,O)}return null}function te(V,O,U,le){var ge=O!==null?O.key:null;if(typeof U=="string"&&U!==""||typeof U=="number")return ge!==null?null:R(V,O,""+U,le);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case A:return U.key===ge?L(V,O,U,le):null;case $:return U.key===ge?Y(V,O,U,le):null;case b:return ge=U._init,te(V,O,ge(U._payload),le)}if(Xt(U)||I(U))return ge!==null?null:ne(V,O,U,le,null);xs(V,U)}return null}function de(V,O,U,le,ge){if(typeof le=="string"&&le!==""||typeof le=="number")return V=V.get(U)||null,R(O,V,""+le,ge);if(typeof le=="object"&&le!==null){switch(le.$$typeof){case A:return V=V.get(le.key===null?U:le.key)||null,L(O,V,le,ge);case $:return V=V.get(le.key===null?U:le.key)||null,Y(O,V,le,ge);case b:var we=le._init;return de(V,O,U,we(le._payload),ge)}if(Xt(le)||I(le))return V=V.get(U)||null,ne(O,V,le,ge,null);xs(O,le)}return null}function pe(V,O,U,le){for(var ge=null,we=null,xe=O,Ee=O=0,Ze=null;xe!==null&&Ee<U.length;Ee++){xe.index>Ee?(Ze=xe,xe=null):Ze=xe.sibling;var Me=te(V,xe,U[Ee],le);if(Me===null){xe===null&&(xe=Ze);break}e&&xe&&Me.alternate===null&&n(V,xe),O=h(Me,O,Ee),we===null?ge=Me:we.sibling=Me,we=Me,xe=Ze}if(Ee===U.length)return i(V,xe),De&&sr(V,Ee),ge;if(xe===null){for(;Ee<U.length;Ee++)xe=se(V,U[Ee],le),xe!==null&&(O=h(xe,O,Ee),we===null?ge=xe:we.sibling=xe,we=xe);return De&&sr(V,Ee),ge}for(xe=u(V,xe);Ee<U.length;Ee++)Ze=de(xe,V,Ee,U[Ee],le),Ze!==null&&(e&&Ze.alternate!==null&&xe.delete(Ze.key===null?Ee:Ze.key),O=h(Ze,O,Ee),we===null?ge=Ze:we.sibling=Ze,we=Ze);return e&&xe.forEach(function(Un){return n(V,Un)}),De&&sr(V,Ee),ge}function me(V,O,U,le){var ge=I(U);if(typeof ge!="function")throw Error(o(150));if(U=ge.call(U),U==null)throw Error(o(151));for(var we=ge=null,xe=O,Ee=O=0,Ze=null,Me=U.next();xe!==null&&!Me.done;Ee++,Me=U.next()){xe.index>Ee?(Ze=xe,xe=null):Ze=xe.sibling;var Un=te(V,xe,Me.value,le);if(Un===null){xe===null&&(xe=Ze);break}e&&xe&&Un.alternate===null&&n(V,xe),O=h(Un,O,Ee),we===null?ge=Un:we.sibling=Un,we=Un,xe=Ze}if(Me.done)return i(V,xe),De&&sr(V,Ee),ge;if(xe===null){for(;!Me.done;Ee++,Me=U.next())Me=se(V,Me.value,le),Me!==null&&(O=h(Me,O,Ee),we===null?ge=Me:we.sibling=Me,we=Me);return De&&sr(V,Ee),ge}for(xe=u(V,xe);!Me.done;Ee++,Me=U.next())Me=de(xe,V,Ee,Me.value,le),Me!==null&&(e&&Me.alternate!==null&&xe.delete(Me.key===null?Ee:Me.key),O=h(Me,O,Ee),we===null?ge=Me:we.sibling=Me,we=Me);return e&&xe.forEach(function(Hy){return n(V,Hy)}),De&&sr(V,Ee),ge}function Ue(V,O,U,le){if(typeof U=="object"&&U!==null&&U.type===F&&U.key===null&&(U=U.props.children),typeof U=="object"&&U!==null){switch(U.$$typeof){case A:e:{for(var ge=U.key,we=O;we!==null;){if(we.key===ge){if(ge=U.type,ge===F){if(we.tag===7){i(V,we.sibling),O=f(we,U.props.children),O.return=V,V=O;break e}}else if(we.elementType===ge||typeof ge=="object"&&ge!==null&&ge.$$typeof===b&&Kf(ge)===we.type){i(V,we.sibling),O=f(we,U.props),O.ref=Ko(V,we,U),O.return=V,V=O;break e}i(V,we);break}else n(V,we);we=we.sibling}U.type===F?(O=pr(U.props.children,V.mode,le,U.key),O.return=V,V=O):(le=Us(U.type,U.key,U.props,null,V.mode,le),le.ref=Ko(V,O,U),le.return=V,V=le)}return x(V);case $:e:{for(we=U.key;O!==null;){if(O.key===we)if(O.tag===4&&O.stateNode.containerInfo===U.containerInfo&&O.stateNode.implementation===U.implementation){i(V,O.sibling),O=f(O,U.children||[]),O.return=V,V=O;break e}else{i(V,O);break}else n(V,O);O=O.sibling}O=ja(U,V.mode,le),O.return=V,V=O}return x(V);case b:return we=U._init,Ue(V,O,we(U._payload),le)}if(Xt(U))return pe(V,O,U,le);if(I(U))return me(V,O,U,le);xs(V,U)}return typeof U=="string"&&U!==""||typeof U=="number"?(U=""+U,O!==null&&O.tag===6?(i(V,O.sibling),O=f(O,U),O.return=V,V=O):(i(V,O),O=$a(U,V.mode,le),O.return=V,V=O),x(V)):i(V,O)}return Ue}var Xr=Gf(!0),Zf=Gf(!1),Ss=In(null),Es=null,Yr=null,Yu=null;function qu(){Yu=Yr=Es=null}function Qu(e){var n=Ss.current;Oe(Ss),e._currentValue=n}function Ku(e,n,i){for(;e!==null;){var u=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,u!==null&&(u.childLanes|=n)):u!==null&&(u.childLanes&n)!==n&&(u.childLanes|=n),e===i)break;e=e.return}}function qr(e,n){Es=e,Yu=Yr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&n)!==0&&(ft=!0),e.firstContext=null)}function Rt(e){var n=e._currentValue;if(Yu!==e)if(e={context:e,memoizedValue:n,next:null},Yr===null){if(Es===null)throw Error(o(308));Yr=e,Es.dependencies={lanes:0,firstContext:e}}else Yr=Yr.next=e;return n}var lr=null;function Gu(e){lr===null?lr=[e]:lr.push(e)}function Jf(e,n,i,u){var f=n.interleaved;return f===null?(i.next=i,Gu(n)):(i.next=f.next,f.next=i),n.interleaved=i,hn(e,u)}function hn(e,n){e.lanes|=n;var i=e.alternate;for(i!==null&&(i.lanes|=n),i=e,e=e.return;e!==null;)e.childLanes|=n,i=e.alternate,i!==null&&(i.childLanes|=n),i=e,e=e.return;return i.tag===3?i.stateNode:null}var Dn=!1;function Zu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ed(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function pn(e,n){return{eventTime:e,lane:n,tag:0,payload:null,callback:null,next:null}}function $n(e,n,i){var u=e.updateQueue;if(u===null)return null;if(u=u.shared,(Re&2)!==0){var f=u.pending;return f===null?n.next=n:(n.next=f.next,f.next=n),u.pending=n,hn(e,i)}return f=u.interleaved,f===null?(n.next=n,Gu(u)):(n.next=f.next,f.next=n),u.interleaved=n,hn(e,i)}function _s(e,n,i){if(n=n.updateQueue,n!==null&&(n=n.shared,(i&4194240)!==0)){var u=n.lanes;u&=e.pendingLanes,i|=u,n.lanes=i,du(e,i)}}function td(e,n){var i=e.updateQueue,u=e.alternate;if(u!==null&&(u=u.updateQueue,i===u)){var f=null,h=null;if(i=i.firstBaseUpdate,i!==null){do{var x={eventTime:i.eventTime,lane:i.lane,tag:i.tag,payload:i.payload,callback:i.callback,next:null};h===null?f=h=x:h=h.next=x,i=i.next}while(i!==null);h===null?f=h=n:h=h.next=n}else f=h=n;i={baseState:u.baseState,firstBaseUpdate:f,lastBaseUpdate:h,shared:u.shared,effects:u.effects},e.updateQueue=i;return}e=i.lastBaseUpdate,e===null?i.firstBaseUpdate=n:e.next=n,i.lastBaseUpdate=n}function ks(e,n,i,u){var f=e.updateQueue;Dn=!1;var h=f.firstBaseUpdate,x=f.lastBaseUpdate,R=f.shared.pending;if(R!==null){f.shared.pending=null;var L=R,Y=L.next;L.next=null,x===null?h=Y:x.next=Y,x=L;var ne=e.alternate;ne!==null&&(ne=ne.updateQueue,R=ne.lastBaseUpdate,R!==x&&(R===null?ne.firstBaseUpdate=Y:R.next=Y,ne.lastBaseUpdate=L))}if(h!==null){var se=f.baseState;x=0,ne=Y=L=null,R=h;do{var te=R.lane,de=R.eventTime;if((u&te)===te){ne!==null&&(ne=ne.next={eventTime:de,lane:0,tag:R.tag,payload:R.payload,callback:R.callback,next:null});e:{var pe=e,me=R;switch(te=n,de=i,me.tag){case 1:if(pe=me.payload,typeof pe=="function"){se=pe.call(de,se,te);break e}se=pe;break e;case 3:pe.flags=pe.flags&-65537|128;case 0:if(pe=me.payload,te=typeof pe=="function"?pe.call(de,se,te):pe,te==null)break e;se=B({},se,te);break e;case 2:Dn=!0}}R.callback!==null&&R.lane!==0&&(e.flags|=64,te=f.effects,te===null?f.effects=[R]:te.push(R))}else de={eventTime:de,lane:te,tag:R.tag,payload:R.payload,callback:R.callback,next:null},ne===null?(Y=ne=de,L=se):ne=ne.next=de,x|=te;if(R=R.next,R===null){if(R=f.shared.pending,R===null)break;te=R,R=te.next,te.next=null,f.lastBaseUpdate=te,f.shared.pending=null}}while(!0);if(ne===null&&(L=se),f.baseState=L,f.firstBaseUpdate=Y,f.lastBaseUpdate=ne,n=f.shared.interleaved,n!==null){f=n;do x|=f.lane,f=f.next;while(f!==n)}else h===null&&(f.shared.lanes=0);cr|=x,e.lanes=x,e.memoizedState=se}}function nd(e,n,i){if(e=n.effects,n.effects=null,e!==null)for(n=0;n<e.length;n++){var u=e[n],f=u.callback;if(f!==null){if(u.callback=null,u=i,typeof f!="function")throw Error(o(191,f));f.call(u)}}}var Go={},Qt=In(Go),Zo=In(Go),Jo=In(Go);function ur(e){if(e===Go)throw Error(o(174));return e}function Ju(e,n){switch(Le(Jo,n),Le(Zo,e),Le(Qt,Go),e=n.nodeType,e){case 9:case 11:n=(n=n.documentElement)?n.namespaceURI:sn(null,"");break;default:e=e===8?n.parentNode:n,n=e.namespaceURI||null,e=e.tagName,n=sn(n,e)}Oe(Qt),Le(Qt,n)}function Qr(){Oe(Qt),Oe(Zo),Oe(Jo)}function rd(e){ur(Jo.current);var n=ur(Qt.current),i=sn(n,e.type);n!==i&&(Le(Zo,e),Le(Qt,i))}function ea(e){Zo.current===e&&(Oe(Qt),Oe(Zo))}var Fe=In(0);function Ns(e){for(var n=e;n!==null;){if(n.tag===13){var i=n.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||i.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if((n.flags&128)!==0)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var ta=[];function na(){for(var e=0;e<ta.length;e++)ta[e]._workInProgressVersionPrimary=null;ta.length=0}var Cs=N.ReactCurrentDispatcher,ra=N.ReactCurrentBatchConfig,ar=0,be=null,qe=null,Ke=null,Ts=!1,ei=!1,ti=0,ay=0;function rt(){throw Error(o(321))}function oa(e,n){if(n===null)return!1;for(var i=0;i<n.length&&i<e.length;i++)if(!zt(e[i],n[i]))return!1;return!0}function ia(e,n,i,u,f,h){if(ar=h,be=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,Cs.current=e===null||e.memoizedState===null?hy:py,e=i(u,f),ei){h=0;do{if(ei=!1,ti=0,25<=h)throw Error(o(301));h+=1,Ke=qe=null,n.updateQueue=null,Cs.current=my,e=i(u,f)}while(ei)}if(Cs.current=Ms,n=qe!==null&&qe.next!==null,ar=0,Ke=qe=be=null,Ts=!1,n)throw Error(o(300));return e}function sa(){var e=ti!==0;return ti=0,e}function Kt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ke===null?be.memoizedState=Ke=e:Ke=Ke.next=e,Ke}function Mt(){if(qe===null){var e=be.alternate;e=e!==null?e.memoizedState:null}else e=qe.next;var n=Ke===null?be.memoizedState:Ke.next;if(n!==null)Ke=n,qe=e;else{if(e===null)throw Error(o(310));qe=e,e={memoizedState:qe.memoizedState,baseState:qe.baseState,baseQueue:qe.baseQueue,queue:qe.queue,next:null},Ke===null?be.memoizedState=Ke=e:Ke=Ke.next=e}return Ke}function ni(e,n){return typeof n=="function"?n(e):n}function la(e){var n=Mt(),i=n.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=e;var u=qe,f=u.baseQueue,h=i.pending;if(h!==null){if(f!==null){var x=f.next;f.next=h.next,h.next=x}u.baseQueue=f=h,i.pending=null}if(f!==null){h=f.next,u=u.baseState;var R=x=null,L=null,Y=h;do{var ne=Y.lane;if((ar&ne)===ne)L!==null&&(L=L.next={lane:0,action:Y.action,hasEagerState:Y.hasEagerState,eagerState:Y.eagerState,next:null}),u=Y.hasEagerState?Y.eagerState:e(u,Y.action);else{var se={lane:ne,action:Y.action,hasEagerState:Y.hasEagerState,eagerState:Y.eagerState,next:null};L===null?(R=L=se,x=u):L=L.next=se,be.lanes|=ne,cr|=ne}Y=Y.next}while(Y!==null&&Y!==h);L===null?x=u:L.next=R,zt(u,n.memoizedState)||(ft=!0),n.memoizedState=u,n.baseState=x,n.baseQueue=L,i.lastRenderedState=u}if(e=i.interleaved,e!==null){f=e;do h=f.lane,be.lanes|=h,cr|=h,f=f.next;while(f!==e)}else f===null&&(i.lanes=0);return[n.memoizedState,i.dispatch]}function ua(e){var n=Mt(),i=n.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=e;var u=i.dispatch,f=i.pending,h=n.memoizedState;if(f!==null){i.pending=null;var x=f=f.next;do h=e(h,x.action),x=x.next;while(x!==f);zt(h,n.memoizedState)||(ft=!0),n.memoizedState=h,n.baseQueue===null&&(n.baseState=h),i.lastRenderedState=h}return[h,u]}function od(){}function id(e,n){var i=be,u=Mt(),f=n(),h=!zt(u.memoizedState,f);if(h&&(u.memoizedState=f,ft=!0),u=u.queue,aa(ud.bind(null,i,u,e),[e]),u.getSnapshot!==n||h||Ke!==null&&Ke.memoizedState.tag&1){if(i.flags|=2048,ri(9,ld.bind(null,i,u,f,n),void 0,null),Ge===null)throw Error(o(349));(ar&30)!==0||sd(i,n,f)}return f}function sd(e,n,i){e.flags|=16384,e={getSnapshot:n,value:i},n=be.updateQueue,n===null?(n={lastEffect:null,stores:null},be.updateQueue=n,n.stores=[e]):(i=n.stores,i===null?n.stores=[e]:i.push(e))}function ld(e,n,i,u){n.value=i,n.getSnapshot=u,ad(n)&&cd(e)}function ud(e,n,i){return i(function(){ad(n)&&cd(e)})}function ad(e){var n=e.getSnapshot;e=e.value;try{var i=n();return!zt(e,i)}catch{return!0}}function cd(e){var n=hn(e,1);n!==null&&Ft(n,e,1,-1)}function fd(e){var n=Kt();return typeof e=="function"&&(e=e()),n.memoizedState=n.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ni,lastRenderedState:e},n.queue=e,e=e.dispatch=dy.bind(null,be,e),[n.memoizedState,e]}function ri(e,n,i,u){return e={tag:e,create:n,destroy:i,deps:u,next:null},n=be.updateQueue,n===null?(n={lastEffect:null,stores:null},be.updateQueue=n,n.lastEffect=e.next=e):(i=n.lastEffect,i===null?n.lastEffect=e.next=e:(u=i.next,i.next=e,e.next=u,n.lastEffect=e)),e}function dd(){return Mt().memoizedState}function Ps(e,n,i,u){var f=Kt();be.flags|=e,f.memoizedState=ri(1|n,i,void 0,u===void 0?null:u)}function Rs(e,n,i,u){var f=Mt();u=u===void 0?null:u;var h=void 0;if(qe!==null){var x=qe.memoizedState;if(h=x.destroy,u!==null&&oa(u,x.deps)){f.memoizedState=ri(n,i,h,u);return}}be.flags|=e,f.memoizedState=ri(1|n,i,h,u)}function hd(e,n){return Ps(8390656,8,e,n)}function aa(e,n){return Rs(2048,8,e,n)}function pd(e,n){return Rs(4,2,e,n)}function md(e,n){return Rs(4,4,e,n)}function gd(e,n){if(typeof n=="function")return e=e(),n(e),function(){n(null)};if(n!=null)return e=e(),n.current=e,function(){n.current=null}}function yd(e,n,i){return i=i!=null?i.concat([e]):null,Rs(4,4,gd.bind(null,n,e),i)}function ca(){}function vd(e,n){var i=Mt();n=n===void 0?null:n;var u=i.memoizedState;return u!==null&&n!==null&&oa(n,u[1])?u[0]:(i.memoizedState=[e,n],e)}function wd(e,n){var i=Mt();n=n===void 0?null:n;var u=i.memoizedState;return u!==null&&n!==null&&oa(n,u[1])?u[0]:(e=e(),i.memoizedState=[e,n],e)}function xd(e,n,i){return(ar&21)===0?(e.baseState&&(e.baseState=!1,ft=!0),e.memoizedState=i):(zt(i,n)||(i=Gi(),be.lanes|=i,cr|=i,e.baseState=!0),n)}function cy(e,n){var i=Ae;Ae=i!==0&&4>i?i:4,e(!0);var u=ra.transition;ra.transition={};try{e(!1),n()}finally{Ae=i,ra.transition=u}}function Sd(){return Mt().memoizedState}function fy(e,n,i){var u=Hn(e);if(i={lane:u,action:i,hasEagerState:!1,eagerState:null,next:null},Ed(e))_d(n,i);else if(i=Jf(e,n,i,u),i!==null){var f=ut();Ft(i,e,u,f),kd(i,n,u)}}function dy(e,n,i){var u=Hn(e),f={lane:u,action:i,hasEagerState:!1,eagerState:null,next:null};if(Ed(e))_d(n,f);else{var h=e.alternate;if(e.lanes===0&&(h===null||h.lanes===0)&&(h=n.lastRenderedReducer,h!==null))try{var x=n.lastRenderedState,R=h(x,i);if(f.hasEagerState=!0,f.eagerState=R,zt(R,x)){var L=n.interleaved;L===null?(f.next=f,Gu(n)):(f.next=L.next,L.next=f),n.interleaved=f;return}}catch{}finally{}i=Jf(e,n,f,u),i!==null&&(f=ut(),Ft(i,e,u,f),kd(i,n,u))}}function Ed(e){var n=e.alternate;return e===be||n!==null&&n===be}function _d(e,n){ei=Ts=!0;var i=e.pending;i===null?n.next=n:(n.next=i.next,i.next=n),e.pending=n}function kd(e,n,i){if((i&4194240)!==0){var u=n.lanes;u&=e.pendingLanes,i|=u,n.lanes=i,du(e,i)}}var Ms={readContext:Rt,useCallback:rt,useContext:rt,useEffect:rt,useImperativeHandle:rt,useInsertionEffect:rt,useLayoutEffect:rt,useMemo:rt,useReducer:rt,useRef:rt,useState:rt,useDebugValue:rt,useDeferredValue:rt,useTransition:rt,useMutableSource:rt,useSyncExternalStore:rt,useId:rt,unstable_isNewReconciler:!1},hy={readContext:Rt,useCallback:function(e,n){return Kt().memoizedState=[e,n===void 0?null:n],e},useContext:Rt,useEffect:hd,useImperativeHandle:function(e,n,i){return i=i!=null?i.concat([e]):null,Ps(4194308,4,gd.bind(null,n,e),i)},useLayoutEffect:function(e,n){return Ps(4194308,4,e,n)},useInsertionEffect:function(e,n){return Ps(4,2,e,n)},useMemo:function(e,n){var i=Kt();return n=n===void 0?null:n,e=e(),i.memoizedState=[e,n],e},useReducer:function(e,n,i){var u=Kt();return n=i!==void 0?i(n):n,u.memoizedState=u.baseState=n,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},u.queue=e,e=e.dispatch=fy.bind(null,be,e),[u.memoizedState,e]},useRef:function(e){var n=Kt();return e={current:e},n.memoizedState=e},useState:fd,useDebugValue:ca,useDeferredValue:function(e){return Kt().memoizedState=e},useTransition:function(){var e=fd(!1),n=e[0];return e=cy.bind(null,e[1]),Kt().memoizedState=e,[n,e]},useMutableSource:function(){},useSyncExternalStore:function(e,n,i){var u=be,f=Kt();if(De){if(i===void 0)throw Error(o(407));i=i()}else{if(i=n(),Ge===null)throw Error(o(349));(ar&30)!==0||sd(u,n,i)}f.memoizedState=i;var h={value:i,getSnapshot:n};return f.queue=h,hd(ud.bind(null,u,h,e),[e]),u.flags|=2048,ri(9,ld.bind(null,u,h,i,n),void 0,null),i},useId:function(){var e=Kt(),n=Ge.identifierPrefix;if(De){var i=dn,u=fn;i=(u&~(1<<32-vt(u)-1)).toString(32)+i,n=":"+n+"R"+i,i=ti++,0<i&&(n+="H"+i.toString(32)),n+=":"}else i=ay++,n=":"+n+"r"+i.toString(32)+":";return e.memoizedState=n},unstable_isNewReconciler:!1},py={readContext:Rt,useCallback:vd,useContext:Rt,useEffect:aa,useImperativeHandle:yd,useInsertionEffect:pd,useLayoutEffect:md,useMemo:wd,useReducer:la,useRef:dd,useState:function(){return la(ni)},useDebugValue:ca,useDeferredValue:function(e){var n=Mt();return xd(n,qe.memoizedState,e)},useTransition:function(){var e=la(ni)[0],n=Mt().memoizedState;return[e,n]},useMutableSource:od,useSyncExternalStore:id,useId:Sd,unstable_isNewReconciler:!1},my={readContext:Rt,useCallback:vd,useContext:Rt,useEffect:aa,useImperativeHandle:yd,useInsertionEffect:pd,useLayoutEffect:md,useMemo:wd,useReducer:ua,useRef:dd,useState:function(){return ua(ni)},useDebugValue:ca,useDeferredValue:function(e){var n=Mt();return qe===null?n.memoizedState=e:xd(n,qe.memoizedState,e)},useTransition:function(){var e=ua(ni)[0],n=Mt().memoizedState;return[e,n]},useMutableSource:od,useSyncExternalStore:id,useId:Sd,unstable_isNewReconciler:!1};function Dt(e,n){if(e&&e.defaultProps){n=B({},n),e=e.defaultProps;for(var i in e)n[i]===void 0&&(n[i]=e[i]);return n}return n}function fa(e,n,i,u){n=e.memoizedState,i=i(u,n),i=i==null?n:B({},n,i),e.memoizedState=i,e.lanes===0&&(e.updateQueue.baseState=i)}var As={isMounted:function(e){return(e=e._reactInternals)?Yt(e)===e:!1},enqueueSetState:function(e,n,i){e=e._reactInternals;var u=ut(),f=Hn(e),h=pn(u,f);h.payload=n,i!=null&&(h.callback=i),n=$n(e,h,f),n!==null&&(Ft(n,e,f,u),_s(n,e,f))},enqueueReplaceState:function(e,n,i){e=e._reactInternals;var u=ut(),f=Hn(e),h=pn(u,f);h.tag=1,h.payload=n,i!=null&&(h.callback=i),n=$n(e,h,f),n!==null&&(Ft(n,e,f,u),_s(n,e,f))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var i=ut(),u=Hn(e),f=pn(i,u);f.tag=2,n!=null&&(f.callback=n),n=$n(e,f,u),n!==null&&(Ft(n,e,u,i),_s(n,e,u))}};function Nd(e,n,i,u,f,h,x){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(u,h,x):n.prototype&&n.prototype.isPureReactComponent?!Vo(i,u)||!Vo(f,h):!0}function Cd(e,n,i){var u=!1,f=zn,h=n.contextType;return typeof h=="object"&&h!==null?h=Rt(h):(f=ct(n)?or:nt.current,u=n.contextTypes,h=(u=u!=null)?Br(e,f):zn),n=new n(i,h),e.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=As,e.stateNode=n,n._reactInternals=e,u&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=f,e.__reactInternalMemoizedMaskedChildContext=h),n}function Td(e,n,i,u){e=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(i,u),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(i,u),n.state!==e&&As.enqueueReplaceState(n,n.state,null)}function da(e,n,i,u){var f=e.stateNode;f.props=i,f.state=e.memoizedState,f.refs={},Zu(e);var h=n.contextType;typeof h=="object"&&h!==null?f.context=Rt(h):(h=ct(n)?or:nt.current,f.context=Br(e,h)),f.state=e.memoizedState,h=n.getDerivedStateFromProps,typeof h=="function"&&(fa(e,n,h,i),f.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(n=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),n!==f.state&&As.enqueueReplaceState(f,f.state,null),ks(e,i,f,u),f.state=e.memoizedState),typeof f.componentDidMount=="function"&&(e.flags|=4194308)}function Kr(e,n){try{var i="",u=n;do i+=ue(u),u=u.return;while(u);var f=i}catch(h){f=`
Error generating stack: `+h.message+`
`+h.stack}return{value:e,source:n,stack:f,digest:null}}function ha(e,n,i){return{value:e,source:null,stack:i??null,digest:n??null}}function pa(e,n){try{console.error(n.value)}catch(i){setTimeout(function(){throw i})}}var gy=typeof WeakMap=="function"?WeakMap:Map;function Pd(e,n,i){i=pn(-1,i),i.tag=3,i.payload={element:null};var u=n.value;return i.callback=function(){js||(js=!0,Ra=u),pa(e,n)},i}function Rd(e,n,i){i=pn(-1,i),i.tag=3;var u=e.type.getDerivedStateFromError;if(typeof u=="function"){var f=n.value;i.payload=function(){return u(f)},i.callback=function(){pa(e,n)}}var h=e.stateNode;return h!==null&&typeof h.componentDidCatch=="function"&&(i.callback=function(){pa(e,n),typeof u!="function"&&(Fn===null?Fn=new Set([this]):Fn.add(this));var x=n.stack;this.componentDidCatch(n.value,{componentStack:x!==null?x:""})}),i}function Md(e,n,i){var u=e.pingCache;if(u===null){u=e.pingCache=new gy;var f=new Set;u.set(n,f)}else f=u.get(n),f===void 0&&(f=new Set,u.set(n,f));f.has(i)||(f.add(i),e=My.bind(null,e,n,i),n.then(e,e))}function Ad(e){do{var n;if((n=e.tag===13)&&(n=e.memoizedState,n=n!==null?n.dehydrated!==null:!0),n)return e;e=e.return}while(e!==null);return null}function Ld(e,n,i,u,f){return(e.mode&1)===0?(e===n?e.flags|=65536:(e.flags|=128,i.flags|=131072,i.flags&=-52805,i.tag===1&&(i.alternate===null?i.tag=17:(n=pn(-1,1),n.tag=2,$n(i,n,1))),i.lanes|=1),e):(e.flags|=65536,e.lanes=f,e)}var yy=N.ReactCurrentOwner,ft=!1;function lt(e,n,i,u){n.child=e===null?Zf(n,null,i,u):Xr(n,e.child,i,u)}function Id(e,n,i,u,f){i=i.render;var h=n.ref;return qr(n,f),u=ia(e,n,i,u,h,f),i=sa(),e!==null&&!ft?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~f,mn(e,n,f)):(De&&i&&Bu(n),n.flags|=1,lt(e,n,u,f),n.child)}function zd(e,n,i,u,f){if(e===null){var h=i.type;return typeof h=="function"&&!Da(h)&&h.defaultProps===void 0&&i.compare===null&&i.defaultProps===void 0?(n.tag=15,n.type=h,Od(e,n,h,u,f)):(e=Us(i.type,null,u,n,n.mode,f),e.ref=n.ref,e.return=n,n.child=e)}if(h=e.child,(e.lanes&f)===0){var x=h.memoizedProps;if(i=i.compare,i=i!==null?i:Vo,i(x,u)&&e.ref===n.ref)return mn(e,n,f)}return n.flags|=1,e=Vn(h,u),e.ref=n.ref,e.return=n,n.child=e}function Od(e,n,i,u,f){if(e!==null){var h=e.memoizedProps;if(Vo(h,u)&&e.ref===n.ref)if(ft=!1,n.pendingProps=u=h,(e.lanes&f)!==0)(e.flags&131072)!==0&&(ft=!0);else return n.lanes=e.lanes,mn(e,n,f)}return ma(e,n,i,u,f)}function Dd(e,n,i){var u=n.pendingProps,f=u.children,h=e!==null?e.memoizedState:null;if(u.mode==="hidden")if((n.mode&1)===0)n.memoizedState={baseLanes:0,cachePool:null,transitions:null},Le(Zr,Et),Et|=i;else{if((i&1073741824)===0)return e=h!==null?h.baseLanes|i:i,n.lanes=n.childLanes=1073741824,n.memoizedState={baseLanes:e,cachePool:null,transitions:null},n.updateQueue=null,Le(Zr,Et),Et|=e,null;n.memoizedState={baseLanes:0,cachePool:null,transitions:null},u=h!==null?h.baseLanes:i,Le(Zr,Et),Et|=u}else h!==null?(u=h.baseLanes|i,n.memoizedState=null):u=i,Le(Zr,Et),Et|=u;return lt(e,n,f,i),n.child}function $d(e,n){var i=n.ref;(e===null&&i!==null||e!==null&&e.ref!==i)&&(n.flags|=512,n.flags|=2097152)}function ma(e,n,i,u,f){var h=ct(i)?or:nt.current;return h=Br(n,h),qr(n,f),i=ia(e,n,i,u,h,f),u=sa(),e!==null&&!ft?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~f,mn(e,n,f)):(De&&u&&Bu(n),n.flags|=1,lt(e,n,i,f),n.child)}function jd(e,n,i,u,f){if(ct(i)){var h=!0;ms(n)}else h=!1;if(qr(n,f),n.stateNode===null)Is(e,n),Cd(n,i,u),da(n,i,u,f),u=!0;else if(e===null){var x=n.stateNode,R=n.memoizedProps;x.props=R;var L=x.context,Y=i.contextType;typeof Y=="object"&&Y!==null?Y=Rt(Y):(Y=ct(i)?or:nt.current,Y=Br(n,Y));var ne=i.getDerivedStateFromProps,se=typeof ne=="function"||typeof x.getSnapshotBeforeUpdate=="function";se||typeof x.UNSAFE_componentWillReceiveProps!="function"&&typeof x.componentWillReceiveProps!="function"||(R!==u||L!==Y)&&Td(n,x,u,Y),Dn=!1;var te=n.memoizedState;x.state=te,ks(n,u,x,f),L=n.memoizedState,R!==u||te!==L||at.current||Dn?(typeof ne=="function"&&(fa(n,i,ne,u),L=n.memoizedState),(R=Dn||Nd(n,i,R,u,te,L,Y))?(se||typeof x.UNSAFE_componentWillMount!="function"&&typeof x.componentWillMount!="function"||(typeof x.componentWillMount=="function"&&x.componentWillMount(),typeof x.UNSAFE_componentWillMount=="function"&&x.UNSAFE_componentWillMount()),typeof x.componentDidMount=="function"&&(n.flags|=4194308)):(typeof x.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=u,n.memoizedState=L),x.props=u,x.state=L,x.context=Y,u=R):(typeof x.componentDidMount=="function"&&(n.flags|=4194308),u=!1)}else{x=n.stateNode,ed(e,n),R=n.memoizedProps,Y=n.type===n.elementType?R:Dt(n.type,R),x.props=Y,se=n.pendingProps,te=x.context,L=i.contextType,typeof L=="object"&&L!==null?L=Rt(L):(L=ct(i)?or:nt.current,L=Br(n,L));var de=i.getDerivedStateFromProps;(ne=typeof de=="function"||typeof x.getSnapshotBeforeUpdate=="function")||typeof x.UNSAFE_componentWillReceiveProps!="function"&&typeof x.componentWillReceiveProps!="function"||(R!==se||te!==L)&&Td(n,x,u,L),Dn=!1,te=n.memoizedState,x.state=te,ks(n,u,x,f);var pe=n.memoizedState;R!==se||te!==pe||at.current||Dn?(typeof de=="function"&&(fa(n,i,de,u),pe=n.memoizedState),(Y=Dn||Nd(n,i,Y,u,te,pe,L)||!1)?(ne||typeof x.UNSAFE_componentWillUpdate!="function"&&typeof x.componentWillUpdate!="function"||(typeof x.componentWillUpdate=="function"&&x.componentWillUpdate(u,pe,L),typeof x.UNSAFE_componentWillUpdate=="function"&&x.UNSAFE_componentWillUpdate(u,pe,L)),typeof x.componentDidUpdate=="function"&&(n.flags|=4),typeof x.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof x.componentDidUpdate!="function"||R===e.memoizedProps&&te===e.memoizedState||(n.flags|=4),typeof x.getSnapshotBeforeUpdate!="function"||R===e.memoizedProps&&te===e.memoizedState||(n.flags|=1024),n.memoizedProps=u,n.memoizedState=pe),x.props=u,x.state=pe,x.context=L,u=Y):(typeof x.componentDidUpdate!="function"||R===e.memoizedProps&&te===e.memoizedState||(n.flags|=4),typeof x.getSnapshotBeforeUpdate!="function"||R===e.memoizedProps&&te===e.memoizedState||(n.flags|=1024),u=!1)}return ga(e,n,i,u,h,f)}function ga(e,n,i,u,f,h){$d(e,n);var x=(n.flags&128)!==0;if(!u&&!x)return f&&Vf(n,i,!1),mn(e,n,h);u=n.stateNode,yy.current=n;var R=x&&typeof i.getDerivedStateFromError!="function"?null:u.render();return n.flags|=1,e!==null&&x?(n.child=Xr(n,e.child,null,h),n.child=Xr(n,null,R,h)):lt(e,n,R,h),n.memoizedState=u.state,f&&Vf(n,i,!0),n.child}function Fd(e){var n=e.stateNode;n.pendingContext?Hf(e,n.pendingContext,n.pendingContext!==n.context):n.context&&Hf(e,n.context,!1),Ju(e,n.containerInfo)}function bd(e,n,i,u,f){return Wr(),Xu(f),n.flags|=256,lt(e,n,i,u),n.child}var ya={dehydrated:null,treeContext:null,retryLane:0};function va(e){return{baseLanes:e,cachePool:null,transitions:null}}function Hd(e,n,i){var u=n.pendingProps,f=Fe.current,h=!1,x=(n.flags&128)!==0,R;if((R=x)||(R=e!==null&&e.memoizedState===null?!1:(f&2)!==0),R?(h=!0,n.flags&=-129):(e===null||e.memoizedState!==null)&&(f|=1),Le(Fe,f&1),e===null)return Wu(n),e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((n.mode&1)===0?n.lanes=1:e.data==="$!"?n.lanes=8:n.lanes=1073741824,null):(x=u.children,e=u.fallback,h?(u=n.mode,h=n.child,x={mode:"hidden",children:x},(u&1)===0&&h!==null?(h.childLanes=0,h.pendingProps=x):h=Ws(x,u,0,null),e=pr(e,u,i,null),h.return=n,e.return=n,h.sibling=e,n.child=h,n.child.memoizedState=va(i),n.memoizedState=ya,e):wa(n,x));if(f=e.memoizedState,f!==null&&(R=f.dehydrated,R!==null))return vy(e,n,x,u,R,f,i);if(h){h=u.fallback,x=n.mode,f=e.child,R=f.sibling;var L={mode:"hidden",children:u.children};return(x&1)===0&&n.child!==f?(u=n.child,u.childLanes=0,u.pendingProps=L,n.deletions=null):(u=Vn(f,L),u.subtreeFlags=f.subtreeFlags&14680064),R!==null?h=Vn(R,h):(h=pr(h,x,i,null),h.flags|=2),h.return=n,u.return=n,u.sibling=h,n.child=u,u=h,h=n.child,x=e.child.memoizedState,x=x===null?va(i):{baseLanes:x.baseLanes|i,cachePool:null,transitions:x.transitions},h.memoizedState=x,h.childLanes=e.childLanes&~i,n.memoizedState=ya,u}return h=e.child,e=h.sibling,u=Vn(h,{mode:"visible",children:u.children}),(n.mode&1)===0&&(u.lanes=i),u.return=n,u.sibling=null,e!==null&&(i=n.deletions,i===null?(n.deletions=[e],n.flags|=16):i.push(e)),n.child=u,n.memoizedState=null,u}function wa(e,n){return n=Ws({mode:"visible",children:n},e.mode,0,null),n.return=e,e.child=n}function Ls(e,n,i,u){return u!==null&&Xu(u),Xr(n,e.child,null,i),e=wa(n,n.pendingProps.children),e.flags|=2,n.memoizedState=null,e}function vy(e,n,i,u,f,h,x){if(i)return n.flags&256?(n.flags&=-257,u=ha(Error(o(422))),Ls(e,n,x,u)):n.memoizedState!==null?(n.child=e.child,n.flags|=128,null):(h=u.fallback,f=n.mode,u=Ws({mode:"visible",children:u.children},f,0,null),h=pr(h,f,x,null),h.flags|=2,u.return=n,h.return=n,u.sibling=h,n.child=u,(n.mode&1)!==0&&Xr(n,e.child,null,x),n.child.memoizedState=va(x),n.memoizedState=ya,h);if((n.mode&1)===0)return Ls(e,n,x,null);if(f.data==="$!"){if(u=f.nextSibling&&f.nextSibling.dataset,u)var R=u.dgst;return u=R,h=Error(o(419)),u=ha(h,u,void 0),Ls(e,n,x,u)}if(R=(x&e.childLanes)!==0,ft||R){if(u=Ge,u!==null){switch(x&-x){case 4:f=2;break;case 16:f=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:f=32;break;case 536870912:f=268435456;break;default:f=0}f=(f&(u.suspendedLanes|x))!==0?0:f,f!==0&&f!==h.retryLane&&(h.retryLane=f,hn(e,f),Ft(u,e,f,-1))}return Oa(),u=ha(Error(o(421))),Ls(e,n,x,u)}return f.data==="$?"?(n.flags|=128,n.child=e.child,n=Ay.bind(null,e),f._reactRetry=n,null):(e=h.treeContext,St=Ln(f.nextSibling),xt=n,De=!0,Ot=null,e!==null&&(Tt[Pt++]=fn,Tt[Pt++]=dn,Tt[Pt++]=ir,fn=e.id,dn=e.overflow,ir=n),n=wa(n,u.children),n.flags|=4096,n)}function Bd(e,n,i){e.lanes|=n;var u=e.alternate;u!==null&&(u.lanes|=n),Ku(e.return,n,i)}function xa(e,n,i,u,f){var h=e.memoizedState;h===null?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:u,tail:i,tailMode:f}:(h.isBackwards=n,h.rendering=null,h.renderingStartTime=0,h.last=u,h.tail=i,h.tailMode=f)}function Vd(e,n,i){var u=n.pendingProps,f=u.revealOrder,h=u.tail;if(lt(e,n,u.children,i),u=Fe.current,(u&2)!==0)u=u&1|2,n.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=n.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Bd(e,i,n);else if(e.tag===19)Bd(e,i,n);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;e.sibling===null;){if(e.return===null||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}u&=1}if(Le(Fe,u),(n.mode&1)===0)n.memoizedState=null;else switch(f){case"forwards":for(i=n.child,f=null;i!==null;)e=i.alternate,e!==null&&Ns(e)===null&&(f=i),i=i.sibling;i=f,i===null?(f=n.child,n.child=null):(f=i.sibling,i.sibling=null),xa(n,!1,f,i,h);break;case"backwards":for(i=null,f=n.child,n.child=null;f!==null;){if(e=f.alternate,e!==null&&Ns(e)===null){n.child=f;break}e=f.sibling,f.sibling=i,i=f,f=e}xa(n,!0,i,null,h);break;case"together":xa(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Is(e,n){(n.mode&1)===0&&e!==null&&(e.alternate=null,n.alternate=null,n.flags|=2)}function mn(e,n,i){if(e!==null&&(n.dependencies=e.dependencies),cr|=n.lanes,(i&n.childLanes)===0)return null;if(e!==null&&n.child!==e.child)throw Error(o(153));if(n.child!==null){for(e=n.child,i=Vn(e,e.pendingProps),n.child=i,i.return=n;e.sibling!==null;)e=e.sibling,i=i.sibling=Vn(e,e.pendingProps),i.return=n;i.sibling=null}return n.child}function wy(e,n,i){switch(n.tag){case 3:Fd(n),Wr();break;case 5:rd(n);break;case 1:ct(n.type)&&ms(n);break;case 4:Ju(n,n.stateNode.containerInfo);break;case 10:var u=n.type._context,f=n.memoizedProps.value;Le(Ss,u._currentValue),u._currentValue=f;break;case 13:if(u=n.memoizedState,u!==null)return u.dehydrated!==null?(Le(Fe,Fe.current&1),n.flags|=128,null):(i&n.child.childLanes)!==0?Hd(e,n,i):(Le(Fe,Fe.current&1),e=mn(e,n,i),e!==null?e.sibling:null);Le(Fe,Fe.current&1);break;case 19:if(u=(i&n.childLanes)!==0,(e.flags&128)!==0){if(u)return Vd(e,n,i);n.flags|=128}if(f=n.memoizedState,f!==null&&(f.rendering=null,f.tail=null,f.lastEffect=null),Le(Fe,Fe.current),u)break;return null;case 22:case 23:return n.lanes=0,Dd(e,n,i)}return mn(e,n,i)}var Ud,Sa,Wd,Xd;Ud=function(e,n){for(var i=n.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===n)break;for(;i.sibling===null;){if(i.return===null||i.return===n)return;i=i.return}i.sibling.return=i.return,i=i.sibling}},Sa=function(){},Wd=function(e,n,i,u){var f=e.memoizedProps;if(f!==u){e=n.stateNode,ur(Qt.current);var h=null;switch(i){case"input":f=Ne(e,f),u=Ne(e,u),h=[];break;case"select":f=B({},f,{value:void 0}),u=B({},u,{value:void 0}),h=[];break;case"textarea":f=rn(e,f),u=rn(e,u),h=[];break;default:typeof f.onClick!="function"&&typeof u.onClick=="function"&&(e.onclick=ds)}vo(i,u);var x;i=null;for(Y in f)if(!u.hasOwnProperty(Y)&&f.hasOwnProperty(Y)&&f[Y]!=null)if(Y==="style"){var R=f[Y];for(x in R)R.hasOwnProperty(x)&&(i||(i={}),i[x]="")}else Y!=="dangerouslySetInnerHTML"&&Y!=="children"&&Y!=="suppressContentEditableWarning"&&Y!=="suppressHydrationWarning"&&Y!=="autoFocus"&&(l.hasOwnProperty(Y)?h||(h=[]):(h=h||[]).push(Y,null));for(Y in u){var L=u[Y];if(R=f!=null?f[Y]:void 0,u.hasOwnProperty(Y)&&L!==R&&(L!=null||R!=null))if(Y==="style")if(R){for(x in R)!R.hasOwnProperty(x)||L&&L.hasOwnProperty(x)||(i||(i={}),i[x]="");for(x in L)L.hasOwnProperty(x)&&R[x]!==L[x]&&(i||(i={}),i[x]=L[x])}else i||(h||(h=[]),h.push(Y,i)),i=L;else Y==="dangerouslySetInnerHTML"?(L=L?L.__html:void 0,R=R?R.__html:void 0,L!=null&&R!==L&&(h=h||[]).push(Y,L)):Y==="children"?typeof L!="string"&&typeof L!="number"||(h=h||[]).push(Y,""+L):Y!=="suppressContentEditableWarning"&&Y!=="suppressHydrationWarning"&&(l.hasOwnProperty(Y)?(L!=null&&Y==="onScroll"&&ze("scroll",e),h||R===L||(h=[])):(h=h||[]).push(Y,L))}i&&(h=h||[]).push("style",i);var Y=h;(n.updateQueue=Y)&&(n.flags|=4)}},Xd=function(e,n,i,u){i!==u&&(n.flags|=4)};function oi(e,n){if(!De)switch(e.tailMode){case"hidden":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e.tail=null:i.sibling=null;break;case"collapsed":i=e.tail;for(var u=null;i!==null;)i.alternate!==null&&(u=i),i=i.sibling;u===null?n||e.tail===null?e.tail=null:e.tail.sibling=null:u.sibling=null}}function ot(e){var n=e.alternate!==null&&e.alternate.child===e.child,i=0,u=0;if(n)for(var f=e.child;f!==null;)i|=f.lanes|f.childLanes,u|=f.subtreeFlags&14680064,u|=f.flags&14680064,f.return=e,f=f.sibling;else for(f=e.child;f!==null;)i|=f.lanes|f.childLanes,u|=f.subtreeFlags,u|=f.flags,f.return=e,f=f.sibling;return e.subtreeFlags|=u,e.childLanes=i,n}function xy(e,n,i){var u=n.pendingProps;switch(Vu(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ot(n),null;case 1:return ct(n.type)&&ps(),ot(n),null;case 3:return u=n.stateNode,Qr(),Oe(at),Oe(nt),na(),u.pendingContext&&(u.context=u.pendingContext,u.pendingContext=null),(e===null||e.child===null)&&(ws(n)?n.flags|=4:e===null||e.memoizedState.isDehydrated&&(n.flags&256)===0||(n.flags|=1024,Ot!==null&&(La(Ot),Ot=null))),Sa(e,n),ot(n),null;case 5:ea(n);var f=ur(Jo.current);if(i=n.type,e!==null&&n.stateNode!=null)Wd(e,n,i,u,f),e.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!u){if(n.stateNode===null)throw Error(o(166));return ot(n),null}if(e=ur(Qt.current),ws(n)){u=n.stateNode,i=n.type;var h=n.memoizedProps;switch(u[qt]=n,u[qo]=h,e=(n.mode&1)!==0,i){case"dialog":ze("cancel",u),ze("close",u);break;case"iframe":case"object":case"embed":ze("load",u);break;case"video":case"audio":for(f=0;f<Wo.length;f++)ze(Wo[f],u);break;case"source":ze("error",u);break;case"img":case"image":case"link":ze("error",u),ze("load",u);break;case"details":ze("toggle",u);break;case"input":Ie(u,h),ze("invalid",u);break;case"select":u._wrapperState={wasMultiple:!!h.multiple},ze("invalid",u);break;case"textarea":_n(u,h),ze("invalid",u)}vo(i,h),f=null;for(var x in h)if(h.hasOwnProperty(x)){var R=h[x];x==="children"?typeof R=="string"?u.textContent!==R&&(h.suppressHydrationWarning!==!0&&fs(u.textContent,R,e),f=["children",R]):typeof R=="number"&&u.textContent!==""+R&&(h.suppressHydrationWarning!==!0&&fs(u.textContent,R,e),f=["children",""+R]):l.hasOwnProperty(x)&&R!=null&&x==="onScroll"&&ze("scroll",u)}switch(i){case"input":_e(u),Nr(u,h,!0);break;case"textarea":_e(u),Qn(u);break;case"select":case"option":break;default:typeof h.onClick=="function"&&(u.onclick=ds)}u=f,n.updateQueue=u,u!==null&&(n.flags|=4)}else{x=f.nodeType===9?f:f.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=on(i)),e==="http://www.w3.org/1999/xhtml"?i==="script"?(e=x.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof u.is=="string"?e=x.createElement(i,{is:u.is}):(e=x.createElement(i),i==="select"&&(x=e,u.multiple?x.multiple=!0:u.size&&(x.size=u.size))):e=x.createElementNS(e,i),e[qt]=n,e[qo]=u,Ud(e,n,!1,!1),n.stateNode=e;e:{switch(x=wo(i,u),i){case"dialog":ze("cancel",e),ze("close",e),f=u;break;case"iframe":case"object":case"embed":ze("load",e),f=u;break;case"video":case"audio":for(f=0;f<Wo.length;f++)ze(Wo[f],e);f=u;break;case"source":ze("error",e),f=u;break;case"img":case"image":case"link":ze("error",e),ze("load",e),f=u;break;case"details":ze("toggle",e),f=u;break;case"input":Ie(e,u),f=Ne(e,u),ze("invalid",e);break;case"option":f=u;break;case"select":e._wrapperState={wasMultiple:!!u.multiple},f=B({},u,{value:void 0}),ze("invalid",e);break;case"textarea":_n(e,u),f=rn(e,u),ze("invalid",e);break;default:f=u}vo(i,f),R=f;for(h in R)if(R.hasOwnProperty(h)){var L=R[h];h==="style"?ji(e,L):h==="dangerouslySetInnerHTML"?(L=L?L.__html:void 0,L!=null&&Di(e,L)):h==="children"?typeof L=="string"?(i!=="textarea"||L!=="")&&ln(e,L):typeof L=="number"&&ln(e,""+L):h!=="suppressContentEditableWarning"&&h!=="suppressHydrationWarning"&&h!=="autoFocus"&&(l.hasOwnProperty(h)?L!=null&&h==="onScroll"&&ze("scroll",e):L!=null&&E(e,h,L,x))}switch(i){case"input":_e(e),Nr(e,u,!1);break;case"textarea":_e(e),Qn(e);break;case"option":u.value!=null&&e.setAttribute("value",""+re(u.value));break;case"select":e.multiple=!!u.multiple,h=u.value,h!=null?It(e,!!u.multiple,h,!1):u.defaultValue!=null&&It(e,!!u.multiple,u.defaultValue,!0);break;default:typeof f.onClick=="function"&&(e.onclick=ds)}switch(i){case"button":case"input":case"select":case"textarea":u=!!u.autoFocus;break e;case"img":u=!0;break e;default:u=!1}}u&&(n.flags|=4)}n.ref!==null&&(n.flags|=512,n.flags|=2097152)}return ot(n),null;case 6:if(e&&n.stateNode!=null)Xd(e,n,e.memoizedProps,u);else{if(typeof u!="string"&&n.stateNode===null)throw Error(o(166));if(i=ur(Jo.current),ur(Qt.current),ws(n)){if(u=n.stateNode,i=n.memoizedProps,u[qt]=n,(h=u.nodeValue!==i)&&(e=xt,e!==null))switch(e.tag){case 3:fs(u.nodeValue,i,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&fs(u.nodeValue,i,(e.mode&1)!==0)}h&&(n.flags|=4)}else u=(i.nodeType===9?i:i.ownerDocument).createTextNode(u),u[qt]=n,n.stateNode=u}return ot(n),null;case 13:if(Oe(Fe),u=n.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(De&&St!==null&&(n.mode&1)!==0&&(n.flags&128)===0)Qf(),Wr(),n.flags|=98560,h=!1;else if(h=ws(n),u!==null&&u.dehydrated!==null){if(e===null){if(!h)throw Error(o(318));if(h=n.memoizedState,h=h!==null?h.dehydrated:null,!h)throw Error(o(317));h[qt]=n}else Wr(),(n.flags&128)===0&&(n.memoizedState=null),n.flags|=4;ot(n),h=!1}else Ot!==null&&(La(Ot),Ot=null),h=!0;if(!h)return n.flags&65536?n:null}return(n.flags&128)!==0?(n.lanes=i,n):(u=u!==null,u!==(e!==null&&e.memoizedState!==null)&&u&&(n.child.flags|=8192,(n.mode&1)!==0&&(e===null||(Fe.current&1)!==0?Qe===0&&(Qe=3):Oa())),n.updateQueue!==null&&(n.flags|=4),ot(n),null);case 4:return Qr(),Sa(e,n),e===null&&Xo(n.stateNode.containerInfo),ot(n),null;case 10:return Qu(n.type._context),ot(n),null;case 17:return ct(n.type)&&ps(),ot(n),null;case 19:if(Oe(Fe),h=n.memoizedState,h===null)return ot(n),null;if(u=(n.flags&128)!==0,x=h.rendering,x===null)if(u)oi(h,!1);else{if(Qe!==0||e!==null&&(e.flags&128)!==0)for(e=n.child;e!==null;){if(x=Ns(e),x!==null){for(n.flags|=128,oi(h,!1),u=x.updateQueue,u!==null&&(n.updateQueue=u,n.flags|=4),n.subtreeFlags=0,u=i,i=n.child;i!==null;)h=i,e=u,h.flags&=14680066,x=h.alternate,x===null?(h.childLanes=0,h.lanes=e,h.child=null,h.subtreeFlags=0,h.memoizedProps=null,h.memoizedState=null,h.updateQueue=null,h.dependencies=null,h.stateNode=null):(h.childLanes=x.childLanes,h.lanes=x.lanes,h.child=x.child,h.subtreeFlags=0,h.deletions=null,h.memoizedProps=x.memoizedProps,h.memoizedState=x.memoizedState,h.updateQueue=x.updateQueue,h.type=x.type,e=x.dependencies,h.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),i=i.sibling;return Le(Fe,Fe.current&1|2),n.child}e=e.sibling}h.tail!==null&&je()>Jr&&(n.flags|=128,u=!0,oi(h,!1),n.lanes=4194304)}else{if(!u)if(e=Ns(x),e!==null){if(n.flags|=128,u=!0,i=e.updateQueue,i!==null&&(n.updateQueue=i,n.flags|=4),oi(h,!0),h.tail===null&&h.tailMode==="hidden"&&!x.alternate&&!De)return ot(n),null}else 2*je()-h.renderingStartTime>Jr&&i!==1073741824&&(n.flags|=128,u=!0,oi(h,!1),n.lanes=4194304);h.isBackwards?(x.sibling=n.child,n.child=x):(i=h.last,i!==null?i.sibling=x:n.child=x,h.last=x)}return h.tail!==null?(n=h.tail,h.rendering=n,h.tail=n.sibling,h.renderingStartTime=je(),n.sibling=null,i=Fe.current,Le(Fe,u?i&1|2:i&1),n):(ot(n),null);case 22:case 23:return za(),u=n.memoizedState!==null,e!==null&&e.memoizedState!==null!==u&&(n.flags|=8192),u&&(n.mode&1)!==0?(Et&1073741824)!==0&&(ot(n),n.subtreeFlags&6&&(n.flags|=8192)):ot(n),null;case 24:return null;case 25:return null}throw Error(o(156,n.tag))}function Sy(e,n){switch(Vu(n),n.tag){case 1:return ct(n.type)&&ps(),e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 3:return Qr(),Oe(at),Oe(nt),na(),e=n.flags,(e&65536)!==0&&(e&128)===0?(n.flags=e&-65537|128,n):null;case 5:return ea(n),null;case 13:if(Oe(Fe),e=n.memoizedState,e!==null&&e.dehydrated!==null){if(n.alternate===null)throw Error(o(340));Wr()}return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 19:return Oe(Fe),null;case 4:return Qr(),null;case 10:return Qu(n.type._context),null;case 22:case 23:return za(),null;case 24:return null;default:return null}}var zs=!1,it=!1,Ey=typeof WeakSet=="function"?WeakSet:Set,he=null;function Gr(e,n){var i=e.ref;if(i!==null)if(typeof i=="function")try{i(null)}catch(u){Ve(e,n,u)}else i.current=null}function Ea(e,n,i){try{i()}catch(u){Ve(e,n,u)}}var Yd=!1;function _y(e,n){if(zu=es,e=Nf(),Cu(e)){if("selectionStart"in e)var i={start:e.selectionStart,end:e.selectionEnd};else e:{i=(i=e.ownerDocument)&&i.defaultView||window;var u=i.getSelection&&i.getSelection();if(u&&u.rangeCount!==0){i=u.anchorNode;var f=u.anchorOffset,h=u.focusNode;u=u.focusOffset;try{i.nodeType,h.nodeType}catch{i=null;break e}var x=0,R=-1,L=-1,Y=0,ne=0,se=e,te=null;t:for(;;){for(var de;se!==i||f!==0&&se.nodeType!==3||(R=x+f),se!==h||u!==0&&se.nodeType!==3||(L=x+u),se.nodeType===3&&(x+=se.nodeValue.length),(de=se.firstChild)!==null;)te=se,se=de;for(;;){if(se===e)break t;if(te===i&&++Y===f&&(R=x),te===h&&++ne===u&&(L=x),(de=se.nextSibling)!==null)break;se=te,te=se.parentNode}se=de}i=R===-1||L===-1?null:{start:R,end:L}}else i=null}i=i||{start:0,end:0}}else i=null;for(Ou={focusedElem:e,selectionRange:i},es=!1,he=n;he!==null;)if(n=he,e=n.child,(n.subtreeFlags&1028)!==0&&e!==null)e.return=n,he=e;else for(;he!==null;){n=he;try{var pe=n.alternate;if((n.flags&1024)!==0)switch(n.tag){case 0:case 11:case 15:break;case 1:if(pe!==null){var me=pe.memoizedProps,Ue=pe.memoizedState,V=n.stateNode,O=V.getSnapshotBeforeUpdate(n.elementType===n.type?me:Dt(n.type,me),Ue);V.__reactInternalSnapshotBeforeUpdate=O}break;case 3:var U=n.stateNode.containerInfo;U.nodeType===1?U.textContent="":U.nodeType===9&&U.documentElement&&U.removeChild(U.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(o(163))}}catch(le){Ve(n,n.return,le)}if(e=n.sibling,e!==null){e.return=n.return,he=e;break}he=n.return}return pe=Yd,Yd=!1,pe}function ii(e,n,i){var u=n.updateQueue;if(u=u!==null?u.lastEffect:null,u!==null){var f=u=u.next;do{if((f.tag&e)===e){var h=f.destroy;f.destroy=void 0,h!==void 0&&Ea(n,i,h)}f=f.next}while(f!==u)}}function Os(e,n){if(n=n.updateQueue,n=n!==null?n.lastEffect:null,n!==null){var i=n=n.next;do{if((i.tag&e)===e){var u=i.create;i.destroy=u()}i=i.next}while(i!==n)}}function _a(e){var n=e.ref;if(n!==null){var i=e.stateNode;switch(e.tag){case 5:e=i;break;default:e=i}typeof n=="function"?n(e):n.current=e}}function qd(e){var n=e.alternate;n!==null&&(e.alternate=null,qd(n)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(n=e.stateNode,n!==null&&(delete n[qt],delete n[qo],delete n[Fu],delete n[iy],delete n[sy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Qd(e){return e.tag===5||e.tag===3||e.tag===4}function Kd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Qd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ka(e,n,i){var u=e.tag;if(u===5||u===6)e=e.stateNode,n?i.nodeType===8?i.parentNode.insertBefore(e,n):i.insertBefore(e,n):(i.nodeType===8?(n=i.parentNode,n.insertBefore(e,i)):(n=i,n.appendChild(e)),i=i._reactRootContainer,i!=null||n.onclick!==null||(n.onclick=ds));else if(u!==4&&(e=e.child,e!==null))for(ka(e,n,i),e=e.sibling;e!==null;)ka(e,n,i),e=e.sibling}function Na(e,n,i){var u=e.tag;if(u===5||u===6)e=e.stateNode,n?i.insertBefore(e,n):i.appendChild(e);else if(u!==4&&(e=e.child,e!==null))for(Na(e,n,i),e=e.sibling;e!==null;)Na(e,n,i),e=e.sibling}var Je=null,$t=!1;function jn(e,n,i){for(i=i.child;i!==null;)Gd(e,n,i),i=i.sibling}function Gd(e,n,i){if(Ct&&typeof Ct.onCommitFiberUnmount=="function")try{Ct.onCommitFiberUnmount(tr,i)}catch{}switch(i.tag){case 5:it||Gr(i,n);case 6:var u=Je,f=$t;Je=null,jn(e,n,i),Je=u,$t=f,Je!==null&&($t?(e=Je,i=i.stateNode,e.nodeType===8?e.parentNode.removeChild(i):e.removeChild(i)):Je.removeChild(i.stateNode));break;case 18:Je!==null&&($t?(e=Je,i=i.stateNode,e.nodeType===8?ju(e.parentNode,i):e.nodeType===1&&ju(e,i),$o(e)):ju(Je,i.stateNode));break;case 4:u=Je,f=$t,Je=i.stateNode.containerInfo,$t=!0,jn(e,n,i),Je=u,$t=f;break;case 0:case 11:case 14:case 15:if(!it&&(u=i.updateQueue,u!==null&&(u=u.lastEffect,u!==null))){f=u=u.next;do{var h=f,x=h.destroy;h=h.tag,x!==void 0&&((h&2)!==0||(h&4)!==0)&&Ea(i,n,x),f=f.next}while(f!==u)}jn(e,n,i);break;case 1:if(!it&&(Gr(i,n),u=i.stateNode,typeof u.componentWillUnmount=="function"))try{u.props=i.memoizedProps,u.state=i.memoizedState,u.componentWillUnmount()}catch(R){Ve(i,n,R)}jn(e,n,i);break;case 21:jn(e,n,i);break;case 22:i.mode&1?(it=(u=it)||i.memoizedState!==null,jn(e,n,i),it=u):jn(e,n,i);break;default:jn(e,n,i)}}function Zd(e){var n=e.updateQueue;if(n!==null){e.updateQueue=null;var i=e.stateNode;i===null&&(i=e.stateNode=new Ey),n.forEach(function(u){var f=Ly.bind(null,e,u);i.has(u)||(i.add(u),u.then(f,f))})}}function jt(e,n){var i=n.deletions;if(i!==null)for(var u=0;u<i.length;u++){var f=i[u];try{var h=e,x=n,R=x;e:for(;R!==null;){switch(R.tag){case 5:Je=R.stateNode,$t=!1;break e;case 3:Je=R.stateNode.containerInfo,$t=!0;break e;case 4:Je=R.stateNode.containerInfo,$t=!0;break e}R=R.return}if(Je===null)throw Error(o(160));Gd(h,x,f),Je=null,$t=!1;var L=f.alternate;L!==null&&(L.return=null),f.return=null}catch(Y){Ve(f,n,Y)}}if(n.subtreeFlags&12854)for(n=n.child;n!==null;)Jd(n,e),n=n.sibling}function Jd(e,n){var i=e.alternate,u=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(jt(n,e),Gt(e),u&4){try{ii(3,e,e.return),Os(3,e)}catch(me){Ve(e,e.return,me)}try{ii(5,e,e.return)}catch(me){Ve(e,e.return,me)}}break;case 1:jt(n,e),Gt(e),u&512&&i!==null&&Gr(i,i.return);break;case 5:if(jt(n,e),Gt(e),u&512&&i!==null&&Gr(i,i.return),e.flags&32){var f=e.stateNode;try{ln(f,"")}catch(me){Ve(e,e.return,me)}}if(u&4&&(f=e.stateNode,f!=null)){var h=e.memoizedProps,x=i!==null?i.memoizedProps:h,R=e.type,L=e.updateQueue;if(e.updateQueue=null,L!==null)try{R==="input"&&h.type==="radio"&&h.name!=null&&Be(f,h),wo(R,x);var Y=wo(R,h);for(x=0;x<L.length;x+=2){var ne=L[x],se=L[x+1];ne==="style"?ji(f,se):ne==="dangerouslySetInnerHTML"?Di(f,se):ne==="children"?ln(f,se):E(f,ne,se,Y)}switch(R){case"input":Wt(f,h);break;case"textarea":Cr(f,h);break;case"select":var te=f._wrapperState.wasMultiple;f._wrapperState.wasMultiple=!!h.multiple;var de=h.value;de!=null?It(f,!!h.multiple,de,!1):te!==!!h.multiple&&(h.defaultValue!=null?It(f,!!h.multiple,h.defaultValue,!0):It(f,!!h.multiple,h.multiple?[]:"",!1))}f[qo]=h}catch(me){Ve(e,e.return,me)}}break;case 6:if(jt(n,e),Gt(e),u&4){if(e.stateNode===null)throw Error(o(162));f=e.stateNode,h=e.memoizedProps;try{f.nodeValue=h}catch(me){Ve(e,e.return,me)}}break;case 3:if(jt(n,e),Gt(e),u&4&&i!==null&&i.memoizedState.isDehydrated)try{$o(n.containerInfo)}catch(me){Ve(e,e.return,me)}break;case 4:jt(n,e),Gt(e);break;case 13:jt(n,e),Gt(e),f=e.child,f.flags&8192&&(h=f.memoizedState!==null,f.stateNode.isHidden=h,!h||f.alternate!==null&&f.alternate.memoizedState!==null||(Pa=je())),u&4&&Zd(e);break;case 22:if(ne=i!==null&&i.memoizedState!==null,e.mode&1?(it=(Y=it)||ne,jt(n,e),it=Y):jt(n,e),Gt(e),u&8192){if(Y=e.memoizedState!==null,(e.stateNode.isHidden=Y)&&!ne&&(e.mode&1)!==0)for(he=e,ne=e.child;ne!==null;){for(se=he=ne;he!==null;){switch(te=he,de=te.child,te.tag){case 0:case 11:case 14:case 15:ii(4,te,te.return);break;case 1:Gr(te,te.return);var pe=te.stateNode;if(typeof pe.componentWillUnmount=="function"){u=te,i=te.return;try{n=u,pe.props=n.memoizedProps,pe.state=n.memoizedState,pe.componentWillUnmount()}catch(me){Ve(u,i,me)}}break;case 5:Gr(te,te.return);break;case 22:if(te.memoizedState!==null){nh(se);continue}}de!==null?(de.return=te,he=de):nh(se)}ne=ne.sibling}e:for(ne=null,se=e;;){if(se.tag===5){if(ne===null){ne=se;try{f=se.stateNode,Y?(h=f.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none"):(R=se.stateNode,L=se.memoizedProps.style,x=L!=null&&L.hasOwnProperty("display")?L.display:null,R.style.display=$i("display",x))}catch(me){Ve(e,e.return,me)}}}else if(se.tag===6){if(ne===null)try{se.stateNode.nodeValue=Y?"":se.memoizedProps}catch(me){Ve(e,e.return,me)}}else if((se.tag!==22&&se.tag!==23||se.memoizedState===null||se===e)&&se.child!==null){se.child.return=se,se=se.child;continue}if(se===e)break e;for(;se.sibling===null;){if(se.return===null||se.return===e)break e;ne===se&&(ne=null),se=se.return}ne===se&&(ne=null),se.sibling.return=se.return,se=se.sibling}}break;case 19:jt(n,e),Gt(e),u&4&&Zd(e);break;case 21:break;default:jt(n,e),Gt(e)}}function Gt(e){var n=e.flags;if(n&2){try{e:{for(var i=e.return;i!==null;){if(Qd(i)){var u=i;break e}i=i.return}throw Error(o(160))}switch(u.tag){case 5:var f=u.stateNode;u.flags&32&&(ln(f,""),u.flags&=-33);var h=Kd(e);Na(e,h,f);break;case 3:case 4:var x=u.stateNode.containerInfo,R=Kd(e);ka(e,R,x);break;default:throw Error(o(161))}}catch(L){Ve(e,e.return,L)}e.flags&=-3}n&4096&&(e.flags&=-4097)}function ky(e,n,i){he=e,eh(e)}function eh(e,n,i){for(var u=(e.mode&1)!==0;he!==null;){var f=he,h=f.child;if(f.tag===22&&u){var x=f.memoizedState!==null||zs;if(!x){var R=f.alternate,L=R!==null&&R.memoizedState!==null||it;R=zs;var Y=it;if(zs=x,(it=L)&&!Y)for(he=f;he!==null;)x=he,L=x.child,x.tag===22&&x.memoizedState!==null?rh(f):L!==null?(L.return=x,he=L):rh(f);for(;h!==null;)he=h,eh(h),h=h.sibling;he=f,zs=R,it=Y}th(e)}else(f.subtreeFlags&8772)!==0&&h!==null?(h.return=f,he=h):th(e)}}function th(e){for(;he!==null;){var n=he;if((n.flags&8772)!==0){var i=n.alternate;try{if((n.flags&8772)!==0)switch(n.tag){case 0:case 11:case 15:it||Os(5,n);break;case 1:var u=n.stateNode;if(n.flags&4&&!it)if(i===null)u.componentDidMount();else{var f=n.elementType===n.type?i.memoizedProps:Dt(n.type,i.memoizedProps);u.componentDidUpdate(f,i.memoizedState,u.__reactInternalSnapshotBeforeUpdate)}var h=n.updateQueue;h!==null&&nd(n,h,u);break;case 3:var x=n.updateQueue;if(x!==null){if(i=null,n.child!==null)switch(n.child.tag){case 5:i=n.child.stateNode;break;case 1:i=n.child.stateNode}nd(n,x,i)}break;case 5:var R=n.stateNode;if(i===null&&n.flags&4){i=R;var L=n.memoizedProps;switch(n.type){case"button":case"input":case"select":case"textarea":L.autoFocus&&i.focus();break;case"img":L.src&&(i.src=L.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(n.memoizedState===null){var Y=n.alternate;if(Y!==null){var ne=Y.memoizedState;if(ne!==null){var se=ne.dehydrated;se!==null&&$o(se)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(o(163))}it||n.flags&512&&_a(n)}catch(te){Ve(n,n.return,te)}}if(n===e){he=null;break}if(i=n.sibling,i!==null){i.return=n.return,he=i;break}he=n.return}}function nh(e){for(;he!==null;){var n=he;if(n===e){he=null;break}var i=n.sibling;if(i!==null){i.return=n.return,he=i;break}he=n.return}}function rh(e){for(;he!==null;){var n=he;try{switch(n.tag){case 0:case 11:case 15:var i=n.return;try{Os(4,n)}catch(L){Ve(n,i,L)}break;case 1:var u=n.stateNode;if(typeof u.componentDidMount=="function"){var f=n.return;try{u.componentDidMount()}catch(L){Ve(n,f,L)}}var h=n.return;try{_a(n)}catch(L){Ve(n,h,L)}break;case 5:var x=n.return;try{_a(n)}catch(L){Ve(n,x,L)}}}catch(L){Ve(n,n.return,L)}if(n===e){he=null;break}var R=n.sibling;if(R!==null){R.return=n.return,he=R;break}he=n.return}}var Ny=Math.ceil,Ds=N.ReactCurrentDispatcher,Ca=N.ReactCurrentOwner,At=N.ReactCurrentBatchConfig,Re=0,Ge=null,Xe=null,et=0,Et=0,Zr=In(0),Qe=0,si=null,cr=0,$s=0,Ta=0,li=null,dt=null,Pa=0,Jr=1/0,gn=null,js=!1,Ra=null,Fn=null,Fs=!1,bn=null,bs=0,ui=0,Ma=null,Hs=-1,Bs=0;function ut(){return(Re&6)!==0?je():Hs!==-1?Hs:Hs=je()}function Hn(e){return(e.mode&1)===0?1:(Re&2)!==0&&et!==0?et&-et:uy.transition!==null?(Bs===0&&(Bs=Gi()),Bs):(e=Ae,e!==0||(e=window.event,e=e===void 0?16:of(e.type)),e)}function Ft(e,n,i,u){if(50<ui)throw ui=0,Ma=null,Error(o(185));Lo(e,i,u),((Re&2)===0||e!==Ge)&&(e===Ge&&((Re&2)===0&&($s|=i),Qe===4&&Bn(e,et)),ht(e,u),i===1&&Re===0&&(n.mode&1)===0&&(Jr=je()+500,gs&&On()))}function ht(e,n){var i=e.callbackNode;Mo(e,n);var u=Ar(e,e===Ge?et:0);if(u===0)i!==null&&Yi(i),e.callbackNode=null,e.callbackPriority=0;else if(n=u&-u,e.callbackPriority!==n){if(i!=null&&Yi(i),n===1)e.tag===0?ly(ih.bind(null,e)):Uf(ih.bind(null,e)),ry(function(){(Re&6)===0&&On()}),i=null;else{switch(Kc(u)){case 1:i=Ro;break;case 4:i=Qi;break;case 16:i=Rr;break;case 536870912:i=Ki;break;default:i=Rr}i=hh(i,oh.bind(null,e))}e.callbackPriority=n,e.callbackNode=i}}function oh(e,n){if(Hs=-1,Bs=0,(Re&6)!==0)throw Error(o(327));var i=e.callbackNode;if(eo()&&e.callbackNode!==i)return null;var u=Ar(e,e===Ge?et:0);if(u===0)return null;if((u&30)!==0||(u&e.expiredLanes)!==0||n)n=Vs(e,u);else{n=u;var f=Re;Re|=2;var h=lh();(Ge!==e||et!==n)&&(gn=null,Jr=je()+500,dr(e,n));do try{Py();break}catch(R){sh(e,R)}while(!0);qu(),Ds.current=h,Re=f,Xe!==null?n=0:(Ge=null,et=0,n=Qe)}if(n!==0){if(n===2&&(f=Ao(e),f!==0&&(u=f,n=Aa(e,f))),n===1)throw i=si,dr(e,0),Bn(e,u),ht(e,je()),i;if(n===6)Bn(e,u);else{if(f=e.current.alternate,(u&30)===0&&!Cy(f)&&(n=Vs(e,u),n===2&&(h=Ao(e),h!==0&&(u=h,n=Aa(e,h))),n===1))throw i=si,dr(e,0),Bn(e,u),ht(e,je()),i;switch(e.finishedWork=f,e.finishedLanes=u,n){case 0:case 1:throw Error(o(345));case 2:hr(e,dt,gn);break;case 3:if(Bn(e,u),(u&130023424)===u&&(n=Pa+500-je(),10<n)){if(Ar(e,0)!==0)break;if(f=e.suspendedLanes,(f&u)!==u){ut(),e.pingedLanes|=e.suspendedLanes&f;break}e.timeoutHandle=$u(hr.bind(null,e,dt,gn),n);break}hr(e,dt,gn);break;case 4:if(Bn(e,u),(u&4194240)===u)break;for(n=e.eventTimes,f=-1;0<u;){var x=31-vt(u);h=1<<x,x=n[x],x>f&&(f=x),u&=~h}if(u=f,u=je()-u,u=(120>u?120:480>u?480:1080>u?1080:1920>u?1920:3e3>u?3e3:4320>u?4320:1960*Ny(u/1960))-u,10<u){e.timeoutHandle=$u(hr.bind(null,e,dt,gn),u);break}hr(e,dt,gn);break;case 5:hr(e,dt,gn);break;default:throw Error(o(329))}}}return ht(e,je()),e.callbackNode===i?oh.bind(null,e):null}function Aa(e,n){var i=li;return e.current.memoizedState.isDehydrated&&(dr(e,n).flags|=256),e=Vs(e,n),e!==2&&(n=dt,dt=i,n!==null&&La(n)),e}function La(e){dt===null?dt=e:dt.push.apply(dt,e)}function Cy(e){for(var n=e;;){if(n.flags&16384){var i=n.updateQueue;if(i!==null&&(i=i.stores,i!==null))for(var u=0;u<i.length;u++){var f=i[u],h=f.getSnapshot;f=f.value;try{if(!zt(h(),f))return!1}catch{return!1}}}if(i=n.child,n.subtreeFlags&16384&&i!==null)i.return=n,n=i;else{if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function Bn(e,n){for(n&=~Ta,n&=~$s,e.suspendedLanes|=n,e.pingedLanes&=~n,e=e.expirationTimes;0<n;){var i=31-vt(n),u=1<<i;e[i]=-1,n&=~u}}function ih(e){if((Re&6)!==0)throw Error(o(327));eo();var n=Ar(e,0);if((n&1)===0)return ht(e,je()),null;var i=Vs(e,n);if(e.tag!==0&&i===2){var u=Ao(e);u!==0&&(n=u,i=Aa(e,u))}if(i===1)throw i=si,dr(e,0),Bn(e,n),ht(e,je()),i;if(i===6)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=n,hr(e,dt,gn),ht(e,je()),null}function Ia(e,n){var i=Re;Re|=1;try{return e(n)}finally{Re=i,Re===0&&(Jr=je()+500,gs&&On())}}function fr(e){bn!==null&&bn.tag===0&&(Re&6)===0&&eo();var n=Re;Re|=1;var i=At.transition,u=Ae;try{if(At.transition=null,Ae=1,e)return e()}finally{Ae=u,At.transition=i,Re=n,(Re&6)===0&&On()}}function za(){Et=Zr.current,Oe(Zr)}function dr(e,n){e.finishedWork=null,e.finishedLanes=0;var i=e.timeoutHandle;if(i!==-1&&(e.timeoutHandle=-1,ny(i)),Xe!==null)for(i=Xe.return;i!==null;){var u=i;switch(Vu(u),u.tag){case 1:u=u.type.childContextTypes,u!=null&&ps();break;case 3:Qr(),Oe(at),Oe(nt),na();break;case 5:ea(u);break;case 4:Qr();break;case 13:Oe(Fe);break;case 19:Oe(Fe);break;case 10:Qu(u.type._context);break;case 22:case 23:za()}i=i.return}if(Ge=e,Xe=e=Vn(e.current,null),et=Et=n,Qe=0,si=null,Ta=$s=cr=0,dt=li=null,lr!==null){for(n=0;n<lr.length;n++)if(i=lr[n],u=i.interleaved,u!==null){i.interleaved=null;var f=u.next,h=i.pending;if(h!==null){var x=h.next;h.next=f,u.next=x}i.pending=u}lr=null}return e}function sh(e,n){do{var i=Xe;try{if(qu(),Cs.current=Ms,Ts){for(var u=be.memoizedState;u!==null;){var f=u.queue;f!==null&&(f.pending=null),u=u.next}Ts=!1}if(ar=0,Ke=qe=be=null,ei=!1,ti=0,Ca.current=null,i===null||i.return===null){Qe=1,si=n,Xe=null;break}e:{var h=e,x=i.return,R=i,L=n;if(n=et,R.flags|=32768,L!==null&&typeof L=="object"&&typeof L.then=="function"){var Y=L,ne=R,se=ne.tag;if((ne.mode&1)===0&&(se===0||se===11||se===15)){var te=ne.alternate;te?(ne.updateQueue=te.updateQueue,ne.memoizedState=te.memoizedState,ne.lanes=te.lanes):(ne.updateQueue=null,ne.memoizedState=null)}var de=Ad(x);if(de!==null){de.flags&=-257,Ld(de,x,R,h,n),de.mode&1&&Md(h,Y,n),n=de,L=Y;var pe=n.updateQueue;if(pe===null){var me=new Set;me.add(L),n.updateQueue=me}else pe.add(L);break e}else{if((n&1)===0){Md(h,Y,n),Oa();break e}L=Error(o(426))}}else if(De&&R.mode&1){var Ue=Ad(x);if(Ue!==null){(Ue.flags&65536)===0&&(Ue.flags|=256),Ld(Ue,x,R,h,n),Xu(Kr(L,R));break e}}h=L=Kr(L,R),Qe!==4&&(Qe=2),li===null?li=[h]:li.push(h),h=x;do{switch(h.tag){case 3:h.flags|=65536,n&=-n,h.lanes|=n;var V=Pd(h,L,n);td(h,V);break e;case 1:R=L;var O=h.type,U=h.stateNode;if((h.flags&128)===0&&(typeof O.getDerivedStateFromError=="function"||U!==null&&typeof U.componentDidCatch=="function"&&(Fn===null||!Fn.has(U)))){h.flags|=65536,n&=-n,h.lanes|=n;var le=Rd(h,R,n);td(h,le);break e}}h=h.return}while(h!==null)}ah(i)}catch(ge){n=ge,Xe===i&&i!==null&&(Xe=i=i.return);continue}break}while(!0)}function lh(){var e=Ds.current;return Ds.current=Ms,e===null?Ms:e}function Oa(){(Qe===0||Qe===3||Qe===2)&&(Qe=4),Ge===null||(cr&268435455)===0&&($s&268435455)===0||Bn(Ge,et)}function Vs(e,n){var i=Re;Re|=2;var u=lh();(Ge!==e||et!==n)&&(gn=null,dr(e,n));do try{Ty();break}catch(f){sh(e,f)}while(!0);if(qu(),Re=i,Ds.current=u,Xe!==null)throw Error(o(261));return Ge=null,et=0,Qe}function Ty(){for(;Xe!==null;)uh(Xe)}function Py(){for(;Xe!==null&&!ou();)uh(Xe)}function uh(e){var n=dh(e.alternate,e,Et);e.memoizedProps=e.pendingProps,n===null?ah(e):Xe=n,Ca.current=null}function ah(e){var n=e;do{var i=n.alternate;if(e=n.return,(n.flags&32768)===0){if(i=xy(i,n,Et),i!==null){Xe=i;return}}else{if(i=Sy(i,n),i!==null){i.flags&=32767,Xe=i;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Qe=6,Xe=null;return}}if(n=n.sibling,n!==null){Xe=n;return}Xe=n=e}while(n!==null);Qe===0&&(Qe=5)}function hr(e,n,i){var u=Ae,f=At.transition;try{At.transition=null,Ae=1,Ry(e,n,i,u)}finally{At.transition=f,Ae=u}return null}function Ry(e,n,i,u){do eo();while(bn!==null);if((Re&6)!==0)throw Error(o(327));i=e.finishedWork;var f=e.finishedLanes;if(i===null)return null;if(e.finishedWork=null,e.finishedLanes=0,i===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var h=i.lanes|i.childLanes;if(a0(e,h),e===Ge&&(Xe=Ge=null,et=0),(i.subtreeFlags&2064)===0&&(i.flags&2064)===0||Fs||(Fs=!0,hh(Rr,function(){return eo(),null})),h=(i.flags&15990)!==0,(i.subtreeFlags&15990)!==0||h){h=At.transition,At.transition=null;var x=Ae;Ae=1;var R=Re;Re|=4,Ca.current=null,_y(e,i),Jd(i,e),Q0(Ou),es=!!zu,Ou=zu=null,e.current=i,ky(i),qi(),Re=R,Ae=x,At.transition=h}else e.current=i;if(Fs&&(Fs=!1,bn=e,bs=f),h=e.pendingLanes,h===0&&(Fn=null),lu(i.stateNode),ht(e,je()),n!==null)for(u=e.onRecoverableError,i=0;i<n.length;i++)f=n[i],u(f.value,{componentStack:f.stack,digest:f.digest});if(js)throw js=!1,e=Ra,Ra=null,e;return(bs&1)!==0&&e.tag!==0&&eo(),h=e.pendingLanes,(h&1)!==0?e===Ma?ui++:(ui=0,Ma=e):ui=0,On(),null}function eo(){if(bn!==null){var e=Kc(bs),n=At.transition,i=Ae;try{if(At.transition=null,Ae=16>e?16:e,bn===null)var u=!1;else{if(e=bn,bn=null,bs=0,(Re&6)!==0)throw Error(o(331));var f=Re;for(Re|=4,he=e.current;he!==null;){var h=he,x=h.child;if((he.flags&16)!==0){var R=h.deletions;if(R!==null){for(var L=0;L<R.length;L++){var Y=R[L];for(he=Y;he!==null;){var ne=he;switch(ne.tag){case 0:case 11:case 15:ii(8,ne,h)}var se=ne.child;if(se!==null)se.return=ne,he=se;else for(;he!==null;){ne=he;var te=ne.sibling,de=ne.return;if(qd(ne),ne===Y){he=null;break}if(te!==null){te.return=de,he=te;break}he=de}}}var pe=h.alternate;if(pe!==null){var me=pe.child;if(me!==null){pe.child=null;do{var Ue=me.sibling;me.sibling=null,me=Ue}while(me!==null)}}he=h}}if((h.subtreeFlags&2064)!==0&&x!==null)x.return=h,he=x;else e:for(;he!==null;){if(h=he,(h.flags&2048)!==0)switch(h.tag){case 0:case 11:case 15:ii(9,h,h.return)}var V=h.sibling;if(V!==null){V.return=h.return,he=V;break e}he=h.return}}var O=e.current;for(he=O;he!==null;){x=he;var U=x.child;if((x.subtreeFlags&2064)!==0&&U!==null)U.return=x,he=U;else e:for(x=O;he!==null;){if(R=he,(R.flags&2048)!==0)try{switch(R.tag){case 0:case 11:case 15:Os(9,R)}}catch(ge){Ve(R,R.return,ge)}if(R===x){he=null;break e}var le=R.sibling;if(le!==null){le.return=R.return,he=le;break e}he=R.return}}if(Re=f,On(),Ct&&typeof Ct.onPostCommitFiberRoot=="function")try{Ct.onPostCommitFiberRoot(tr,e)}catch{}u=!0}return u}finally{Ae=i,At.transition=n}}return!1}function ch(e,n,i){n=Kr(i,n),n=Pd(e,n,1),e=$n(e,n,1),n=ut(),e!==null&&(Lo(e,1,n),ht(e,n))}function Ve(e,n,i){if(e.tag===3)ch(e,e,i);else for(;n!==null;){if(n.tag===3){ch(n,e,i);break}else if(n.tag===1){var u=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof u.componentDidCatch=="function"&&(Fn===null||!Fn.has(u))){e=Kr(i,e),e=Rd(n,e,1),n=$n(n,e,1),e=ut(),n!==null&&(Lo(n,1,e),ht(n,e));break}}n=n.return}}function My(e,n,i){var u=e.pingCache;u!==null&&u.delete(n),n=ut(),e.pingedLanes|=e.suspendedLanes&i,Ge===e&&(et&i)===i&&(Qe===4||Qe===3&&(et&130023424)===et&&500>je()-Pa?dr(e,0):Ta|=i),ht(e,n)}function fh(e,n){n===0&&((e.mode&1)===0?n=1:(n=un,un<<=1,(un&130023424)===0&&(un=4194304)));var i=ut();e=hn(e,n),e!==null&&(Lo(e,n,i),ht(e,i))}function Ay(e){var n=e.memoizedState,i=0;n!==null&&(i=n.retryLane),fh(e,i)}function Ly(e,n){var i=0;switch(e.tag){case 13:var u=e.stateNode,f=e.memoizedState;f!==null&&(i=f.retryLane);break;case 19:u=e.stateNode;break;default:throw Error(o(314))}u!==null&&u.delete(n),fh(e,i)}var dh;dh=function(e,n,i){if(e!==null)if(e.memoizedProps!==n.pendingProps||at.current)ft=!0;else{if((e.lanes&i)===0&&(n.flags&128)===0)return ft=!1,wy(e,n,i);ft=(e.flags&131072)!==0}else ft=!1,De&&(n.flags&1048576)!==0&&Wf(n,vs,n.index);switch(n.lanes=0,n.tag){case 2:var u=n.type;Is(e,n),e=n.pendingProps;var f=Br(n,nt.current);qr(n,i),f=ia(null,n,u,e,f,i);var h=sa();return n.flags|=1,typeof f=="object"&&f!==null&&typeof f.render=="function"&&f.$$typeof===void 0?(n.tag=1,n.memoizedState=null,n.updateQueue=null,ct(u)?(h=!0,ms(n)):h=!1,n.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,Zu(n),f.updater=As,n.stateNode=f,f._reactInternals=n,da(n,u,e,i),n=ga(null,n,u,!0,h,i)):(n.tag=0,De&&h&&Bu(n),lt(null,n,f,i),n=n.child),n;case 16:u=n.elementType;e:{switch(Is(e,n),e=n.pendingProps,f=u._init,u=f(u._payload),n.type=u,f=n.tag=zy(u),e=Dt(u,e),f){case 0:n=ma(null,n,u,e,i);break e;case 1:n=jd(null,n,u,e,i);break e;case 11:n=Id(null,n,u,e,i);break e;case 14:n=zd(null,n,u,Dt(u.type,e),i);break e}throw Error(o(306,u,""))}return n;case 0:return u=n.type,f=n.pendingProps,f=n.elementType===u?f:Dt(u,f),ma(e,n,u,f,i);case 1:return u=n.type,f=n.pendingProps,f=n.elementType===u?f:Dt(u,f),jd(e,n,u,f,i);case 3:e:{if(Fd(n),e===null)throw Error(o(387));u=n.pendingProps,h=n.memoizedState,f=h.element,ed(e,n),ks(n,u,null,i);var x=n.memoizedState;if(u=x.element,h.isDehydrated)if(h={element:u,isDehydrated:!1,cache:x.cache,pendingSuspenseBoundaries:x.pendingSuspenseBoundaries,transitions:x.transitions},n.updateQueue.baseState=h,n.memoizedState=h,n.flags&256){f=Kr(Error(o(423)),n),n=bd(e,n,u,i,f);break e}else if(u!==f){f=Kr(Error(o(424)),n),n=bd(e,n,u,i,f);break e}else for(St=Ln(n.stateNode.containerInfo.firstChild),xt=n,De=!0,Ot=null,i=Zf(n,null,u,i),n.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling;else{if(Wr(),u===f){n=mn(e,n,i);break e}lt(e,n,u,i)}n=n.child}return n;case 5:return rd(n),e===null&&Wu(n),u=n.type,f=n.pendingProps,h=e!==null?e.memoizedProps:null,x=f.children,Du(u,f)?x=null:h!==null&&Du(u,h)&&(n.flags|=32),$d(e,n),lt(e,n,x,i),n.child;case 6:return e===null&&Wu(n),null;case 13:return Hd(e,n,i);case 4:return Ju(n,n.stateNode.containerInfo),u=n.pendingProps,e===null?n.child=Xr(n,null,u,i):lt(e,n,u,i),n.child;case 11:return u=n.type,f=n.pendingProps,f=n.elementType===u?f:Dt(u,f),Id(e,n,u,f,i);case 7:return lt(e,n,n.pendingProps,i),n.child;case 8:return lt(e,n,n.pendingProps.children,i),n.child;case 12:return lt(e,n,n.pendingProps.children,i),n.child;case 10:e:{if(u=n.type._context,f=n.pendingProps,h=n.memoizedProps,x=f.value,Le(Ss,u._currentValue),u._currentValue=x,h!==null)if(zt(h.value,x)){if(h.children===f.children&&!at.current){n=mn(e,n,i);break e}}else for(h=n.child,h!==null&&(h.return=n);h!==null;){var R=h.dependencies;if(R!==null){x=h.child;for(var L=R.firstContext;L!==null;){if(L.context===u){if(h.tag===1){L=pn(-1,i&-i),L.tag=2;var Y=h.updateQueue;if(Y!==null){Y=Y.shared;var ne=Y.pending;ne===null?L.next=L:(L.next=ne.next,ne.next=L),Y.pending=L}}h.lanes|=i,L=h.alternate,L!==null&&(L.lanes|=i),Ku(h.return,i,n),R.lanes|=i;break}L=L.next}}else if(h.tag===10)x=h.type===n.type?null:h.child;else if(h.tag===18){if(x=h.return,x===null)throw Error(o(341));x.lanes|=i,R=x.alternate,R!==null&&(R.lanes|=i),Ku(x,i,n),x=h.sibling}else x=h.child;if(x!==null)x.return=h;else for(x=h;x!==null;){if(x===n){x=null;break}if(h=x.sibling,h!==null){h.return=x.return,x=h;break}x=x.return}h=x}lt(e,n,f.children,i),n=n.child}return n;case 9:return f=n.type,u=n.pendingProps.children,qr(n,i),f=Rt(f),u=u(f),n.flags|=1,lt(e,n,u,i),n.child;case 14:return u=n.type,f=Dt(u,n.pendingProps),f=Dt(u.type,f),zd(e,n,u,f,i);case 15:return Od(e,n,n.type,n.pendingProps,i);case 17:return u=n.type,f=n.pendingProps,f=n.elementType===u?f:Dt(u,f),Is(e,n),n.tag=1,ct(u)?(e=!0,ms(n)):e=!1,qr(n,i),Cd(n,u,f),da(n,u,f,i),ga(null,n,u,!0,e,i);case 19:return Vd(e,n,i);case 22:return Dd(e,n,i)}throw Error(o(156,n.tag))};function hh(e,n){return Xi(e,n)}function Iy(e,n,i,u){this.tag=e,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=u,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lt(e,n,i,u){return new Iy(e,n,i,u)}function Da(e){return e=e.prototype,!(!e||!e.isReactComponent)}function zy(e){if(typeof e=="function")return Da(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ee)return 11;if(e===W)return 14}return 2}function Vn(e,n){var i=e.alternate;return i===null?(i=Lt(e.tag,n,e.key,e.mode),i.elementType=e.elementType,i.type=e.type,i.stateNode=e.stateNode,i.alternate=e,e.alternate=i):(i.pendingProps=n,i.type=e.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=e.flags&14680064,i.childLanes=e.childLanes,i.lanes=e.lanes,i.child=e.child,i.memoizedProps=e.memoizedProps,i.memoizedState=e.memoizedState,i.updateQueue=e.updateQueue,n=e.dependencies,i.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},i.sibling=e.sibling,i.index=e.index,i.ref=e.ref,i}function Us(e,n,i,u,f,h){var x=2;if(u=e,typeof e=="function")Da(e)&&(x=1);else if(typeof e=="string")x=5;else e:switch(e){case F:return pr(i.children,f,h,n);case X:x=8,f|=8;break;case K:return e=Lt(12,i,n,f|2),e.elementType=K,e.lanes=h,e;case G:return e=Lt(13,i,n,f),e.elementType=G,e.lanes=h,e;case P:return e=Lt(19,i,n,f),e.elementType=P,e.lanes=h,e;case H:return Ws(i,f,h,n);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Z:x=10;break e;case J:x=9;break e;case ee:x=11;break e;case W:x=14;break e;case b:x=16,u=null;break e}throw Error(o(130,e==null?e:typeof e,""))}return n=Lt(x,i,n,f),n.elementType=e,n.type=u,n.lanes=h,n}function pr(e,n,i,u){return e=Lt(7,e,u,n),e.lanes=i,e}function Ws(e,n,i,u){return e=Lt(22,e,u,n),e.elementType=H,e.lanes=i,e.stateNode={isHidden:!1},e}function $a(e,n,i){return e=Lt(6,e,null,n),e.lanes=i,e}function ja(e,n,i){return n=Lt(4,e.children!==null?e.children:[],e.key,n),n.lanes=i,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}function Oy(e,n,i,u,f){this.tag=n,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Lr(0),this.expirationTimes=Lr(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Lr(0),this.identifierPrefix=u,this.onRecoverableError=f,this.mutableSourceEagerHydrationData=null}function Fa(e,n,i,u,f,h,x,R,L){return e=new Oy(e,n,i,R,L),n===1?(n=1,h===!0&&(n|=8)):n=0,h=Lt(3,null,null,n),e.current=h,h.stateNode=e,h.memoizedState={element:u,isDehydrated:i,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zu(h),e}function Dy(e,n,i){var u=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:$,key:u==null?null:""+u,children:e,containerInfo:n,implementation:i}}function ph(e){if(!e)return zn;e=e._reactInternals;e:{if(Yt(e)!==e||e.tag!==1)throw Error(o(170));var n=e;do{switch(n.tag){case 3:n=n.stateNode.context;break e;case 1:if(ct(n.type)){n=n.stateNode.__reactInternalMemoizedMergedChildContext;break e}}n=n.return}while(n!==null);throw Error(o(171))}if(e.tag===1){var i=e.type;if(ct(i))return Bf(e,i,n)}return n}function mh(e,n,i,u,f,h,x,R,L){return e=Fa(i,u,!0,e,f,h,x,R,L),e.context=ph(null),i=e.current,u=ut(),f=Hn(i),h=pn(u,f),h.callback=n??null,$n(i,h,f),e.current.lanes=f,Lo(e,f,u),ht(e,u),e}function Xs(e,n,i,u){var f=n.current,h=ut(),x=Hn(f);return i=ph(i),n.context===null?n.context=i:n.pendingContext=i,n=pn(h,x),n.payload={element:e},u=u===void 0?null:u,u!==null&&(n.callback=u),e=$n(f,n,x),e!==null&&(Ft(e,f,x,h),_s(e,f,x)),x}function Ys(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function gh(e,n){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var i=e.retryLane;e.retryLane=i!==0&&i<n?i:n}}function ba(e,n){gh(e,n),(e=e.alternate)&&gh(e,n)}function $y(){return null}var yh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ha(e){this._internalRoot=e}qs.prototype.render=Ha.prototype.render=function(e){var n=this._internalRoot;if(n===null)throw Error(o(409));Xs(e,n,null,null)},qs.prototype.unmount=Ha.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var n=e.containerInfo;fr(function(){Xs(null,e,null,null)}),n[an]=null}};function qs(e){this._internalRoot=e}qs.prototype.unstable_scheduleHydration=function(e){if(e){var n=Jc();e={blockedOn:null,target:e,priority:n};for(var i=0;i<Rn.length&&n!==0&&n<Rn[i].priority;i++);Rn.splice(i,0,e),i===0&&nf(e)}};function Ba(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Qs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function vh(){}function jy(e,n,i,u,f){if(f){if(typeof u=="function"){var h=u;u=function(){var Y=Ys(x);h.call(Y)}}var x=mh(n,u,e,0,null,!1,!1,"",vh);return e._reactRootContainer=x,e[an]=x.current,Xo(e.nodeType===8?e.parentNode:e),fr(),x}for(;f=e.lastChild;)e.removeChild(f);if(typeof u=="function"){var R=u;u=function(){var Y=Ys(L);R.call(Y)}}var L=Fa(e,0,!1,null,null,!1,!1,"",vh);return e._reactRootContainer=L,e[an]=L.current,Xo(e.nodeType===8?e.parentNode:e),fr(function(){Xs(n,L,i,u)}),L}function Ks(e,n,i,u,f){var h=i._reactRootContainer;if(h){var x=h;if(typeof f=="function"){var R=f;f=function(){var L=Ys(x);R.call(L)}}Xs(n,x,e,f)}else x=jy(i,n,e,f,u);return Ys(x)}Gc=function(e){switch(e.tag){case 3:var n=e.stateNode;if(n.current.memoizedState.isDehydrated){var i=nr(n.pendingLanes);i!==0&&(du(n,i|1),ht(n,je()),(Re&6)===0&&(Jr=je()+500,On()))}break;case 13:fr(function(){var u=hn(e,1);if(u!==null){var f=ut();Ft(u,e,1,f)}}),ba(e,1)}},hu=function(e){if(e.tag===13){var n=hn(e,134217728);if(n!==null){var i=ut();Ft(n,e,134217728,i)}ba(e,134217728)}},Zc=function(e){if(e.tag===13){var n=Hn(e),i=hn(e,n);if(i!==null){var u=ut();Ft(i,e,n,u)}ba(e,n)}},Jc=function(){return Ae},ef=function(e,n){var i=Ae;try{return Ae=e,n()}finally{Ae=i}},Eo=function(e,n,i){switch(n){case"input":if(Wt(e,i),n=i.name,i.type==="radio"&&n!=null){for(i=e;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),n=0;n<i.length;n++){var u=i[n];if(u!==e&&u.form===e.form){var f=hs(u);if(!f)throw Error(o(90));ye(u),Wt(u,f)}}}break;case"textarea":Cr(e,i);break;case"select":n=i.value,n!=null&&It(e,!!i.multiple,n,!1)}},Hi=Ia,Bi=fr;var Fy={usingClientEntryPoint:!1,Events:[Qo,br,hs,bi,_o,Ia]},ai={findFiberByHostInstance:rr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},by={bundleType:ai.bundleType,version:ai.version,rendererPackageName:ai.rendererPackageName,rendererConfig:ai.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:N.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ui(e),e===null?null:e.stateNode},findFiberByHostInstance:ai.findFiberByHostInstance||$y,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Gs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Gs.isDisabled&&Gs.supportsFiber)try{tr=Gs.inject(by),Ct=Gs}catch{}}return pt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Fy,pt.createPortal=function(e,n){var i=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ba(n))throw Error(o(200));return Dy(e,n,null,i)},pt.createRoot=function(e,n){if(!Ba(e))throw Error(o(299));var i=!1,u="",f=yh;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onRecoverableError!==void 0&&(f=n.onRecoverableError)),n=Fa(e,1,!1,null,null,i,!1,u,f),e[an]=n.current,Xo(e.nodeType===8?e.parentNode:e),new Ha(n)},pt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var n=e._reactInternals;if(n===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=Ui(n),e=e===null?null:e.stateNode,e},pt.flushSync=function(e){return fr(e)},pt.hydrate=function(e,n,i){if(!Qs(n))throw Error(o(200));return Ks(null,e,n,!0,i)},pt.hydrateRoot=function(e,n,i){if(!Ba(e))throw Error(o(405));var u=i!=null&&i.hydratedSources||null,f=!1,h="",x=yh;if(i!=null&&(i.unstable_strictMode===!0&&(f=!0),i.identifierPrefix!==void 0&&(h=i.identifierPrefix),i.onRecoverableError!==void 0&&(x=i.onRecoverableError)),n=mh(n,null,e,1,i??null,f,!1,h,x),e[an]=n.current,Xo(e),u)for(e=0;e<u.length;e++)i=u[e],f=i._getVersion,f=f(i._source),n.mutableSourceEagerHydrationData==null?n.mutableSourceEagerHydrationData=[i,f]:n.mutableSourceEagerHydrationData.push(i,f);return new qs(n)},pt.render=function(e,n,i){if(!Qs(n))throw Error(o(200));return Ks(null,e,n,!1,i)},pt.unmountComponentAtNode=function(e){if(!Qs(e))throw Error(o(40));return e._reactRootContainer?(fr(function(){Ks(null,null,e,!1,function(){e._reactRootContainer=null,e[an]=null})}),!0):!1},pt.unstable_batchedUpdates=Ia,pt.unstable_renderSubtreeIntoContainer=function(e,n,i,u){if(!Qs(i))throw Error(o(200));if(e==null||e._reactInternals===void 0)throw Error(o(38));return Ks(e,n,i,!1,u)},pt.version="18.3.1-next-f1338f8080-20240426",pt}var Ch;function Kp(){if(Ch)return Wa.exports;Ch=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(r){console.error(r)}}return t(),Wa.exports=qy(),Wa.exports}var Th;function Qy(){if(Th)return Zs;Th=1;var t=Kp();return Zs.createRoot=t.createRoot,Zs.hydrateRoot=t.hydrateRoot,Zs}var Ky=Qy();function Gp(t,r){return function(){return t.apply(r,arguments)}}const{toString:Gy}=Object.prototype,{getPrototypeOf:Mc}=Object,{iterator:Al,toStringTag:Zp}=Symbol,Ll=(t=>r=>{const o=Gy.call(r);return t[o]||(t[o]=o.slice(8,-1).toLowerCase())})(Object.create(null)),Vt=t=>(t=t.toLowerCase(),r=>Ll(r)===t),Il=t=>r=>typeof r===t,{isArray:go}=Array,vi=Il("undefined");function Zy(t){return t!==null&&!vi(t)&&t.constructor!==null&&!vi(t.constructor)&&gt(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const Jp=Vt("ArrayBuffer");function Jy(t){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(t):r=t&&t.buffer&&Jp(t.buffer),r}const ev=Il("string"),gt=Il("function"),em=Il("number"),zl=t=>t!==null&&typeof t=="object",tv=t=>t===!0||t===!1,al=t=>{if(Ll(t)!=="object")return!1;const r=Mc(t);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Zp in t)&&!(Al in t)},nv=Vt("Date"),rv=Vt("File"),ov=Vt("Blob"),iv=Vt("FileList"),sv=t=>zl(t)&&gt(t.pipe),lv=t=>{let r;return t&&(typeof FormData=="function"&&t instanceof FormData||gt(t.append)&&((r=Ll(t))==="formdata"||r==="object"&&gt(t.toString)&&t.toString()==="[object FormData]"))},uv=Vt("URLSearchParams"),[av,cv,fv,dv]=["ReadableStream","Request","Response","Headers"].map(Vt),hv=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ri(t,r,{allOwnKeys:o=!1}={}){if(t===null||typeof t>"u")return;let s,l;if(typeof t!="object"&&(t=[t]),go(t))for(s=0,l=t.length;s<l;s++)r.call(null,t[s],s,t);else{const a=o?Object.getOwnPropertyNames(t):Object.keys(t),c=a.length;let d;for(s=0;s<c;s++)d=a[s],r.call(null,t[d],d,t)}}function tm(t,r){r=r.toLowerCase();const o=Object.keys(t);let s=o.length,l;for(;s-- >0;)if(l=o[s],r===l.toLowerCase())return l;return null}const gr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,nm=t=>!vi(t)&&t!==gr;function cc(){const{caseless:t}=nm(this)&&this||{},r={},o=(s,l)=>{const a=t&&tm(r,l)||l;al(r[a])&&al(s)?r[a]=cc(r[a],s):al(s)?r[a]=cc({},s):go(s)?r[a]=s.slice():r[a]=s};for(let s=0,l=arguments.length;s<l;s++)arguments[s]&&Ri(arguments[s],o);return r}const pv=(t,r,o,{allOwnKeys:s}={})=>(Ri(r,(l,a)=>{o&&gt(l)?t[a]=Gp(l,o):t[a]=l},{allOwnKeys:s}),t),mv=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),gv=(t,r,o,s)=>{t.prototype=Object.create(r.prototype,s),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:r.prototype}),o&&Object.assign(t.prototype,o)},yv=(t,r,o,s)=>{let l,a,c;const d={};if(r=r||{},t==null)return r;do{for(l=Object.getOwnPropertyNames(t),a=l.length;a-- >0;)c=l[a],(!s||s(c,t,r))&&!d[c]&&(r[c]=t[c],d[c]=!0);t=o!==!1&&Mc(t)}while(t&&(!o||o(t,r))&&t!==Object.prototype);return r},vv=(t,r,o)=>{t=String(t),(o===void 0||o>t.length)&&(o=t.length),o-=r.length;const s=t.indexOf(r,o);return s!==-1&&s===o},wv=t=>{if(!t)return null;if(go(t))return t;let r=t.length;if(!em(r))return null;const o=new Array(r);for(;r-- >0;)o[r]=t[r];return o},xv=(t=>r=>t&&r instanceof t)(typeof Uint8Array<"u"&&Mc(Uint8Array)),Sv=(t,r)=>{const s=(t&&t[Al]).call(t);let l;for(;(l=s.next())&&!l.done;){const a=l.value;r.call(t,a[0],a[1])}},Ev=(t,r)=>{let o;const s=[];for(;(o=t.exec(r))!==null;)s.push(o);return s},_v=Vt("HTMLFormElement"),kv=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(o,s,l){return s.toUpperCase()+l}),Ph=(({hasOwnProperty:t})=>(r,o)=>t.call(r,o))(Object.prototype),Nv=Vt("RegExp"),rm=(t,r)=>{const o=Object.getOwnPropertyDescriptors(t),s={};Ri(o,(l,a)=>{let c;(c=r(l,a,t))!==!1&&(s[a]=c||l)}),Object.defineProperties(t,s)},Cv=t=>{rm(t,(r,o)=>{if(gt(t)&&["arguments","caller","callee"].indexOf(o)!==-1)return!1;const s=t[o];if(gt(s)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+o+"'")})}})},Tv=(t,r)=>{const o={},s=l=>{l.forEach(a=>{o[a]=!0})};return go(t)?s(t):s(String(t).split(r)),o},Pv=()=>{},Rv=(t,r)=>t!=null&&Number.isFinite(t=+t)?t:r;function Mv(t){return!!(t&&gt(t.append)&&t[Zp]==="FormData"&&t[Al])}const Av=t=>{const r=new Array(10),o=(s,l)=>{if(zl(s)){if(r.indexOf(s)>=0)return;if(!("toJSON"in s)){r[l]=s;const a=go(s)?[]:{};return Ri(s,(c,d)=>{const p=o(c,l+1);!vi(p)&&(a[d]=p)}),r[l]=void 0,a}}return s};return o(t,0)},Lv=Vt("AsyncFunction"),Iv=t=>t&&(zl(t)||gt(t))&&gt(t.then)&&gt(t.catch),om=((t,r)=>t?setImmediate:r?((o,s)=>(gr.addEventListener("message",({source:l,data:a})=>{l===gr&&a===o&&s.length&&s.shift()()},!1),l=>{s.push(l),gr.postMessage(o,"*")}))(`axios@${Math.random()}`,[]):o=>setTimeout(o))(typeof setImmediate=="function",gt(gr.postMessage)),zv=typeof queueMicrotask<"u"?queueMicrotask.bind(gr):typeof process<"u"&&process.nextTick||om,Ov=t=>t!=null&&gt(t[Al]),Q={isArray:go,isArrayBuffer:Jp,isBuffer:Zy,isFormData:lv,isArrayBufferView:Jy,isString:ev,isNumber:em,isBoolean:tv,isObject:zl,isPlainObject:al,isReadableStream:av,isRequest:cv,isResponse:fv,isHeaders:dv,isUndefined:vi,isDate:nv,isFile:rv,isBlob:ov,isRegExp:Nv,isFunction:gt,isStream:sv,isURLSearchParams:uv,isTypedArray:xv,isFileList:iv,forEach:Ri,merge:cc,extend:pv,trim:hv,stripBOM:mv,inherits:gv,toFlatObject:yv,kindOf:Ll,kindOfTest:Vt,endsWith:vv,toArray:wv,forEachEntry:Sv,matchAll:Ev,isHTMLForm:_v,hasOwnProperty:Ph,hasOwnProp:Ph,reduceDescriptors:rm,freezeMethods:Cv,toObjectSet:Tv,toCamelCase:kv,noop:Pv,toFiniteNumber:Rv,findKey:tm,global:gr,isContextDefined:nm,isSpecCompliantForm:Mv,toJSONObject:Av,isAsyncFn:Lv,isThenable:Iv,setImmediate:om,asap:zv,isIterable:Ov};function ke(t,r,o,s,l){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",r&&(this.code=r),o&&(this.config=o),s&&(this.request=s),l&&(this.response=l,this.status=l.status?l.status:null)}Q.inherits(ke,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Q.toJSONObject(this.config),code:this.code,status:this.status}}});const im=ke.prototype,sm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{sm[t]={value:t}});Object.defineProperties(ke,sm);Object.defineProperty(im,"isAxiosError",{value:!0});ke.from=(t,r,o,s,l,a)=>{const c=Object.create(im);return Q.toFlatObject(t,c,function(p){return p!==Error.prototype},d=>d!=="isAxiosError"),ke.call(c,t.message,r,o,s,l),c.cause=t,c.name=t.name,a&&Object.assign(c,a),c};const Dv=null;function fc(t){return Q.isPlainObject(t)||Q.isArray(t)}function lm(t){return Q.endsWith(t,"[]")?t.slice(0,-2):t}function Rh(t,r,o){return t?t.concat(r).map(function(l,a){return l=lm(l),!o&&a?"["+l+"]":l}).join(o?".":""):r}function $v(t){return Q.isArray(t)&&!t.some(fc)}const jv=Q.toFlatObject(Q,{},null,function(r){return/^is[A-Z]/.test(r)});function Ol(t,r,o){if(!Q.isObject(t))throw new TypeError("target must be an object");r=r||new FormData,o=Q.toFlatObject(o,{metaTokens:!0,dots:!1,indexes:!1},!1,function(S,k){return!Q.isUndefined(k[S])});const s=o.metaTokens,l=o.visitor||m,a=o.dots,c=o.indexes,p=(o.Blob||typeof Blob<"u"&&Blob)&&Q.isSpecCompliantForm(r);if(!Q.isFunction(l))throw new TypeError("visitor must be a function");function g(_){if(_===null)return"";if(Q.isDate(_))return _.toISOString();if(Q.isBoolean(_))return _.toString();if(!p&&Q.isBlob(_))throw new ke("Blob is not supported. Use a Buffer instead.");return Q.isArrayBuffer(_)||Q.isTypedArray(_)?p&&typeof Blob=="function"?new Blob([_]):Buffer.from(_):_}function m(_,S,k){let C=_;if(_&&!k&&typeof _=="object"){if(Q.endsWith(S,"{}"))S=s?S:S.slice(0,-2),_=JSON.stringify(_);else if(Q.isArray(_)&&$v(_)||(Q.isFileList(_)||Q.endsWith(S,"[]"))&&(C=Q.toArray(_)))return S=lm(S),C.forEach(function(E,N){!(Q.isUndefined(E)||E===null)&&r.append(c===!0?Rh([S],N,a):c===null?S:S+"[]",g(E))}),!1}return fc(_)?!0:(r.append(Rh(k,S,a),g(_)),!1)}const v=[],y=Object.assign(jv,{defaultVisitor:m,convertValue:g,isVisitable:fc});function w(_,S){if(!Q.isUndefined(_)){if(v.indexOf(_)!==-1)throw Error("Circular reference detected in "+S.join("."));v.push(_),Q.forEach(_,function(C,z){(!(Q.isUndefined(C)||C===null)&&l.call(r,C,Q.isString(z)?z.trim():z,S,y))===!0&&w(C,S?S.concat(z):[z])}),v.pop()}}if(!Q.isObject(t))throw new TypeError("data must be an object");return w(t),r}function Mh(t){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(s){return r[s]})}function Ac(t,r){this._pairs=[],t&&Ol(t,this,r)}const um=Ac.prototype;um.append=function(r,o){this._pairs.push([r,o])};um.toString=function(r){const o=r?function(s){return r.call(this,s,Mh)}:Mh;return this._pairs.map(function(l){return o(l[0])+"="+o(l[1])},"").join("&")};function Fv(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function am(t,r,o){if(!r)return t;const s=o&&o.encode||Fv;Q.isFunction(o)&&(o={serialize:o});const l=o&&o.serialize;let a;if(l?a=l(r,o):a=Q.isURLSearchParams(r)?r.toString():new Ac(r,o).toString(s),a){const c=t.indexOf("#");c!==-1&&(t=t.slice(0,c)),t+=(t.indexOf("?")===-1?"?":"&")+a}return t}class Ah{constructor(){this.handlers=[]}use(r,o,s){return this.handlers.push({fulfilled:r,rejected:o,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){Q.forEach(this.handlers,function(s){s!==null&&r(s)})}}const cm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},bv=typeof URLSearchParams<"u"?URLSearchParams:Ac,Hv=typeof FormData<"u"?FormData:null,Bv=typeof Blob<"u"?Blob:null,Vv={isBrowser:!0,classes:{URLSearchParams:bv,FormData:Hv,Blob:Bv},protocols:["http","https","file","blob","url","data"]},Lc=typeof window<"u"&&typeof document<"u",dc=typeof navigator=="object"&&navigator||void 0,Uv=Lc&&(!dc||["ReactNative","NativeScript","NS"].indexOf(dc.product)<0),Wv=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Xv=Lc&&window.location.href||"http://localhost",Yv=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Lc,hasStandardBrowserEnv:Uv,hasStandardBrowserWebWorkerEnv:Wv,navigator:dc,origin:Xv},Symbol.toStringTag,{value:"Module"})),st={...Yv,...Vv};function qv(t,r){return Ol(t,new st.classes.URLSearchParams,Object.assign({visitor:function(o,s,l,a){return st.isNode&&Q.isBuffer(o)?(this.append(s,o.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)}},r))}function Qv(t){return Q.matchAll(/\w+|\[(\w*)]/g,t).map(r=>r[0]==="[]"?"":r[1]||r[0])}function Kv(t){const r={},o=Object.keys(t);let s;const l=o.length;let a;for(s=0;s<l;s++)a=o[s],r[a]=t[a];return r}function fm(t){function r(o,s,l,a){let c=o[a++];if(c==="__proto__")return!0;const d=Number.isFinite(+c),p=a>=o.length;return c=!c&&Q.isArray(l)?l.length:c,p?(Q.hasOwnProp(l,c)?l[c]=[l[c],s]:l[c]=s,!d):((!l[c]||!Q.isObject(l[c]))&&(l[c]=[]),r(o,s,l[c],a)&&Q.isArray(l[c])&&(l[c]=Kv(l[c])),!d)}if(Q.isFormData(t)&&Q.isFunction(t.entries)){const o={};return Q.forEachEntry(t,(s,l)=>{r(Qv(s),l,o,0)}),o}return null}function Gv(t,r,o){if(Q.isString(t))try{return(r||JSON.parse)(t),Q.trim(t)}catch(s){if(s.name!=="SyntaxError")throw s}return(o||JSON.stringify)(t)}const Mi={transitional:cm,adapter:["xhr","http","fetch"],transformRequest:[function(r,o){const s=o.getContentType()||"",l=s.indexOf("application/json")>-1,a=Q.isObject(r);if(a&&Q.isHTMLForm(r)&&(r=new FormData(r)),Q.isFormData(r))return l?JSON.stringify(fm(r)):r;if(Q.isArrayBuffer(r)||Q.isBuffer(r)||Q.isStream(r)||Q.isFile(r)||Q.isBlob(r)||Q.isReadableStream(r))return r;if(Q.isArrayBufferView(r))return r.buffer;if(Q.isURLSearchParams(r))return o.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let d;if(a){if(s.indexOf("application/x-www-form-urlencoded")>-1)return qv(r,this.formSerializer).toString();if((d=Q.isFileList(r))||s.indexOf("multipart/form-data")>-1){const p=this.env&&this.env.FormData;return Ol(d?{"files[]":r}:r,p&&new p,this.formSerializer)}}return a||l?(o.setContentType("application/json",!1),Gv(r)):r}],transformResponse:[function(r){const o=this.transitional||Mi.transitional,s=o&&o.forcedJSONParsing,l=this.responseType==="json";if(Q.isResponse(r)||Q.isReadableStream(r))return r;if(r&&Q.isString(r)&&(s&&!this.responseType||l)){const c=!(o&&o.silentJSONParsing)&&l;try{return JSON.parse(r)}catch(d){if(c)throw d.name==="SyntaxError"?ke.from(d,ke.ERR_BAD_RESPONSE,this,null,this.response):d}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:st.classes.FormData,Blob:st.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Q.forEach(["delete","get","head","post","put","patch"],t=>{Mi.headers[t]={}});const Zv=Q.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Jv=t=>{const r={};let o,s,l;return t&&t.split(`
`).forEach(function(c){l=c.indexOf(":"),o=c.substring(0,l).trim().toLowerCase(),s=c.substring(l+1).trim(),!(!o||r[o]&&Zv[o])&&(o==="set-cookie"?r[o]?r[o].push(s):r[o]=[s]:r[o]=r[o]?r[o]+", "+s:s)}),r},Lh=Symbol("internals");function fi(t){return t&&String(t).trim().toLowerCase()}function cl(t){return t===!1||t==null?t:Q.isArray(t)?t.map(cl):String(t)}function ew(t){const r=Object.create(null),o=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=o.exec(t);)r[s[1]]=s[2];return r}const tw=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function qa(t,r,o,s,l){if(Q.isFunction(s))return s.call(this,r,o);if(l&&(r=o),!!Q.isString(r)){if(Q.isString(s))return r.indexOf(s)!==-1;if(Q.isRegExp(s))return s.test(r)}}function nw(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,o,s)=>o.toUpperCase()+s)}function rw(t,r){const o=Q.toCamelCase(" "+r);["get","set","has"].forEach(s=>{Object.defineProperty(t,s+o,{value:function(l,a,c){return this[s].call(this,r,l,a,c)},configurable:!0})})}let yt=class{constructor(r){r&&this.set(r)}set(r,o,s){const l=this;function a(d,p,g){const m=fi(p);if(!m)throw new Error("header name must be a non-empty string");const v=Q.findKey(l,m);(!v||l[v]===void 0||g===!0||g===void 0&&l[v]!==!1)&&(l[v||p]=cl(d))}const c=(d,p)=>Q.forEach(d,(g,m)=>a(g,m,p));if(Q.isPlainObject(r)||r instanceof this.constructor)c(r,o);else if(Q.isString(r)&&(r=r.trim())&&!tw(r))c(Jv(r),o);else if(Q.isObject(r)&&Q.isIterable(r)){let d={},p,g;for(const m of r){if(!Q.isArray(m))throw TypeError("Object iterator must return a key-value pair");d[g=m[0]]=(p=d[g])?Q.isArray(p)?[...p,m[1]]:[p,m[1]]:m[1]}c(d,o)}else r!=null&&a(o,r,s);return this}get(r,o){if(r=fi(r),r){const s=Q.findKey(this,r);if(s){const l=this[s];if(!o)return l;if(o===!0)return ew(l);if(Q.isFunction(o))return o.call(this,l,s);if(Q.isRegExp(o))return o.exec(l);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,o){if(r=fi(r),r){const s=Q.findKey(this,r);return!!(s&&this[s]!==void 0&&(!o||qa(this,this[s],s,o)))}return!1}delete(r,o){const s=this;let l=!1;function a(c){if(c=fi(c),c){const d=Q.findKey(s,c);d&&(!o||qa(s,s[d],d,o))&&(delete s[d],l=!0)}}return Q.isArray(r)?r.forEach(a):a(r),l}clear(r){const o=Object.keys(this);let s=o.length,l=!1;for(;s--;){const a=o[s];(!r||qa(this,this[a],a,r,!0))&&(delete this[a],l=!0)}return l}normalize(r){const o=this,s={};return Q.forEach(this,(l,a)=>{const c=Q.findKey(s,a);if(c){o[c]=cl(l),delete o[a];return}const d=r?nw(a):String(a).trim();d!==a&&delete o[a],o[d]=cl(l),s[d]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const o=Object.create(null);return Q.forEach(this,(s,l)=>{s!=null&&s!==!1&&(o[l]=r&&Q.isArray(s)?s.join(", "):s)}),o}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,o])=>r+": "+o).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...o){const s=new this(r);return o.forEach(l=>s.set(l)),s}static accessor(r){const s=(this[Lh]=this[Lh]={accessors:{}}).accessors,l=this.prototype;function a(c){const d=fi(c);s[d]||(rw(l,c),s[d]=!0)}return Q.isArray(r)?r.forEach(a):a(r),this}};yt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);Q.reduceDescriptors(yt.prototype,({value:t},r)=>{let o=r[0].toUpperCase()+r.slice(1);return{get:()=>t,set(s){this[o]=s}}});Q.freezeMethods(yt);function Qa(t,r){const o=this||Mi,s=r||o,l=yt.from(s.headers);let a=s.data;return Q.forEach(t,function(d){a=d.call(o,a,l.normalize(),r?r.status:void 0)}),l.normalize(),a}function dm(t){return!!(t&&t.__CANCEL__)}function yo(t,r,o){ke.call(this,t??"canceled",ke.ERR_CANCELED,r,o),this.name="CanceledError"}Q.inherits(yo,ke,{__CANCEL__:!0});function hm(t,r,o){const s=o.config.validateStatus;!o.status||!s||s(o.status)?t(o):r(new ke("Request failed with status code "+o.status,[ke.ERR_BAD_REQUEST,ke.ERR_BAD_RESPONSE][Math.floor(o.status/100)-4],o.config,o.request,o))}function ow(t){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return r&&r[1]||""}function iw(t,r){t=t||10;const o=new Array(t),s=new Array(t);let l=0,a=0,c;return r=r!==void 0?r:1e3,function(p){const g=Date.now(),m=s[a];c||(c=g),o[l]=p,s[l]=g;let v=a,y=0;for(;v!==l;)y+=o[v++],v=v%t;if(l=(l+1)%t,l===a&&(a=(a+1)%t),g-c<r)return;const w=m&&g-m;return w?Math.round(y*1e3/w):void 0}}function sw(t,r){let o=0,s=1e3/r,l,a;const c=(g,m=Date.now())=>{o=m,l=null,a&&(clearTimeout(a),a=null),t.apply(null,g)};return[(...g)=>{const m=Date.now(),v=m-o;v>=s?c(g,m):(l=g,a||(a=setTimeout(()=>{a=null,c(l)},s-v)))},()=>l&&c(l)]}const yl=(t,r,o=3)=>{let s=0;const l=iw(50,250);return sw(a=>{const c=a.loaded,d=a.lengthComputable?a.total:void 0,p=c-s,g=l(p),m=c<=d;s=c;const v={loaded:c,total:d,progress:d?c/d:void 0,bytes:p,rate:g||void 0,estimated:g&&d&&m?(d-c)/g:void 0,event:a,lengthComputable:d!=null,[r?"download":"upload"]:!0};t(v)},o)},Ih=(t,r)=>{const o=t!=null;return[s=>r[0]({lengthComputable:o,total:t,loaded:s}),r[1]]},zh=t=>(...r)=>Q.asap(()=>t(...r)),lw=st.hasStandardBrowserEnv?((t,r)=>o=>(o=new URL(o,st.origin),t.protocol===o.protocol&&t.host===o.host&&(r||t.port===o.port)))(new URL(st.origin),st.navigator&&/(msie|trident)/i.test(st.navigator.userAgent)):()=>!0,uw=st.hasStandardBrowserEnv?{write(t,r,o,s,l,a){const c=[t+"="+encodeURIComponent(r)];Q.isNumber(o)&&c.push("expires="+new Date(o).toGMTString()),Q.isString(s)&&c.push("path="+s),Q.isString(l)&&c.push("domain="+l),a===!0&&c.push("secure"),document.cookie=c.join("; ")},read(t){const r=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function aw(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function cw(t,r){return r?t.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):t}function pm(t,r,o){let s=!aw(r);return t&&(s||o==!1)?cw(t,r):r}const Oh=t=>t instanceof yt?{...t}:t;function Sr(t,r){r=r||{};const o={};function s(g,m,v,y){return Q.isPlainObject(g)&&Q.isPlainObject(m)?Q.merge.call({caseless:y},g,m):Q.isPlainObject(m)?Q.merge({},m):Q.isArray(m)?m.slice():m}function l(g,m,v,y){if(Q.isUndefined(m)){if(!Q.isUndefined(g))return s(void 0,g,v,y)}else return s(g,m,v,y)}function a(g,m){if(!Q.isUndefined(m))return s(void 0,m)}function c(g,m){if(Q.isUndefined(m)){if(!Q.isUndefined(g))return s(void 0,g)}else return s(void 0,m)}function d(g,m,v){if(v in r)return s(g,m);if(v in t)return s(void 0,g)}const p={url:a,method:a,data:a,baseURL:c,transformRequest:c,transformResponse:c,paramsSerializer:c,timeout:c,timeoutMessage:c,withCredentials:c,withXSRFToken:c,adapter:c,responseType:c,xsrfCookieName:c,xsrfHeaderName:c,onUploadProgress:c,onDownloadProgress:c,decompress:c,maxContentLength:c,maxBodyLength:c,beforeRedirect:c,transport:c,httpAgent:c,httpsAgent:c,cancelToken:c,socketPath:c,responseEncoding:c,validateStatus:d,headers:(g,m,v)=>l(Oh(g),Oh(m),v,!0)};return Q.forEach(Object.keys(Object.assign({},t,r)),function(m){const v=p[m]||l,y=v(t[m],r[m],m);Q.isUndefined(y)&&v!==d||(o[m]=y)}),o}const mm=t=>{const r=Sr({},t);let{data:o,withXSRFToken:s,xsrfHeaderName:l,xsrfCookieName:a,headers:c,auth:d}=r;r.headers=c=yt.from(c),r.url=am(pm(r.baseURL,r.url,r.allowAbsoluteUrls),t.params,t.paramsSerializer),d&&c.set("Authorization","Basic "+btoa((d.username||"")+":"+(d.password?unescape(encodeURIComponent(d.password)):"")));let p;if(Q.isFormData(o)){if(st.hasStandardBrowserEnv||st.hasStandardBrowserWebWorkerEnv)c.setContentType(void 0);else if((p=c.getContentType())!==!1){const[g,...m]=p?p.split(";").map(v=>v.trim()).filter(Boolean):[];c.setContentType([g||"multipart/form-data",...m].join("; "))}}if(st.hasStandardBrowserEnv&&(s&&Q.isFunction(s)&&(s=s(r)),s||s!==!1&&lw(r.url))){const g=l&&a&&uw.read(a);g&&c.set(l,g)}return r},fw=typeof XMLHttpRequest<"u",dw=fw&&function(t){return new Promise(function(o,s){const l=mm(t);let a=l.data;const c=yt.from(l.headers).normalize();let{responseType:d,onUploadProgress:p,onDownloadProgress:g}=l,m,v,y,w,_;function S(){w&&w(),_&&_(),l.cancelToken&&l.cancelToken.unsubscribe(m),l.signal&&l.signal.removeEventListener("abort",m)}let k=new XMLHttpRequest;k.open(l.method.toUpperCase(),l.url,!0),k.timeout=l.timeout;function C(){if(!k)return;const E=yt.from("getAllResponseHeaders"in k&&k.getAllResponseHeaders()),A={data:!d||d==="text"||d==="json"?k.responseText:k.response,status:k.status,statusText:k.statusText,headers:E,config:t,request:k};hm(function(F){o(F),S()},function(F){s(F),S()},A),k=null}"onloadend"in k?k.onloadend=C:k.onreadystatechange=function(){!k||k.readyState!==4||k.status===0&&!(k.responseURL&&k.responseURL.indexOf("file:")===0)||setTimeout(C)},k.onabort=function(){k&&(s(new ke("Request aborted",ke.ECONNABORTED,t,k)),k=null)},k.onerror=function(){s(new ke("Network Error",ke.ERR_NETWORK,t,k)),k=null},k.ontimeout=function(){let N=l.timeout?"timeout of "+l.timeout+"ms exceeded":"timeout exceeded";const A=l.transitional||cm;l.timeoutErrorMessage&&(N=l.timeoutErrorMessage),s(new ke(N,A.clarifyTimeoutError?ke.ETIMEDOUT:ke.ECONNABORTED,t,k)),k=null},a===void 0&&c.setContentType(null),"setRequestHeader"in k&&Q.forEach(c.toJSON(),function(N,A){k.setRequestHeader(A,N)}),Q.isUndefined(l.withCredentials)||(k.withCredentials=!!l.withCredentials),d&&d!=="json"&&(k.responseType=l.responseType),g&&([y,_]=yl(g,!0),k.addEventListener("progress",y)),p&&k.upload&&([v,w]=yl(p),k.upload.addEventListener("progress",v),k.upload.addEventListener("loadend",w)),(l.cancelToken||l.signal)&&(m=E=>{k&&(s(!E||E.type?new yo(null,t,k):E),k.abort(),k=null)},l.cancelToken&&l.cancelToken.subscribe(m),l.signal&&(l.signal.aborted?m():l.signal.addEventListener("abort",m)));const z=ow(l.url);if(z&&st.protocols.indexOf(z)===-1){s(new ke("Unsupported protocol "+z+":",ke.ERR_BAD_REQUEST,t));return}k.send(a||null)})},hw=(t,r)=>{const{length:o}=t=t?t.filter(Boolean):[];if(r||o){let s=new AbortController,l;const a=function(g){if(!l){l=!0,d();const m=g instanceof Error?g:this.reason;s.abort(m instanceof ke?m:new yo(m instanceof Error?m.message:m))}};let c=r&&setTimeout(()=>{c=null,a(new ke(`timeout ${r} of ms exceeded`,ke.ETIMEDOUT))},r);const d=()=>{t&&(c&&clearTimeout(c),c=null,t.forEach(g=>{g.unsubscribe?g.unsubscribe(a):g.removeEventListener("abort",a)}),t=null)};t.forEach(g=>g.addEventListener("abort",a));const{signal:p}=s;return p.unsubscribe=()=>Q.asap(d),p}},pw=function*(t,r){let o=t.byteLength;if(o<r){yield t;return}let s=0,l;for(;s<o;)l=s+r,yield t.slice(s,l),s=l},mw=async function*(t,r){for await(const o of gw(t))yield*pw(o,r)},gw=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const r=t.getReader();try{for(;;){const{done:o,value:s}=await r.read();if(o)break;yield s}}finally{await r.cancel()}},Dh=(t,r,o,s)=>{const l=mw(t,r);let a=0,c,d=p=>{c||(c=!0,s&&s(p))};return new ReadableStream({async pull(p){try{const{done:g,value:m}=await l.next();if(g){d(),p.close();return}let v=m.byteLength;if(o){let y=a+=v;o(y)}p.enqueue(new Uint8Array(m))}catch(g){throw d(g),g}},cancel(p){return d(p),l.return()}},{highWaterMark:2})},Dl=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",gm=Dl&&typeof ReadableStream=="function",yw=Dl&&(typeof TextEncoder=="function"?(t=>r=>t.encode(r))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),ym=(t,...r)=>{try{return!!t(...r)}catch{return!1}},vw=gm&&ym(()=>{let t=!1;const r=new Request(st.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!r}),$h=64*1024,hc=gm&&ym(()=>Q.isReadableStream(new Response("").body)),vl={stream:hc&&(t=>t.body)};Dl&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!vl[r]&&(vl[r]=Q.isFunction(t[r])?o=>o[r]():(o,s)=>{throw new ke(`Response type '${r}' is not supported`,ke.ERR_NOT_SUPPORT,s)})})})(new Response);const ww=async t=>{if(t==null)return 0;if(Q.isBlob(t))return t.size;if(Q.isSpecCompliantForm(t))return(await new Request(st.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(Q.isArrayBufferView(t)||Q.isArrayBuffer(t))return t.byteLength;if(Q.isURLSearchParams(t)&&(t=t+""),Q.isString(t))return(await yw(t)).byteLength},xw=async(t,r)=>{const o=Q.toFiniteNumber(t.getContentLength());return o??ww(r)},Sw=Dl&&(async t=>{let{url:r,method:o,data:s,signal:l,cancelToken:a,timeout:c,onDownloadProgress:d,onUploadProgress:p,responseType:g,headers:m,withCredentials:v="same-origin",fetchOptions:y}=mm(t);g=g?(g+"").toLowerCase():"text";let w=hw([l,a&&a.toAbortSignal()],c),_;const S=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let k;try{if(p&&vw&&o!=="get"&&o!=="head"&&(k=await xw(m,s))!==0){let A=new Request(r,{method:"POST",body:s,duplex:"half"}),$;if(Q.isFormData(s)&&($=A.headers.get("content-type"))&&m.setContentType($),A.body){const[F,X]=Ih(k,yl(zh(p)));s=Dh(A.body,$h,F,X)}}Q.isString(v)||(v=v?"include":"omit");const C="credentials"in Request.prototype;_=new Request(r,{...y,signal:w,method:o.toUpperCase(),headers:m.normalize().toJSON(),body:s,duplex:"half",credentials:C?v:void 0});let z=await fetch(_,y);const E=hc&&(g==="stream"||g==="response");if(hc&&(d||E&&S)){const A={};["status","statusText","headers"].forEach(K=>{A[K]=z[K]});const $=Q.toFiniteNumber(z.headers.get("content-length")),[F,X]=d&&Ih($,yl(zh(d),!0))||[];z=new Response(Dh(z.body,$h,F,()=>{X&&X(),S&&S()}),A)}g=g||"text";let N=await vl[Q.findKey(vl,g)||"text"](z,t);return!E&&S&&S(),await new Promise((A,$)=>{hm(A,$,{data:N,headers:yt.from(z.headers),status:z.status,statusText:z.statusText,config:t,request:_})})}catch(C){throw S&&S(),C&&C.name==="TypeError"&&/Load failed|fetch/i.test(C.message)?Object.assign(new ke("Network Error",ke.ERR_NETWORK,t,_),{cause:C.cause||C}):ke.from(C,C&&C.code,t,_)}}),pc={http:Dv,xhr:dw,fetch:Sw};Q.forEach(pc,(t,r)=>{if(t){try{Object.defineProperty(t,"name",{value:r})}catch{}Object.defineProperty(t,"adapterName",{value:r})}});const jh=t=>`- ${t}`,Ew=t=>Q.isFunction(t)||t===null||t===!1,vm={getAdapter:t=>{t=Q.isArray(t)?t:[t];const{length:r}=t;let o,s;const l={};for(let a=0;a<r;a++){o=t[a];let c;if(s=o,!Ew(o)&&(s=pc[(c=String(o)).toLowerCase()],s===void 0))throw new ke(`Unknown adapter '${c}'`);if(s)break;l[c||"#"+a]=s}if(!s){const a=Object.entries(l).map(([d,p])=>`adapter ${d} `+(p===!1?"is not supported by the environment":"is not available in the build"));let c=r?a.length>1?`since :
`+a.map(jh).join(`
`):" "+jh(a[0]):"as no adapter specified";throw new ke("There is no suitable adapter to dispatch the request "+c,"ERR_NOT_SUPPORT")}return s},adapters:pc};function Ka(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new yo(null,t)}function Fh(t){return Ka(t),t.headers=yt.from(t.headers),t.data=Qa.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),vm.getAdapter(t.adapter||Mi.adapter)(t).then(function(s){return Ka(t),s.data=Qa.call(t,t.transformResponse,s),s.headers=yt.from(s.headers),s},function(s){return dm(s)||(Ka(t),s&&s.response&&(s.response.data=Qa.call(t,t.transformResponse,s.response),s.response.headers=yt.from(s.response.headers))),Promise.reject(s)})}const wm="1.10.0",$l={};["object","boolean","number","function","string","symbol"].forEach((t,r)=>{$l[t]=function(s){return typeof s===t||"a"+(r<1?"n ":" ")+t}});const bh={};$l.transitional=function(r,o,s){function l(a,c){return"[Axios v"+wm+"] Transitional option '"+a+"'"+c+(s?". "+s:"")}return(a,c,d)=>{if(r===!1)throw new ke(l(c," has been removed"+(o?" in "+o:"")),ke.ERR_DEPRECATED);return o&&!bh[c]&&(bh[c]=!0,console.warn(l(c," has been deprecated since v"+o+" and will be removed in the near future"))),r?r(a,c,d):!0}};$l.spelling=function(r){return(o,s)=>(console.warn(`${s} is likely a misspelling of ${r}`),!0)};function _w(t,r,o){if(typeof t!="object")throw new ke("options must be an object",ke.ERR_BAD_OPTION_VALUE);const s=Object.keys(t);let l=s.length;for(;l-- >0;){const a=s[l],c=r[a];if(c){const d=t[a],p=d===void 0||c(d,a,t);if(p!==!0)throw new ke("option "+a+" must be "+p,ke.ERR_BAD_OPTION_VALUE);continue}if(o!==!0)throw new ke("Unknown option "+a,ke.ERR_BAD_OPTION)}}const fl={assertOptions:_w,validators:$l},Zt=fl.validators;let vr=class{constructor(r){this.defaults=r||{},this.interceptors={request:new Ah,response:new Ah}}async request(r,o){try{return await this._request(r,o)}catch(s){if(s instanceof Error){let l={};Error.captureStackTrace?Error.captureStackTrace(l):l=new Error;const a=l.stack?l.stack.replace(/^.+\n/,""):"";try{s.stack?a&&!String(s.stack).endsWith(a.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+a):s.stack=a}catch{}}throw s}}_request(r,o){typeof r=="string"?(o=o||{},o.url=r):o=r||{},o=Sr(this.defaults,o);const{transitional:s,paramsSerializer:l,headers:a}=o;s!==void 0&&fl.assertOptions(s,{silentJSONParsing:Zt.transitional(Zt.boolean),forcedJSONParsing:Zt.transitional(Zt.boolean),clarifyTimeoutError:Zt.transitional(Zt.boolean)},!1),l!=null&&(Q.isFunction(l)?o.paramsSerializer={serialize:l}:fl.assertOptions(l,{encode:Zt.function,serialize:Zt.function},!0)),o.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?o.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:o.allowAbsoluteUrls=!0),fl.assertOptions(o,{baseUrl:Zt.spelling("baseURL"),withXsrfToken:Zt.spelling("withXSRFToken")},!0),o.method=(o.method||this.defaults.method||"get").toLowerCase();let c=a&&Q.merge(a.common,a[o.method]);a&&Q.forEach(["delete","get","head","post","put","patch","common"],_=>{delete a[_]}),o.headers=yt.concat(c,a);const d=[];let p=!0;this.interceptors.request.forEach(function(S){typeof S.runWhen=="function"&&S.runWhen(o)===!1||(p=p&&S.synchronous,d.unshift(S.fulfilled,S.rejected))});const g=[];this.interceptors.response.forEach(function(S){g.push(S.fulfilled,S.rejected)});let m,v=0,y;if(!p){const _=[Fh.bind(this),void 0];for(_.unshift.apply(_,d),_.push.apply(_,g),y=_.length,m=Promise.resolve(o);v<y;)m=m.then(_[v++],_[v++]);return m}y=d.length;let w=o;for(v=0;v<y;){const _=d[v++],S=d[v++];try{w=_(w)}catch(k){S.call(this,k);break}}try{m=Fh.call(this,w)}catch(_){return Promise.reject(_)}for(v=0,y=g.length;v<y;)m=m.then(g[v++],g[v++]);return m}getUri(r){r=Sr(this.defaults,r);const o=pm(r.baseURL,r.url,r.allowAbsoluteUrls);return am(o,r.params,r.paramsSerializer)}};Q.forEach(["delete","get","head","options"],function(r){vr.prototype[r]=function(o,s){return this.request(Sr(s||{},{method:r,url:o,data:(s||{}).data}))}});Q.forEach(["post","put","patch"],function(r){function o(s){return function(a,c,d){return this.request(Sr(d||{},{method:r,headers:s?{"Content-Type":"multipart/form-data"}:{},url:a,data:c}))}}vr.prototype[r]=o(),vr.prototype[r+"Form"]=o(!0)});let kw=class xm{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let o;this.promise=new Promise(function(a){o=a});const s=this;this.promise.then(l=>{if(!s._listeners)return;let a=s._listeners.length;for(;a-- >0;)s._listeners[a](l);s._listeners=null}),this.promise.then=l=>{let a;const c=new Promise(d=>{s.subscribe(d),a=d}).then(l);return c.cancel=function(){s.unsubscribe(a)},c},r(function(a,c,d){s.reason||(s.reason=new yo(a,c,d),o(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const o=this._listeners.indexOf(r);o!==-1&&this._listeners.splice(o,1)}toAbortSignal(){const r=new AbortController,o=s=>{r.abort(s)};return this.subscribe(o),r.signal.unsubscribe=()=>this.unsubscribe(o),r.signal}static source(){let r;return{token:new xm(function(l){r=l}),cancel:r}}};function Nw(t){return function(o){return t.apply(null,o)}}function Cw(t){return Q.isObject(t)&&t.isAxiosError===!0}const mc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(mc).forEach(([t,r])=>{mc[r]=t});function Sm(t){const r=new vr(t),o=Gp(vr.prototype.request,r);return Q.extend(o,vr.prototype,r,{allOwnKeys:!0}),Q.extend(o,r,null,{allOwnKeys:!0}),o.create=function(l){return Sm(Sr(t,l))},o}const We=Sm(Mi);We.Axios=vr;We.CanceledError=yo;We.CancelToken=kw;We.isCancel=dm;We.VERSION=wm;We.toFormData=Ol;We.AxiosError=ke;We.Cancel=We.CanceledError;We.all=function(r){return Promise.all(r)};We.spread=Nw;We.isAxiosError=Cw;We.mergeConfig=Sr;We.AxiosHeaders=yt;We.formToJSON=t=>fm(Q.isHTMLForm(t)?new FormData(t):t);We.getAdapter=vm.getAdapter;We.HttpStatusCode=mc;We.default=We;const{Axios:UN,AxiosError:WN,CanceledError:XN,isCancel:YN,CancelToken:qN,VERSION:QN,all:KN,Cancel:GN,isAxiosError:ZN,spread:JN,toFormData:e2,AxiosHeaders:t2,HttpStatusCode:n2,formToJSON:r2,getAdapter:o2,mergeConfig:i2}=We,Tw="http://localhost:5053/api",_t=We.create({baseURL:Tw,headers:{"Content-Type":"application/json"}}),no={getWorkflows:async(t="default")=>(await _t.get(`/workflow?userId=${t}`)).data,getWorkflow:async t=>(await _t.get(`/workflow/${t}`)).data,createWorkflow:async t=>(await _t.post("/workflow",t)).data,updateWorkflow:async(t,r)=>(await _t.put(`/workflow/${t}`,r)).data,deleteWorkflow:async t=>{await _t.delete(`/workflow/${t}`)},toggleWorkflow:async(t,r)=>{await _t.post(`/workflow/${t}/toggle`,r)},executeWorkflow:async(t,r)=>(await _t.post(`/workflow/${t}/execute`,r||{})).data,getExecutionHistory:async(t,r=1,o=50)=>(await _t.get(`/workflow/${t}/executions?page=${r}&pageSize=${o}`)).data,getExecution:async t=>(await _t.get(`/workflow/executions/${t}`)).data,stopExecution:async t=>{await _t.post(`/workflow/executions/${t}/stop`)}},Hh={getNodes:async()=>(await _t.get("/nodes")).data,getNode:async t=>(await _t.get(`/nodes/${t}`)).data,getCategories:async()=>(await _t.get("/nodes/categories")).data},Pw=({onSelectWorkflow:t,onCreateWorkflow:r})=>{const[o,s]=q.useState([]),[l,a]=q.useState(!0),[c,d]=q.useState(null);q.useEffect(()=>{p()},[]);const p=async()=>{try{a(!0);const y=await no.getWorkflows();s(y),d(null)}catch(y){d("Failed to fetch workflows"),console.error("Error fetching workflows:",y)}finally{a(!1)}},g=async y=>{try{await no.toggleWorkflow(y.id,!y.isActive),await p()}catch(w){console.error("Error toggling workflow:",w)}},m=async y=>{if(window.confirm(`Are you sure you want to delete "${y.name}"?`))try{await no.deleteWorkflow(y.id),await p()}catch(w){console.error("Error deleting workflow:",w)}},v=async y=>{try{await no.executeWorkflow(y.id),alert("Workflow executed successfully!")}catch(w){console.error("Error executing workflow:",w),alert("Failed to execute workflow")}};return l?M.jsxs("div",{className:"flex items-center justify-center h-64",children:[M.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),M.jsx("span",{className:"ml-2",children:"Loading workflows..."})]}):c?M.jsxs("div",{className:"text-center py-8",children:[M.jsx("p",{className:"text-red-600",children:c}),M.jsx("button",{onClick:p,className:"mt-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600",children:"Retry"})]}):M.jsxs("div",{className:"p-6",children:[M.jsxs("div",{className:"flex justify-between items-center mb-6",children:[M.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"Workflows"}),M.jsx("button",{onClick:r,className:"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors",children:"Create Workflow"})]}),o.length===0?M.jsxs("div",{className:"text-center py-12",children:[M.jsx("div",{className:"text-6xl mb-4",children:"🔧"}),M.jsx("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"No workflows yet"}),M.jsx("p",{className:"text-gray-600 mb-4",children:"Create your first workflow to get started"}),M.jsx("button",{onClick:r,className:"px-6 py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors",children:"Create Your First Workflow"})]}):M.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.map(y=>M.jsx("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow",children:M.jsxs("div",{className:"p-6",children:[M.jsxs("div",{className:"flex items-start justify-between mb-4",children:[M.jsxs("div",{className:"flex-1",children:[M.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-1",children:y.name}),M.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:y.description||"No description"})]}),M.jsx("div",{className:"flex items-center gap-2",children:M.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${y.isActive?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700"}`,children:y.isActive?"Active":"Inactive"})})]}),M.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[M.jsxs("span",{children:[y.nodes.length," nodes"]}),M.jsxs("span",{children:["Updated ",new Date(y.updatedAt).toLocaleDateString()]})]}),M.jsxs("div",{className:"flex gap-2",children:[M.jsx("button",{onClick:()=>t(y),className:"flex-1 px-3 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors",children:"Edit"}),M.jsx("button",{onClick:()=>v(y),className:"px-3 py-2 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors",disabled:!y.isActive,children:"Run"}),M.jsx("button",{onClick:()=>g(y),className:`px-3 py-2 text-sm rounded-md transition-colors ${y.isActive?"bg-yellow-500 text-white hover:bg-yellow-600":"bg-gray-500 text-white hover:bg-gray-600"}`,children:y.isActive?"Pause":"Activate"}),M.jsx("button",{onClick:()=>m(y),className:"px-3 py-2 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors",children:"Delete"})]})]})},y.id))})]})};function Ye(t){if(typeof t=="string"||typeof t=="number")return""+t;let r="";if(Array.isArray(t))for(let o=0,s;o<t.length;o++)(s=Ye(t[o]))!==""&&(r+=(r&&" ")+s);else for(let o in t)t[o]&&(r+=(r&&" ")+o);return r}var Rw={value:()=>{}};function jl(){for(var t=0,r=arguments.length,o={},s;t<r;++t){if(!(s=arguments[t]+"")||s in o||/[\s.]/.test(s))throw new Error("illegal type: "+s);o[s]=[]}return new dl(o)}function dl(t){this._=t}function Mw(t,r){return t.trim().split(/^|\s+/).map(function(o){var s="",l=o.indexOf(".");if(l>=0&&(s=o.slice(l+1),o=o.slice(0,l)),o&&!r.hasOwnProperty(o))throw new Error("unknown type: "+o);return{type:o,name:s}})}dl.prototype=jl.prototype={constructor:dl,on:function(t,r){var o=this._,s=Mw(t+"",o),l,a=-1,c=s.length;if(arguments.length<2){for(;++a<c;)if((l=(t=s[a]).type)&&(l=Aw(o[l],t.name)))return l;return}if(r!=null&&typeof r!="function")throw new Error("invalid callback: "+r);for(;++a<c;)if(l=(t=s[a]).type)o[l]=Bh(o[l],t.name,r);else if(r==null)for(l in o)o[l]=Bh(o[l],t.name,null);return this},copy:function(){var t={},r=this._;for(var o in r)t[o]=r[o].slice();return new dl(t)},call:function(t,r){if((l=arguments.length-2)>0)for(var o=new Array(l),s=0,l,a;s<l;++s)o[s]=arguments[s+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(a=this._[t],s=0,l=a.length;s<l;++s)a[s].value.apply(r,o)},apply:function(t,r,o){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var s=this._[t],l=0,a=s.length;l<a;++l)s[l].value.apply(r,o)}};function Aw(t,r){for(var o=0,s=t.length,l;o<s;++o)if((l=t[o]).name===r)return l.value}function Bh(t,r,o){for(var s=0,l=t.length;s<l;++s)if(t[s].name===r){t[s]=Rw,t=t.slice(0,s).concat(t.slice(s+1));break}return o!=null&&t.push({name:r,value:o}),t}var gc="http://www.w3.org/1999/xhtml";const Vh={svg:"http://www.w3.org/2000/svg",xhtml:gc,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Fl(t){var r=t+="",o=r.indexOf(":");return o>=0&&(r=t.slice(0,o))!=="xmlns"&&(t=t.slice(o+1)),Vh.hasOwnProperty(r)?{space:Vh[r],local:t}:t}function Lw(t){return function(){var r=this.ownerDocument,o=this.namespaceURI;return o===gc&&r.documentElement.namespaceURI===gc?r.createElement(t):r.createElementNS(o,t)}}function Iw(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function Em(t){var r=Fl(t);return(r.local?Iw:Lw)(r)}function zw(){}function Ic(t){return t==null?zw:function(){return this.querySelector(t)}}function Ow(t){typeof t!="function"&&(t=Ic(t));for(var r=this._groups,o=r.length,s=new Array(o),l=0;l<o;++l)for(var a=r[l],c=a.length,d=s[l]=new Array(c),p,g,m=0;m<c;++m)(p=a[m])&&(g=t.call(p,p.__data__,m,a))&&("__data__"in p&&(g.__data__=p.__data__),d[m]=g);return new Nt(s,this._parents)}function Dw(t){return t==null?[]:Array.isArray(t)?t:Array.from(t)}function $w(){return[]}function _m(t){return t==null?$w:function(){return this.querySelectorAll(t)}}function jw(t){return function(){return Dw(t.apply(this,arguments))}}function Fw(t){typeof t=="function"?t=jw(t):t=_m(t);for(var r=this._groups,o=r.length,s=[],l=[],a=0;a<o;++a)for(var c=r[a],d=c.length,p,g=0;g<d;++g)(p=c[g])&&(s.push(t.call(p,p.__data__,g,c)),l.push(p));return new Nt(s,l)}function km(t){return function(){return this.matches(t)}}function Nm(t){return function(r){return r.matches(t)}}var bw=Array.prototype.find;function Hw(t){return function(){return bw.call(this.children,t)}}function Bw(){return this.firstElementChild}function Vw(t){return this.select(t==null?Bw:Hw(typeof t=="function"?t:Nm(t)))}var Uw=Array.prototype.filter;function Ww(){return Array.from(this.children)}function Xw(t){return function(){return Uw.call(this.children,t)}}function Yw(t){return this.selectAll(t==null?Ww:Xw(typeof t=="function"?t:Nm(t)))}function qw(t){typeof t!="function"&&(t=km(t));for(var r=this._groups,o=r.length,s=new Array(o),l=0;l<o;++l)for(var a=r[l],c=a.length,d=s[l]=[],p,g=0;g<c;++g)(p=a[g])&&t.call(p,p.__data__,g,a)&&d.push(p);return new Nt(s,this._parents)}function Cm(t){return new Array(t.length)}function Qw(){return new Nt(this._enter||this._groups.map(Cm),this._parents)}function wl(t,r){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=r}wl.prototype={constructor:wl,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,r){return this._parent.insertBefore(t,r)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};function Kw(t){return function(){return t}}function Gw(t,r,o,s,l,a){for(var c=0,d,p=r.length,g=a.length;c<g;++c)(d=r[c])?(d.__data__=a[c],s[c]=d):o[c]=new wl(t,a[c]);for(;c<p;++c)(d=r[c])&&(l[c]=d)}function Zw(t,r,o,s,l,a,c){var d,p,g=new Map,m=r.length,v=a.length,y=new Array(m),w;for(d=0;d<m;++d)(p=r[d])&&(y[d]=w=c.call(p,p.__data__,d,r)+"",g.has(w)?l[d]=p:g.set(w,p));for(d=0;d<v;++d)w=c.call(t,a[d],d,a)+"",(p=g.get(w))?(s[d]=p,p.__data__=a[d],g.delete(w)):o[d]=new wl(t,a[d]);for(d=0;d<m;++d)(p=r[d])&&g.get(y[d])===p&&(l[d]=p)}function Jw(t){return t.__data__}function ex(t,r){if(!arguments.length)return Array.from(this,Jw);var o=r?Zw:Gw,s=this._parents,l=this._groups;typeof t!="function"&&(t=Kw(t));for(var a=l.length,c=new Array(a),d=new Array(a),p=new Array(a),g=0;g<a;++g){var m=s[g],v=l[g],y=v.length,w=tx(t.call(m,m&&m.__data__,g,s)),_=w.length,S=d[g]=new Array(_),k=c[g]=new Array(_),C=p[g]=new Array(y);o(m,v,S,k,C,w,r);for(var z=0,E=0,N,A;z<_;++z)if(N=S[z]){for(z>=E&&(E=z+1);!(A=k[E])&&++E<_;);N._next=A||null}}return c=new Nt(c,s),c._enter=d,c._exit=p,c}function tx(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function nx(){return new Nt(this._exit||this._groups.map(Cm),this._parents)}function rx(t,r,o){var s=this.enter(),l=this,a=this.exit();return typeof t=="function"?(s=t(s),s&&(s=s.selection())):s=s.append(t+""),r!=null&&(l=r(l),l&&(l=l.selection())),o==null?a.remove():o(a),s&&l?s.merge(l).order():l}function ox(t){for(var r=t.selection?t.selection():t,o=this._groups,s=r._groups,l=o.length,a=s.length,c=Math.min(l,a),d=new Array(l),p=0;p<c;++p)for(var g=o[p],m=s[p],v=g.length,y=d[p]=new Array(v),w,_=0;_<v;++_)(w=g[_]||m[_])&&(y[_]=w);for(;p<l;++p)d[p]=o[p];return new Nt(d,this._parents)}function ix(){for(var t=this._groups,r=-1,o=t.length;++r<o;)for(var s=t[r],l=s.length-1,a=s[l],c;--l>=0;)(c=s[l])&&(a&&c.compareDocumentPosition(a)^4&&a.parentNode.insertBefore(c,a),a=c);return this}function sx(t){t||(t=lx);function r(v,y){return v&&y?t(v.__data__,y.__data__):!v-!y}for(var o=this._groups,s=o.length,l=new Array(s),a=0;a<s;++a){for(var c=o[a],d=c.length,p=l[a]=new Array(d),g,m=0;m<d;++m)(g=c[m])&&(p[m]=g);p.sort(r)}return new Nt(l,this._parents).order()}function lx(t,r){return t<r?-1:t>r?1:t>=r?0:NaN}function ux(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this}function ax(){return Array.from(this)}function cx(){for(var t=this._groups,r=0,o=t.length;r<o;++r)for(var s=t[r],l=0,a=s.length;l<a;++l){var c=s[l];if(c)return c}return null}function fx(){let t=0;for(const r of this)++t;return t}function dx(){return!this.node()}function hx(t){for(var r=this._groups,o=0,s=r.length;o<s;++o)for(var l=r[o],a=0,c=l.length,d;a<c;++a)(d=l[a])&&t.call(d,d.__data__,a,l);return this}function px(t){return function(){this.removeAttribute(t)}}function mx(t){return function(){this.removeAttributeNS(t.space,t.local)}}function gx(t,r){return function(){this.setAttribute(t,r)}}function yx(t,r){return function(){this.setAttributeNS(t.space,t.local,r)}}function vx(t,r){return function(){var o=r.apply(this,arguments);o==null?this.removeAttribute(t):this.setAttribute(t,o)}}function wx(t,r){return function(){var o=r.apply(this,arguments);o==null?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,o)}}function xx(t,r){var o=Fl(t);if(arguments.length<2){var s=this.node();return o.local?s.getAttributeNS(o.space,o.local):s.getAttribute(o)}return this.each((r==null?o.local?mx:px:typeof r=="function"?o.local?wx:vx:o.local?yx:gx)(o,r))}function Tm(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function Sx(t){return function(){this.style.removeProperty(t)}}function Ex(t,r,o){return function(){this.style.setProperty(t,r,o)}}function _x(t,r,o){return function(){var s=r.apply(this,arguments);s==null?this.style.removeProperty(t):this.style.setProperty(t,s,o)}}function kx(t,r,o){return arguments.length>1?this.each((r==null?Sx:typeof r=="function"?_x:Ex)(t,r,o??"")):lo(this.node(),t)}function lo(t,r){return t.style.getPropertyValue(r)||Tm(t).getComputedStyle(t,null).getPropertyValue(r)}function Nx(t){return function(){delete this[t]}}function Cx(t,r){return function(){this[t]=r}}function Tx(t,r){return function(){var o=r.apply(this,arguments);o==null?delete this[t]:this[t]=o}}function Px(t,r){return arguments.length>1?this.each((r==null?Nx:typeof r=="function"?Tx:Cx)(t,r)):this.node()[t]}function Pm(t){return t.trim().split(/^|\s+/)}function zc(t){return t.classList||new Rm(t)}function Rm(t){this._node=t,this._names=Pm(t.getAttribute("class")||"")}Rm.prototype={add:function(t){var r=this._names.indexOf(t);r<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var r=this._names.indexOf(t);r>=0&&(this._names.splice(r,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};function Mm(t,r){for(var o=zc(t),s=-1,l=r.length;++s<l;)o.add(r[s])}function Am(t,r){for(var o=zc(t),s=-1,l=r.length;++s<l;)o.remove(r[s])}function Rx(t){return function(){Mm(this,t)}}function Mx(t){return function(){Am(this,t)}}function Ax(t,r){return function(){(r.apply(this,arguments)?Mm:Am)(this,t)}}function Lx(t,r){var o=Pm(t+"");if(arguments.length<2){for(var s=zc(this.node()),l=-1,a=o.length;++l<a;)if(!s.contains(o[l]))return!1;return!0}return this.each((typeof r=="function"?Ax:r?Rx:Mx)(o,r))}function Ix(){this.textContent=""}function zx(t){return function(){this.textContent=t}}function Ox(t){return function(){var r=t.apply(this,arguments);this.textContent=r??""}}function Dx(t){return arguments.length?this.each(t==null?Ix:(typeof t=="function"?Ox:zx)(t)):this.node().textContent}function $x(){this.innerHTML=""}function jx(t){return function(){this.innerHTML=t}}function Fx(t){return function(){var r=t.apply(this,arguments);this.innerHTML=r??""}}function bx(t){return arguments.length?this.each(t==null?$x:(typeof t=="function"?Fx:jx)(t)):this.node().innerHTML}function Hx(){this.nextSibling&&this.parentNode.appendChild(this)}function Bx(){return this.each(Hx)}function Vx(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Ux(){return this.each(Vx)}function Wx(t){var r=typeof t=="function"?t:Em(t);return this.select(function(){return this.appendChild(r.apply(this,arguments))})}function Xx(){return null}function Yx(t,r){var o=typeof t=="function"?t:Em(t),s=r==null?Xx:typeof r=="function"?r:Ic(r);return this.select(function(){return this.insertBefore(o.apply(this,arguments),s.apply(this,arguments)||null)})}function qx(){var t=this.parentNode;t&&t.removeChild(this)}function Qx(){return this.each(qx)}function Kx(){var t=this.cloneNode(!1),r=this.parentNode;return r?r.insertBefore(t,this.nextSibling):t}function Gx(){var t=this.cloneNode(!0),r=this.parentNode;return r?r.insertBefore(t,this.nextSibling):t}function Zx(t){return this.select(t?Gx:Kx)}function Jx(t){return arguments.length?this.property("__data__",t):this.node().__data__}function e1(t){return function(r){t.call(this,r,this.__data__)}}function t1(t){return t.trim().split(/^|\s+/).map(function(r){var o="",s=r.indexOf(".");return s>=0&&(o=r.slice(s+1),r=r.slice(0,s)),{type:r,name:o}})}function n1(t){return function(){var r=this.__on;if(r){for(var o=0,s=-1,l=r.length,a;o<l;++o)a=r[o],(!t.type||a.type===t.type)&&a.name===t.name?this.removeEventListener(a.type,a.listener,a.options):r[++s]=a;++s?r.length=s:delete this.__on}}}function r1(t,r,o){return function(){var s=this.__on,l,a=e1(r);if(s){for(var c=0,d=s.length;c<d;++c)if((l=s[c]).type===t.type&&l.name===t.name){this.removeEventListener(l.type,l.listener,l.options),this.addEventListener(l.type,l.listener=a,l.options=o),l.value=r;return}}this.addEventListener(t.type,a,o),l={type:t.type,name:t.name,value:r,listener:a,options:o},s?s.push(l):this.__on=[l]}}function o1(t,r,o){var s=t1(t+""),l,a=s.length,c;if(arguments.length<2){var d=this.node().__on;if(d){for(var p=0,g=d.length,m;p<g;++p)for(l=0,m=d[p];l<a;++l)if((c=s[l]).type===m.type&&c.name===m.name)return m.value}return}for(d=r?r1:n1,l=0;l<a;++l)this.each(d(s[l],r,o));return this}function Lm(t,r,o){var s=Tm(t),l=s.CustomEvent;typeof l=="function"?l=new l(r,o):(l=s.document.createEvent("Event"),o?(l.initEvent(r,o.bubbles,o.cancelable),l.detail=o.detail):l.initEvent(r,!1,!1)),t.dispatchEvent(l)}function i1(t,r){return function(){return Lm(this,t,r)}}function s1(t,r){return function(){return Lm(this,t,r.apply(this,arguments))}}function l1(t,r){return this.each((typeof r=="function"?s1:i1)(t,r))}function*u1(){for(var t=this._groups,r=0,o=t.length;r<o;++r)for(var s=t[r],l=0,a=s.length,c;l<a;++l)(c=s[l])&&(yield c)}var Im=[null];function Nt(t,r){this._groups=t,this._parents=r}function Ai(){return new Nt([[document.documentElement]],Im)}function a1(){return this}Nt.prototype=Ai.prototype={constructor:Nt,select:Ow,selectAll:Fw,selectChild:Vw,selectChildren:Yw,filter:qw,data:ex,enter:Qw,exit:nx,join:rx,merge:ox,selection:a1,order:ix,sort:sx,call:ux,nodes:ax,node:cx,size:fx,empty:dx,each:hx,attr:xx,style:kx,property:Px,classed:Lx,text:Dx,html:bx,raise:Bx,lower:Ux,append:Wx,insert:Yx,remove:Qx,clone:Zx,datum:Jx,on:o1,dispatch:l1,[Symbol.iterator]:u1};function kt(t){return typeof t=="string"?new Nt([[document.querySelector(t)]],[document.documentElement]):new Nt([[t]],Im)}function c1(t){let r;for(;r=t.sourceEvent;)t=r;return t}function bt(t,r){if(t=c1(t),r===void 0&&(r=t.currentTarget),r){var o=r.ownerSVGElement||r;if(o.createSVGPoint){var s=o.createSVGPoint();return s.x=t.clientX,s.y=t.clientY,s=s.matrixTransform(r.getScreenCTM().inverse()),[s.x,s.y]}if(r.getBoundingClientRect){var l=r.getBoundingClientRect();return[t.clientX-l.left-r.clientLeft,t.clientY-l.top-r.clientTop]}}return[t.pageX,t.pageY]}const f1={passive:!1},wi={capture:!0,passive:!1};function Ga(t){t.stopImmediatePropagation()}function io(t){t.preventDefault(),t.stopImmediatePropagation()}function zm(t){var r=t.document.documentElement,o=kt(t).on("dragstart.drag",io,wi);"onselectstart"in r?o.on("selectstart.drag",io,wi):(r.__noselect=r.style.MozUserSelect,r.style.MozUserSelect="none")}function Om(t,r){var o=t.document.documentElement,s=kt(t).on("dragstart.drag",null);r&&(s.on("click.drag",io,wi),setTimeout(function(){s.on("click.drag",null)},0)),"onselectstart"in o?s.on("selectstart.drag",null):(o.style.MozUserSelect=o.__noselect,delete o.__noselect)}const Js=t=>()=>t;function yc(t,{sourceEvent:r,subject:o,target:s,identifier:l,active:a,x:c,y:d,dx:p,dy:g,dispatch:m}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:r,enumerable:!0,configurable:!0},subject:{value:o,enumerable:!0,configurable:!0},target:{value:s,enumerable:!0,configurable:!0},identifier:{value:l,enumerable:!0,configurable:!0},active:{value:a,enumerable:!0,configurable:!0},x:{value:c,enumerable:!0,configurable:!0},y:{value:d,enumerable:!0,configurable:!0},dx:{value:p,enumerable:!0,configurable:!0},dy:{value:g,enumerable:!0,configurable:!0},_:{value:m}})}yc.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};function d1(t){return!t.ctrlKey&&!t.button}function h1(){return this.parentNode}function p1(t,r){return r??{x:t.x,y:t.y}}function m1(){return navigator.maxTouchPoints||"ontouchstart"in this}function Dm(){var t=d1,r=h1,o=p1,s=m1,l={},a=jl("start","drag","end"),c=0,d,p,g,m,v=0;function y(N){N.on("mousedown.drag",w).filter(s).on("touchstart.drag",k).on("touchmove.drag",C,f1).on("touchend.drag touchcancel.drag",z).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function w(N,A){if(!(m||!t.call(this,N,A))){var $=E(this,r.call(this,N,A),N,A,"mouse");$&&(kt(N.view).on("mousemove.drag",_,wi).on("mouseup.drag",S,wi),zm(N.view),Ga(N),g=!1,d=N.clientX,p=N.clientY,$("start",N))}}function _(N){if(io(N),!g){var A=N.clientX-d,$=N.clientY-p;g=A*A+$*$>v}l.mouse("drag",N)}function S(N){kt(N.view).on("mousemove.drag mouseup.drag",null),Om(N.view,g),io(N),l.mouse("end",N)}function k(N,A){if(t.call(this,N,A)){var $=N.changedTouches,F=r.call(this,N,A),X=$.length,K,Z;for(K=0;K<X;++K)(Z=E(this,F,N,A,$[K].identifier,$[K]))&&(Ga(N),Z("start",N,$[K]))}}function C(N){var A=N.changedTouches,$=A.length,F,X;for(F=0;F<$;++F)(X=l[A[F].identifier])&&(io(N),X("drag",N,A[F]))}function z(N){var A=N.changedTouches,$=A.length,F,X;for(m&&clearTimeout(m),m=setTimeout(function(){m=null},500),F=0;F<$;++F)(X=l[A[F].identifier])&&(Ga(N),X("end",N,A[F]))}function E(N,A,$,F,X,K){var Z=a.copy(),J=bt(K||$,A),ee,G,P;if((P=o.call(N,new yc("beforestart",{sourceEvent:$,target:y,identifier:X,active:c,x:J[0],y:J[1],dx:0,dy:0,dispatch:Z}),F))!=null)return ee=P.x-J[0]||0,G=P.y-J[1]||0,function W(b,H,D){var I=J,B;switch(b){case"start":l[X]=W,B=c++;break;case"end":delete l[X],--c;case"drag":J=bt(D||H,A),B=c;break}Z.call(b,N,new yc(b,{sourceEvent:H,subject:P,target:y,identifier:X,active:B,x:J[0]+ee,y:J[1]+G,dx:J[0]-I[0],dy:J[1]-I[1],dispatch:Z}),F)}}return y.filter=function(N){return arguments.length?(t=typeof N=="function"?N:Js(!!N),y):t},y.container=function(N){return arguments.length?(r=typeof N=="function"?N:Js(N),y):r},y.subject=function(N){return arguments.length?(o=typeof N=="function"?N:Js(N),y):o},y.touchable=function(N){return arguments.length?(s=typeof N=="function"?N:Js(!!N),y):s},y.on=function(){var N=a.on.apply(a,arguments);return N===a?y:N},y.clickDistance=function(N){return arguments.length?(v=(N=+N)*N,y):Math.sqrt(v)},y}function Oc(t,r,o){t.prototype=r.prototype=o,o.constructor=t}function $m(t,r){var o=Object.create(t.prototype);for(var s in r)o[s]=r[s];return o}function Li(){}var xi=.7,xl=1/xi,so="\\s*([+-]?\\d+)\\s*",Si="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",en="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",g1=/^#([0-9a-f]{3,8})$/,y1=new RegExp(`^rgb\\(${so},${so},${so}\\)$`),v1=new RegExp(`^rgb\\(${en},${en},${en}\\)$`),w1=new RegExp(`^rgba\\(${so},${so},${so},${Si}\\)$`),x1=new RegExp(`^rgba\\(${en},${en},${en},${Si}\\)$`),S1=new RegExp(`^hsl\\(${Si},${en},${en}\\)$`),E1=new RegExp(`^hsla\\(${Si},${en},${en},${Si}\\)$`),Uh={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Oc(Li,Er,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:Wh,formatHex:Wh,formatHex8:_1,formatHsl:k1,formatRgb:Xh,toString:Xh});function Wh(){return this.rgb().formatHex()}function _1(){return this.rgb().formatHex8()}function k1(){return jm(this).formatHsl()}function Xh(){return this.rgb().formatRgb()}function Er(t){var r,o;return t=(t+"").trim().toLowerCase(),(r=g1.exec(t))?(o=r[1].length,r=parseInt(r[1],16),o===6?Yh(r):o===3?new mt(r>>8&15|r>>4&240,r>>4&15|r&240,(r&15)<<4|r&15,1):o===8?el(r>>24&255,r>>16&255,r>>8&255,(r&255)/255):o===4?el(r>>12&15|r>>8&240,r>>8&15|r>>4&240,r>>4&15|r&240,((r&15)<<4|r&15)/255):null):(r=y1.exec(t))?new mt(r[1],r[2],r[3],1):(r=v1.exec(t))?new mt(r[1]*255/100,r[2]*255/100,r[3]*255/100,1):(r=w1.exec(t))?el(r[1],r[2],r[3],r[4]):(r=x1.exec(t))?el(r[1]*255/100,r[2]*255/100,r[3]*255/100,r[4]):(r=S1.exec(t))?Kh(r[1],r[2]/100,r[3]/100,1):(r=E1.exec(t))?Kh(r[1],r[2]/100,r[3]/100,r[4]):Uh.hasOwnProperty(t)?Yh(Uh[t]):t==="transparent"?new mt(NaN,NaN,NaN,0):null}function Yh(t){return new mt(t>>16&255,t>>8&255,t&255,1)}function el(t,r,o,s){return s<=0&&(t=r=o=NaN),new mt(t,r,o,s)}function N1(t){return t instanceof Li||(t=Er(t)),t?(t=t.rgb(),new mt(t.r,t.g,t.b,t.opacity)):new mt}function vc(t,r,o,s){return arguments.length===1?N1(t):new mt(t,r,o,s??1)}function mt(t,r,o,s){this.r=+t,this.g=+r,this.b=+o,this.opacity=+s}Oc(mt,vc,$m(Li,{brighter(t){return t=t==null?xl:Math.pow(xl,t),new mt(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=t==null?xi:Math.pow(xi,t),new mt(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new mt(wr(this.r),wr(this.g),wr(this.b),Sl(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:qh,formatHex:qh,formatHex8:C1,formatRgb:Qh,toString:Qh}));function qh(){return`#${yr(this.r)}${yr(this.g)}${yr(this.b)}`}function C1(){return`#${yr(this.r)}${yr(this.g)}${yr(this.b)}${yr((isNaN(this.opacity)?1:this.opacity)*255)}`}function Qh(){const t=Sl(this.opacity);return`${t===1?"rgb(":"rgba("}${wr(this.r)}, ${wr(this.g)}, ${wr(this.b)}${t===1?")":`, ${t})`}`}function Sl(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function wr(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function yr(t){return t=wr(t),(t<16?"0":"")+t.toString(16)}function Kh(t,r,o,s){return s<=0?t=r=o=NaN:o<=0||o>=1?t=r=NaN:r<=0&&(t=NaN),new Ht(t,r,o,s)}function jm(t){if(t instanceof Ht)return new Ht(t.h,t.s,t.l,t.opacity);if(t instanceof Li||(t=Er(t)),!t)return new Ht;if(t instanceof Ht)return t;t=t.rgb();var r=t.r/255,o=t.g/255,s=t.b/255,l=Math.min(r,o,s),a=Math.max(r,o,s),c=NaN,d=a-l,p=(a+l)/2;return d?(r===a?c=(o-s)/d+(o<s)*6:o===a?c=(s-r)/d+2:c=(r-o)/d+4,d/=p<.5?a+l:2-a-l,c*=60):d=p>0&&p<1?0:c,new Ht(c,d,p,t.opacity)}function T1(t,r,o,s){return arguments.length===1?jm(t):new Ht(t,r,o,s??1)}function Ht(t,r,o,s){this.h=+t,this.s=+r,this.l=+o,this.opacity=+s}Oc(Ht,T1,$m(Li,{brighter(t){return t=t==null?xl:Math.pow(xl,t),new Ht(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=t==null?xi:Math.pow(xi,t),new Ht(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,r=isNaN(t)||isNaN(this.s)?0:this.s,o=this.l,s=o+(o<.5?o:1-o)*r,l=2*o-s;return new mt(Za(t>=240?t-240:t+120,l,s),Za(t,l,s),Za(t<120?t+240:t-120,l,s),this.opacity)},clamp(){return new Ht(Gh(this.h),tl(this.s),tl(this.l),Sl(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=Sl(this.opacity);return`${t===1?"hsl(":"hsla("}${Gh(this.h)}, ${tl(this.s)*100}%, ${tl(this.l)*100}%${t===1?")":`, ${t})`}`}}));function Gh(t){return t=(t||0)%360,t<0?t+360:t}function tl(t){return Math.max(0,Math.min(1,t||0))}function Za(t,r,o){return(t<60?r+(o-r)*t/60:t<180?o:t<240?r+(o-r)*(240-t)/60:r)*255}const Dc=t=>()=>t;function P1(t,r){return function(o){return t+o*r}}function R1(t,r,o){return t=Math.pow(t,o),r=Math.pow(r,o)-t,o=1/o,function(s){return Math.pow(t+s*r,o)}}function M1(t){return(t=+t)==1?Fm:function(r,o){return o-r?R1(r,o,t):Dc(isNaN(r)?o:r)}}function Fm(t,r){var o=r-t;return o?P1(t,o):Dc(isNaN(t)?r:t)}const El=function t(r){var o=M1(r);function s(l,a){var c=o((l=vc(l)).r,(a=vc(a)).r),d=o(l.g,a.g),p=o(l.b,a.b),g=Fm(l.opacity,a.opacity);return function(m){return l.r=c(m),l.g=d(m),l.b=p(m),l.opacity=g(m),l+""}}return s.gamma=t,s}(1);function A1(t,r){r||(r=[]);var o=t?Math.min(r.length,t.length):0,s=r.slice(),l;return function(a){for(l=0;l<o;++l)s[l]=t[l]*(1-a)+r[l]*a;return s}}function L1(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function I1(t,r){var o=r?r.length:0,s=t?Math.min(o,t.length):0,l=new Array(s),a=new Array(o),c;for(c=0;c<s;++c)l[c]=gi(t[c],r[c]);for(;c<o;++c)a[c]=r[c];return function(d){for(c=0;c<s;++c)a[c]=l[c](d);return a}}function z1(t,r){var o=new Date;return t=+t,r=+r,function(s){return o.setTime(t*(1-s)+r*s),o}}function Jt(t,r){return t=+t,r=+r,function(o){return t*(1-o)+r*o}}function O1(t,r){var o={},s={},l;(t===null||typeof t!="object")&&(t={}),(r===null||typeof r!="object")&&(r={});for(l in r)l in t?o[l]=gi(t[l],r[l]):s[l]=r[l];return function(a){for(l in o)s[l]=o[l](a);return s}}var wc=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Ja=new RegExp(wc.source,"g");function D1(t){return function(){return t}}function $1(t){return function(r){return t(r)+""}}function bm(t,r){var o=wc.lastIndex=Ja.lastIndex=0,s,l,a,c=-1,d=[],p=[];for(t=t+"",r=r+"";(s=wc.exec(t))&&(l=Ja.exec(r));)(a=l.index)>o&&(a=r.slice(o,a),d[c]?d[c]+=a:d[++c]=a),(s=s[0])===(l=l[0])?d[c]?d[c]+=l:d[++c]=l:(d[++c]=null,p.push({i:c,x:Jt(s,l)})),o=Ja.lastIndex;return o<r.length&&(a=r.slice(o),d[c]?d[c]+=a:d[++c]=a),d.length<2?p[0]?$1(p[0].x):D1(r):(r=p.length,function(g){for(var m=0,v;m<r;++m)d[(v=p[m]).i]=v.x(g);return d.join("")})}function gi(t,r){var o=typeof r,s;return r==null||o==="boolean"?Dc(r):(o==="number"?Jt:o==="string"?(s=Er(r))?(r=s,El):bm:r instanceof Er?El:r instanceof Date?z1:L1(r)?A1:Array.isArray(r)?I1:typeof r.valueOf!="function"&&typeof r.toString!="function"||isNaN(r)?O1:Jt)(t,r)}var Zh=180/Math.PI,xc={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Hm(t,r,o,s,l,a){var c,d,p;return(c=Math.sqrt(t*t+r*r))&&(t/=c,r/=c),(p=t*o+r*s)&&(o-=t*p,s-=r*p),(d=Math.sqrt(o*o+s*s))&&(o/=d,s/=d,p/=d),t*s<r*o&&(t=-t,r=-r,p=-p,c=-c),{translateX:l,translateY:a,rotate:Math.atan2(r,t)*Zh,skewX:Math.atan(p)*Zh,scaleX:c,scaleY:d}}var nl;function j1(t){const r=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(t+"");return r.isIdentity?xc:Hm(r.a,r.b,r.c,r.d,r.e,r.f)}function F1(t){return t==null||(nl||(nl=document.createElementNS("http://www.w3.org/2000/svg","g")),nl.setAttribute("transform",t),!(t=nl.transform.baseVal.consolidate()))?xc:(t=t.matrix,Hm(t.a,t.b,t.c,t.d,t.e,t.f))}function Bm(t,r,o,s){function l(g){return g.length?g.pop()+" ":""}function a(g,m,v,y,w,_){if(g!==v||m!==y){var S=w.push("translate(",null,r,null,o);_.push({i:S-4,x:Jt(g,v)},{i:S-2,x:Jt(m,y)})}else(v||y)&&w.push("translate("+v+r+y+o)}function c(g,m,v,y){g!==m?(g-m>180?m+=360:m-g>180&&(g+=360),y.push({i:v.push(l(v)+"rotate(",null,s)-2,x:Jt(g,m)})):m&&v.push(l(v)+"rotate("+m+s)}function d(g,m,v,y){g!==m?y.push({i:v.push(l(v)+"skewX(",null,s)-2,x:Jt(g,m)}):m&&v.push(l(v)+"skewX("+m+s)}function p(g,m,v,y,w,_){if(g!==v||m!==y){var S=w.push(l(w)+"scale(",null,",",null,")");_.push({i:S-4,x:Jt(g,v)},{i:S-2,x:Jt(m,y)})}else(v!==1||y!==1)&&w.push(l(w)+"scale("+v+","+y+")")}return function(g,m){var v=[],y=[];return g=t(g),m=t(m),a(g.translateX,g.translateY,m.translateX,m.translateY,v,y),c(g.rotate,m.rotate,v,y),d(g.skewX,m.skewX,v,y),p(g.scaleX,g.scaleY,m.scaleX,m.scaleY,v,y),g=m=null,function(w){for(var _=-1,S=y.length,k;++_<S;)v[(k=y[_]).i]=k.x(w);return v.join("")}}}var b1=Bm(j1,"px, ","px)","deg)"),H1=Bm(F1,", ",")",")"),B1=1e-12;function Jh(t){return((t=Math.exp(t))+1/t)/2}function V1(t){return((t=Math.exp(t))-1/t)/2}function U1(t){return((t=Math.exp(2*t))-1)/(t+1)}const hl=function t(r,o,s){function l(a,c){var d=a[0],p=a[1],g=a[2],m=c[0],v=c[1],y=c[2],w=m-d,_=v-p,S=w*w+_*_,k,C;if(S<B1)C=Math.log(y/g)/r,k=function(F){return[d+F*w,p+F*_,g*Math.exp(r*F*C)]};else{var z=Math.sqrt(S),E=(y*y-g*g+s*S)/(2*g*o*z),N=(y*y-g*g-s*S)/(2*y*o*z),A=Math.log(Math.sqrt(E*E+1)-E),$=Math.log(Math.sqrt(N*N+1)-N);C=($-A)/r,k=function(F){var X=F*C,K=Jh(A),Z=g/(o*z)*(K*U1(r*X+A)-V1(A));return[d+Z*w,p+Z*_,g*K/Jh(r*X+A)]}}return k.duration=C*1e3*r/Math.SQRT2,k}return l.rho=function(a){var c=Math.max(.001,+a),d=c*c,p=d*d;return t(c,d,p)},l}(Math.SQRT2,2,4);var uo=0,pi=0,di=0,Vm=1e3,_l,mi,kl=0,_r=0,bl=0,Ei=typeof performance=="object"&&performance.now?performance:Date,Um=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function $c(){return _r||(Um(W1),_r=Ei.now()+bl)}function W1(){_r=0}function Nl(){this._call=this._time=this._next=null}Nl.prototype=Wm.prototype={constructor:Nl,restart:function(t,r,o){if(typeof t!="function")throw new TypeError("callback is not a function");o=(o==null?$c():+o)+(r==null?0:+r),!this._next&&mi!==this&&(mi?mi._next=this:_l=this,mi=this),this._call=t,this._time=o,Sc()},stop:function(){this._call&&(this._call=null,this._time=1/0,Sc())}};function Wm(t,r,o){var s=new Nl;return s.restart(t,r,o),s}function X1(){$c(),++uo;for(var t=_l,r;t;)(r=_r-t._time)>=0&&t._call.call(void 0,r),t=t._next;--uo}function ep(){_r=(kl=Ei.now())+bl,uo=pi=0;try{X1()}finally{uo=0,q1(),_r=0}}function Y1(){var t=Ei.now(),r=t-kl;r>Vm&&(bl-=r,kl=t)}function q1(){for(var t,r=_l,o,s=1/0;r;)r._call?(s>r._time&&(s=r._time),t=r,r=r._next):(o=r._next,r._next=null,r=t?t._next=o:_l=o);mi=t,Sc(s)}function Sc(t){if(!uo){pi&&(pi=clearTimeout(pi));var r=t-_r;r>24?(t<1/0&&(pi=setTimeout(ep,t-Ei.now()-bl)),di&&(di=clearInterval(di))):(di||(kl=Ei.now(),di=setInterval(Y1,Vm)),uo=1,Um(ep))}}function tp(t,r,o){var s=new Nl;return r=r==null?0:+r,s.restart(l=>{s.stop(),t(l+r)},r,o),s}var Q1=jl("start","end","cancel","interrupt"),K1=[],Xm=0,np=1,Ec=2,pl=3,rp=4,_c=5,ml=6;function Hl(t,r,o,s,l,a){var c=t.__transition;if(!c)t.__transition={};else if(o in c)return;G1(t,o,{name:r,index:s,group:l,on:Q1,tween:K1,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:Xm})}function jc(t,r){var o=Ut(t,r);if(o.state>Xm)throw new Error("too late; already scheduled");return o}function nn(t,r){var o=Ut(t,r);if(o.state>pl)throw new Error("too late; already running");return o}function Ut(t,r){var o=t.__transition;if(!o||!(o=o[r]))throw new Error("transition not found");return o}function G1(t,r,o){var s=t.__transition,l;s[r]=o,o.timer=Wm(a,0,o.time);function a(g){o.state=np,o.timer.restart(c,o.delay,o.time),o.delay<=g&&c(g-o.delay)}function c(g){var m,v,y,w;if(o.state!==np)return p();for(m in s)if(w=s[m],w.name===o.name){if(w.state===pl)return tp(c);w.state===rp?(w.state=ml,w.timer.stop(),w.on.call("interrupt",t,t.__data__,w.index,w.group),delete s[m]):+m<r&&(w.state=ml,w.timer.stop(),w.on.call("cancel",t,t.__data__,w.index,w.group),delete s[m])}if(tp(function(){o.state===pl&&(o.state=rp,o.timer.restart(d,o.delay,o.time),d(g))}),o.state=Ec,o.on.call("start",t,t.__data__,o.index,o.group),o.state===Ec){for(o.state=pl,l=new Array(y=o.tween.length),m=0,v=-1;m<y;++m)(w=o.tween[m].value.call(t,t.__data__,o.index,o.group))&&(l[++v]=w);l.length=v+1}}function d(g){for(var m=g<o.duration?o.ease.call(null,g/o.duration):(o.timer.restart(p),o.state=_c,1),v=-1,y=l.length;++v<y;)l[v].call(t,m);o.state===_c&&(o.on.call("end",t,t.__data__,o.index,o.group),p())}function p(){o.state=ml,o.timer.stop(),delete s[r];for(var g in s)return;delete t.__transition}}function gl(t,r){var o=t.__transition,s,l,a=!0,c;if(o){r=r==null?null:r+"";for(c in o){if((s=o[c]).name!==r){a=!1;continue}l=s.state>Ec&&s.state<_c,s.state=ml,s.timer.stop(),s.on.call(l?"interrupt":"cancel",t,t.__data__,s.index,s.group),delete o[c]}a&&delete t.__transition}}function Z1(t){return this.each(function(){gl(this,t)})}function J1(t,r){var o,s;return function(){var l=nn(this,t),a=l.tween;if(a!==o){s=o=a;for(var c=0,d=s.length;c<d;++c)if(s[c].name===r){s=s.slice(),s.splice(c,1);break}}l.tween=s}}function eS(t,r,o){var s,l;if(typeof o!="function")throw new Error;return function(){var a=nn(this,t),c=a.tween;if(c!==s){l=(s=c).slice();for(var d={name:r,value:o},p=0,g=l.length;p<g;++p)if(l[p].name===r){l[p]=d;break}p===g&&l.push(d)}a.tween=l}}function tS(t,r){var o=this._id;if(t+="",arguments.length<2){for(var s=Ut(this.node(),o).tween,l=0,a=s.length,c;l<a;++l)if((c=s[l]).name===t)return c.value;return null}return this.each((r==null?J1:eS)(o,t,r))}function Fc(t,r,o){var s=t._id;return t.each(function(){var l=nn(this,s);(l.value||(l.value={}))[r]=o.apply(this,arguments)}),function(l){return Ut(l,s).value[r]}}function Ym(t,r){var o;return(typeof r=="number"?Jt:r instanceof Er?El:(o=Er(r))?(r=o,El):bm)(t,r)}function nS(t){return function(){this.removeAttribute(t)}}function rS(t){return function(){this.removeAttributeNS(t.space,t.local)}}function oS(t,r,o){var s,l=o+"",a;return function(){var c=this.getAttribute(t);return c===l?null:c===s?a:a=r(s=c,o)}}function iS(t,r,o){var s,l=o+"",a;return function(){var c=this.getAttributeNS(t.space,t.local);return c===l?null:c===s?a:a=r(s=c,o)}}function sS(t,r,o){var s,l,a;return function(){var c,d=o(this),p;return d==null?void this.removeAttribute(t):(c=this.getAttribute(t),p=d+"",c===p?null:c===s&&p===l?a:(l=p,a=r(s=c,d)))}}function lS(t,r,o){var s,l,a;return function(){var c,d=o(this),p;return d==null?void this.removeAttributeNS(t.space,t.local):(c=this.getAttributeNS(t.space,t.local),p=d+"",c===p?null:c===s&&p===l?a:(l=p,a=r(s=c,d)))}}function uS(t,r){var o=Fl(t),s=o==="transform"?H1:Ym;return this.attrTween(t,typeof r=="function"?(o.local?lS:sS)(o,s,Fc(this,"attr."+t,r)):r==null?(o.local?rS:nS)(o):(o.local?iS:oS)(o,s,r))}function aS(t,r){return function(o){this.setAttribute(t,r.call(this,o))}}function cS(t,r){return function(o){this.setAttributeNS(t.space,t.local,r.call(this,o))}}function fS(t,r){var o,s;function l(){var a=r.apply(this,arguments);return a!==s&&(o=(s=a)&&cS(t,a)),o}return l._value=r,l}function dS(t,r){var o,s;function l(){var a=r.apply(this,arguments);return a!==s&&(o=(s=a)&&aS(t,a)),o}return l._value=r,l}function hS(t,r){var o="attr."+t;if(arguments.length<2)return(o=this.tween(o))&&o._value;if(r==null)return this.tween(o,null);if(typeof r!="function")throw new Error;var s=Fl(t);return this.tween(o,(s.local?fS:dS)(s,r))}function pS(t,r){return function(){jc(this,t).delay=+r.apply(this,arguments)}}function mS(t,r){return r=+r,function(){jc(this,t).delay=r}}function gS(t){var r=this._id;return arguments.length?this.each((typeof t=="function"?pS:mS)(r,t)):Ut(this.node(),r).delay}function yS(t,r){return function(){nn(this,t).duration=+r.apply(this,arguments)}}function vS(t,r){return r=+r,function(){nn(this,t).duration=r}}function wS(t){var r=this._id;return arguments.length?this.each((typeof t=="function"?yS:vS)(r,t)):Ut(this.node(),r).duration}function xS(t,r){if(typeof r!="function")throw new Error;return function(){nn(this,t).ease=r}}function SS(t){var r=this._id;return arguments.length?this.each(xS(r,t)):Ut(this.node(),r).ease}function ES(t,r){return function(){var o=r.apply(this,arguments);if(typeof o!="function")throw new Error;nn(this,t).ease=o}}function _S(t){if(typeof t!="function")throw new Error;return this.each(ES(this._id,t))}function kS(t){typeof t!="function"&&(t=km(t));for(var r=this._groups,o=r.length,s=new Array(o),l=0;l<o;++l)for(var a=r[l],c=a.length,d=s[l]=[],p,g=0;g<c;++g)(p=a[g])&&t.call(p,p.__data__,g,a)&&d.push(p);return new Sn(s,this._parents,this._name,this._id)}function NS(t){if(t._id!==this._id)throw new Error;for(var r=this._groups,o=t._groups,s=r.length,l=o.length,a=Math.min(s,l),c=new Array(s),d=0;d<a;++d)for(var p=r[d],g=o[d],m=p.length,v=c[d]=new Array(m),y,w=0;w<m;++w)(y=p[w]||g[w])&&(v[w]=y);for(;d<s;++d)c[d]=r[d];return new Sn(c,this._parents,this._name,this._id)}function CS(t){return(t+"").trim().split(/^|\s+/).every(function(r){var o=r.indexOf(".");return o>=0&&(r=r.slice(0,o)),!r||r==="start"})}function TS(t,r,o){var s,l,a=CS(r)?jc:nn;return function(){var c=a(this,t),d=c.on;d!==s&&(l=(s=d).copy()).on(r,o),c.on=l}}function PS(t,r){var o=this._id;return arguments.length<2?Ut(this.node(),o).on.on(t):this.each(TS(o,t,r))}function RS(t){return function(){var r=this.parentNode;for(var o in this.__transition)if(+o!==t)return;r&&r.removeChild(this)}}function MS(){return this.on("end.remove",RS(this._id))}function AS(t){var r=this._name,o=this._id;typeof t!="function"&&(t=Ic(t));for(var s=this._groups,l=s.length,a=new Array(l),c=0;c<l;++c)for(var d=s[c],p=d.length,g=a[c]=new Array(p),m,v,y=0;y<p;++y)(m=d[y])&&(v=t.call(m,m.__data__,y,d))&&("__data__"in m&&(v.__data__=m.__data__),g[y]=v,Hl(g[y],r,o,y,g,Ut(m,o)));return new Sn(a,this._parents,r,o)}function LS(t){var r=this._name,o=this._id;typeof t!="function"&&(t=_m(t));for(var s=this._groups,l=s.length,a=[],c=[],d=0;d<l;++d)for(var p=s[d],g=p.length,m,v=0;v<g;++v)if(m=p[v]){for(var y=t.call(m,m.__data__,v,p),w,_=Ut(m,o),S=0,k=y.length;S<k;++S)(w=y[S])&&Hl(w,r,o,S,y,_);a.push(y),c.push(m)}return new Sn(a,c,r,o)}var IS=Ai.prototype.constructor;function zS(){return new IS(this._groups,this._parents)}function OS(t,r){var o,s,l;return function(){var a=lo(this,t),c=(this.style.removeProperty(t),lo(this,t));return a===c?null:a===o&&c===s?l:l=r(o=a,s=c)}}function qm(t){return function(){this.style.removeProperty(t)}}function DS(t,r,o){var s,l=o+"",a;return function(){var c=lo(this,t);return c===l?null:c===s?a:a=r(s=c,o)}}function $S(t,r,o){var s,l,a;return function(){var c=lo(this,t),d=o(this),p=d+"";return d==null&&(p=d=(this.style.removeProperty(t),lo(this,t))),c===p?null:c===s&&p===l?a:(l=p,a=r(s=c,d))}}function jS(t,r){var o,s,l,a="style."+r,c="end."+a,d;return function(){var p=nn(this,t),g=p.on,m=p.value[a]==null?d||(d=qm(r)):void 0;(g!==o||l!==m)&&(s=(o=g).copy()).on(c,l=m),p.on=s}}function FS(t,r,o){var s=(t+="")=="transform"?b1:Ym;return r==null?this.styleTween(t,OS(t,s)).on("end.style."+t,qm(t)):typeof r=="function"?this.styleTween(t,$S(t,s,Fc(this,"style."+t,r))).each(jS(this._id,t)):this.styleTween(t,DS(t,s,r),o).on("end.style."+t,null)}function bS(t,r,o){return function(s){this.style.setProperty(t,r.call(this,s),o)}}function HS(t,r,o){var s,l;function a(){var c=r.apply(this,arguments);return c!==l&&(s=(l=c)&&bS(t,c,o)),s}return a._value=r,a}function BS(t,r,o){var s="style."+(t+="");if(arguments.length<2)return(s=this.tween(s))&&s._value;if(r==null)return this.tween(s,null);if(typeof r!="function")throw new Error;return this.tween(s,HS(t,r,o??""))}function VS(t){return function(){this.textContent=t}}function US(t){return function(){var r=t(this);this.textContent=r??""}}function WS(t){return this.tween("text",typeof t=="function"?US(Fc(this,"text",t)):VS(t==null?"":t+""))}function XS(t){return function(r){this.textContent=t.call(this,r)}}function YS(t){var r,o;function s(){var l=t.apply(this,arguments);return l!==o&&(r=(o=l)&&XS(l)),r}return s._value=t,s}function qS(t){var r="text";if(arguments.length<1)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,YS(t))}function QS(){for(var t=this._name,r=this._id,o=Qm(),s=this._groups,l=s.length,a=0;a<l;++a)for(var c=s[a],d=c.length,p,g=0;g<d;++g)if(p=c[g]){var m=Ut(p,r);Hl(p,t,o,g,c,{time:m.time+m.delay+m.duration,delay:0,duration:m.duration,ease:m.ease})}return new Sn(s,this._parents,t,o)}function KS(){var t,r,o=this,s=o._id,l=o.size();return new Promise(function(a,c){var d={value:c},p={value:function(){--l===0&&a()}};o.each(function(){var g=nn(this,s),m=g.on;m!==t&&(r=(t=m).copy(),r._.cancel.push(d),r._.interrupt.push(d),r._.end.push(p)),g.on=r}),l===0&&a()})}var GS=0;function Sn(t,r,o,s){this._groups=t,this._parents=r,this._name=o,this._id=s}function Qm(){return++GS}var yn=Ai.prototype;Sn.prototype={constructor:Sn,select:AS,selectAll:LS,selectChild:yn.selectChild,selectChildren:yn.selectChildren,filter:kS,merge:NS,selection:zS,transition:QS,call:yn.call,nodes:yn.nodes,node:yn.node,size:yn.size,empty:yn.empty,each:yn.each,on:PS,attr:uS,attrTween:hS,style:FS,styleTween:BS,text:WS,textTween:qS,remove:MS,tween:tS,delay:gS,duration:wS,ease:SS,easeVarying:_S,end:KS,[Symbol.iterator]:yn[Symbol.iterator]};function ZS(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}var JS={time:null,delay:0,duration:250,ease:ZS};function eE(t,r){for(var o;!(o=t.__transition)||!(o=o[r]);)if(!(t=t.parentNode))throw new Error(`transition ${r} not found`);return o}function tE(t){var r,o;t instanceof Sn?(r=t._id,t=t._name):(r=Qm(),(o=JS).time=$c(),t=t==null?null:t+"");for(var s=this._groups,l=s.length,a=0;a<l;++a)for(var c=s[a],d=c.length,p,g=0;g<d;++g)(p=c[g])&&Hl(p,t,r,g,c,o||eE(p,r));return new Sn(s,this._parents,t,r)}Ai.prototype.interrupt=Z1;Ai.prototype.transition=tE;const rl=t=>()=>t;function nE(t,{sourceEvent:r,target:o,transform:s,dispatch:l}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:r,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},transform:{value:s,enumerable:!0,configurable:!0},_:{value:l}})}function vn(t,r,o){this.k=t,this.x=r,this.y=o}vn.prototype={constructor:vn,scale:function(t){return t===1?this:new vn(this.k*t,this.x,this.y)},translate:function(t,r){return t===0&r===0?this:new vn(this.k,this.x+this.k*t,this.y+this.k*r)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Bl=new vn(1,0,0);Km.prototype=vn.prototype;function Km(t){for(;!t.__zoom;)if(!(t=t.parentNode))return Bl;return t.__zoom}function ec(t){t.stopImmediatePropagation()}function hi(t){t.preventDefault(),t.stopImmediatePropagation()}function rE(t){return(!t.ctrlKey||t.type==="wheel")&&!t.button}function oE(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t,t.hasAttribute("viewBox")?(t=t.viewBox.baseVal,[[t.x,t.y],[t.x+t.width,t.y+t.height]]):[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]):[[0,0],[t.clientWidth,t.clientHeight]]}function op(){return this.__zoom||Bl}function iE(t){return-t.deltaY*(t.deltaMode===1?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function sE(){return navigator.maxTouchPoints||"ontouchstart"in this}function lE(t,r,o){var s=t.invertX(r[0][0])-o[0][0],l=t.invertX(r[1][0])-o[1][0],a=t.invertY(r[0][1])-o[0][1],c=t.invertY(r[1][1])-o[1][1];return t.translate(l>s?(s+l)/2:Math.min(0,s)||Math.max(0,l),c>a?(a+c)/2:Math.min(0,a)||Math.max(0,c))}function Gm(){var t=rE,r=oE,o=lE,s=iE,l=sE,a=[0,1/0],c=[[-1/0,-1/0],[1/0,1/0]],d=250,p=hl,g=jl("start","zoom","end"),m,v,y,w=500,_=150,S=0,k=10;function C(P){P.property("__zoom",op).on("wheel.zoom",X,{passive:!1}).on("mousedown.zoom",K).on("dblclick.zoom",Z).filter(l).on("touchstart.zoom",J).on("touchmove.zoom",ee).on("touchend.zoom touchcancel.zoom",G).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}C.transform=function(P,W,b,H){var D=P.selection?P.selection():P;D.property("__zoom",op),P!==D?A(P,W,b,H):D.interrupt().each(function(){$(this,arguments).event(H).start().zoom(null,typeof W=="function"?W.apply(this,arguments):W).end()})},C.scaleBy=function(P,W,b,H){C.scaleTo(P,function(){var D=this.__zoom.k,I=typeof W=="function"?W.apply(this,arguments):W;return D*I},b,H)},C.scaleTo=function(P,W,b,H){C.transform(P,function(){var D=r.apply(this,arguments),I=this.__zoom,B=b==null?N(D):typeof b=="function"?b.apply(this,arguments):b,T=I.invert(B),j=typeof W=="function"?W.apply(this,arguments):W;return o(E(z(I,j),B,T),D,c)},b,H)},C.translateBy=function(P,W,b,H){C.transform(P,function(){return o(this.__zoom.translate(typeof W=="function"?W.apply(this,arguments):W,typeof b=="function"?b.apply(this,arguments):b),r.apply(this,arguments),c)},null,H)},C.translateTo=function(P,W,b,H,D){C.transform(P,function(){var I=r.apply(this,arguments),B=this.__zoom,T=H==null?N(I):typeof H=="function"?H.apply(this,arguments):H;return o(Bl.translate(T[0],T[1]).scale(B.k).translate(typeof W=="function"?-W.apply(this,arguments):-W,typeof b=="function"?-b.apply(this,arguments):-b),I,c)},H,D)};function z(P,W){return W=Math.max(a[0],Math.min(a[1],W)),W===P.k?P:new vn(W,P.x,P.y)}function E(P,W,b){var H=W[0]-b[0]*P.k,D=W[1]-b[1]*P.k;return H===P.x&&D===P.y?P:new vn(P.k,H,D)}function N(P){return[(+P[0][0]+ +P[1][0])/2,(+P[0][1]+ +P[1][1])/2]}function A(P,W,b,H){P.on("start.zoom",function(){$(this,arguments).event(H).start()}).on("interrupt.zoom end.zoom",function(){$(this,arguments).event(H).end()}).tween("zoom",function(){var D=this,I=arguments,B=$(D,I).event(H),T=r.apply(D,I),j=b==null?N(T):typeof b=="function"?b.apply(D,I):b,oe=Math.max(T[1][0]-T[0][0],T[1][1]-T[0][1]),ie=D.__zoom,ue=typeof W=="function"?W.apply(D,I):W,ae=p(ie.invert(j).concat(oe/ie.k),ue.invert(j).concat(oe/ue.k));return function(fe){if(fe===1)fe=ue;else{var re=ae(fe),ce=oe/re[2];fe=new vn(ce,j[0]-re[0]*ce,j[1]-re[1]*ce)}B.zoom(null,fe)}})}function $(P,W,b){return!b&&P.__zooming||new F(P,W)}function F(P,W){this.that=P,this.args=W,this.active=0,this.sourceEvent=null,this.extent=r.apply(P,W),this.taps=0}F.prototype={event:function(P){return P&&(this.sourceEvent=P),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(P,W){return this.mouse&&P!=="mouse"&&(this.mouse[1]=W.invert(this.mouse[0])),this.touch0&&P!=="touch"&&(this.touch0[1]=W.invert(this.touch0[0])),this.touch1&&P!=="touch"&&(this.touch1[1]=W.invert(this.touch1[0])),this.that.__zoom=W,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(P){var W=kt(this.that).datum();g.call(P,this.that,new nE(P,{sourceEvent:this.sourceEvent,target:C,transform:this.that.__zoom,dispatch:g}),W)}};function X(P,...W){if(!t.apply(this,arguments))return;var b=$(this,W).event(P),H=this.__zoom,D=Math.max(a[0],Math.min(a[1],H.k*Math.pow(2,s.apply(this,arguments)))),I=bt(P);if(b.wheel)(b.mouse[0][0]!==I[0]||b.mouse[0][1]!==I[1])&&(b.mouse[1]=H.invert(b.mouse[0]=I)),clearTimeout(b.wheel);else{if(H.k===D)return;b.mouse=[I,H.invert(I)],gl(this),b.start()}hi(P),b.wheel=setTimeout(B,_),b.zoom("mouse",o(E(z(H,D),b.mouse[0],b.mouse[1]),b.extent,c));function B(){b.wheel=null,b.end()}}function K(P,...W){if(y||!t.apply(this,arguments))return;var b=P.currentTarget,H=$(this,W,!0).event(P),D=kt(P.view).on("mousemove.zoom",j,!0).on("mouseup.zoom",oe,!0),I=bt(P,b),B=P.clientX,T=P.clientY;zm(P.view),ec(P),H.mouse=[I,this.__zoom.invert(I)],gl(this),H.start();function j(ie){if(hi(ie),!H.moved){var ue=ie.clientX-B,ae=ie.clientY-T;H.moved=ue*ue+ae*ae>S}H.event(ie).zoom("mouse",o(E(H.that.__zoom,H.mouse[0]=bt(ie,b),H.mouse[1]),H.extent,c))}function oe(ie){D.on("mousemove.zoom mouseup.zoom",null),Om(ie.view,H.moved),hi(ie),H.event(ie).end()}}function Z(P,...W){if(t.apply(this,arguments)){var b=this.__zoom,H=bt(P.changedTouches?P.changedTouches[0]:P,this),D=b.invert(H),I=b.k*(P.shiftKey?.5:2),B=o(E(z(b,I),H,D),r.apply(this,W),c);hi(P),d>0?kt(this).transition().duration(d).call(A,B,H,P):kt(this).call(C.transform,B,H,P)}}function J(P,...W){if(t.apply(this,arguments)){var b=P.touches,H=b.length,D=$(this,W,P.changedTouches.length===H).event(P),I,B,T,j;for(ec(P),B=0;B<H;++B)T=b[B],j=bt(T,this),j=[j,this.__zoom.invert(j),T.identifier],D.touch0?!D.touch1&&D.touch0[2]!==j[2]&&(D.touch1=j,D.taps=0):(D.touch0=j,I=!0,D.taps=1+!!m);m&&(m=clearTimeout(m)),I&&(D.taps<2&&(v=j[0],m=setTimeout(function(){m=null},w)),gl(this),D.start())}}function ee(P,...W){if(this.__zooming){var b=$(this,W).event(P),H=P.changedTouches,D=H.length,I,B,T,j;for(hi(P),I=0;I<D;++I)B=H[I],T=bt(B,this),b.touch0&&b.touch0[2]===B.identifier?b.touch0[0]=T:b.touch1&&b.touch1[2]===B.identifier&&(b.touch1[0]=T);if(B=b.that.__zoom,b.touch1){var oe=b.touch0[0],ie=b.touch0[1],ue=b.touch1[0],ae=b.touch1[1],fe=(fe=ue[0]-oe[0])*fe+(fe=ue[1]-oe[1])*fe,re=(re=ae[0]-ie[0])*re+(re=ae[1]-ie[1])*re;B=z(B,Math.sqrt(fe/re)),T=[(oe[0]+ue[0])/2,(oe[1]+ue[1])/2],j=[(ie[0]+ae[0])/2,(ie[1]+ae[1])/2]}else if(b.touch0)T=b.touch0[0],j=b.touch0[1];else return;b.zoom("touch",o(E(B,T,j),b.extent,c))}}function G(P,...W){if(this.__zooming){var b=$(this,W).event(P),H=P.changedTouches,D=H.length,I,B;for(ec(P),y&&clearTimeout(y),y=setTimeout(function(){y=null},w),I=0;I<D;++I)B=H[I],b.touch0&&b.touch0[2]===B.identifier?delete b.touch0:b.touch1&&b.touch1[2]===B.identifier&&delete b.touch1;if(b.touch1&&!b.touch0&&(b.touch0=b.touch1,delete b.touch1),b.touch0)b.touch0[1]=this.__zoom.invert(b.touch0[0]);else if(b.end(),b.taps===2&&(B=bt(B,this),Math.hypot(v[0]-B[0],v[1]-B[1])<k)){var T=kt(this).on("dblclick.zoom");T&&T.apply(this,arguments)}}}return C.wheelDelta=function(P){return arguments.length?(s=typeof P=="function"?P:rl(+P),C):s},C.filter=function(P){return arguments.length?(t=typeof P=="function"?P:rl(!!P),C):t},C.touchable=function(P){return arguments.length?(l=typeof P=="function"?P:rl(!!P),C):l},C.extent=function(P){return arguments.length?(r=typeof P=="function"?P:rl([[+P[0][0],+P[0][1]],[+P[1][0],+P[1][1]]]),C):r},C.scaleExtent=function(P){return arguments.length?(a[0]=+P[0],a[1]=+P[1],C):[a[0],a[1]]},C.translateExtent=function(P){return arguments.length?(c[0][0]=+P[0][0],c[1][0]=+P[1][0],c[0][1]=+P[0][1],c[1][1]=+P[1][1],C):[[c[0][0],c[0][1]],[c[1][0],c[1][1]]]},C.constrain=function(P){return arguments.length?(o=P,C):o},C.duration=function(P){return arguments.length?(d=+P,C):d},C.interpolate=function(P){return arguments.length?(p=P,C):p},C.on=function(){var P=g.on.apply(g,arguments);return P===g?C:P},C.clickDistance=function(P){return arguments.length?(S=(P=+P)*P,C):Math.sqrt(S)},C.tapDistance=function(P){return arguments.length?(k=+P,C):k},C}const tn={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:t=>`Node type "${t}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:t=>`The old edge with id=${t} does not exist.`,error009:t=>`Marker type "${t}" doesn't exist.`,error008:(t,{id:r,sourceHandle:o,targetHandle:s})=>`Couldn't create edge for ${t} handle id: "${t==="source"?o:s}", edge id: ${r}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:t=>`Edge type "${t}" not found. Using fallback type "default".`,error012:t=>`Node with id "${t}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error013:(t="react")=>`It seems that you haven't loaded the styles. Please import '@xyflow/${t}/dist/style.css' or base.css to make sure everything is working properly.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},_i=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],Zm=["Enter"," ","Escape"],Jm={"node.a11yDescription.default":"Press enter or space to select a node. Press delete to remove it and escape to cancel.","node.a11yDescription.keyboardDisabled":"Press enter or space to select a node. You can then use the arrow keys to move the node around. Press delete to remove it and escape to cancel.","node.a11yDescription.ariaLiveMessage":({direction:t,x:r,y:o})=>`Moved selected node ${t}. New position, x: ${r}, y: ${o}`,"edge.a11yDescription.default":"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.","controls.ariaLabel":"Control Panel","controls.zoomIn.ariaLabel":"Zoom In","controls.zoomOut.ariaLabel":"Zoom Out","controls.fitView.ariaLabel":"Fit View","controls.interactive.ariaLabel":"Toggle Interactivity","minimap.ariaLabel":"Mini Map","handle.ariaLabel":"Handle"};var ao;(function(t){t.Strict="strict",t.Loose="loose"})(ao||(ao={}));var xr;(function(t){t.Free="free",t.Vertical="vertical",t.Horizontal="horizontal"})(xr||(xr={}));var ki;(function(t){t.Partial="partial",t.Full="full"})(ki||(ki={}));const eg={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};var Yn;(function(t){t.Bezier="default",t.Straight="straight",t.Step="step",t.SmoothStep="smoothstep",t.SimpleBezier="simplebezier"})(Yn||(Yn={}));var Cl;(function(t){t.Arrow="arrow",t.ArrowClosed="arrowclosed"})(Cl||(Cl={}));var ve;(function(t){t.Left="left",t.Top="top",t.Right="right",t.Bottom="bottom"})(ve||(ve={}));const ip={[ve.Left]:ve.Right,[ve.Right]:ve.Left,[ve.Top]:ve.Bottom,[ve.Bottom]:ve.Top};function tg(t){return t===null?null:t?"valid":"invalid"}const ng=t=>"id"in t&&"source"in t&&"target"in t,uE=t=>"id"in t&&"position"in t&&!("source"in t)&&!("target"in t),bc=t=>"id"in t&&"internals"in t&&!("source"in t)&&!("target"in t),Ii=(t,r=[0,0])=>{const{width:o,height:s}=En(t),l=t.origin??r,a=o*l[0],c=s*l[1];return{x:t.position.x-a,y:t.position.y-c}},aE=(t,r={nodeOrigin:[0,0]})=>{if(t.length===0)return{x:0,y:0,width:0,height:0};const o=t.reduce((s,l)=>{const a=typeof l=="string";let c=!r.nodeLookup&&!a?l:void 0;r.nodeLookup&&(c=a?r.nodeLookup.get(l):bc(l)?l:r.nodeLookup.get(l.id));const d=c?Tl(c,r.nodeOrigin):{x:0,y:0,x2:0,y2:0};return Vl(s,d)},{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return Ul(o)},zi=(t,r={})=>{if(t.size===0)return{x:0,y:0,width:0,height:0};let o={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return t.forEach(s=>{if(r.filter===void 0||r.filter(s)){const l=Tl(s);o=Vl(o,l)}}),Ul(o)},Hc=(t,r,[o,s,l]=[0,0,1],a=!1,c=!1)=>{const d={...Oi(r,[o,s,l]),width:r.width/l,height:r.height/l},p=[];for(const g of t.values()){const{measured:m,selectable:v=!0,hidden:y=!1}=g;if(c&&!v||y)continue;const w=m.width??g.width??g.initialWidth??null,_=m.height??g.height??g.initialHeight??null,S=Ni(d,fo(g)),k=(w??0)*(_??0),C=a&&S>0;(!g.internals.handleBounds||C||S>=k||g.dragging)&&p.push(g)}return p},cE=(t,r)=>{const o=new Set;return t.forEach(s=>{o.add(s.id)}),r.filter(s=>o.has(s.source)||o.has(s.target))};function fE(t,r){const o=new Map,s=r!=null&&r.nodes?new Set(r.nodes.map(l=>l.id)):null;return t.forEach(l=>{l.measured.width&&l.measured.height&&((r==null?void 0:r.includeHiddenNodes)||!l.hidden)&&(!s||s.has(l.id))&&o.set(l.id,l)}),o}async function dE({nodes:t,width:r,height:o,panZoom:s,minZoom:l,maxZoom:a},c){if(t.size===0)return Promise.resolve(!0);const d=fE(t,c),p=zi(d),g=Bc(p,r,o,(c==null?void 0:c.minZoom)??l,(c==null?void 0:c.maxZoom)??a,(c==null?void 0:c.padding)??.1);return await s.setViewport(g,{duration:c==null?void 0:c.duration,ease:c==null?void 0:c.ease,interpolate:c==null?void 0:c.interpolate}),Promise.resolve(!0)}function rg({nodeId:t,nextPosition:r,nodeLookup:o,nodeOrigin:s=[0,0],nodeExtent:l,onError:a}){const c=o.get(t),d=c.parentId?o.get(c.parentId):void 0,{x:p,y:g}=d?d.internals.positionAbsolute:{x:0,y:0},m=c.origin??s;let v=l;if(c.extent==="parent"&&!c.expandParent)if(!d)a==null||a("005",tn.error005());else{const w=d.measured.width,_=d.measured.height;w&&_&&(v=[[p,g],[p+w,g+_]])}else d&&ho(c.extent)&&(v=[[c.extent[0][0]+p,c.extent[0][1]+g],[c.extent[1][0]+p,c.extent[1][1]+g]]);const y=ho(v)?kr(r,v,c.measured):r;return(c.measured.width===void 0||c.measured.height===void 0)&&(a==null||a("015",tn.error015())),{position:{x:y.x-p+(c.measured.width??0)*m[0],y:y.y-g+(c.measured.height??0)*m[1]},positionAbsolute:y}}async function hE({nodesToRemove:t=[],edgesToRemove:r=[],nodes:o,edges:s,onBeforeDelete:l}){const a=new Set(t.map(y=>y.id)),c=[];for(const y of o){if(y.deletable===!1)continue;const w=a.has(y.id),_=!w&&y.parentId&&c.find(S=>S.id===y.parentId);(w||_)&&c.push(y)}const d=new Set(r.map(y=>y.id)),p=s.filter(y=>y.deletable!==!1),m=cE(c,p);for(const y of p)d.has(y.id)&&!m.find(_=>_.id===y.id)&&m.push(y);if(!l)return{edges:m,nodes:c};const v=await l({nodes:c,edges:m});return typeof v=="boolean"?v?{edges:m,nodes:c}:{edges:[],nodes:[]}:v}const co=(t,r=0,o=1)=>Math.min(Math.max(t,r),o),kr=(t={x:0,y:0},r,o)=>({x:co(t.x,r[0][0],r[1][0]-((o==null?void 0:o.width)??0)),y:co(t.y,r[0][1],r[1][1]-((o==null?void 0:o.height)??0))});function og(t,r,o){const{width:s,height:l}=En(o),{x:a,y:c}=o.internals.positionAbsolute;return kr(t,[[a,c],[a+s,c+l]],r)}const sp=(t,r,o)=>t<r?co(Math.abs(t-r),1,r)/r:t>o?-co(Math.abs(t-o),1,r)/r:0,ig=(t,r,o=15,s=40)=>{const l=sp(t.x,s,r.width-s)*o,a=sp(t.y,s,r.height-s)*o;return[l,a]},Vl=(t,r)=>({x:Math.min(t.x,r.x),y:Math.min(t.y,r.y),x2:Math.max(t.x2,r.x2),y2:Math.max(t.y2,r.y2)}),kc=({x:t,y:r,width:o,height:s})=>({x:t,y:r,x2:t+o,y2:r+s}),Ul=({x:t,y:r,x2:o,y2:s})=>({x:t,y:r,width:o-t,height:s-r}),fo=(t,r=[0,0])=>{var l,a;const{x:o,y:s}=bc(t)?t.internals.positionAbsolute:Ii(t,r);return{x:o,y:s,width:((l=t.measured)==null?void 0:l.width)??t.width??t.initialWidth??0,height:((a=t.measured)==null?void 0:a.height)??t.height??t.initialHeight??0}},Tl=(t,r=[0,0])=>{var l,a;const{x:o,y:s}=bc(t)?t.internals.positionAbsolute:Ii(t,r);return{x:o,y:s,x2:o+(((l=t.measured)==null?void 0:l.width)??t.width??t.initialWidth??0),y2:s+(((a=t.measured)==null?void 0:a.height)??t.height??t.initialHeight??0)}},sg=(t,r)=>Ul(Vl(kc(t),kc(r))),Ni=(t,r)=>{const o=Math.max(0,Math.min(t.x+t.width,r.x+r.width)-Math.max(t.x,r.x)),s=Math.max(0,Math.min(t.y+t.height,r.y+r.height)-Math.max(t.y,r.y));return Math.ceil(o*s)},lp=t=>Bt(t.width)&&Bt(t.height)&&Bt(t.x)&&Bt(t.y),Bt=t=>!isNaN(t)&&isFinite(t),pE=(t,r)=>{},Wl=(t,r=[1,1])=>({x:r[0]*Math.round(t.x/r[0]),y:r[1]*Math.round(t.y/r[1])}),Oi=({x:t,y:r},[o,s,l],a=!1,c=[1,1])=>{const d={x:(t-o)/l,y:(r-s)/l};return a?Wl(d,c):d},Pl=({x:t,y:r},[o,s,l])=>({x:t*l+o,y:r*l+s});function to(t,r){if(typeof t=="number")return Math.floor((r-r/(1+t))*.5);if(typeof t=="string"&&t.endsWith("px")){const o=parseFloat(t);if(!Number.isNaN(o))return Math.floor(o)}if(typeof t=="string"&&t.endsWith("%")){const o=parseFloat(t);if(!Number.isNaN(o))return Math.floor(r*o*.01)}return console.error(`[React Flow] The padding value "${t}" is invalid. Please provide a number or a string with a valid unit (px or %).`),0}function mE(t,r,o){if(typeof t=="string"||typeof t=="number"){const s=to(t,o),l=to(t,r);return{top:s,right:l,bottom:s,left:l,x:l*2,y:s*2}}if(typeof t=="object"){const s=to(t.top??t.y??0,o),l=to(t.bottom??t.y??0,o),a=to(t.left??t.x??0,r),c=to(t.right??t.x??0,r);return{top:s,right:c,bottom:l,left:a,x:a+c,y:s+l}}return{top:0,right:0,bottom:0,left:0,x:0,y:0}}function gE(t,r,o,s,l,a){const{x:c,y:d}=Pl(t,[r,o,s]),{x:p,y:g}=Pl({x:t.x+t.width,y:t.y+t.height},[r,o,s]),m=l-p,v=a-g;return{left:Math.floor(c),top:Math.floor(d),right:Math.floor(m),bottom:Math.floor(v)}}const Bc=(t,r,o,s,l,a)=>{const c=mE(a,r,o),d=(r-c.x)/t.width,p=(o-c.y)/t.height,g=Math.min(d,p),m=co(g,s,l),v=t.x+t.width/2,y=t.y+t.height/2,w=r/2-v*m,_=o/2-y*m,S=gE(t,w,_,m,r,o),k={left:Math.min(S.left-c.left,0),top:Math.min(S.top-c.top,0),right:Math.min(S.right-c.right,0),bottom:Math.min(S.bottom-c.bottom,0)};return{x:w-k.left+k.right,y:_-k.top+k.bottom,zoom:m}},Rl=()=>{var t;return typeof navigator<"u"&&((t=navigator==null?void 0:navigator.userAgent)==null?void 0:t.indexOf("Mac"))>=0};function ho(t){return t!==void 0&&t!=="parent"}function En(t){var r,o;return{width:((r=t.measured)==null?void 0:r.width)??t.width??t.initialWidth??0,height:((o=t.measured)==null?void 0:o.height)??t.height??t.initialHeight??0}}function lg(t){var r,o;return(((r=t.measured)==null?void 0:r.width)??t.width??t.initialWidth)!==void 0&&(((o=t.measured)==null?void 0:o.height)??t.height??t.initialHeight)!==void 0}function ug(t,r={width:0,height:0},o,s,l){const a={...t},c=s.get(o);if(c){const d=c.origin||l;a.x+=c.internals.positionAbsolute.x-(r.width??0)*d[0],a.y+=c.internals.positionAbsolute.y-(r.height??0)*d[1]}return a}function up(t,r){if(t.size!==r.size)return!1;for(const o of t)if(!r.has(o))return!1;return!0}function yE(){let t,r;return{promise:new Promise((s,l)=>{t=s,r=l}),resolve:t,reject:r}}function vE(t){return{...Jm,...t||{}}}function yi(t,{snapGrid:r=[0,0],snapToGrid:o=!1,transform:s,containerBounds:l}){const{x:a,y:c}=wn(t),d=Oi({x:a-((l==null?void 0:l.left)??0),y:c-((l==null?void 0:l.top)??0)},s),{x:p,y:g}=o?Wl(d,r):d;return{xSnapped:p,ySnapped:g,...d}}const Vc=t=>({width:t.offsetWidth,height:t.offsetHeight}),ag=t=>{var r;return((r=t==null?void 0:t.getRootNode)==null?void 0:r.call(t))||(window==null?void 0:window.document)},wE=["INPUT","SELECT","TEXTAREA"];function cg(t){var s,l;const r=((l=(s=t.composedPath)==null?void 0:s.call(t))==null?void 0:l[0])||t.target;return(r==null?void 0:r.nodeType)!==1?!1:wE.includes(r.nodeName)||r.hasAttribute("contenteditable")||!!r.closest(".nokey")}const fg=t=>"clientX"in t,wn=(t,r)=>{var a,c;const o=fg(t),s=o?t.clientX:(a=t.touches)==null?void 0:a[0].clientX,l=o?t.clientY:(c=t.touches)==null?void 0:c[0].clientY;return{x:s-((r==null?void 0:r.left)??0),y:l-((r==null?void 0:r.top)??0)}},ap=(t,r,o,s,l)=>{const a=r.querySelectorAll(`.${t}`);return!a||!a.length?null:Array.from(a).map(c=>{const d=c.getBoundingClientRect();return{id:c.getAttribute("data-handleid"),type:t,nodeId:l,position:c.getAttribute("data-handlepos"),x:(d.left-o.left)/s,y:(d.top-o.top)/s,...Vc(c)}})};function dg({sourceX:t,sourceY:r,targetX:o,targetY:s,sourceControlX:l,sourceControlY:a,targetControlX:c,targetControlY:d}){const p=t*.125+l*.375+c*.375+o*.125,g=r*.125+a*.375+d*.375+s*.125,m=Math.abs(p-t),v=Math.abs(g-r);return[p,g,m,v]}function ol(t,r){return t>=0?.5*t:r*25*Math.sqrt(-t)}function cp({pos:t,x1:r,y1:o,x2:s,y2:l,c:a}){switch(t){case ve.Left:return[r-ol(r-s,a),o];case ve.Right:return[r+ol(s-r,a),o];case ve.Top:return[r,o-ol(o-l,a)];case ve.Bottom:return[r,o+ol(l-o,a)]}}function hg({sourceX:t,sourceY:r,sourcePosition:o=ve.Bottom,targetX:s,targetY:l,targetPosition:a=ve.Top,curvature:c=.25}){const[d,p]=cp({pos:o,x1:t,y1:r,x2:s,y2:l,c}),[g,m]=cp({pos:a,x1:s,y1:l,x2:t,y2:r,c}),[v,y,w,_]=dg({sourceX:t,sourceY:r,targetX:s,targetY:l,sourceControlX:d,sourceControlY:p,targetControlX:g,targetControlY:m});return[`M${t},${r} C${d},${p} ${g},${m} ${s},${l}`,v,y,w,_]}function pg({sourceX:t,sourceY:r,targetX:o,targetY:s}){const l=Math.abs(o-t)/2,a=o<t?o+l:o-l,c=Math.abs(s-r)/2,d=s<r?s+c:s-c;return[a,d,l,c]}function xE({sourceNode:t,targetNode:r,selected:o=!1,zIndex:s=0,elevateOnSelect:l=!1}){if(!l)return s;const a=o||r.selected||t.selected,c=Math.max(t.internals.z||0,r.internals.z||0,1e3);return s+(a?c:0)}function SE({sourceNode:t,targetNode:r,width:o,height:s,transform:l}){const a=Vl(Tl(t),Tl(r));a.x===a.x2&&(a.x2+=1),a.y===a.y2&&(a.y2+=1);const c={x:-l[0]/l[2],y:-l[1]/l[2],width:o/l[2],height:s/l[2]};return Ni(c,Ul(a))>0}const EE=({source:t,sourceHandle:r,target:o,targetHandle:s})=>`xy-edge__${t}${r||""}-${o}${s||""}`,_E=(t,r)=>r.some(o=>o.source===t.source&&o.target===t.target&&(o.sourceHandle===t.sourceHandle||!o.sourceHandle&&!t.sourceHandle)&&(o.targetHandle===t.targetHandle||!o.targetHandle&&!t.targetHandle)),mg=(t,r)=>{if(!t.source||!t.target)return r;let o;return ng(t)?o={...t}:o={...t,id:EE(t)},_E(o,r)?r:(o.sourceHandle===null&&delete o.sourceHandle,o.targetHandle===null&&delete o.targetHandle,r.concat(o))};function gg({sourceX:t,sourceY:r,targetX:o,targetY:s}){const[l,a,c,d]=pg({sourceX:t,sourceY:r,targetX:o,targetY:s});return[`M ${t},${r}L ${o},${s}`,l,a,c,d]}const fp={[ve.Left]:{x:-1,y:0},[ve.Right]:{x:1,y:0},[ve.Top]:{x:0,y:-1},[ve.Bottom]:{x:0,y:1}},kE=({source:t,sourcePosition:r=ve.Bottom,target:o})=>r===ve.Left||r===ve.Right?t.x<o.x?{x:1,y:0}:{x:-1,y:0}:t.y<o.y?{x:0,y:1}:{x:0,y:-1},dp=(t,r)=>Math.sqrt(Math.pow(r.x-t.x,2)+Math.pow(r.y-t.y,2));function NE({source:t,sourcePosition:r=ve.Bottom,target:o,targetPosition:s=ve.Top,center:l,offset:a}){const c=fp[r],d=fp[s],p={x:t.x+c.x*a,y:t.y+c.y*a},g={x:o.x+d.x*a,y:o.y+d.y*a},m=kE({source:p,sourcePosition:r,target:g}),v=m.x!==0?"x":"y",y=m[v];let w=[],_,S;const k={x:0,y:0},C={x:0,y:0},[z,E,N,A]=pg({sourceX:t.x,sourceY:t.y,targetX:o.x,targetY:o.y});if(c[v]*d[v]===-1){_=l.x??z,S=l.y??E;const F=[{x:_,y:p.y},{x:_,y:g.y}],X=[{x:p.x,y:S},{x:g.x,y:S}];c[v]===y?w=v==="x"?F:X:w=v==="x"?X:F}else{const F=[{x:p.x,y:g.y}],X=[{x:g.x,y:p.y}];if(v==="x"?w=c.x===y?X:F:w=c.y===y?F:X,r===s){const G=Math.abs(t[v]-o[v]);if(G<=a){const P=Math.min(a-1,a-G);c[v]===y?k[v]=(p[v]>t[v]?-1:1)*P:C[v]=(g[v]>o[v]?-1:1)*P}}if(r!==s){const G=v==="x"?"y":"x",P=c[v]===d[G],W=p[G]>g[G],b=p[G]<g[G];(c[v]===1&&(!P&&W||P&&b)||c[v]!==1&&(!P&&b||P&&W))&&(w=v==="x"?F:X)}const K={x:p.x+k.x,y:p.y+k.y},Z={x:g.x+C.x,y:g.y+C.y},J=Math.max(Math.abs(K.x-w[0].x),Math.abs(Z.x-w[0].x)),ee=Math.max(Math.abs(K.y-w[0].y),Math.abs(Z.y-w[0].y));J>=ee?(_=(K.x+Z.x)/2,S=w[0].y):(_=w[0].x,S=(K.y+Z.y)/2)}return[[t,{x:p.x+k.x,y:p.y+k.y},...w,{x:g.x+C.x,y:g.y+C.y},o],_,S,N,A]}function CE(t,r,o,s){const l=Math.min(dp(t,r)/2,dp(r,o)/2,s),{x:a,y:c}=r;if(t.x===a&&a===o.x||t.y===c&&c===o.y)return`L${a} ${c}`;if(t.y===c){const g=t.x<o.x?-1:1,m=t.y<o.y?1:-1;return`L ${a+l*g},${c}Q ${a},${c} ${a},${c+l*m}`}const d=t.x<o.x?1:-1,p=t.y<o.y?-1:1;return`L ${a},${c+l*p}Q ${a},${c} ${a+l*d},${c}`}function Nc({sourceX:t,sourceY:r,sourcePosition:o=ve.Bottom,targetX:s,targetY:l,targetPosition:a=ve.Top,borderRadius:c=5,centerX:d,centerY:p,offset:g=20}){const[m,v,y,w,_]=NE({source:{x:t,y:r},sourcePosition:o,target:{x:s,y:l},targetPosition:a,center:{x:d,y:p},offset:g});return[m.reduce((k,C,z)=>{let E="";return z>0&&z<m.length-1?E=CE(m[z-1],C,m[z+1],c):E=`${z===0?"M":"L"}${C.x} ${C.y}`,k+=E,k},""),v,y,w,_]}function hp(t){var r;return t&&!!(t.internals.handleBounds||(r=t.handles)!=null&&r.length)&&!!(t.measured.width||t.width||t.initialWidth)}function TE(t){var v;const{sourceNode:r,targetNode:o}=t;if(!hp(r)||!hp(o))return null;const s=r.internals.handleBounds||pp(r.handles),l=o.internals.handleBounds||pp(o.handles),a=mp((s==null?void 0:s.source)??[],t.sourceHandle),c=mp(t.connectionMode===ao.Strict?(l==null?void 0:l.target)??[]:((l==null?void 0:l.target)??[]).concat((l==null?void 0:l.source)??[]),t.targetHandle);if(!a||!c)return(v=t.onError)==null||v.call(t,"008",tn.error008(a?"target":"source",{id:t.id,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle})),null;const d=(a==null?void 0:a.position)||ve.Bottom,p=(c==null?void 0:c.position)||ve.Top,g=Ci(r,a,d),m=Ci(o,c,p);return{sourceX:g.x,sourceY:g.y,targetX:m.x,targetY:m.y,sourcePosition:d,targetPosition:p}}function pp(t){if(!t)return null;const r=[],o=[];for(const s of t)s.width=s.width??1,s.height=s.height??1,s.type==="source"?r.push(s):s.type==="target"&&o.push(s);return{source:r,target:o}}function Ci(t,r,o=ve.Left,s=!1){const l=((r==null?void 0:r.x)??0)+t.internals.positionAbsolute.x,a=((r==null?void 0:r.y)??0)+t.internals.positionAbsolute.y,{width:c,height:d}=r??En(t);if(s)return{x:l+c/2,y:a+d/2};switch((r==null?void 0:r.position)??o){case ve.Top:return{x:l+c/2,y:a};case ve.Right:return{x:l+c,y:a+d/2};case ve.Bottom:return{x:l+c/2,y:a+d};case ve.Left:return{x:l,y:a+d/2}}}function mp(t,r){return t&&(r?t.find(o=>o.id===r):t[0])||null}function Cc(t,r){return t?typeof t=="string"?t:`${r?`${r}__`:""}${Object.keys(t).sort().map(s=>`${s}=${t[s]}`).join("&")}`:""}function PE(t,{id:r,defaultColor:o,defaultMarkerStart:s,defaultMarkerEnd:l}){const a=new Set;return t.reduce((c,d)=>([d.markerStart||s,d.markerEnd||l].forEach(p=>{if(p&&typeof p=="object"){const g=Cc(p,r);a.has(g)||(c.push({id:g,color:p.color||o,...p}),a.add(g))}}),c),[]).sort((c,d)=>c.id.localeCompare(d.id))}const Uc={nodeOrigin:[0,0],nodeExtent:_i,elevateNodesOnSelect:!0,defaults:{}},RE={...Uc,checkEquality:!0};function Wc(t,r){const o={...t};for(const s in r)r[s]!==void 0&&(o[s]=r[s]);return o}function ME(t,r,o){const s=Wc(Uc,o);for(const l of t.values())if(l.parentId)Xc(l,t,r,s);else{const a=Ii(l,s.nodeOrigin),c=ho(l.extent)?l.extent:s.nodeExtent,d=kr(a,c,En(l));l.internals.positionAbsolute=d}}function Tc(t,r,o,s){var p,g;const l=Wc(RE,s);let a=t.length>0;const c=new Map(r),d=l!=null&&l.elevateNodesOnSelect?1e3:0;r.clear(),o.clear();for(const m of t){let v=c.get(m.id);if(l.checkEquality&&m===(v==null?void 0:v.internals.userNode))r.set(m.id,v);else{const y=Ii(m,l.nodeOrigin),w=ho(m.extent)?m.extent:l.nodeExtent,_=kr(y,w,En(m));v={...l.defaults,...m,measured:{width:(p=m.measured)==null?void 0:p.width,height:(g=m.measured)==null?void 0:g.height},internals:{positionAbsolute:_,handleBounds:m.measured?v==null?void 0:v.internals.handleBounds:void 0,z:yg(m,d),userNode:m}},r.set(m.id,v)}(v.measured===void 0||v.measured.width===void 0||v.measured.height===void 0)&&!v.hidden&&(a=!1),m.parentId&&Xc(v,r,o,s)}return a}function AE(t,r){if(!t.parentId)return;const o=r.get(t.parentId);o?o.set(t.id,t):r.set(t.parentId,new Map([[t.id,t]]))}function Xc(t,r,o,s){const{elevateNodesOnSelect:l,nodeOrigin:a,nodeExtent:c}=Wc(Uc,s),d=t.parentId,p=r.get(d);if(!p){console.warn(`Parent node ${d} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);return}AE(t,o);const g=l?1e3:0,{x:m,y:v,z:y}=LE(t,p,a,c,g),{positionAbsolute:w}=t.internals,_=m!==w.x||v!==w.y;(_||y!==t.internals.z)&&r.set(t.id,{...t,internals:{...t.internals,positionAbsolute:_?{x:m,y:v}:w,z:y}})}function yg(t,r){return(Bt(t.zIndex)?t.zIndex:0)+(t.selected?r:0)}function LE(t,r,o,s,l){const{x:a,y:c}=r.internals.positionAbsolute,d=En(t),p=Ii(t,o),g=ho(t.extent)?kr(p,t.extent,d):p;let m=kr({x:a+g.x,y:c+g.y},s,d);t.extent==="parent"&&(m=og(m,d,r));const v=yg(t,l),y=r.internals.z??0;return{x:m.x,y:m.y,z:y>v?y:v}}function Yc(t,r,o,s=[0,0]){var c;const l=[],a=new Map;for(const d of t){const p=r.get(d.parentId);if(!p)continue;const g=((c=a.get(d.parentId))==null?void 0:c.expandedRect)??fo(p),m=sg(g,d.rect);a.set(d.parentId,{expandedRect:m,parent:p})}return a.size>0&&a.forEach(({expandedRect:d,parent:p},g)=>{var E;const m=p.internals.positionAbsolute,v=En(p),y=p.origin??s,w=d.x<m.x?Math.round(Math.abs(m.x-d.x)):0,_=d.y<m.y?Math.round(Math.abs(m.y-d.y)):0,S=Math.max(v.width,Math.round(d.width)),k=Math.max(v.height,Math.round(d.height)),C=(S-v.width)*y[0],z=(k-v.height)*y[1];(w>0||_>0||C||z)&&(l.push({id:g,type:"position",position:{x:p.position.x-w+C,y:p.position.y-_+z}}),(E=o.get(g))==null||E.forEach(N=>{t.some(A=>A.id===N.id)||l.push({id:N.id,type:"position",position:{x:N.position.x+w,y:N.position.y+_}})})),(v.width<d.width||v.height<d.height||w||_)&&l.push({id:g,type:"dimensions",setAttributes:!0,dimensions:{width:S+(w?y[0]*w-C:0),height:k+(_?y[1]*_-z:0)}})}),l}function IE(t,r,o,s,l,a){const c=s==null?void 0:s.querySelector(".xyflow__viewport");let d=!1;if(!c)return{changes:[],updatedInternals:d};const p=[],g=window.getComputedStyle(c),{m22:m}=new window.DOMMatrixReadOnly(g.transform),v=[];for(const y of t.values()){const w=r.get(y.id);if(!w)continue;if(w.hidden){r.set(w.id,{...w,internals:{...w.internals,handleBounds:void 0}}),d=!0;continue}const _=Vc(y.nodeElement),S=w.measured.width!==_.width||w.measured.height!==_.height;if(!!(_.width&&_.height&&(S||!w.internals.handleBounds||y.force))){const C=y.nodeElement.getBoundingClientRect(),z=ho(w.extent)?w.extent:a;let{positionAbsolute:E}=w.internals;w.parentId&&w.extent==="parent"?E=og(E,_,r.get(w.parentId)):z&&(E=kr(E,z,_));const N={...w,measured:_,internals:{...w.internals,positionAbsolute:E,handleBounds:{source:ap("source",y.nodeElement,C,m,w.id),target:ap("target",y.nodeElement,C,m,w.id)}}};r.set(w.id,N),w.parentId&&Xc(N,r,o,{nodeOrigin:l}),d=!0,S&&(p.push({id:w.id,type:"dimensions",dimensions:_}),w.expandParent&&w.parentId&&v.push({id:w.id,parentId:w.parentId,rect:fo(N,l)}))}}if(v.length>0){const y=Yc(v,r,o,l);p.push(...y)}return{changes:p,updatedInternals:d}}async function zE({delta:t,panZoom:r,transform:o,translateExtent:s,width:l,height:a}){if(!r||!t.x&&!t.y)return Promise.resolve(!1);const c=await r.setViewportConstrained({x:o[0]+t.x,y:o[1]+t.y,zoom:o[2]},[[0,0],[l,a]],s),d=!!c&&(c.x!==o[0]||c.y!==o[1]||c.k!==o[2]);return Promise.resolve(d)}function gp(t,r,o,s,l,a){let c=l;const d=s.get(c)||new Map;s.set(c,d.set(o,r)),c=`${l}-${t}`;const p=s.get(c)||new Map;if(s.set(c,p.set(o,r)),a){c=`${l}-${t}-${a}`;const g=s.get(c)||new Map;s.set(c,g.set(o,r))}}function vg(t,r,o){t.clear(),r.clear();for(const s of o){const{source:l,target:a,sourceHandle:c=null,targetHandle:d=null}=s,p={edgeId:s.id,source:l,target:a,sourceHandle:c,targetHandle:d},g=`${l}-${c}--${a}-${d}`,m=`${a}-${d}--${l}-${c}`;gp("source",p,m,t,l,c),gp("target",p,g,t,a,d),r.set(s.id,s)}}function wg(t,r){if(!t.parentId)return!1;const o=r.get(t.parentId);return o?o.selected?!0:wg(o,r):!1}function yp(t,r,o){var l;let s=t;do{if((l=s==null?void 0:s.matches)!=null&&l.call(s,r))return!0;if(s===o)return!1;s=s==null?void 0:s.parentElement}while(s);return!1}function OE(t,r,o,s){const l=new Map;for(const[a,c]of t)if((c.selected||c.id===s)&&(!c.parentId||!wg(c,t))&&(c.draggable||r&&typeof c.draggable>"u")){const d=t.get(a);d&&l.set(a,{id:a,position:d.position||{x:0,y:0},distance:{x:o.x-d.internals.positionAbsolute.x,y:o.y-d.internals.positionAbsolute.y},extent:d.extent,parentId:d.parentId,origin:d.origin,expandParent:d.expandParent,internals:{positionAbsolute:d.internals.positionAbsolute||{x:0,y:0}},measured:{width:d.measured.width??0,height:d.measured.height??0}})}return l}function tc({nodeId:t,dragItems:r,nodeLookup:o,dragging:s=!0}){var c,d,p;const l=[];for(const[g,m]of r){const v=(c=o.get(g))==null?void 0:c.internals.userNode;v&&l.push({...v,position:m.position,dragging:s})}if(!t)return[l[0],l];const a=(d=o.get(t))==null?void 0:d.internals.userNode;return[a?{...a,position:((p=r.get(t))==null?void 0:p.position)||a.position,dragging:s}:l[0],l]}function DE({onNodeMouseDown:t,getStoreItems:r,onDragStart:o,onDrag:s,onDragStop:l}){let a={x:null,y:null},c=0,d=new Map,p=!1,g={x:0,y:0},m=null,v=!1,y=null,w=!1;function _({noDragClassName:k,handleSelector:C,domNode:z,isSelectable:E,nodeId:N,nodeClickDistance:A=0}){y=kt(z);function $({x:Z,y:J},ee){const{nodeLookup:G,nodeExtent:P,snapGrid:W,snapToGrid:b,nodeOrigin:H,onNodeDrag:D,onSelectionDrag:I,onError:B,updateNodePositions:T}=r();a={x:Z,y:J};let j=!1,oe={x:0,y:0,x2:0,y2:0};if(d.size>1&&P){const ie=zi(d);oe=kc(ie)}for(const[ie,ue]of d){if(!G.has(ie))continue;let ae={x:Z-ue.distance.x,y:J-ue.distance.y};b&&(ae=Wl(ae,W));let fe=[[P[0][0],P[0][1]],[P[1][0],P[1][1]]];if(d.size>1&&P&&!ue.extent){const{positionAbsolute:Se}=ue.internals,_e=Se.x-oe.x+P[0][0],ye=Se.x+ue.measured.width-oe.x2+P[1][0],Pe=Se.y-oe.y+P[0][1],Ne=Se.y+ue.measured.height-oe.y2+P[1][1];fe=[[_e,Pe],[ye,Ne]]}const{position:re,positionAbsolute:ce}=rg({nodeId:ie,nextPosition:ae,nodeLookup:G,nodeExtent:fe,nodeOrigin:H,onError:B});j=j||ue.position.x!==re.x||ue.position.y!==re.y,ue.position=re,ue.internals.positionAbsolute=ce}if(j&&(T(d,!0),ee&&(s||D||!N&&I))){const[ie,ue]=tc({nodeId:N,dragItems:d,nodeLookup:G});s==null||s(ee,d,ie,ue),D==null||D(ee,ie,ue),N||I==null||I(ee,ue)}}async function F(){if(!m)return;const{transform:Z,panBy:J,autoPanSpeed:ee,autoPanOnNodeDrag:G}=r();if(!G){p=!1,cancelAnimationFrame(c);return}const[P,W]=ig(g,m,ee);(P!==0||W!==0)&&(a.x=(a.x??0)-P/Z[2],a.y=(a.y??0)-W/Z[2],await J({x:P,y:W})&&$(a,null)),c=requestAnimationFrame(F)}function X(Z){var j;const{nodeLookup:J,multiSelectionActive:ee,nodesDraggable:G,transform:P,snapGrid:W,snapToGrid:b,selectNodesOnDrag:H,onNodeDragStart:D,onSelectionDragStart:I,unselectNodesAndEdges:B}=r();v=!0,(!H||!E)&&!ee&&N&&((j=J.get(N))!=null&&j.selected||B()),E&&H&&N&&(t==null||t(N));const T=yi(Z.sourceEvent,{transform:P,snapGrid:W,snapToGrid:b,containerBounds:m});if(a=T,d=OE(J,G,T,N),d.size>0&&(o||D||!N&&I)){const[oe,ie]=tc({nodeId:N,dragItems:d,nodeLookup:J});o==null||o(Z.sourceEvent,d,oe,ie),D==null||D(Z.sourceEvent,oe,ie),N||I==null||I(Z.sourceEvent,ie)}}const K=Dm().clickDistance(A).on("start",Z=>{const{domNode:J,nodeDragThreshold:ee,transform:G,snapGrid:P,snapToGrid:W}=r();m=(J==null?void 0:J.getBoundingClientRect())||null,w=!1,ee===0&&X(Z),a=yi(Z.sourceEvent,{transform:G,snapGrid:P,snapToGrid:W,containerBounds:m}),g=wn(Z.sourceEvent,m)}).on("drag",Z=>{const{autoPanOnNodeDrag:J,transform:ee,snapGrid:G,snapToGrid:P,nodeDragThreshold:W,nodeLookup:b}=r(),H=yi(Z.sourceEvent,{transform:ee,snapGrid:G,snapToGrid:P,containerBounds:m});if((Z.sourceEvent.type==="touchmove"&&Z.sourceEvent.touches.length>1||N&&!b.has(N))&&(w=!0),!w){if(!p&&J&&v&&(p=!0,F()),!v){const D=H.xSnapped-(a.x??0),I=H.ySnapped-(a.y??0);Math.sqrt(D*D+I*I)>W&&X(Z)}(a.x!==H.xSnapped||a.y!==H.ySnapped)&&d&&v&&(g=wn(Z.sourceEvent,m),$(H,Z.sourceEvent))}}).on("end",Z=>{if(!(!v||w)&&(p=!1,v=!1,cancelAnimationFrame(c),d.size>0)){const{nodeLookup:J,updateNodePositions:ee,onNodeDragStop:G,onSelectionDragStop:P}=r();if(ee(d,!1),l||G||!N&&P){const[W,b]=tc({nodeId:N,dragItems:d,nodeLookup:J,dragging:!1});l==null||l(Z.sourceEvent,d,W,b),G==null||G(Z.sourceEvent,W,b),N||P==null||P(Z.sourceEvent,b)}}}).filter(Z=>{const J=Z.target;return!Z.button&&(!k||!yp(J,`.${k}`,z))&&(!C||yp(J,C,z))});y.call(K)}function S(){y==null||y.on(".drag",null)}return{update:_,destroy:S}}function $E(t,r,o){const s=[],l={x:t.x-o,y:t.y-o,width:o*2,height:o*2};for(const a of r.values())Ni(l,fo(a))>0&&s.push(a);return s}const jE=250;function FE(t,r,o,s){var d,p;let l=[],a=1/0;const c=$E(t,o,r+jE);for(const g of c){const m=[...((d=g.internals.handleBounds)==null?void 0:d.source)??[],...((p=g.internals.handleBounds)==null?void 0:p.target)??[]];for(const v of m){if(s.nodeId===v.nodeId&&s.type===v.type&&s.id===v.id)continue;const{x:y,y:w}=Ci(g,v,v.position,!0),_=Math.sqrt(Math.pow(y-t.x,2)+Math.pow(w-t.y,2));_>r||(_<a?(l=[{...v,x:y,y:w}],a=_):_===a&&l.push({...v,x:y,y:w}))}}if(!l.length)return null;if(l.length>1){const g=s.type==="source"?"target":"source";return l.find(m=>m.type===g)??l[0]}return l[0]}function xg(t,r,o,s,l,a=!1){var g,m,v;const c=s.get(t);if(!c)return null;const d=l==="strict"?(g=c.internals.handleBounds)==null?void 0:g[r]:[...((m=c.internals.handleBounds)==null?void 0:m.source)??[],...((v=c.internals.handleBounds)==null?void 0:v.target)??[]],p=(o?d==null?void 0:d.find(y=>y.id===o):d==null?void 0:d[0])??null;return p&&a?{...p,...Ci(c,p,p.position,!0)}:p}function Sg(t,r){return t||(r!=null&&r.classList.contains("target")?"target":r!=null&&r.classList.contains("source")?"source":null)}function bE(t,r){let o=null;return r?o=!0:t&&!r&&(o=!1),o}const Eg=()=>!0;function HE(t,{connectionMode:r,connectionRadius:o,handleId:s,nodeId:l,edgeUpdaterType:a,isTarget:c,domNode:d,nodeLookup:p,lib:g,autoPanOnConnect:m,flowId:v,panBy:y,cancelConnection:w,onConnectStart:_,onConnect:S,onConnectEnd:k,isValidConnection:C=Eg,onReconnectEnd:z,updateConnection:E,getTransform:N,getFromHandle:A,autoPanSpeed:$}){const F=ag(t.target);let X=0,K;const{x:Z,y:J}=wn(t),ee=F==null?void 0:F.elementFromPoint(Z,J),G=Sg(a,ee),P=d==null?void 0:d.getBoundingClientRect();if(!P||!G)return;const W=xg(l,G,s,p,r);if(!W)return;let b=wn(t,P),H=!1,D=null,I=!1,B=null;function T(){if(!m||!P)return;const[ce,Se]=ig(b,P,$);y({x:ce,y:Se}),X=requestAnimationFrame(T)}const j={...W,nodeId:l,type:G,position:W.position},oe=p.get(l),ue={inProgress:!0,isValid:null,from:Ci(oe,j,ve.Left,!0),fromHandle:j,fromPosition:j.position,fromNode:oe,to:b,toHandle:null,toPosition:ip[j.position],toNode:null};E(ue);let ae=ue;_==null||_(t,{nodeId:l,handleId:s,handleType:G});function fe(ce){if(!A()||!j){re(ce);return}const Se=N();b=wn(ce,P),K=FE(Oi(b,Se,!1,[1,1]),o,p,j),H||(T(),H=!0);const _e=_g(ce,{handle:K,connectionMode:r,fromNodeId:l,fromHandleId:s,fromType:c?"target":"source",isValidConnection:C,doc:F,lib:g,flowId:v,nodeLookup:p});B=_e.handleDomNode,D=_e.connection,I=bE(!!K,_e.isValid);const ye={...ae,isValid:I,to:_e.toHandle&&I?Pl({x:_e.toHandle.x,y:_e.toHandle.y},Se):b,toHandle:_e.toHandle,toPosition:I&&_e.toHandle?_e.toHandle.position:ip[j.position],toNode:_e.toHandle?p.get(_e.toHandle.nodeId):null};I&&K&&ae.toHandle&&ye.toHandle&&ae.toHandle.type===ye.toHandle.type&&ae.toHandle.nodeId===ye.toHandle.nodeId&&ae.toHandle.id===ye.toHandle.id&&ae.to.x===ye.to.x&&ae.to.y===ye.to.y||(E(ye),ae=ye)}function re(ce){(K||B)&&D&&I&&(S==null||S(D));const{inProgress:Se,..._e}=ae,ye={..._e,toPosition:ae.toHandle?ae.toPosition:null};k==null||k(ce,ye),a&&(z==null||z(ce,ye)),w(),cancelAnimationFrame(X),H=!1,I=!1,D=null,B=null,F.removeEventListener("mousemove",fe),F.removeEventListener("mouseup",re),F.removeEventListener("touchmove",fe),F.removeEventListener("touchend",re)}F.addEventListener("mousemove",fe),F.addEventListener("mouseup",re),F.addEventListener("touchmove",fe),F.addEventListener("touchend",re)}function _g(t,{handle:r,connectionMode:o,fromNodeId:s,fromHandleId:l,fromType:a,doc:c,lib:d,flowId:p,isValidConnection:g=Eg,nodeLookup:m}){const v=a==="target",y=r?c.querySelector(`.${d}-flow__handle[data-id="${p}-${r==null?void 0:r.nodeId}-${r==null?void 0:r.id}-${r==null?void 0:r.type}"]`):null,{x:w,y:_}=wn(t),S=c.elementFromPoint(w,_),k=S!=null&&S.classList.contains(`${d}-flow__handle`)?S:y,C={handleDomNode:k,isValid:!1,connection:null,toHandle:null};if(k){const z=Sg(void 0,k),E=k.getAttribute("data-nodeid"),N=k.getAttribute("data-handleid"),A=k.classList.contains("connectable"),$=k.classList.contains("connectableend");if(!E||!z)return C;const F={source:v?E:s,sourceHandle:v?N:l,target:v?s:E,targetHandle:v?l:N};C.connection=F;const K=A&&$&&(o===ao.Strict?v&&z==="source"||!v&&z==="target":E!==s||N!==l);C.isValid=K&&g(F),C.toHandle=xg(E,z,N,m,o,!0)}return C}const Pc={onPointerDown:HE,isValid:_g};function BE({domNode:t,panZoom:r,getTransform:o,getViewScale:s}){const l=kt(t);function a({translateExtent:d,width:p,height:g,zoomStep:m=10,pannable:v=!0,zoomable:y=!0,inversePan:w=!1}){const _=E=>{const N=o();if(E.sourceEvent.type!=="wheel"||!r)return;const A=-E.sourceEvent.deltaY*(E.sourceEvent.deltaMode===1?.05:E.sourceEvent.deltaMode?1:.002)*m,$=N[2]*Math.pow(2,A);r.scaleTo($)};let S=[0,0];const k=E=>{(E.sourceEvent.type==="mousedown"||E.sourceEvent.type==="touchstart")&&(S=[E.sourceEvent.clientX??E.sourceEvent.touches[0].clientX,E.sourceEvent.clientY??E.sourceEvent.touches[0].clientY])},C=E=>{const N=o();if(E.sourceEvent.type!=="mousemove"&&E.sourceEvent.type!=="touchmove"||!r)return;const A=[E.sourceEvent.clientX??E.sourceEvent.touches[0].clientX,E.sourceEvent.clientY??E.sourceEvent.touches[0].clientY],$=[A[0]-S[0],A[1]-S[1]];S=A;const F=s()*Math.max(N[2],Math.log(N[2]))*(w?-1:1),X={x:N[0]-$[0]*F,y:N[1]-$[1]*F},K=[[0,0],[p,g]];r.setViewportConstrained({x:X.x,y:X.y,zoom:N[2]},K,d)},z=Gm().on("start",k).on("zoom",v?C:null).on("zoom.wheel",y?_:null);l.call(z,{})}function c(){l.on("zoom",null)}return{update:a,destroy:c,pointer:bt}}const VE=(t,r)=>t.x!==r.x||t.y!==r.y||t.zoom!==r.k,Xl=t=>({x:t.x,y:t.y,zoom:t.k}),nc=({x:t,y:r,zoom:o})=>Bl.translate(t,r).scale(o),ro=(t,r)=>t.target.closest(`.${r}`),kg=(t,r)=>r===2&&Array.isArray(t)&&t.includes(2),UE=t=>((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2,rc=(t,r=0,o=UE,s=()=>{})=>{const l=typeof r=="number"&&r>0;return l||s(),l?t.transition().duration(r).ease(o).on("end",s):t},Ng=t=>{const r=t.ctrlKey&&Rl()?10:1;return-t.deltaY*(t.deltaMode===1?.05:t.deltaMode?1:.002)*r};function WE({zoomPanValues:t,noWheelClassName:r,d3Selection:o,d3Zoom:s,panOnScrollMode:l,panOnScrollSpeed:a,zoomOnPinch:c,onPanZoomStart:d,onPanZoom:p,onPanZoomEnd:g}){return m=>{if(ro(m,r))return!1;m.preventDefault(),m.stopImmediatePropagation();const v=o.property("__zoom").k||1;if(m.ctrlKey&&c){const k=bt(m),C=Ng(m),z=v*Math.pow(2,C);s.scaleTo(o,z,k,m);return}const y=m.deltaMode===1?20:1;let w=l===xr.Vertical?0:m.deltaX*y,_=l===xr.Horizontal?0:m.deltaY*y;!Rl()&&m.shiftKey&&l!==xr.Vertical&&(w=m.deltaY*y,_=0),s.translateBy(o,-(w/v)*a,-(_/v)*a,{internal:!0});const S=Xl(o.property("__zoom"));clearTimeout(t.panScrollTimeout),t.isPanScrolling||(t.isPanScrolling=!0,d==null||d(m,S)),t.isPanScrolling&&(p==null||p(m,S),t.panScrollTimeout=setTimeout(()=>{g==null||g(m,S),t.isPanScrolling=!1},150))}}function XE({noWheelClassName:t,preventScrolling:r,d3ZoomHandler:o}){return function(s,l){const a=s.type==="wheel",c=!r&&a&&!s.ctrlKey,d=ro(s,t);if(s.ctrlKey&&a&&d&&s.preventDefault(),c||d)return null;s.preventDefault(),o.call(this,s,l)}}function YE({zoomPanValues:t,onDraggingChange:r,onPanZoomStart:o}){return s=>{var a,c,d;if((a=s.sourceEvent)!=null&&a.internal)return;const l=Xl(s.transform);t.mouseButton=((c=s.sourceEvent)==null?void 0:c.button)||0,t.isZoomingOrPanning=!0,t.prevViewport=l,((d=s.sourceEvent)==null?void 0:d.type)==="mousedown"&&r(!0),o&&(o==null||o(s.sourceEvent,l))}}function qE({zoomPanValues:t,panOnDrag:r,onPaneContextMenu:o,onTransformChange:s,onPanZoom:l}){return a=>{var c,d;t.usedRightMouseButton=!!(o&&kg(r,t.mouseButton??0)),(c=a.sourceEvent)!=null&&c.sync||s([a.transform.x,a.transform.y,a.transform.k]),l&&!((d=a.sourceEvent)!=null&&d.internal)&&(l==null||l(a.sourceEvent,Xl(a.transform)))}}function QE({zoomPanValues:t,panOnDrag:r,panOnScroll:o,onDraggingChange:s,onPanZoomEnd:l,onPaneContextMenu:a}){return c=>{var d;if(!((d=c.sourceEvent)!=null&&d.internal)&&(t.isZoomingOrPanning=!1,a&&kg(r,t.mouseButton??0)&&!t.usedRightMouseButton&&c.sourceEvent&&a(c.sourceEvent),t.usedRightMouseButton=!1,s(!1),l&&VE(t.prevViewport,c.transform))){const p=Xl(c.transform);t.prevViewport=p,clearTimeout(t.timerId),t.timerId=setTimeout(()=>{l==null||l(c.sourceEvent,p)},o?150:0)}}}function KE({zoomActivationKeyPressed:t,zoomOnScroll:r,zoomOnPinch:o,panOnDrag:s,panOnScroll:l,zoomOnDoubleClick:a,userSelectionActive:c,noWheelClassName:d,noPanClassName:p,lib:g}){return m=>{var _;const v=t||r,y=o&&m.ctrlKey;if(m.button===1&&m.type==="mousedown"&&(ro(m,`${g}-flow__node`)||ro(m,`${g}-flow__edge`)))return!0;if(!s&&!v&&!l&&!a&&!o||c||ro(m,d)&&m.type==="wheel"||ro(m,p)&&(m.type!=="wheel"||l&&m.type==="wheel"&&!t)||!o&&m.ctrlKey&&m.type==="wheel")return!1;if(!o&&m.type==="touchstart"&&((_=m.touches)==null?void 0:_.length)>1)return m.preventDefault(),!1;if(!v&&!l&&!y&&m.type==="wheel"||!s&&(m.type==="mousedown"||m.type==="touchstart")||Array.isArray(s)&&!s.includes(m.button)&&m.type==="mousedown")return!1;const w=Array.isArray(s)&&s.includes(m.button)||!m.button||m.button<=1;return(!m.ctrlKey||m.type==="wheel")&&w}}function GE({domNode:t,minZoom:r,maxZoom:o,paneClickDistance:s,translateExtent:l,viewport:a,onPanZoom:c,onPanZoomStart:d,onPanZoomEnd:p,onDraggingChange:g}){const m={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},v=t.getBoundingClientRect(),y=Gm().clickDistance(!Bt(s)||s<0?0:s).scaleExtent([r,o]).translateExtent(l),w=kt(t).call(y);E({x:a.x,y:a.y,zoom:co(a.zoom,r,o)},[[0,0],[v.width,v.height]],l);const _=w.on("wheel.zoom"),S=w.on("dblclick.zoom");y.wheelDelta(Ng);function k(ee,G){return w?new Promise(P=>{y==null||y.interpolate((G==null?void 0:G.interpolate)==="linear"?gi:hl).transform(rc(w,G==null?void 0:G.duration,G==null?void 0:G.ease,()=>P(!0)),ee)}):Promise.resolve(!1)}function C({noWheelClassName:ee,noPanClassName:G,onPaneContextMenu:P,userSelectionActive:W,panOnScroll:b,panOnDrag:H,panOnScrollMode:D,panOnScrollSpeed:I,preventScrolling:B,zoomOnPinch:T,zoomOnScroll:j,zoomOnDoubleClick:oe,zoomActivationKeyPressed:ie,lib:ue,onTransformChange:ae}){W&&!m.isZoomingOrPanning&&z();const re=b&&!ie&&!W?WE({zoomPanValues:m,noWheelClassName:ee,d3Selection:w,d3Zoom:y,panOnScrollMode:D,panOnScrollSpeed:I,zoomOnPinch:T,onPanZoomStart:d,onPanZoom:c,onPanZoomEnd:p}):XE({noWheelClassName:ee,preventScrolling:B,d3ZoomHandler:_});if(w.on("wheel.zoom",re,{passive:!1}),!W){const Se=YE({zoomPanValues:m,onDraggingChange:g,onPanZoomStart:d});y.on("start",Se);const _e=qE({zoomPanValues:m,panOnDrag:H,onPaneContextMenu:!!P,onPanZoom:c,onTransformChange:ae});y.on("zoom",_e);const ye=QE({zoomPanValues:m,panOnDrag:H,panOnScroll:b,onPaneContextMenu:P,onPanZoomEnd:p,onDraggingChange:g});y.on("end",ye)}const ce=KE({zoomActivationKeyPressed:ie,panOnDrag:H,zoomOnScroll:j,panOnScroll:b,zoomOnDoubleClick:oe,zoomOnPinch:T,userSelectionActive:W,noPanClassName:G,noWheelClassName:ee,lib:ue});y.filter(ce),oe?w.on("dblclick.zoom",S):w.on("dblclick.zoom",null)}function z(){y.on("zoom",null)}async function E(ee,G,P){const W=nc(ee),b=y==null?void 0:y.constrain()(W,G,P);return b&&await k(b),new Promise(H=>H(b))}async function N(ee,G){const P=nc(ee);return await k(P,G),new Promise(W=>W(P))}function A(ee){if(w){const G=nc(ee),P=w.property("__zoom");(P.k!==ee.zoom||P.x!==ee.x||P.y!==ee.y)&&(y==null||y.transform(w,G,null,{sync:!0}))}}function $(){const ee=w?Km(w.node()):{x:0,y:0,k:1};return{x:ee.x,y:ee.y,zoom:ee.k}}function F(ee,G){return w?new Promise(P=>{y==null||y.interpolate((G==null?void 0:G.interpolate)==="linear"?gi:hl).scaleTo(rc(w,G==null?void 0:G.duration,G==null?void 0:G.ease,()=>P(!0)),ee)}):Promise.resolve(!1)}function X(ee,G){return w?new Promise(P=>{y==null||y.interpolate((G==null?void 0:G.interpolate)==="linear"?gi:hl).scaleBy(rc(w,G==null?void 0:G.duration,G==null?void 0:G.ease,()=>P(!0)),ee)}):Promise.resolve(!1)}function K(ee){y==null||y.scaleExtent(ee)}function Z(ee){y==null||y.translateExtent(ee)}function J(ee){const G=!Bt(ee)||ee<0?0:ee;y==null||y.clickDistance(G)}return{update:C,destroy:z,setViewport:N,setViewportConstrained:E,getViewport:$,scaleTo:F,scaleBy:X,setScaleExtent:K,setTranslateExtent:Z,syncViewport:A,setClickDistance:J}}var po;(function(t){t.Line="line",t.Handle="handle"})(po||(po={}));function ZE({width:t,prevWidth:r,height:o,prevHeight:s,affectsX:l,affectsY:a}){const c=t-r,d=o-s,p=[c>0?1:c<0?-1:0,d>0?1:d<0?-1:0];return c&&l&&(p[0]=p[0]*-1),d&&a&&(p[1]=p[1]*-1),p}function JE(t){const r=t.includes("right")||t.includes("left"),o=t.includes("bottom")||t.includes("top"),s=t.includes("left"),l=t.includes("top");return{isHorizontal:r,isVertical:o,affectsX:s,affectsY:l}}function Wn(t,r){return Math.max(0,r-t)}function Xn(t,r){return Math.max(0,t-r)}function il(t,r,o){return Math.max(0,r-t,t-o)}function vp(t,r){return t?!r:r}function e_(t,r,o,s,l,a,c,d){let{affectsX:p,affectsY:g}=r;const{isHorizontal:m,isVertical:v}=r,y=m&&v,{xSnapped:w,ySnapped:_}=o,{minWidth:S,maxWidth:k,minHeight:C,maxHeight:z}=s,{x:E,y:N,width:A,height:$,aspectRatio:F}=t;let X=Math.floor(m?w-t.pointerX:0),K=Math.floor(v?_-t.pointerY:0);const Z=A+(p?-X:X),J=$+(g?-K:K),ee=-a[0]*A,G=-a[1]*$;let P=il(Z,S,k),W=il(J,C,z);if(c){let D=0,I=0;p&&X<0?D=Wn(E+X+ee,c[0][0]):!p&&X>0&&(D=Xn(E+Z+ee,c[1][0])),g&&K<0?I=Wn(N+K+G,c[0][1]):!g&&K>0&&(I=Xn(N+J+G,c[1][1])),P=Math.max(P,D),W=Math.max(W,I)}if(d){let D=0,I=0;p&&X>0?D=Xn(E+X,d[0][0]):!p&&X<0&&(D=Wn(E+Z,d[1][0])),g&&K>0?I=Xn(N+K,d[0][1]):!g&&K<0&&(I=Wn(N+J,d[1][1])),P=Math.max(P,D),W=Math.max(W,I)}if(l){if(m){const D=il(Z/F,C,z)*F;if(P=Math.max(P,D),c){let I=0;!p&&!g||p&&!g&&y?I=Xn(N+G+Z/F,c[1][1])*F:I=Wn(N+G+(p?X:-X)/F,c[0][1])*F,P=Math.max(P,I)}if(d){let I=0;!p&&!g||p&&!g&&y?I=Wn(N+Z/F,d[1][1])*F:I=Xn(N+(p?X:-X)/F,d[0][1])*F,P=Math.max(P,I)}}if(v){const D=il(J*F,S,k)/F;if(W=Math.max(W,D),c){let I=0;!p&&!g||g&&!p&&y?I=Xn(E+J*F+ee,c[1][0])/F:I=Wn(E+(g?K:-K)*F+ee,c[0][0])/F,W=Math.max(W,I)}if(d){let I=0;!p&&!g||g&&!p&&y?I=Wn(E+J*F,d[1][0])/F:I=Xn(E+(g?K:-K)*F,d[0][0])/F,W=Math.max(W,I)}}}K=K+(K<0?W:-W),X=X+(X<0?P:-P),l&&(y?Z>J*F?K=(vp(p,g)?-X:X)/F:X=(vp(p,g)?-K:K)*F:m?(K=X/F,g=p):(X=K*F,p=g));const b=p?E+X:E,H=g?N+K:N;return{width:A+(p?-X:X),height:$+(g?-K:K),x:a[0]*X*(p?-1:1)+b,y:a[1]*K*(g?-1:1)+H}}const Cg={width:0,height:0,x:0,y:0},t_={...Cg,pointerX:0,pointerY:0,aspectRatio:1};function n_(t){return[[0,0],[t.measured.width,t.measured.height]]}function r_(t,r,o){const s=r.position.x+t.position.x,l=r.position.y+t.position.y,a=t.measured.width??0,c=t.measured.height??0,d=o[0]*a,p=o[1]*c;return[[s-d,l-p],[s+a-d,l+c-p]]}function o_({domNode:t,nodeId:r,getStoreItems:o,onChange:s,onEnd:l}){const a=kt(t);function c({controlPosition:p,boundaries:g,keepAspectRatio:m,resizeDirection:v,onResizeStart:y,onResize:w,onResizeEnd:_,shouldResize:S}){let k={...Cg},C={...t_};const z=JE(p);let E,N=null,A=[],$,F,X;const K=Dm().on("start",Z=>{const{nodeLookup:J,transform:ee,snapGrid:G,snapToGrid:P,nodeOrigin:W,paneDomNode:b}=o();if(E=J.get(r),!E)return;N=(b==null?void 0:b.getBoundingClientRect())??null;const{xSnapped:H,ySnapped:D}=yi(Z.sourceEvent,{transform:ee,snapGrid:G,snapToGrid:P,containerBounds:N});k={width:E.measured.width??0,height:E.measured.height??0,x:E.position.x??0,y:E.position.y??0},C={...k,pointerX:H,pointerY:D,aspectRatio:k.width/k.height},$=void 0,E.parentId&&(E.extent==="parent"||E.expandParent)&&($=J.get(E.parentId),F=$&&E.extent==="parent"?n_($):void 0),A=[],X=void 0;for(const[I,B]of J)if(B.parentId===r&&(A.push({id:I,position:{...B.position},extent:B.extent}),B.extent==="parent"||B.expandParent)){const T=r_(B,E,B.origin??W);X?X=[[Math.min(T[0][0],X[0][0]),Math.min(T[0][1],X[0][1])],[Math.max(T[1][0],X[1][0]),Math.max(T[1][1],X[1][1])]]:X=T}y==null||y(Z,{...k})}).on("drag",Z=>{const{transform:J,snapGrid:ee,snapToGrid:G,nodeOrigin:P}=o(),W=yi(Z.sourceEvent,{transform:J,snapGrid:ee,snapToGrid:G,containerBounds:N}),b=[];if(!E)return;const{x:H,y:D,width:I,height:B}=k,T={},j=E.origin??P,{width:oe,height:ie,x:ue,y:ae}=e_(C,z,W,g,m,j,F,X),fe=oe!==I,re=ie!==B,ce=ue!==H&&fe,Se=ae!==D&&re;if(!ce&&!Se&&!fe&&!re)return;if((ce||Se||j[0]===1||j[1]===1)&&(T.x=ce?ue:k.x,T.y=Se?ae:k.y,k.x=T.x,k.y=T.y,A.length>0)){const Ne=ue-H,Ie=ae-D;for(const Be of A)Be.position={x:Be.position.x-Ne+j[0]*(oe-I),y:Be.position.y-Ie+j[1]*(ie-B)},b.push(Be)}if((fe||re)&&(T.width=fe&&(!v||v==="horizontal")?oe:k.width,T.height=re&&(!v||v==="vertical")?ie:k.height,k.width=T.width,k.height=T.height),$&&E.expandParent){const Ne=j[0]*(T.width??0);T.x&&T.x<Ne&&(k.x=Ne,C.x=C.x-(T.x-Ne));const Ie=j[1]*(T.height??0);T.y&&T.y<Ie&&(k.y=Ie,C.y=C.y-(T.y-Ie))}const _e=ZE({width:k.width,prevWidth:I,height:k.height,prevHeight:B,affectsX:z.affectsX,affectsY:z.affectsY}),ye={...k,direction:_e};(S==null?void 0:S(Z,ye))!==!1&&(w==null||w(Z,ye),s(T,b))}).on("end",Z=>{_==null||_(Z,{...k}),l==null||l({...k})});a.call(K)}function d(){a.on(".drag",null)}return{update:c,destroy:d}}var oc={exports:{}},ic={},sc={exports:{}},lc={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wp;function i_(){if(wp)return lc;wp=1;var t=Pi();function r(v,y){return v===y&&(v!==0||1/v===1/y)||v!==v&&y!==y}var o=typeof Object.is=="function"?Object.is:r,s=t.useState,l=t.useEffect,a=t.useLayoutEffect,c=t.useDebugValue;function d(v,y){var w=y(),_=s({inst:{value:w,getSnapshot:y}}),S=_[0].inst,k=_[1];return a(function(){S.value=w,S.getSnapshot=y,p(S)&&k({inst:S})},[v,w,y]),l(function(){return p(S)&&k({inst:S}),v(function(){p(S)&&k({inst:S})})},[v]),c(w),w}function p(v){var y=v.getSnapshot;v=v.value;try{var w=y();return!o(v,w)}catch{return!0}}function g(v,y){return y()}var m=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?g:d;return lc.useSyncExternalStore=t.useSyncExternalStore!==void 0?t.useSyncExternalStore:m,lc}var xp;function s_(){return xp||(xp=1,sc.exports=i_()),sc.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sp;function l_(){if(Sp)return ic;Sp=1;var t=Pi(),r=s_();function o(g,m){return g===m&&(g!==0||1/g===1/m)||g!==g&&m!==m}var s=typeof Object.is=="function"?Object.is:o,l=r.useSyncExternalStore,a=t.useRef,c=t.useEffect,d=t.useMemo,p=t.useDebugValue;return ic.useSyncExternalStoreWithSelector=function(g,m,v,y,w){var _=a(null);if(_.current===null){var S={hasValue:!1,value:null};_.current=S}else S=_.current;_=d(function(){function C($){if(!z){if(z=!0,E=$,$=y($),w!==void 0&&S.hasValue){var F=S.value;if(w(F,$))return N=F}return N=$}if(F=N,s(E,$))return F;var X=y($);return w!==void 0&&w(F,X)?(E=$,F):(E=$,N=X)}var z=!1,E,N,A=v===void 0?null:v;return[function(){return C(m())},A===null?void 0:function(){return C(A())}]},[m,v,y,w]);var k=l(g,_[0],_[1]);return c(function(){S.hasValue=!0,S.value=k},[k]),p(k),k},ic}var Ep;function u_(){return Ep||(Ep=1,oc.exports=l_()),oc.exports}var a_=u_();const c_=Qp(a_),f_={},_p=t=>{let r;const o=new Set,s=(m,v)=>{const y=typeof m=="function"?m(r):m;if(!Object.is(y,r)){const w=r;r=v??(typeof y!="object"||y===null)?y:Object.assign({},r,y),o.forEach(_=>_(r,w))}},l=()=>r,p={setState:s,getState:l,getInitialState:()=>g,subscribe:m=>(o.add(m),()=>o.delete(m)),destroy:()=>{(f_?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),o.clear()}},g=r=t(s,l,p);return p},d_=t=>t?_p(t):_p,{useDebugValue:h_}=Wy,{useSyncExternalStoreWithSelector:p_}=c_,m_=t=>t;function Tg(t,r=m_,o){const s=p_(t.subscribe,t.getState,t.getServerState||t.getInitialState,r,o);return h_(s),s}const kp=(t,r)=>{const o=d_(t),s=(l,a=r)=>Tg(o,l,a);return Object.assign(s,o),s},g_=(t,r)=>t?kp(t,r):kp;function He(t,r){if(Object.is(t,r))return!0;if(typeof t!="object"||t===null||typeof r!="object"||r===null)return!1;if(t instanceof Map&&r instanceof Map){if(t.size!==r.size)return!1;for(const[s,l]of t)if(!Object.is(l,r.get(s)))return!1;return!0}if(t instanceof Set&&r instanceof Set){if(t.size!==r.size)return!1;for(const s of t)if(!r.has(s))return!1;return!0}const o=Object.keys(t);if(o.length!==Object.keys(r).length)return!1;for(const s of o)if(!Object.prototype.hasOwnProperty.call(r,s)||!Object.is(t[s],r[s]))return!1;return!0}Kp();const Yl=q.createContext(null),y_=Yl.Provider,Pg=tn.error001();function Te(t,r){const o=q.useContext(Yl);if(o===null)throw new Error(Pg);return Tg(o,t,r)}function $e(){const t=q.useContext(Yl);if(t===null)throw new Error(Pg);return q.useMemo(()=>({getState:t.getState,setState:t.setState,subscribe:t.subscribe}),[t])}const Np={display:"none"},v_={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},Rg="react-flow__node-desc",Mg="react-flow__edge-desc",w_="react-flow__aria-live",x_=t=>t.ariaLiveMessage,S_=t=>t.ariaLabelConfig;function E_({rfId:t}){const r=Te(x_);return M.jsx("div",{id:`${w_}-${t}`,"aria-live":"assertive","aria-atomic":"true",style:v_,children:r})}function __({rfId:t,disableKeyboardA11y:r}){const o=Te(S_);return M.jsxs(M.Fragment,{children:[M.jsx("div",{id:`${Rg}-${t}`,style:Np,children:r?o["node.a11yDescription.default"]:o["node.a11yDescription.keyboardDisabled"]}),M.jsx("div",{id:`${Mg}-${t}`,style:Np,children:o["edge.a11yDescription.default"]}),!r&&M.jsx(E_,{rfId:t})]})}const k_=t=>t.userSelectionActive?"none":"all",ql=q.forwardRef(({position:t="top-left",children:r,className:o,style:s,...l},a)=>{const c=Te(k_),d=`${t}`.split("-");return M.jsx("div",{className:Ye(["react-flow__panel",o,...d]),style:{...s,pointerEvents:c},ref:a,...l,children:r})});ql.displayName="Panel";function N_({proOptions:t,position:r="bottom-right"}){return t!=null&&t.hideAttribution?null:M.jsx(ql,{position:r,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev",children:M.jsx("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution",children:"React Flow"})})}const C_=t=>{const r=[],o=[];for(const[,s]of t.nodeLookup)s.selected&&r.push(s.internals.userNode);for(const[,s]of t.edgeLookup)s.selected&&o.push(s);return{selectedNodes:r,selectedEdges:o}},sl=t=>t.id;function T_(t,r){return He(t.selectedNodes.map(sl),r.selectedNodes.map(sl))&&He(t.selectedEdges.map(sl),r.selectedEdges.map(sl))}function P_({onSelectionChange:t}){const r=$e(),{selectedNodes:o,selectedEdges:s}=Te(C_,T_);return q.useEffect(()=>{const l={nodes:o,edges:s};t==null||t(l),r.getState().onSelectionChangeHandlers.forEach(a=>a(l))},[o,s,t]),null}const R_=t=>!!t.onSelectionChangeHandlers;function M_({onSelectionChange:t}){const r=Te(R_);return t||r?M.jsx(P_,{onSelectionChange:t}):null}const Ag=[0,0],A_={x:0,y:0,zoom:1},L_=["nodes","edges","defaultNodes","defaultEdges","onConnect","onConnectStart","onConnectEnd","onClickConnectStart","onClickConnectEnd","nodesDraggable","autoPanOnNodeFocus","nodesConnectable","nodesFocusable","edgesFocusable","edgesReconnectable","elevateNodesOnSelect","elevateEdgesOnSelect","minZoom","maxZoom","nodeExtent","onNodesChange","onEdgesChange","elementsSelectable","connectionMode","snapGrid","snapToGrid","translateExtent","connectOnClick","defaultEdgeOptions","fitView","fitViewOptions","onNodesDelete","onEdgesDelete","onDelete","onNodeDrag","onNodeDragStart","onNodeDragStop","onSelectionDrag","onSelectionDragStart","onSelectionDragStop","onMoveStart","onMove","onMoveEnd","noPanClassName","nodeOrigin","autoPanOnConnect","autoPanOnNodeDrag","onError","connectionRadius","isValidConnection","selectNodesOnDrag","nodeDragThreshold","onBeforeDelete","debug","autoPanSpeed","paneClickDistance","ariaLabelConfig"],Cp=[...L_,"rfId"],I_=t=>({setNodes:t.setNodes,setEdges:t.setEdges,setMinZoom:t.setMinZoom,setMaxZoom:t.setMaxZoom,setTranslateExtent:t.setTranslateExtent,setNodeExtent:t.setNodeExtent,reset:t.reset,setDefaultNodesAndEdges:t.setDefaultNodesAndEdges,setPaneClickDistance:t.setPaneClickDistance}),Tp={translateExtent:_i,nodeOrigin:Ag,minZoom:.5,maxZoom:2,elementsSelectable:!0,noPanClassName:"nopan",rfId:"1",paneClickDistance:0};function z_(t){const{setNodes:r,setEdges:o,setMinZoom:s,setMaxZoom:l,setTranslateExtent:a,setNodeExtent:c,reset:d,setDefaultNodesAndEdges:p,setPaneClickDistance:g}=Te(I_,He),m=$e();q.useEffect(()=>(p(t.defaultNodes,t.defaultEdges),()=>{v.current=Tp,d()}),[]);const v=q.useRef(Tp);return q.useEffect(()=>{for(const y of Cp){const w=t[y],_=v.current[y];w!==_&&(typeof t[y]>"u"||(y==="nodes"?r(w):y==="edges"?o(w):y==="minZoom"?s(w):y==="maxZoom"?l(w):y==="translateExtent"?a(w):y==="nodeExtent"?c(w):y==="paneClickDistance"?g(w):y==="fitView"?m.setState({fitViewQueued:w}):y==="fitViewOptions"&&m.setState({fitViewOptions:w}),y==="ariaLabelConfig"?m.setState({ariaLabelConfig:vE(w)}):m.setState({[y]:w})))}v.current=t},Cp.map(y=>t[y])),null}function Pp(){return typeof window>"u"||!window.matchMedia?null:window.matchMedia("(prefers-color-scheme: dark)")}function O_(t){var s;const[r,o]=q.useState(t==="system"?null:t);return q.useEffect(()=>{if(t!=="system"){o(t);return}const l=Pp(),a=()=>o(l!=null&&l.matches?"dark":"light");return a(),l==null||l.addEventListener("change",a),()=>{l==null||l.removeEventListener("change",a)}},[t]),r!==null?r:(s=Pp())!=null&&s.matches?"dark":"light"}const Rp=typeof document<"u"?document:null;function Ti(t=null,r={target:Rp,actInsideInputWithModifier:!0}){const[o,s]=q.useState(!1),l=q.useRef(!1),a=q.useRef(new Set([])),[c,d]=q.useMemo(()=>{if(t!==null){const g=(Array.isArray(t)?t:[t]).filter(v=>typeof v=="string").map(v=>v.replace("+",`
`).replace(`

`,`
+`).split(`
`)),m=g.reduce((v,y)=>v.concat(...y),[]);return[g,m]}return[[],[]]},[t]);return q.useEffect(()=>{const p=(r==null?void 0:r.target)??Rp,g=(r==null?void 0:r.actInsideInputWithModifier)??!0;if(t!==null){const m=w=>{var k,C;if(l.current=w.ctrlKey||w.metaKey||w.shiftKey||w.altKey,(!l.current||l.current&&!g)&&cg(w))return!1;const S=Ap(w.code,d);if(a.current.add(w[S]),Mp(c,a.current,!1)){const z=((C=(k=w.composedPath)==null?void 0:k.call(w))==null?void 0:C[0])||w.target,E=(z==null?void 0:z.nodeName)==="BUTTON"||(z==null?void 0:z.nodeName)==="A";r.preventDefault!==!1&&(l.current||!E)&&w.preventDefault(),s(!0)}},v=w=>{const _=Ap(w.code,d);Mp(c,a.current,!0)?(s(!1),a.current.clear()):a.current.delete(w[_]),w.key==="Meta"&&a.current.clear(),l.current=!1},y=()=>{a.current.clear(),s(!1)};return p==null||p.addEventListener("keydown",m),p==null||p.addEventListener("keyup",v),window.addEventListener("blur",y),window.addEventListener("contextmenu",y),()=>{p==null||p.removeEventListener("keydown",m),p==null||p.removeEventListener("keyup",v),window.removeEventListener("blur",y),window.removeEventListener("contextmenu",y)}}},[t,s]),o}function Mp(t,r,o){return t.filter(s=>o||s.length===r.size).some(s=>s.every(l=>r.has(l)))}function Ap(t,r){return r.includes(t)?"code":"key"}const D_=()=>{const t=$e();return q.useMemo(()=>({zoomIn:r=>{const{panZoom:o}=t.getState();return o?o.scaleBy(1.2,{duration:r==null?void 0:r.duration}):Promise.resolve(!1)},zoomOut:r=>{const{panZoom:o}=t.getState();return o?o.scaleBy(1/1.2,{duration:r==null?void 0:r.duration}):Promise.resolve(!1)},zoomTo:(r,o)=>{const{panZoom:s}=t.getState();return s?s.scaleTo(r,{duration:o==null?void 0:o.duration}):Promise.resolve(!1)},getZoom:()=>t.getState().transform[2],setViewport:async(r,o)=>{const{transform:[s,l,a],panZoom:c}=t.getState();return c?(await c.setViewport({x:r.x??s,y:r.y??l,zoom:r.zoom??a},o),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>{const[r,o,s]=t.getState().transform;return{x:r,y:o,zoom:s}},setCenter:async(r,o,s)=>t.getState().setCenter(r,o,s),fitBounds:async(r,o)=>{const{width:s,height:l,minZoom:a,maxZoom:c,panZoom:d}=t.getState(),p=Bc(r,s,l,a,c,(o==null?void 0:o.padding)??.1);return d?(await d.setViewport(p,{duration:o==null?void 0:o.duration,ease:o==null?void 0:o.ease,interpolate:o==null?void 0:o.interpolate}),Promise.resolve(!0)):Promise.resolve(!1)},screenToFlowPosition:(r,o={})=>{const{transform:s,snapGrid:l,snapToGrid:a,domNode:c}=t.getState();if(!c)return r;const{x:d,y:p}=c.getBoundingClientRect(),g={x:r.x-d,y:r.y-p},m=o.snapGrid??l,v=o.snapToGrid??a;return Oi(g,s,v,m)},flowToScreenPosition:r=>{const{transform:o,domNode:s}=t.getState();if(!s)return r;const{x:l,y:a}=s.getBoundingClientRect(),c=Pl(r,o);return{x:c.x+l,y:c.y+a}}}),[])};function Lg(t,r){const o=[],s=new Map,l=[];for(const a of t)if(a.type==="add"){l.push(a);continue}else if(a.type==="remove"||a.type==="replace")s.set(a.id,[a]);else{const c=s.get(a.id);c?c.push(a):s.set(a.id,[a])}for(const a of r){const c=s.get(a.id);if(!c){o.push(a);continue}if(c[0].type==="remove")continue;if(c[0].type==="replace"){o.push({...c[0].item});continue}const d={...a};for(const p of c)$_(p,d);o.push(d)}return l.length&&l.forEach(a=>{a.index!==void 0?o.splice(a.index,0,{...a.item}):o.push({...a.item})}),o}function $_(t,r){switch(t.type){case"select":{r.selected=t.selected;break}case"position":{typeof t.position<"u"&&(r.position=t.position),typeof t.dragging<"u"&&(r.dragging=t.dragging);break}case"dimensions":{typeof t.dimensions<"u"&&(r.measured??(r.measured={}),r.measured.width=t.dimensions.width,r.measured.height=t.dimensions.height,t.setAttributes&&((t.setAttributes===!0||t.setAttributes==="width")&&(r.width=t.dimensions.width),(t.setAttributes===!0||t.setAttributes==="height")&&(r.height=t.dimensions.height))),typeof t.resizing=="boolean"&&(r.resizing=t.resizing);break}}}function Ig(t,r){return Lg(t,r)}function zg(t,r){return Lg(t,r)}function mr(t,r){return{id:t,type:"select",selected:r}}function oo(t,r=new Set,o=!1){const s=[];for(const[l,a]of t){const c=r.has(l);!(a.selected===void 0&&!c)&&a.selected!==c&&(o&&(a.selected=c),s.push(mr(a.id,c)))}return s}function Lp({items:t=[],lookup:r}){var l;const o=[],s=new Map(t.map(a=>[a.id,a]));for(const[a,c]of t.entries()){const d=r.get(c.id),p=((l=d==null?void 0:d.internals)==null?void 0:l.userNode)??d;p!==void 0&&p!==c&&o.push({id:c.id,item:c,type:"replace"}),p===void 0&&o.push({item:c,type:"add",index:a})}for(const[a]of r)s.get(a)===void 0&&o.push({id:a,type:"remove"});return o}function Ip(t){return{id:t.id,type:"remove"}}const zp=t=>uE(t),j_=t=>ng(t);function Og(t){return q.forwardRef(t)}const F_=typeof window<"u"?q.useLayoutEffect:q.useEffect;function Op(t){const[r,o]=q.useState(BigInt(0)),[s]=q.useState(()=>b_(()=>o(l=>l+BigInt(1))));return F_(()=>{const l=s.get();l.length&&(t(l),s.reset())},[r]),s}function b_(t){let r=[];return{get:()=>r,reset:()=>{r=[]},push:o=>{r.push(o),t()}}}const Dg=q.createContext(null);function H_({children:t}){const r=$e(),o=q.useCallback(d=>{const{nodes:p=[],setNodes:g,hasDefaultNodes:m,onNodesChange:v,nodeLookup:y,fitViewQueued:w}=r.getState();let _=p;for(const k of d)_=typeof k=="function"?k(_):k;const S=Lp({items:_,lookup:y});m&&g(_),S.length>0?v==null||v(S):w&&window.requestAnimationFrame(()=>{const{fitViewQueued:k,nodes:C,setNodes:z}=r.getState();k&&z(C)})},[]),s=Op(o),l=q.useCallback(d=>{const{edges:p=[],setEdges:g,hasDefaultEdges:m,onEdgesChange:v,edgeLookup:y}=r.getState();let w=p;for(const _ of d)w=typeof _=="function"?_(w):_;m?g(w):v&&v(Lp({items:w,lookup:y}))},[]),a=Op(l),c=q.useMemo(()=>({nodeQueue:s,edgeQueue:a}),[]);return M.jsx(Dg.Provider,{value:c,children:t})}function B_(){const t=q.useContext(Dg);if(!t)throw new Error("useBatchContext must be used within a BatchProvider");return t}const V_=t=>!!t.panZoom;function qc(){const t=D_(),r=$e(),o=B_(),s=Te(V_),l=q.useMemo(()=>{const a=v=>r.getState().nodeLookup.get(v),c=v=>{o.nodeQueue.push(v)},d=v=>{o.edgeQueue.push(v)},p=v=>{var C,z;const{nodeLookup:y,nodeOrigin:w}=r.getState(),_=zp(v)?v:y.get(v.id),S=_.parentId?ug(_.position,_.measured,_.parentId,y,w):_.position,k={..._,position:S,width:((C=_.measured)==null?void 0:C.width)??_.width,height:((z=_.measured)==null?void 0:z.height)??_.height};return fo(k)},g=(v,y,w={replace:!1})=>{c(_=>_.map(S=>{if(S.id===v){const k=typeof y=="function"?y(S):y;return w.replace&&zp(k)?k:{...S,...k}}return S}))},m=(v,y,w={replace:!1})=>{d(_=>_.map(S=>{if(S.id===v){const k=typeof y=="function"?y(S):y;return w.replace&&j_(k)?k:{...S,...k}}return S}))};return{getNodes:()=>r.getState().nodes.map(v=>({...v})),getNode:v=>{var y;return(y=a(v))==null?void 0:y.internals.userNode},getInternalNode:a,getEdges:()=>{const{edges:v=[]}=r.getState();return v.map(y=>({...y}))},getEdge:v=>r.getState().edgeLookup.get(v),setNodes:c,setEdges:d,addNodes:v=>{const y=Array.isArray(v)?v:[v];o.nodeQueue.push(w=>[...w,...y])},addEdges:v=>{const y=Array.isArray(v)?v:[v];o.edgeQueue.push(w=>[...w,...y])},toObject:()=>{const{nodes:v=[],edges:y=[],transform:w}=r.getState(),[_,S,k]=w;return{nodes:v.map(C=>({...C})),edges:y.map(C=>({...C})),viewport:{x:_,y:S,zoom:k}}},deleteElements:async({nodes:v=[],edges:y=[]})=>{const{nodes:w,edges:_,onNodesDelete:S,onEdgesDelete:k,triggerNodeChanges:C,triggerEdgeChanges:z,onDelete:E,onBeforeDelete:N}=r.getState(),{nodes:A,edges:$}=await hE({nodesToRemove:v,edgesToRemove:y,nodes:w,edges:_,onBeforeDelete:N}),F=$.length>0,X=A.length>0;if(F){const K=$.map(Ip);k==null||k($),z(K)}if(X){const K=A.map(Ip);S==null||S(A),C(K)}return(X||F)&&(E==null||E({nodes:A,edges:$})),{deletedNodes:A,deletedEdges:$}},getIntersectingNodes:(v,y=!0,w)=>{const _=lp(v),S=_?v:p(v),k=w!==void 0;return S?(w||r.getState().nodes).filter(C=>{const z=r.getState().nodeLookup.get(C.id);if(z&&!_&&(C.id===v.id||!z.internals.positionAbsolute))return!1;const E=fo(k?C:z),N=Ni(E,S);return y&&N>0||N>=S.width*S.height}):[]},isNodeIntersecting:(v,y,w=!0)=>{const S=lp(v)?v:p(v);if(!S)return!1;const k=Ni(S,y);return w&&k>0||k>=S.width*S.height},updateNode:g,updateNodeData:(v,y,w={replace:!1})=>{g(v,_=>{const S=typeof y=="function"?y(_):y;return w.replace?{..._,data:S}:{..._,data:{..._.data,...S}}},w)},updateEdge:m,updateEdgeData:(v,y,w={replace:!1})=>{m(v,_=>{const S=typeof y=="function"?y(_):y;return w.replace?{..._,data:S}:{..._,data:{..._.data,...S}}},w)},getNodesBounds:v=>{const{nodeLookup:y,nodeOrigin:w}=r.getState();return aE(v,{nodeLookup:y,nodeOrigin:w})},getHandleConnections:({type:v,id:y,nodeId:w})=>{var _;return Array.from(((_=r.getState().connectionLookup.get(`${w}-${v}${y?`-${y}`:""}`))==null?void 0:_.values())??[])},getNodeConnections:({type:v,handleId:y,nodeId:w})=>{var _;return Array.from(((_=r.getState().connectionLookup.get(`${w}${v?y?`-${v}-${y}`:`-${v}`:""}`))==null?void 0:_.values())??[])},fitView:async v=>{const y=r.getState().fitViewResolver??yE();return r.setState({fitViewQueued:!0,fitViewOptions:v,fitViewResolver:y}),o.nodeQueue.push(w=>[...w]),y.promise}}},[]);return q.useMemo(()=>({...l,...t,viewportInitialized:s}),[s])}const Dp=t=>t.selected,U_=typeof window<"u"?window:void 0;function W_({deleteKeyCode:t,multiSelectionKeyCode:r}){const o=$e(),{deleteElements:s}=qc(),l=Ti(t,{actInsideInputWithModifier:!1}),a=Ti(r,{target:U_});q.useEffect(()=>{if(l){const{edges:c,nodes:d}=o.getState();s({nodes:d.filter(Dp),edges:c.filter(Dp)}),o.setState({nodesSelectionActive:!1})}},[l]),q.useEffect(()=>{o.setState({multiSelectionActive:a})},[a])}function X_(t){const r=$e();q.useEffect(()=>{const o=()=>{var l,a;if(!t.current)return!1;const s=Vc(t.current);(s.height===0||s.width===0)&&((a=(l=r.getState()).onError)==null||a.call(l,"004",tn.error004())),r.setState({width:s.width||500,height:s.height||500})};if(t.current){o(),window.addEventListener("resize",o);const s=new ResizeObserver(()=>o());return s.observe(t.current),()=>{window.removeEventListener("resize",o),s&&t.current&&s.unobserve(t.current)}}},[])}const Ql={position:"absolute",width:"100%",height:"100%",top:0,left:0},Y_=t=>({userSelectionActive:t.userSelectionActive,lib:t.lib});function q_({onPaneContextMenu:t,zoomOnScroll:r=!0,zoomOnPinch:o=!0,panOnScroll:s=!1,panOnScrollSpeed:l=.5,panOnScrollMode:a=xr.Free,zoomOnDoubleClick:c=!0,panOnDrag:d=!0,defaultViewport:p,translateExtent:g,minZoom:m,maxZoom:v,zoomActivationKeyCode:y,preventScrolling:w=!0,children:_,noWheelClassName:S,noPanClassName:k,onViewportChange:C,isControlledViewport:z,paneClickDistance:E}){const N=$e(),A=q.useRef(null),{userSelectionActive:$,lib:F}=Te(Y_,He),X=Ti(y),K=q.useRef();X_(A);const Z=q.useCallback(J=>{C==null||C({x:J[0],y:J[1],zoom:J[2]}),z||N.setState({transform:J})},[C,z]);return q.useEffect(()=>{if(A.current){K.current=GE({domNode:A.current,minZoom:m,maxZoom:v,translateExtent:g,viewport:p,paneClickDistance:E,onDraggingChange:P=>N.setState({paneDragging:P}),onPanZoomStart:(P,W)=>{const{onViewportChangeStart:b,onMoveStart:H}=N.getState();H==null||H(P,W),b==null||b(W)},onPanZoom:(P,W)=>{const{onViewportChange:b,onMove:H}=N.getState();H==null||H(P,W),b==null||b(W)},onPanZoomEnd:(P,W)=>{const{onViewportChangeEnd:b,onMoveEnd:H}=N.getState();H==null||H(P,W),b==null||b(W)}});const{x:J,y:ee,zoom:G}=K.current.getViewport();return N.setState({panZoom:K.current,transform:[J,ee,G],domNode:A.current.closest(".react-flow")}),()=>{var P;(P=K.current)==null||P.destroy()}}},[]),q.useEffect(()=>{var J;(J=K.current)==null||J.update({onPaneContextMenu:t,zoomOnScroll:r,zoomOnPinch:o,panOnScroll:s,panOnScrollSpeed:l,panOnScrollMode:a,zoomOnDoubleClick:c,panOnDrag:d,zoomActivationKeyPressed:X,preventScrolling:w,noPanClassName:k,userSelectionActive:$,noWheelClassName:S,lib:F,onTransformChange:Z})},[t,r,o,s,l,a,c,d,X,w,k,$,S,F,Z]),M.jsx("div",{className:"react-flow__renderer",ref:A,style:Ql,children:_})}const Q_=t=>({userSelectionActive:t.userSelectionActive,userSelectionRect:t.userSelectionRect});function K_(){const{userSelectionActive:t,userSelectionRect:r}=Te(Q_,He);return t&&r?M.jsx("div",{className:"react-flow__selection react-flow__container",style:{width:r.width,height:r.height,transform:`translate(${r.x}px, ${r.y}px)`}}):null}const uc=(t,r)=>o=>{o.target===r.current&&(t==null||t(o))},G_=t=>({userSelectionActive:t.userSelectionActive,elementsSelectable:t.elementsSelectable,connectionInProgress:t.connection.inProgress,dragging:t.paneDragging});function Z_({isSelecting:t,selectionKeyPressed:r,selectionMode:o=ki.Full,panOnDrag:s,selectionOnDrag:l,onSelectionStart:a,onSelectionEnd:c,onPaneClick:d,onPaneContextMenu:p,onPaneScroll:g,onPaneMouseEnter:m,onPaneMouseMove:v,onPaneMouseLeave:y,children:w}){const _=$e(),{userSelectionActive:S,elementsSelectable:k,dragging:C,connectionInProgress:z}=Te(G_,He),E=k&&(t||S),N=q.useRef(null),A=q.useRef(),$=q.useRef(new Set),F=q.useRef(new Set),X=q.useRef(!1),K=q.useRef(!1),Z=H=>{if(X.current||z){X.current=!1;return}d==null||d(H),_.getState().resetSelectedElements(),_.setState({nodesSelectionActive:!1})},J=H=>{if(Array.isArray(s)&&(s!=null&&s.includes(2))){H.preventDefault();return}p==null||p(H)},ee=g?H=>g(H):void 0,G=H=>{var j,oe;const{resetSelectedElements:D,domNode:I}=_.getState();if(A.current=I==null?void 0:I.getBoundingClientRect(),!k||!t||H.button!==0||H.target!==N.current||!A.current)return;(oe=(j=H.target)==null?void 0:j.setPointerCapture)==null||oe.call(j,H.pointerId),K.current=!0,X.current=!1;const{x:B,y:T}=wn(H.nativeEvent,A.current);D(),_.setState({userSelectionRect:{width:0,height:0,startX:B,startY:T,x:B,y:T}}),a==null||a(H)},P=H=>{const{userSelectionRect:D,transform:I,nodeLookup:B,edgeLookup:T,connectionLookup:j,triggerNodeChanges:oe,triggerEdgeChanges:ie,defaultEdgeOptions:ue}=_.getState();if(!A.current||!D)return;X.current=!0;const{x:ae,y:fe}=wn(H.nativeEvent,A.current),{startX:re,startY:ce}=D,Se={startX:re,startY:ce,x:ae<re?ae:re,y:fe<ce?fe:ce,width:Math.abs(ae-re),height:Math.abs(fe-ce)},_e=$.current,ye=F.current;$.current=new Set(Hc(B,Se,I,o===ki.Partial,!0).map(Ne=>Ne.id)),F.current=new Set;const Pe=(ue==null?void 0:ue.selectable)??!0;for(const Ne of $.current){const Ie=j.get(Ne);if(Ie)for(const{edgeId:Be}of Ie.values()){const Wt=T.get(Be);Wt&&(Wt.selectable??Pe)&&F.current.add(Be)}}if(!up(_e,$.current)){const Ne=oo(B,$.current,!0);oe(Ne)}if(!up(ye,F.current)){const Ne=oo(T,F.current);ie(Ne)}_.setState({userSelectionRect:Se,userSelectionActive:!0,nodesSelectionActive:!1})},W=H=>{var I,B;if(H.button!==0||!K.current)return;(B=(I=H.target)==null?void 0:I.releasePointerCapture)==null||B.call(I,H.pointerId);const{userSelectionRect:D}=_.getState();!S&&D&&H.target===N.current&&(Z==null||Z(H)),_.setState({userSelectionActive:!1,userSelectionRect:null,nodesSelectionActive:$.current.size>0}),c==null||c(H),(r||l)&&(X.current=!1),K.current=!1},b=s===!0||Array.isArray(s)&&s.includes(0);return M.jsxs("div",{className:Ye(["react-flow__pane",{draggable:b,dragging:C,selection:t}]),onClick:E?void 0:uc(Z,N),onContextMenu:uc(J,N),onWheel:uc(ee,N),onPointerEnter:E?void 0:m,onPointerDown:E?G:v,onPointerMove:E?P:v,onPointerUp:E?W:void 0,onPointerLeave:y,ref:N,style:Ql,children:[w,M.jsx(K_,{})]})}function Rc({id:t,store:r,unselect:o=!1,nodeRef:s}){const{addSelectedNodes:l,unselectNodesAndEdges:a,multiSelectionActive:c,nodeLookup:d,onError:p}=r.getState(),g=d.get(t);if(!g){p==null||p("012",tn.error012(t));return}r.setState({nodesSelectionActive:!1}),g.selected?(o||g.selected&&c)&&(a({nodes:[g],edges:[]}),requestAnimationFrame(()=>{var m;return(m=s==null?void 0:s.current)==null?void 0:m.blur()})):l([t])}function $g({nodeRef:t,disabled:r=!1,noDragClassName:o,handleSelector:s,nodeId:l,isSelectable:a,nodeClickDistance:c}){const d=$e(),[p,g]=q.useState(!1),m=q.useRef();return q.useEffect(()=>{m.current=DE({getStoreItems:()=>d.getState(),onNodeMouseDown:v=>{Rc({id:v,store:d,nodeRef:t})},onDragStart:()=>{g(!0)},onDragStop:()=>{g(!1)}})},[]),q.useEffect(()=>{var v,y;if(r)(v=m.current)==null||v.destroy();else if(t.current)return(y=m.current)==null||y.update({noDragClassName:o,handleSelector:s,domNode:t.current,isSelectable:a,nodeId:l,nodeClickDistance:c}),()=>{var w;(w=m.current)==null||w.destroy()}},[o,s,r,a,t,l]),p}const J_=t=>r=>r.selected&&(r.draggable||t&&typeof r.draggable>"u");function jg(){const t=$e();return q.useCallback(o=>{const{nodeExtent:s,snapToGrid:l,snapGrid:a,nodesDraggable:c,onError:d,updateNodePositions:p,nodeLookup:g,nodeOrigin:m}=t.getState(),v=new Map,y=J_(c),w=l?a[0]:5,_=l?a[1]:5,S=o.direction.x*w*o.factor,k=o.direction.y*_*o.factor;for(const[,C]of g){if(!y(C))continue;let z={x:C.internals.positionAbsolute.x+S,y:C.internals.positionAbsolute.y+k};l&&(z=Wl(z,a));const{position:E,positionAbsolute:N}=rg({nodeId:C.id,nextPosition:z,nodeLookup:g,nodeExtent:s,nodeOrigin:m,onError:d});C.position=E,C.internals.positionAbsolute=N,v.set(C.id,C)}p(v)},[])}const Qc=q.createContext(null),ek=Qc.Provider;Qc.Consumer;const Fg=()=>q.useContext(Qc),tk=t=>({connectOnClick:t.connectOnClick,noPanClassName:t.noPanClassName,rfId:t.rfId}),nk=(t,r,o)=>s=>{const{connectionClickStartHandle:l,connectionMode:a,connection:c}=s,{fromHandle:d,toHandle:p,isValid:g}=c,m=(p==null?void 0:p.nodeId)===t&&(p==null?void 0:p.id)===r&&(p==null?void 0:p.type)===o;return{connectingFrom:(d==null?void 0:d.nodeId)===t&&(d==null?void 0:d.id)===r&&(d==null?void 0:d.type)===o,connectingTo:m,clickConnecting:(l==null?void 0:l.nodeId)===t&&(l==null?void 0:l.id)===r&&(l==null?void 0:l.type)===o,isPossibleEndHandle:a===ao.Strict?(d==null?void 0:d.type)!==o:t!==(d==null?void 0:d.nodeId)||r!==(d==null?void 0:d.id),connectionInProcess:!!d,clickConnectionInProcess:!!l,valid:m&&g}};function rk({type:t="source",position:r=ve.Top,isValidConnection:o,isConnectable:s=!0,isConnectableStart:l=!0,isConnectableEnd:a=!0,id:c,onConnect:d,children:p,className:g,onMouseDown:m,onTouchStart:v,...y},w){var W,b;const _=c||null,S=t==="target",k=$e(),C=Fg(),{connectOnClick:z,noPanClassName:E,rfId:N}=Te(tk,He),{connectingFrom:A,connectingTo:$,clickConnecting:F,isPossibleEndHandle:X,connectionInProcess:K,clickConnectionInProcess:Z,valid:J}=Te(nk(C,_,t),He);C||(b=(W=k.getState()).onError)==null||b.call(W,"010",tn.error010());const ee=H=>{const{defaultEdgeOptions:D,onConnect:I,hasDefaultEdges:B}=k.getState(),T={...D,...H};if(B){const{edges:j,setEdges:oe}=k.getState();oe(mg(T,j))}I==null||I(T),d==null||d(T)},G=H=>{if(!C)return;const D=fg(H.nativeEvent);if(l&&(D&&H.button===0||!D)){const I=k.getState();Pc.onPointerDown(H.nativeEvent,{autoPanOnConnect:I.autoPanOnConnect,connectionMode:I.connectionMode,connectionRadius:I.connectionRadius,domNode:I.domNode,nodeLookup:I.nodeLookup,lib:I.lib,isTarget:S,handleId:_,nodeId:C,flowId:I.rfId,panBy:I.panBy,cancelConnection:I.cancelConnection,onConnectStart:I.onConnectStart,onConnectEnd:I.onConnectEnd,updateConnection:I.updateConnection,onConnect:ee,isValidConnection:o||I.isValidConnection,getTransform:()=>k.getState().transform,getFromHandle:()=>k.getState().connection.fromHandle,autoPanSpeed:I.autoPanSpeed})}D?m==null||m(H):v==null||v(H)},P=H=>{const{onClickConnectStart:D,onClickConnectEnd:I,connectionClickStartHandle:B,connectionMode:T,isValidConnection:j,lib:oe,rfId:ie,nodeLookup:ue,connection:ae}=k.getState();if(!C||!B&&!l)return;if(!B){D==null||D(H.nativeEvent,{nodeId:C,handleId:_,handleType:t}),k.setState({connectionClickStartHandle:{nodeId:C,type:t,id:_}});return}const fe=ag(H.target),re=o||j,{connection:ce,isValid:Se}=Pc.isValid(H.nativeEvent,{handle:{nodeId:C,id:_,type:t},connectionMode:T,fromNodeId:B.nodeId,fromHandleId:B.id||null,fromType:B.type,isValidConnection:re,flowId:ie,doc:fe,lib:oe,nodeLookup:ue});Se&&ce&&ee(ce);const _e=structuredClone(ae);delete _e.inProgress,_e.toPosition=_e.toHandle?_e.toHandle.position:null,I==null||I(H,_e),k.setState({connectionClickStartHandle:null})};return M.jsx("div",{"data-handleid":_,"data-nodeid":C,"data-handlepos":r,"data-id":`${N}-${C}-${_}-${t}`,className:Ye(["react-flow__handle",`react-flow__handle-${r}`,"nodrag",E,g,{source:!S,target:S,connectable:s,connectablestart:l,connectableend:a,clickconnecting:F,connectingfrom:A,connectingto:$,valid:J,connectionindicator:s&&(!K||X)&&(K||Z?a:l)}]),onMouseDown:G,onTouchStart:G,onClick:z?P:void 0,ref:w,...y,children:p})}const mo=q.memo(Og(rk));function ok({data:t,isConnectable:r,sourcePosition:o=ve.Bottom}){return M.jsxs(M.Fragment,{children:[t==null?void 0:t.label,M.jsx(mo,{type:"source",position:o,isConnectable:r})]})}function ik({data:t,isConnectable:r,targetPosition:o=ve.Top,sourcePosition:s=ve.Bottom}){return M.jsxs(M.Fragment,{children:[M.jsx(mo,{type:"target",position:o,isConnectable:r}),t==null?void 0:t.label,M.jsx(mo,{type:"source",position:s,isConnectable:r})]})}function sk(){return null}function lk({data:t,isConnectable:r,targetPosition:o=ve.Top}){return M.jsxs(M.Fragment,{children:[M.jsx(mo,{type:"target",position:o,isConnectable:r}),t==null?void 0:t.label]})}const Ml={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}},$p={input:ok,default:ik,output:lk,group:sk};function uk(t){var r,o,s,l;return t.internals.handleBounds===void 0?{width:t.width??t.initialWidth??((r=t.style)==null?void 0:r.width),height:t.height??t.initialHeight??((o=t.style)==null?void 0:o.height)}:{width:t.width??((s=t.style)==null?void 0:s.width),height:t.height??((l=t.style)==null?void 0:l.height)}}const ak=t=>{const{width:r,height:o,x:s,y:l}=zi(t.nodeLookup,{filter:a=>!!a.selected});return{width:Bt(r)?r:null,height:Bt(o)?o:null,userSelectionActive:t.userSelectionActive,transformString:`translate(${t.transform[0]}px,${t.transform[1]}px) scale(${t.transform[2]}) translate(${s}px,${l}px)`}};function ck({onSelectionContextMenu:t,noPanClassName:r,disableKeyboardA11y:o}){const s=$e(),{width:l,height:a,transformString:c,userSelectionActive:d}=Te(ak,He),p=jg(),g=q.useRef(null);if(q.useEffect(()=>{var y;o||(y=g.current)==null||y.focus({preventScroll:!0})},[o]),$g({nodeRef:g}),d||!l||!a)return null;const m=t?y=>{const w=s.getState().nodes.filter(_=>_.selected);t(y,w)}:void 0,v=y=>{Object.prototype.hasOwnProperty.call(Ml,y.key)&&(y.preventDefault(),p({direction:Ml[y.key],factor:y.shiftKey?4:1}))};return M.jsx("div",{className:Ye(["react-flow__nodesselection","react-flow__container",r]),style:{transform:c},children:M.jsx("div",{ref:g,className:"react-flow__nodesselection-rect",onContextMenu:m,tabIndex:o?void 0:-1,onKeyDown:o?void 0:v,style:{width:l,height:a}})})}const jp=typeof window<"u"?window:void 0,fk=t=>({nodesSelectionActive:t.nodesSelectionActive,userSelectionActive:t.userSelectionActive});function bg({children:t,onPaneClick:r,onPaneMouseEnter:o,onPaneMouseMove:s,onPaneMouseLeave:l,onPaneContextMenu:a,onPaneScroll:c,paneClickDistance:d,deleteKeyCode:p,selectionKeyCode:g,selectionOnDrag:m,selectionMode:v,onSelectionStart:y,onSelectionEnd:w,multiSelectionKeyCode:_,panActivationKeyCode:S,zoomActivationKeyCode:k,elementsSelectable:C,zoomOnScroll:z,zoomOnPinch:E,panOnScroll:N,panOnScrollSpeed:A,panOnScrollMode:$,zoomOnDoubleClick:F,panOnDrag:X,defaultViewport:K,translateExtent:Z,minZoom:J,maxZoom:ee,preventScrolling:G,onSelectionContextMenu:P,noWheelClassName:W,noPanClassName:b,disableKeyboardA11y:H,onViewportChange:D,isControlledViewport:I}){const{nodesSelectionActive:B,userSelectionActive:T}=Te(fk),j=Ti(g,{target:jp}),oe=Ti(S,{target:jp}),ie=oe||X,ue=oe||N,ae=m&&ie!==!0,fe=j||T||ae;return W_({deleteKeyCode:p,multiSelectionKeyCode:_}),M.jsx(q_,{onPaneContextMenu:a,elementsSelectable:C,zoomOnScroll:z,zoomOnPinch:E,panOnScroll:ue,panOnScrollSpeed:A,panOnScrollMode:$,zoomOnDoubleClick:F,panOnDrag:!j&&ie,defaultViewport:K,translateExtent:Z,minZoom:J,maxZoom:ee,zoomActivationKeyCode:k,preventScrolling:G,noWheelClassName:W,noPanClassName:b,onViewportChange:D,isControlledViewport:I,paneClickDistance:d,children:M.jsxs(Z_,{onSelectionStart:y,onSelectionEnd:w,onPaneClick:r,onPaneMouseEnter:o,onPaneMouseMove:s,onPaneMouseLeave:l,onPaneContextMenu:a,onPaneScroll:c,panOnDrag:ie,isSelecting:!!fe,selectionMode:v,selectionKeyPressed:j,selectionOnDrag:ae,children:[t,B&&M.jsx(ck,{onSelectionContextMenu:P,noPanClassName:b,disableKeyboardA11y:H})]})})}bg.displayName="FlowRenderer";const dk=q.memo(bg),hk=t=>r=>t?Hc(r.nodeLookup,{x:0,y:0,width:r.width,height:r.height},r.transform,!0).map(o=>o.id):Array.from(r.nodeLookup.keys());function pk(t){return Te(q.useCallback(hk(t),[t]),He)}const mk=t=>t.updateNodeInternals;function gk(){const t=Te(mk),[r]=q.useState(()=>typeof ResizeObserver>"u"?null:new ResizeObserver(o=>{const s=new Map;o.forEach(l=>{const a=l.target.getAttribute("data-id");s.set(a,{id:a,nodeElement:l.target,force:!0})}),t(s)}));return q.useEffect(()=>()=>{r==null||r.disconnect()},[r]),r}function yk({node:t,nodeType:r,hasDimensions:o,resizeObserver:s}){const l=$e(),a=q.useRef(null),c=q.useRef(null),d=q.useRef(t.sourcePosition),p=q.useRef(t.targetPosition),g=q.useRef(r),m=o&&!!t.internals.handleBounds;return q.useEffect(()=>{a.current&&!t.hidden&&(!m||c.current!==a.current)&&(c.current&&(s==null||s.unobserve(c.current)),s==null||s.observe(a.current),c.current=a.current)},[m,t.hidden]),q.useEffect(()=>()=>{c.current&&(s==null||s.unobserve(c.current),c.current=null)},[]),q.useEffect(()=>{if(a.current){const v=g.current!==r,y=d.current!==t.sourcePosition,w=p.current!==t.targetPosition;(v||y||w)&&(g.current=r,d.current=t.sourcePosition,p.current=t.targetPosition,l.getState().updateNodeInternals(new Map([[t.id,{id:t.id,nodeElement:a.current,force:!0}]])))}},[t.id,r,t.sourcePosition,t.targetPosition]),a}function vk({id:t,onClick:r,onMouseEnter:o,onMouseMove:s,onMouseLeave:l,onContextMenu:a,onDoubleClick:c,nodesDraggable:d,elementsSelectable:p,nodesConnectable:g,nodesFocusable:m,resizeObserver:v,noDragClassName:y,noPanClassName:w,disableKeyboardA11y:_,rfId:S,nodeTypes:k,nodeClickDistance:C,onError:z}){const{node:E,internals:N,isParent:A}=Te(re=>{const ce=re.nodeLookup.get(t),Se=re.parentLookup.has(t);return{node:ce,internals:ce.internals,isParent:Se}},He);let $=E.type||"default",F=(k==null?void 0:k[$])||$p[$];F===void 0&&(z==null||z("003",tn.error003($)),$="default",F=$p.default);const X=!!(E.draggable||d&&typeof E.draggable>"u"),K=!!(E.selectable||p&&typeof E.selectable>"u"),Z=!!(E.connectable||g&&typeof E.connectable>"u"),J=!!(E.focusable||m&&typeof E.focusable>"u"),ee=$e(),G=lg(E),P=yk({node:E,nodeType:$,hasDimensions:G,resizeObserver:v}),W=$g({nodeRef:P,disabled:E.hidden||!X,noDragClassName:y,handleSelector:E.dragHandle,nodeId:t,isSelectable:K,nodeClickDistance:C}),b=jg();if(E.hidden)return null;const H=En(E),D=uk(E),I=K||X||r||o||s||l,B=o?re=>o(re,{...N.userNode}):void 0,T=s?re=>s(re,{...N.userNode}):void 0,j=l?re=>l(re,{...N.userNode}):void 0,oe=a?re=>a(re,{...N.userNode}):void 0,ie=c?re=>c(re,{...N.userNode}):void 0,ue=re=>{const{selectNodesOnDrag:ce,nodeDragThreshold:Se}=ee.getState();K&&(!ce||!X||Se>0)&&Rc({id:t,store:ee,nodeRef:P}),r&&r(re,{...N.userNode})},ae=re=>{if(!(cg(re.nativeEvent)||_)){if(Zm.includes(re.key)&&K){const ce=re.key==="Escape";Rc({id:t,store:ee,unselect:ce,nodeRef:P})}else if(X&&E.selected&&Object.prototype.hasOwnProperty.call(Ml,re.key)){re.preventDefault();const{ariaLabelConfig:ce}=ee.getState();ee.setState({ariaLiveMessage:ce["node.a11yDescription.ariaLiveMessage"]({direction:re.key.replace("Arrow","").toLowerCase(),x:~~N.positionAbsolute.x,y:~~N.positionAbsolute.y})}),b({direction:Ml[re.key],factor:re.shiftKey?4:1})}}},fe=()=>{var Ne;if(_||!((Ne=P.current)!=null&&Ne.matches(":focus-visible")))return;const{transform:re,width:ce,height:Se,autoPanOnNodeFocus:_e,setCenter:ye}=ee.getState();if(!_e)return;Hc(new Map([[t,E]]),{x:0,y:0,width:ce,height:Se},re,!0).length>0||ye(E.position.x+H.width/2,E.position.y+H.height/2,{zoom:re[2]})};return M.jsx("div",{className:Ye(["react-flow__node",`react-flow__node-${$}`,{[w]:X},E.className,{selected:E.selected,selectable:K,parent:A,draggable:X,dragging:W}]),ref:P,style:{zIndex:N.z,transform:`translate(${N.positionAbsolute.x}px,${N.positionAbsolute.y}px)`,pointerEvents:I?"all":"none",visibility:G?"visible":"hidden",...E.style,...D},"data-id":t,"data-testid":`rf__node-${t}`,onMouseEnter:B,onMouseMove:T,onMouseLeave:j,onContextMenu:oe,onClick:ue,onDoubleClick:ie,onKeyDown:J?ae:void 0,tabIndex:J?0:void 0,onFocus:J?fe:void 0,role:E.ariaRole??(J?"group":void 0),"aria-roledescription":"node","aria-describedby":_?void 0:`${Rg}-${S}`,"aria-label":E.ariaLabel,...E.domAttributes,children:M.jsx(ek,{value:t,children:M.jsx(F,{id:t,data:E.data,type:$,positionAbsoluteX:N.positionAbsolute.x,positionAbsoluteY:N.positionAbsolute.y,selected:E.selected??!1,selectable:K,draggable:X,deletable:E.deletable??!0,isConnectable:Z,sourcePosition:E.sourcePosition,targetPosition:E.targetPosition,dragging:W,dragHandle:E.dragHandle,zIndex:N.z,parentId:E.parentId,...H})})})}const wk=t=>({nodesDraggable:t.nodesDraggable,nodesConnectable:t.nodesConnectable,nodesFocusable:t.nodesFocusable,elementsSelectable:t.elementsSelectable,onError:t.onError});function Hg(t){const{nodesDraggable:r,nodesConnectable:o,nodesFocusable:s,elementsSelectable:l,onError:a}=Te(wk,He),c=pk(t.onlyRenderVisibleElements),d=gk();return M.jsx("div",{className:"react-flow__nodes",style:Ql,children:c.map(p=>M.jsx(vk,{id:p,nodeTypes:t.nodeTypes,nodeExtent:t.nodeExtent,onClick:t.onNodeClick,onMouseEnter:t.onNodeMouseEnter,onMouseMove:t.onNodeMouseMove,onMouseLeave:t.onNodeMouseLeave,onContextMenu:t.onNodeContextMenu,onDoubleClick:t.onNodeDoubleClick,noDragClassName:t.noDragClassName,noPanClassName:t.noPanClassName,rfId:t.rfId,disableKeyboardA11y:t.disableKeyboardA11y,resizeObserver:d,nodesDraggable:r,nodesConnectable:o,nodesFocusable:s,elementsSelectable:l,nodeClickDistance:t.nodeClickDistance,onError:a},p))})}Hg.displayName="NodeRenderer";const xk=q.memo(Hg);function Sk(t){return Te(q.useCallback(o=>{if(!t)return o.edges.map(l=>l.id);const s=[];if(o.width&&o.height)for(const l of o.edges){const a=o.nodeLookup.get(l.source),c=o.nodeLookup.get(l.target);a&&c&&SE({sourceNode:a,targetNode:c,width:o.width,height:o.height,transform:o.transform})&&s.push(l.id)}return s},[t]),He)}const Ek=({color:t="none",strokeWidth:r=1})=>M.jsx("polyline",{style:{stroke:t,strokeWidth:r},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),_k=({color:t="none",strokeWidth:r=1})=>M.jsx("polyline",{style:{stroke:t,fill:t,strokeWidth:r},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"}),Fp={[Cl.Arrow]:Ek,[Cl.ArrowClosed]:_k};function kk(t){const r=$e();return q.useMemo(()=>{var l,a;return Object.prototype.hasOwnProperty.call(Fp,t)?Fp[t]:((a=(l=r.getState()).onError)==null||a.call(l,"009",tn.error009(t)),null)},[t])}const Nk=({id:t,type:r,color:o,width:s=12.5,height:l=12.5,markerUnits:a="strokeWidth",strokeWidth:c,orient:d="auto-start-reverse"})=>{const p=kk(r);return p?M.jsx("marker",{className:"react-flow__arrowhead",id:t,markerWidth:`${s}`,markerHeight:`${l}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:d,refX:"0",refY:"0",children:M.jsx(p,{color:o,strokeWidth:c})}):null},Bg=({defaultColor:t,rfId:r})=>{const o=Te(a=>a.edges),s=Te(a=>a.defaultEdgeOptions),l=q.useMemo(()=>PE(o,{id:r,defaultColor:t,defaultMarkerStart:s==null?void 0:s.markerStart,defaultMarkerEnd:s==null?void 0:s.markerEnd}),[o,s,r,t]);return l.length?M.jsx("svg",{className:"react-flow__marker","aria-hidden":"true",children:M.jsx("defs",{children:l.map(a=>M.jsx(Nk,{id:a.id,type:a.type,color:a.color,width:a.width,height:a.height,markerUnits:a.markerUnits,strokeWidth:a.strokeWidth,orient:a.orient},a.id))})}):null};Bg.displayName="MarkerDefinitions";var Ck=q.memo(Bg);function Vg({x:t,y:r,label:o,labelStyle:s,labelShowBg:l=!0,labelBgStyle:a,labelBgPadding:c=[2,4],labelBgBorderRadius:d=2,children:p,className:g,...m}){const[v,y]=q.useState({x:1,y:0,width:0,height:0}),w=Ye(["react-flow__edge-textwrapper",g]),_=q.useRef(null);return q.useEffect(()=>{if(_.current){const S=_.current.getBBox();y({x:S.x,y:S.y,width:S.width,height:S.height})}},[o]),o?M.jsxs("g",{transform:`translate(${t-v.width/2} ${r-v.height/2})`,className:w,visibility:v.width?"visible":"hidden",...m,children:[l&&M.jsx("rect",{width:v.width+2*c[0],x:-c[0],y:-c[1],height:v.height+2*c[1],className:"react-flow__edge-textbg",style:a,rx:d,ry:d}),M.jsx("text",{className:"react-flow__edge-text",y:v.height/2,dy:"0.3em",ref:_,style:s,children:o}),p]}):null}Vg.displayName="EdgeText";const Tk=q.memo(Vg);function Kl({path:t,labelX:r,labelY:o,label:s,labelStyle:l,labelShowBg:a,labelBgStyle:c,labelBgPadding:d,labelBgBorderRadius:p,interactionWidth:g=20,...m}){return M.jsxs(M.Fragment,{children:[M.jsx("path",{...m,d:t,fill:"none",className:Ye(["react-flow__edge-path",m.className])}),g&&M.jsx("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),s&&Bt(r)&&Bt(o)?M.jsx(Tk,{x:r,y:o,label:s,labelStyle:l,labelShowBg:a,labelBgStyle:c,labelBgPadding:d,labelBgBorderRadius:p}):null]})}function bp({pos:t,x1:r,y1:o,x2:s,y2:l}){return t===ve.Left||t===ve.Right?[.5*(r+s),o]:[r,.5*(o+l)]}function Ug({sourceX:t,sourceY:r,sourcePosition:o=ve.Bottom,targetX:s,targetY:l,targetPosition:a=ve.Top}){const[c,d]=bp({pos:o,x1:t,y1:r,x2:s,y2:l}),[p,g]=bp({pos:a,x1:s,y1:l,x2:t,y2:r}),[m,v,y,w]=dg({sourceX:t,sourceY:r,targetX:s,targetY:l,sourceControlX:c,sourceControlY:d,targetControlX:p,targetControlY:g});return[`M${t},${r} C${c},${d} ${p},${g} ${s},${l}`,m,v,y,w]}function Wg(t){return q.memo(({id:r,sourceX:o,sourceY:s,targetX:l,targetY:a,sourcePosition:c,targetPosition:d,label:p,labelStyle:g,labelShowBg:m,labelBgStyle:v,labelBgPadding:y,labelBgBorderRadius:w,style:_,markerEnd:S,markerStart:k,interactionWidth:C})=>{const[z,E,N]=Ug({sourceX:o,sourceY:s,sourcePosition:c,targetX:l,targetY:a,targetPosition:d}),A=t.isInternal?void 0:r;return M.jsx(Kl,{id:A,path:z,labelX:E,labelY:N,label:p,labelStyle:g,labelShowBg:m,labelBgStyle:v,labelBgPadding:y,labelBgBorderRadius:w,style:_,markerEnd:S,markerStart:k,interactionWidth:C})})}const Pk=Wg({isInternal:!1}),Xg=Wg({isInternal:!0});Pk.displayName="SimpleBezierEdge";Xg.displayName="SimpleBezierEdgeInternal";function Yg(t){return q.memo(({id:r,sourceX:o,sourceY:s,targetX:l,targetY:a,label:c,labelStyle:d,labelShowBg:p,labelBgStyle:g,labelBgPadding:m,labelBgBorderRadius:v,style:y,sourcePosition:w=ve.Bottom,targetPosition:_=ve.Top,markerEnd:S,markerStart:k,pathOptions:C,interactionWidth:z})=>{const[E,N,A]=Nc({sourceX:o,sourceY:s,sourcePosition:w,targetX:l,targetY:a,targetPosition:_,borderRadius:C==null?void 0:C.borderRadius,offset:C==null?void 0:C.offset}),$=t.isInternal?void 0:r;return M.jsx(Kl,{id:$,path:E,labelX:N,labelY:A,label:c,labelStyle:d,labelShowBg:p,labelBgStyle:g,labelBgPadding:m,labelBgBorderRadius:v,style:y,markerEnd:S,markerStart:k,interactionWidth:z})})}const qg=Yg({isInternal:!1}),Qg=Yg({isInternal:!0});qg.displayName="SmoothStepEdge";Qg.displayName="SmoothStepEdgeInternal";function Kg(t){return q.memo(({id:r,...o})=>{var l;const s=t.isInternal?void 0:r;return M.jsx(qg,{...o,id:s,pathOptions:q.useMemo(()=>{var a;return{borderRadius:0,offset:(a=o.pathOptions)==null?void 0:a.offset}},[(l=o.pathOptions)==null?void 0:l.offset])})})}const Rk=Kg({isInternal:!1}),Gg=Kg({isInternal:!0});Rk.displayName="StepEdge";Gg.displayName="StepEdgeInternal";function Zg(t){return q.memo(({id:r,sourceX:o,sourceY:s,targetX:l,targetY:a,label:c,labelStyle:d,labelShowBg:p,labelBgStyle:g,labelBgPadding:m,labelBgBorderRadius:v,style:y,markerEnd:w,markerStart:_,interactionWidth:S})=>{const[k,C,z]=gg({sourceX:o,sourceY:s,targetX:l,targetY:a}),E=t.isInternal?void 0:r;return M.jsx(Kl,{id:E,path:k,labelX:C,labelY:z,label:c,labelStyle:d,labelShowBg:p,labelBgStyle:g,labelBgPadding:m,labelBgBorderRadius:v,style:y,markerEnd:w,markerStart:_,interactionWidth:S})})}const Mk=Zg({isInternal:!1}),Jg=Zg({isInternal:!0});Mk.displayName="StraightEdge";Jg.displayName="StraightEdgeInternal";function e0(t){return q.memo(({id:r,sourceX:o,sourceY:s,targetX:l,targetY:a,sourcePosition:c=ve.Bottom,targetPosition:d=ve.Top,label:p,labelStyle:g,labelShowBg:m,labelBgStyle:v,labelBgPadding:y,labelBgBorderRadius:w,style:_,markerEnd:S,markerStart:k,pathOptions:C,interactionWidth:z})=>{const[E,N,A]=hg({sourceX:o,sourceY:s,sourcePosition:c,targetX:l,targetY:a,targetPosition:d,curvature:C==null?void 0:C.curvature}),$=t.isInternal?void 0:r;return M.jsx(Kl,{id:$,path:E,labelX:N,labelY:A,label:p,labelStyle:g,labelShowBg:m,labelBgStyle:v,labelBgPadding:y,labelBgBorderRadius:w,style:_,markerEnd:S,markerStart:k,interactionWidth:z})})}const Ak=e0({isInternal:!1}),t0=e0({isInternal:!0});Ak.displayName="BezierEdge";t0.displayName="BezierEdgeInternal";const Hp={default:t0,straight:Jg,step:Gg,smoothstep:Qg,simplebezier:Xg},Bp={sourceX:null,sourceY:null,targetX:null,targetY:null,sourcePosition:null,targetPosition:null},Lk=(t,r,o)=>o===ve.Left?t-r:o===ve.Right?t+r:t,Ik=(t,r,o)=>o===ve.Top?t-r:o===ve.Bottom?t+r:t,Vp="react-flow__edgeupdater";function Up({position:t,centerX:r,centerY:o,radius:s=10,onMouseDown:l,onMouseEnter:a,onMouseOut:c,type:d}){return M.jsx("circle",{onMouseDown:l,onMouseEnter:a,onMouseOut:c,className:Ye([Vp,`${Vp}-${d}`]),cx:Lk(r,s,t),cy:Ik(o,s,t),r:s,stroke:"transparent",fill:"transparent"})}function zk({isReconnectable:t,reconnectRadius:r,edge:o,sourceX:s,sourceY:l,targetX:a,targetY:c,sourcePosition:d,targetPosition:p,onReconnect:g,onReconnectStart:m,onReconnectEnd:v,setReconnecting:y,setUpdateHover:w}){const _=$e(),S=(N,A)=>{if(N.button!==0)return;const{autoPanOnConnect:$,domNode:F,isValidConnection:X,connectionMode:K,connectionRadius:Z,lib:J,onConnectStart:ee,onConnectEnd:G,cancelConnection:P,nodeLookup:W,rfId:b,panBy:H,updateConnection:D}=_.getState(),I=A.type==="target";y(!0),m==null||m(N,o,A.type);const B=(j,oe)=>{y(!1),v==null||v(j,o,A.type,oe)},T=j=>g==null?void 0:g(o,j);Pc.onPointerDown(N.nativeEvent,{autoPanOnConnect:$,connectionMode:K,connectionRadius:Z,domNode:F,handleId:A.id,nodeId:A.nodeId,nodeLookup:W,isTarget:I,edgeUpdaterType:A.type,lib:J,flowId:b,cancelConnection:P,panBy:H,isValidConnection:X,onConnect:T,onConnectStart:ee,onConnectEnd:G,onReconnectEnd:B,updateConnection:D,getTransform:()=>_.getState().transform,getFromHandle:()=>_.getState().connection.fromHandle})},k=N=>S(N,{nodeId:o.target,id:o.targetHandle??null,type:"target"}),C=N=>S(N,{nodeId:o.source,id:o.sourceHandle??null,type:"source"}),z=()=>w(!0),E=()=>w(!1);return M.jsxs(M.Fragment,{children:[(t===!0||t==="source")&&M.jsx(Up,{position:d,centerX:s,centerY:l,radius:r,onMouseDown:k,onMouseEnter:z,onMouseOut:E,type:"source"}),(t===!0||t==="target")&&M.jsx(Up,{position:p,centerX:a,centerY:c,radius:r,onMouseDown:C,onMouseEnter:z,onMouseOut:E,type:"target"})]})}function Ok({id:t,edgesFocusable:r,edgesReconnectable:o,elementsSelectable:s,onClick:l,onDoubleClick:a,onContextMenu:c,onMouseEnter:d,onMouseMove:p,onMouseLeave:g,reconnectRadius:m,onReconnect:v,onReconnectStart:y,onReconnectEnd:w,rfId:_,edgeTypes:S,noPanClassName:k,onError:C,disableKeyboardA11y:z}){let E=Te(ye=>ye.edgeLookup.get(t));const N=Te(ye=>ye.defaultEdgeOptions);E=N?{...N,...E}:E;let A=E.type||"default",$=(S==null?void 0:S[A])||Hp[A];$===void 0&&(C==null||C("011",tn.error011(A)),A="default",$=Hp.default);const F=!!(E.focusable||r&&typeof E.focusable>"u"),X=typeof v<"u"&&(E.reconnectable||o&&typeof E.reconnectable>"u"),K=!!(E.selectable||s&&typeof E.selectable>"u"),Z=q.useRef(null),[J,ee]=q.useState(!1),[G,P]=q.useState(!1),W=$e(),{zIndex:b,sourceX:H,sourceY:D,targetX:I,targetY:B,sourcePosition:T,targetPosition:j}=Te(q.useCallback(ye=>{const Pe=ye.nodeLookup.get(E.source),Ne=ye.nodeLookup.get(E.target);if(!Pe||!Ne)return{zIndex:E.zIndex,...Bp};const Ie=TE({id:t,sourceNode:Pe,targetNode:Ne,sourceHandle:E.sourceHandle||null,targetHandle:E.targetHandle||null,connectionMode:ye.connectionMode,onError:C});return{zIndex:xE({selected:E.selected,zIndex:E.zIndex,sourceNode:Pe,targetNode:Ne,elevateOnSelect:ye.elevateEdgesOnSelect}),...Ie||Bp}},[E.source,E.target,E.sourceHandle,E.targetHandle,E.selected,E.zIndex]),He),oe=q.useMemo(()=>E.markerStart?`url('#${Cc(E.markerStart,_)}')`:void 0,[E.markerStart,_]),ie=q.useMemo(()=>E.markerEnd?`url('#${Cc(E.markerEnd,_)}')`:void 0,[E.markerEnd,_]);if(E.hidden||H===null||D===null||I===null||B===null)return null;const ue=ye=>{var Be;const{addSelectedEdges:Pe,unselectNodesAndEdges:Ne,multiSelectionActive:Ie}=W.getState();K&&(W.setState({nodesSelectionActive:!1}),E.selected&&Ie?(Ne({nodes:[],edges:[E]}),(Be=Z.current)==null||Be.blur()):Pe([t])),l&&l(ye,E)},ae=a?ye=>{a(ye,{...E})}:void 0,fe=c?ye=>{c(ye,{...E})}:void 0,re=d?ye=>{d(ye,{...E})}:void 0,ce=p?ye=>{p(ye,{...E})}:void 0,Se=g?ye=>{g(ye,{...E})}:void 0,_e=ye=>{var Pe;if(!z&&Zm.includes(ye.key)&&K){const{unselectNodesAndEdges:Ne,addSelectedEdges:Ie}=W.getState();ye.key==="Escape"?((Pe=Z.current)==null||Pe.blur(),Ne({edges:[E]})):Ie([t])}};return M.jsx("svg",{style:{zIndex:b},children:M.jsxs("g",{className:Ye(["react-flow__edge",`react-flow__edge-${A}`,E.className,k,{selected:E.selected,animated:E.animated,inactive:!K&&!l,updating:J,selectable:K}]),onClick:ue,onDoubleClick:ae,onContextMenu:fe,onMouseEnter:re,onMouseMove:ce,onMouseLeave:Se,onKeyDown:F?_e:void 0,tabIndex:F?0:void 0,role:E.ariaRole??(F?"group":"img"),"aria-roledescription":"edge","data-id":t,"data-testid":`rf__edge-${t}`,"aria-label":E.ariaLabel===null?void 0:E.ariaLabel||`Edge from ${E.source} to ${E.target}`,"aria-describedby":F?`${Mg}-${_}`:void 0,ref:Z,...E.domAttributes,children:[!G&&M.jsx($,{id:t,source:E.source,target:E.target,type:E.type,selected:E.selected,animated:E.animated,selectable:K,deletable:E.deletable??!0,label:E.label,labelStyle:E.labelStyle,labelShowBg:E.labelShowBg,labelBgStyle:E.labelBgStyle,labelBgPadding:E.labelBgPadding,labelBgBorderRadius:E.labelBgBorderRadius,sourceX:H,sourceY:D,targetX:I,targetY:B,sourcePosition:T,targetPosition:j,data:E.data,style:E.style,sourceHandleId:E.sourceHandle,targetHandleId:E.targetHandle,markerStart:oe,markerEnd:ie,pathOptions:"pathOptions"in E?E.pathOptions:void 0,interactionWidth:E.interactionWidth}),X&&M.jsx(zk,{edge:E,isReconnectable:X,reconnectRadius:m,onReconnect:v,onReconnectStart:y,onReconnectEnd:w,sourceX:H,sourceY:D,targetX:I,targetY:B,sourcePosition:T,targetPosition:j,setUpdateHover:ee,setReconnecting:P})]})})}const Dk=t=>({edgesFocusable:t.edgesFocusable,edgesReconnectable:t.edgesReconnectable,elementsSelectable:t.elementsSelectable,connectionMode:t.connectionMode,onError:t.onError});function n0({defaultMarkerColor:t,onlyRenderVisibleElements:r,rfId:o,edgeTypes:s,noPanClassName:l,onReconnect:a,onEdgeContextMenu:c,onEdgeMouseEnter:d,onEdgeMouseMove:p,onEdgeMouseLeave:g,onEdgeClick:m,reconnectRadius:v,onEdgeDoubleClick:y,onReconnectStart:w,onReconnectEnd:_,disableKeyboardA11y:S}){const{edgesFocusable:k,edgesReconnectable:C,elementsSelectable:z,onError:E}=Te(Dk,He),N=Sk(r);return M.jsxs("div",{className:"react-flow__edges",children:[M.jsx(Ck,{defaultColor:t,rfId:o}),N.map(A=>M.jsx(Ok,{id:A,edgesFocusable:k,edgesReconnectable:C,elementsSelectable:z,noPanClassName:l,onReconnect:a,onContextMenu:c,onMouseEnter:d,onMouseMove:p,onMouseLeave:g,onClick:m,reconnectRadius:v,onDoubleClick:y,onReconnectStart:w,onReconnectEnd:_,rfId:o,onError:E,edgeTypes:s,disableKeyboardA11y:S},A))]})}n0.displayName="EdgeRenderer";const $k=q.memo(n0),jk=t=>`translate(${t.transform[0]}px,${t.transform[1]}px) scale(${t.transform[2]})`;function Fk({children:t}){const r=Te(jk);return M.jsx("div",{className:"react-flow__viewport xyflow__viewport react-flow__container",style:{transform:r},children:t})}function bk(t){const r=qc(),o=q.useRef(!1);q.useEffect(()=>{!o.current&&r.viewportInitialized&&t&&(setTimeout(()=>t(r),1),o.current=!0)},[t,r.viewportInitialized])}const Hk=t=>{var r;return(r=t.panZoom)==null?void 0:r.syncViewport};function Bk(t){const r=Te(Hk),o=$e();return q.useEffect(()=>{t&&(r==null||r(t),o.setState({transform:[t.x,t.y,t.zoom]}))},[t,r]),null}function Vk(t){return t.connection.inProgress?{...t.connection,to:Oi(t.connection.to,t.transform)}:{...t.connection}}function Uk(t){return Vk}function Wk(t){const r=Uk();return Te(r,He)}const Xk=t=>({nodesConnectable:t.nodesConnectable,isValid:t.connection.isValid,inProgress:t.connection.inProgress,width:t.width,height:t.height});function Yk({containerStyle:t,style:r,type:o,component:s}){const{nodesConnectable:l,width:a,height:c,isValid:d,inProgress:p}=Te(Xk,He);return!(a&&l&&p)?null:M.jsx("svg",{style:t,width:a,height:c,className:"react-flow__connectionline react-flow__container",children:M.jsx("g",{className:Ye(["react-flow__connection",tg(d)]),children:M.jsx(r0,{style:r,type:o,CustomComponent:s,isValid:d})})})}const r0=({style:t,type:r=Yn.Bezier,CustomComponent:o,isValid:s})=>{const{inProgress:l,from:a,fromNode:c,fromHandle:d,fromPosition:p,to:g,toNode:m,toHandle:v,toPosition:y}=Wk();if(!l)return;if(o)return M.jsx(o,{connectionLineType:r,connectionLineStyle:t,fromNode:c,fromHandle:d,fromX:a.x,fromY:a.y,toX:g.x,toY:g.y,fromPosition:p,toPosition:y,connectionStatus:tg(s),toNode:m,toHandle:v});let w="";const _={sourceX:a.x,sourceY:a.y,sourcePosition:p,targetX:g.x,targetY:g.y,targetPosition:y};switch(r){case Yn.Bezier:[w]=hg(_);break;case Yn.SimpleBezier:[w]=Ug(_);break;case Yn.Step:[w]=Nc({..._,borderRadius:0});break;case Yn.SmoothStep:[w]=Nc(_);break;default:[w]=gg(_)}return M.jsx("path",{d:w,fill:"none",className:"react-flow__connection-path",style:t})};r0.displayName="ConnectionLine";const qk={};function Wp(t=qk){q.useRef(t),$e(),q.useEffect(()=>{},[t])}function Qk(){$e(),q.useRef(!1),q.useEffect(()=>{},[])}function o0({nodeTypes:t,edgeTypes:r,onInit:o,onNodeClick:s,onEdgeClick:l,onNodeDoubleClick:a,onEdgeDoubleClick:c,onNodeMouseEnter:d,onNodeMouseMove:p,onNodeMouseLeave:g,onNodeContextMenu:m,onSelectionContextMenu:v,onSelectionStart:y,onSelectionEnd:w,connectionLineType:_,connectionLineStyle:S,connectionLineComponent:k,connectionLineContainerStyle:C,selectionKeyCode:z,selectionOnDrag:E,selectionMode:N,multiSelectionKeyCode:A,panActivationKeyCode:$,zoomActivationKeyCode:F,deleteKeyCode:X,onlyRenderVisibleElements:K,elementsSelectable:Z,defaultViewport:J,translateExtent:ee,minZoom:G,maxZoom:P,preventScrolling:W,defaultMarkerColor:b,zoomOnScroll:H,zoomOnPinch:D,panOnScroll:I,panOnScrollSpeed:B,panOnScrollMode:T,zoomOnDoubleClick:j,panOnDrag:oe,onPaneClick:ie,onPaneMouseEnter:ue,onPaneMouseMove:ae,onPaneMouseLeave:fe,onPaneScroll:re,onPaneContextMenu:ce,paneClickDistance:Se,nodeClickDistance:_e,onEdgeContextMenu:ye,onEdgeMouseEnter:Pe,onEdgeMouseMove:Ne,onEdgeMouseLeave:Ie,reconnectRadius:Be,onReconnect:Wt,onReconnectStart:Nr,onReconnectEnd:qn,noDragClassName:Xt,noWheelClassName:It,noPanClassName:rn,disableKeyboardA11y:_n,nodeExtent:Cr,rfId:Qn,viewport:on,onViewportChange:sn}){return Wp(t),Wp(r),Qk(),bk(o),Bk(on),M.jsx(dk,{onPaneClick:ie,onPaneMouseEnter:ue,onPaneMouseMove:ae,onPaneMouseLeave:fe,onPaneContextMenu:ce,onPaneScroll:re,paneClickDistance:Se,deleteKeyCode:X,selectionKeyCode:z,selectionOnDrag:E,selectionMode:N,onSelectionStart:y,onSelectionEnd:w,multiSelectionKeyCode:A,panActivationKeyCode:$,zoomActivationKeyCode:F,elementsSelectable:Z,zoomOnScroll:H,zoomOnPinch:D,zoomOnDoubleClick:j,panOnScroll:I,panOnScrollSpeed:B,panOnScrollMode:T,panOnDrag:oe,defaultViewport:J,translateExtent:ee,minZoom:G,maxZoom:P,onSelectionContextMenu:v,preventScrolling:W,noDragClassName:Xt,noWheelClassName:It,noPanClassName:rn,disableKeyboardA11y:_n,onViewportChange:sn,isControlledViewport:!!on,children:M.jsxs(Fk,{children:[M.jsx($k,{edgeTypes:r,onEdgeClick:l,onEdgeDoubleClick:c,onReconnect:Wt,onReconnectStart:Nr,onReconnectEnd:qn,onlyRenderVisibleElements:K,onEdgeContextMenu:ye,onEdgeMouseEnter:Pe,onEdgeMouseMove:Ne,onEdgeMouseLeave:Ie,reconnectRadius:Be,defaultMarkerColor:b,noPanClassName:rn,disableKeyboardA11y:_n,rfId:Qn}),M.jsx(Yk,{style:S,type:_,component:k,containerStyle:C}),M.jsx("div",{className:"react-flow__edgelabel-renderer"}),M.jsx(xk,{nodeTypes:t,onNodeClick:s,onNodeDoubleClick:a,onNodeMouseEnter:d,onNodeMouseMove:p,onNodeMouseLeave:g,onNodeContextMenu:m,nodeClickDistance:_e,onlyRenderVisibleElements:K,noPanClassName:rn,noDragClassName:Xt,disableKeyboardA11y:_n,nodeExtent:Cr,rfId:Qn}),M.jsx("div",{className:"react-flow__viewport-portal"})]})})}o0.displayName="GraphView";const Kk=q.memo(o0),Xp=({nodes:t,edges:r,defaultNodes:o,defaultEdges:s,width:l,height:a,fitView:c,fitViewOptions:d,minZoom:p=.5,maxZoom:g=2,nodeOrigin:m,nodeExtent:v}={})=>{const y=new Map,w=new Map,_=new Map,S=new Map,k=s??r??[],C=o??t??[],z=m??[0,0],E=v??_i;vg(_,S,k);const N=Tc(C,y,w,{nodeOrigin:z,nodeExtent:E,elevateNodesOnSelect:!1});let A=[0,0,1];if(c&&l&&a){const $=zi(y,{filter:Z=>!!((Z.width||Z.initialWidth)&&(Z.height||Z.initialHeight))}),{x:F,y:X,zoom:K}=Bc($,l,a,p,g,(d==null?void 0:d.padding)??.1);A=[F,X,K]}return{rfId:"1",width:0,height:0,transform:A,nodes:C,nodesInitialized:N,nodeLookup:y,parentLookup:w,edges:k,edgeLookup:S,connectionLookup:_,onNodesChange:null,onEdgesChange:null,hasDefaultNodes:o!==void 0,hasDefaultEdges:s!==void 0,panZoom:null,minZoom:p,maxZoom:g,translateExtent:_i,nodeExtent:E,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionMode:ao.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:z,nodeDragThreshold:1,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesReconnectable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,elevateEdgesOnSelect:!1,selectNodesOnDrag:!0,multiSelectionActive:!1,fitViewQueued:c??!1,fitViewOptions:d,fitViewResolver:null,connection:{...eg},connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,autoPanOnNodeFocus:!0,autoPanSpeed:15,connectionRadius:20,onError:pE,isValidConnection:void 0,onSelectionChangeHandlers:[],lib:"react",debug:!1,ariaLabelConfig:Jm}},Gk=({nodes:t,edges:r,defaultNodes:o,defaultEdges:s,width:l,height:a,fitView:c,fitViewOptions:d,minZoom:p,maxZoom:g,nodeOrigin:m,nodeExtent:v})=>g_((y,w)=>{async function _(){const{nodeLookup:S,panZoom:k,fitViewOptions:C,fitViewResolver:z,width:E,height:N,minZoom:A,maxZoom:$}=w();k&&(await dE({nodes:S,width:E,height:N,panZoom:k,minZoom:A,maxZoom:$},C),z==null||z.resolve(!0),y({fitViewResolver:null}))}return{...Xp({nodes:t,edges:r,width:l,height:a,fitView:c,fitViewOptions:d,minZoom:p,maxZoom:g,nodeOrigin:m,nodeExtent:v,defaultNodes:o,defaultEdges:s}),setNodes:S=>{const{nodeLookup:k,parentLookup:C,nodeOrigin:z,elevateNodesOnSelect:E,fitViewQueued:N}=w(),A=Tc(S,k,C,{nodeOrigin:z,nodeExtent:v,elevateNodesOnSelect:E,checkEquality:!0});N&&A?(_(),y({nodes:S,nodesInitialized:A,fitViewQueued:!1,fitViewOptions:void 0})):y({nodes:S,nodesInitialized:A})},setEdges:S=>{const{connectionLookup:k,edgeLookup:C}=w();vg(k,C,S),y({edges:S})},setDefaultNodesAndEdges:(S,k)=>{if(S){const{setNodes:C}=w();C(S),y({hasDefaultNodes:!0})}if(k){const{setEdges:C}=w();C(k),y({hasDefaultEdges:!0})}},updateNodeInternals:S=>{const{triggerNodeChanges:k,nodeLookup:C,parentLookup:z,domNode:E,nodeOrigin:N,nodeExtent:A,debug:$,fitViewQueued:F}=w(),{changes:X,updatedInternals:K}=IE(S,C,z,E,N,A);K&&(ME(C,z,{nodeOrigin:N,nodeExtent:A}),F?(_(),y({fitViewQueued:!1,fitViewOptions:void 0})):y({}),(X==null?void 0:X.length)>0&&($&&console.log("React Flow: trigger node changes",X),k==null||k(X)))},updateNodePositions:(S,k=!1)=>{const C=[],z=[],{nodeLookup:E,triggerNodeChanges:N}=w();for(const[A,$]of S){const F=E.get(A),X=!!(F!=null&&F.expandParent&&(F!=null&&F.parentId)&&($!=null&&$.position)),K={id:A,type:"position",position:X?{x:Math.max(0,$.position.x),y:Math.max(0,$.position.y)}:$.position,dragging:k};X&&F.parentId&&C.push({id:A,parentId:F.parentId,rect:{...$.internals.positionAbsolute,width:$.measured.width??0,height:$.measured.height??0}}),z.push(K)}if(C.length>0){const{parentLookup:A,nodeOrigin:$}=w(),F=Yc(C,E,A,$);z.push(...F)}N(z)},triggerNodeChanges:S=>{const{onNodesChange:k,setNodes:C,nodes:z,hasDefaultNodes:E,debug:N}=w();if(S!=null&&S.length){if(E){const A=Ig(S,z);C(A)}N&&console.log("React Flow: trigger node changes",S),k==null||k(S)}},triggerEdgeChanges:S=>{const{onEdgesChange:k,setEdges:C,edges:z,hasDefaultEdges:E,debug:N}=w();if(S!=null&&S.length){if(E){const A=zg(S,z);C(A)}N&&console.log("React Flow: trigger edge changes",S),k==null||k(S)}},addSelectedNodes:S=>{const{multiSelectionActive:k,edgeLookup:C,nodeLookup:z,triggerNodeChanges:E,triggerEdgeChanges:N}=w();if(k){const A=S.map($=>mr($,!0));E(A);return}E(oo(z,new Set([...S]),!0)),N(oo(C))},addSelectedEdges:S=>{const{multiSelectionActive:k,edgeLookup:C,nodeLookup:z,triggerNodeChanges:E,triggerEdgeChanges:N}=w();if(k){const A=S.map($=>mr($,!0));N(A);return}N(oo(C,new Set([...S]))),E(oo(z,new Set,!0))},unselectNodesAndEdges:({nodes:S,edges:k}={})=>{const{edges:C,nodes:z,nodeLookup:E,triggerNodeChanges:N,triggerEdgeChanges:A}=w(),$=S||z,F=k||C,X=$.map(Z=>{const J=E.get(Z.id);return J&&(J.selected=!1),mr(Z.id,!1)}),K=F.map(Z=>mr(Z.id,!1));N(X),A(K)},setMinZoom:S=>{const{panZoom:k,maxZoom:C}=w();k==null||k.setScaleExtent([S,C]),y({minZoom:S})},setMaxZoom:S=>{const{panZoom:k,minZoom:C}=w();k==null||k.setScaleExtent([C,S]),y({maxZoom:S})},setTranslateExtent:S=>{var k;(k=w().panZoom)==null||k.setTranslateExtent(S),y({translateExtent:S})},setPaneClickDistance:S=>{var k;(k=w().panZoom)==null||k.setClickDistance(S)},resetSelectedElements:()=>{const{edges:S,nodes:k,triggerNodeChanges:C,triggerEdgeChanges:z,elementsSelectable:E}=w();if(!E)return;const N=k.reduce(($,F)=>F.selected?[...$,mr(F.id,!1)]:$,[]),A=S.reduce(($,F)=>F.selected?[...$,mr(F.id,!1)]:$,[]);C(N),z(A)},setNodeExtent:S=>{const{nodes:k,nodeLookup:C,parentLookup:z,nodeOrigin:E,elevateNodesOnSelect:N,nodeExtent:A}=w();S[0][0]===A[0][0]&&S[0][1]===A[0][1]&&S[1][0]===A[1][0]&&S[1][1]===A[1][1]||(Tc(k,C,z,{nodeOrigin:E,nodeExtent:S,elevateNodesOnSelect:N,checkEquality:!1}),y({nodeExtent:S}))},panBy:S=>{const{transform:k,width:C,height:z,panZoom:E,translateExtent:N}=w();return zE({delta:S,panZoom:E,transform:k,translateExtent:N,width:C,height:z})},setCenter:async(S,k,C)=>{const{width:z,height:E,maxZoom:N,panZoom:A}=w();if(!A)return Promise.resolve(!1);const $=typeof(C==null?void 0:C.zoom)<"u"?C.zoom:N;return await A.setViewport({x:z/2-S*$,y:E/2-k*$,zoom:$},{duration:C==null?void 0:C.duration,ease:C==null?void 0:C.ease,interpolate:C==null?void 0:C.interpolate}),Promise.resolve(!0)},cancelConnection:()=>{y({connection:{...eg}})},updateConnection:S=>{y({connection:S})},reset:()=>y({...Xp()})}},Object.is);function i0({initialNodes:t,initialEdges:r,defaultNodes:o,defaultEdges:s,initialWidth:l,initialHeight:a,initialMinZoom:c,initialMaxZoom:d,initialFitViewOptions:p,fitView:g,nodeOrigin:m,nodeExtent:v,children:y}){const[w]=q.useState(()=>Gk({nodes:t,edges:r,defaultNodes:o,defaultEdges:s,width:l,height:a,fitView:g,minZoom:c,maxZoom:d,fitViewOptions:p,nodeOrigin:m,nodeExtent:v}));return M.jsx(y_,{value:w,children:M.jsx(H_,{children:y})})}function Zk({children:t,nodes:r,edges:o,defaultNodes:s,defaultEdges:l,width:a,height:c,fitView:d,fitViewOptions:p,minZoom:g,maxZoom:m,nodeOrigin:v,nodeExtent:y}){return q.useContext(Yl)?M.jsx(M.Fragment,{children:t}):M.jsx(i0,{initialNodes:r,initialEdges:o,defaultNodes:s,defaultEdges:l,initialWidth:a,initialHeight:c,fitView:d,initialFitViewOptions:p,initialMinZoom:g,initialMaxZoom:m,nodeOrigin:v,nodeExtent:y,children:t})}const Jk={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0};function eN({nodes:t,edges:r,defaultNodes:o,defaultEdges:s,className:l,nodeTypes:a,edgeTypes:c,onNodeClick:d,onEdgeClick:p,onInit:g,onMove:m,onMoveStart:v,onMoveEnd:y,onConnect:w,onConnectStart:_,onConnectEnd:S,onClickConnectStart:k,onClickConnectEnd:C,onNodeMouseEnter:z,onNodeMouseMove:E,onNodeMouseLeave:N,onNodeContextMenu:A,onNodeDoubleClick:$,onNodeDragStart:F,onNodeDrag:X,onNodeDragStop:K,onNodesDelete:Z,onEdgesDelete:J,onDelete:ee,onSelectionChange:G,onSelectionDragStart:P,onSelectionDrag:W,onSelectionDragStop:b,onSelectionContextMenu:H,onSelectionStart:D,onSelectionEnd:I,onBeforeDelete:B,connectionMode:T,connectionLineType:j=Yn.Bezier,connectionLineStyle:oe,connectionLineComponent:ie,connectionLineContainerStyle:ue,deleteKeyCode:ae="Backspace",selectionKeyCode:fe="Shift",selectionOnDrag:re=!1,selectionMode:ce=ki.Full,panActivationKeyCode:Se="Space",multiSelectionKeyCode:_e=Rl()?"Meta":"Control",zoomActivationKeyCode:ye=Rl()?"Meta":"Control",snapToGrid:Pe,snapGrid:Ne,onlyRenderVisibleElements:Ie=!1,selectNodesOnDrag:Be,nodesDraggable:Wt,autoPanOnNodeFocus:Nr,nodesConnectable:qn,nodesFocusable:Xt,nodeOrigin:It=Ag,edgesFocusable:rn,edgesReconnectable:_n,elementsSelectable:Cr=!0,defaultViewport:Qn=A_,minZoom:on=.5,maxZoom:sn=2,translateExtent:Kn=_i,preventScrolling:Di=!0,nodeExtent:ln,defaultMarkerColor:Gn="#b1b1b7",zoomOnScroll:Gl=!0,zoomOnPinch:$i=!0,panOnScroll:ji=!1,panOnScrollSpeed:Zl=.5,panOnScrollMode:vo=xr.Free,zoomOnDoubleClick:wo=!0,panOnDrag:xo=!0,onPaneClick:So,onPaneMouseEnter:Eo,onPaneMouseMove:kn,onPaneMouseLeave:Nn,onPaneScroll:Fi,onPaneContextMenu:bi,paneClickDistance:_o=0,nodeClickDistance:Hi=0,children:Bi,onReconnect:ko,onReconnectStart:Vi,onReconnectEnd:Zn,onEdgeContextMenu:No,onEdgeDoubleClick:Jn,onEdgeMouseEnter:Jl,onEdgeMouseMove:er,onEdgeMouseLeave:Tr,reconnectRadius:Pr=10,onNodesChange:Co,onEdgesChange:eu,noDragClassName:tu="nodrag",noWheelClassName:nu="nowheel",noPanClassName:Yt="nopan",fitView:To,fitViewOptions:Po,connectOnClick:ru,attributionPosition:Ui,proOptions:Wi,defaultEdgeOptions:Xi,elevateNodesOnSelect:Yi,elevateEdgesOnSelect:ou,disableKeyboardA11y:qi=!1,autoPanOnConnect:je,autoPanOnNodeDrag:iu,autoPanSpeed:Ro,connectionRadius:Qi,isValidConnection:Rr,onError:su,style:Ki,id:tr,nodeDragThreshold:Ct,viewport:lu,onViewportChange:vt,width:uu,height:au,colorMode:cu="light",debug:Mr,onScroll:un,ariaLabelConfig:nr,...Ar},fu){const Mo=tr||"1",Ao=O_(cu),Gi=q.useCallback(Lr=>{Lr.currentTarget.scrollTo({top:0,left:0,behavior:"instant"}),un==null||un(Lr)},[un]);return M.jsx("div",{"data-testid":"rf__wrapper",...Ar,onScroll:Gi,style:{...Ki,...Jk},ref:fu,className:Ye(["react-flow",l,Ao]),id:tr,role:"application",children:M.jsxs(Zk,{nodes:t,edges:r,width:uu,height:au,fitView:To,fitViewOptions:Po,minZoom:on,maxZoom:sn,nodeOrigin:It,nodeExtent:ln,children:[M.jsx(Kk,{onInit:g,onNodeClick:d,onEdgeClick:p,onNodeMouseEnter:z,onNodeMouseMove:E,onNodeMouseLeave:N,onNodeContextMenu:A,onNodeDoubleClick:$,nodeTypes:a,edgeTypes:c,connectionLineType:j,connectionLineStyle:oe,connectionLineComponent:ie,connectionLineContainerStyle:ue,selectionKeyCode:fe,selectionOnDrag:re,selectionMode:ce,deleteKeyCode:ae,multiSelectionKeyCode:_e,panActivationKeyCode:Se,zoomActivationKeyCode:ye,onlyRenderVisibleElements:Ie,defaultViewport:Qn,translateExtent:Kn,minZoom:on,maxZoom:sn,preventScrolling:Di,zoomOnScroll:Gl,zoomOnPinch:$i,zoomOnDoubleClick:wo,panOnScroll:ji,panOnScrollSpeed:Zl,panOnScrollMode:vo,panOnDrag:xo,onPaneClick:So,onPaneMouseEnter:Eo,onPaneMouseMove:kn,onPaneMouseLeave:Nn,onPaneScroll:Fi,onPaneContextMenu:bi,paneClickDistance:_o,nodeClickDistance:Hi,onSelectionContextMenu:H,onSelectionStart:D,onSelectionEnd:I,onReconnect:ko,onReconnectStart:Vi,onReconnectEnd:Zn,onEdgeContextMenu:No,onEdgeDoubleClick:Jn,onEdgeMouseEnter:Jl,onEdgeMouseMove:er,onEdgeMouseLeave:Tr,reconnectRadius:Pr,defaultMarkerColor:Gn,noDragClassName:tu,noWheelClassName:nu,noPanClassName:Yt,rfId:Mo,disableKeyboardA11y:qi,nodeExtent:ln,viewport:lu,onViewportChange:vt}),M.jsx(z_,{nodes:t,edges:r,defaultNodes:o,defaultEdges:s,onConnect:w,onConnectStart:_,onConnectEnd:S,onClickConnectStart:k,onClickConnectEnd:C,nodesDraggable:Wt,autoPanOnNodeFocus:Nr,nodesConnectable:qn,nodesFocusable:Xt,edgesFocusable:rn,edgesReconnectable:_n,elementsSelectable:Cr,elevateNodesOnSelect:Yi,elevateEdgesOnSelect:ou,minZoom:on,maxZoom:sn,nodeExtent:ln,onNodesChange:Co,onEdgesChange:eu,snapToGrid:Pe,snapGrid:Ne,connectionMode:T,translateExtent:Kn,connectOnClick:ru,defaultEdgeOptions:Xi,fitView:To,fitViewOptions:Po,onNodesDelete:Z,onEdgesDelete:J,onDelete:ee,onNodeDragStart:F,onNodeDrag:X,onNodeDragStop:K,onSelectionDrag:W,onSelectionDragStart:P,onSelectionDragStop:b,onMove:m,onMoveStart:v,onMoveEnd:y,noPanClassName:Yt,nodeOrigin:It,rfId:Mo,autoPanOnConnect:je,autoPanOnNodeDrag:iu,autoPanSpeed:Ro,onError:su,connectionRadius:Qi,isValidConnection:Rr,selectNodesOnDrag:Be,nodeDragThreshold:Ct,onBeforeDelete:B,paneClickDistance:_o,debug:Mr,ariaLabelConfig:nr}),M.jsx(M_,{onSelectionChange:G}),Bi,M.jsx(N_,{proOptions:Wi,position:Ui}),M.jsx(__,{rfId:Mo,disableKeyboardA11y:qi})]})})}var tN=Og(eN);function nN(t){const[r,o]=q.useState(t),s=q.useCallback(l=>o(a=>Ig(l,a)),[]);return[r,o,s]}function rN(t){const[r,o]=q.useState(t),s=q.useCallback(l=>o(a=>zg(l,a)),[]);return[r,o,s]}function oN({dimensions:t,lineWidth:r,variant:o,className:s}){return M.jsx("path",{strokeWidth:r,d:`M${t[0]/2} 0 V${t[1]} M0 ${t[1]/2} H${t[0]}`,className:Ye(["react-flow__background-pattern",o,s])})}function iN({radius:t,className:r}){return M.jsx("circle",{cx:t,cy:t,r:t,className:Ye(["react-flow__background-pattern","dots",r])})}var xn;(function(t){t.Lines="lines",t.Dots="dots",t.Cross="cross"})(xn||(xn={}));const sN={[xn.Dots]:1,[xn.Lines]:1,[xn.Cross]:6},lN=t=>({transform:t.transform,patternId:`pattern-${t.rfId}`});function s0({id:t,variant:r=xn.Dots,gap:o=20,size:s,lineWidth:l=1,offset:a=0,color:c,bgColor:d,style:p,className:g,patternClassName:m}){const v=q.useRef(null),{transform:y,patternId:w}=Te(lN,He),_=s||sN[r],S=r===xn.Dots,k=r===xn.Cross,C=Array.isArray(o)?o:[o,o],z=[C[0]*y[2]||1,C[1]*y[2]||1],E=_*y[2],N=Array.isArray(a)?a:[a,a],A=k?[E,E]:z,$=[N[0]*y[2]||1+A[0]/2,N[1]*y[2]||1+A[1]/2],F=`${w}${t||""}`;return M.jsxs("svg",{className:Ye(["react-flow__background",g]),style:{...p,...Ql,"--xy-background-color-props":d,"--xy-background-pattern-color-props":c},ref:v,"data-testid":"rf__background",children:[M.jsx("pattern",{id:F,x:y[0]%z[0],y:y[1]%z[1],width:z[0],height:z[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${$[0]},-${$[1]})`,children:S?M.jsx(iN,{radius:E/2,className:m}):M.jsx(oN,{dimensions:A,lineWidth:l,variant:r,className:m})}),M.jsx("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${F})`})]})}s0.displayName="Background";const uN=q.memo(s0);function aN(){return M.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",children:M.jsx("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"})})}function cN(){return M.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5",children:M.jsx("path",{d:"M0 0h32v4.2H0z"})})}function fN(){return M.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30",children:M.jsx("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"})})}function dN(){return M.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:M.jsx("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"})})}function hN(){return M.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:M.jsx("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"})})}function ll({children:t,className:r,...o}){return M.jsx("button",{type:"button",className:Ye(["react-flow__controls-button",r]),...o,children:t})}const pN=t=>({isInteractive:t.nodesDraggable||t.nodesConnectable||t.elementsSelectable,minZoomReached:t.transform[2]<=t.minZoom,maxZoomReached:t.transform[2]>=t.maxZoom,ariaLabelConfig:t.ariaLabelConfig});function l0({style:t,showZoom:r=!0,showFitView:o=!0,showInteractive:s=!0,fitViewOptions:l,onZoomIn:a,onZoomOut:c,onFitView:d,onInteractiveChange:p,className:g,children:m,position:v="bottom-left",orientation:y="vertical","aria-label":w}){const _=$e(),{isInteractive:S,minZoomReached:k,maxZoomReached:C,ariaLabelConfig:z}=Te(pN,He),{zoomIn:E,zoomOut:N,fitView:A}=qc(),$=()=>{E(),a==null||a()},F=()=>{N(),c==null||c()},X=()=>{A(l),d==null||d()},K=()=>{_.setState({nodesDraggable:!S,nodesConnectable:!S,elementsSelectable:!S}),p==null||p(!S)},Z=y==="horizontal"?"horizontal":"vertical";return M.jsxs(ql,{className:Ye(["react-flow__controls",Z,g]),position:v,style:t,"data-testid":"rf__controls","aria-label":w??z["controls.ariaLabel"],children:[r&&M.jsxs(M.Fragment,{children:[M.jsx(ll,{onClick:$,className:"react-flow__controls-zoomin",title:z["controls.zoomIn.ariaLabel"],"aria-label":z["controls.zoomIn.ariaLabel"],disabled:C,children:M.jsx(aN,{})}),M.jsx(ll,{onClick:F,className:"react-flow__controls-zoomout",title:z["controls.zoomOut.ariaLabel"],"aria-label":z["controls.zoomOut.ariaLabel"],disabled:k,children:M.jsx(cN,{})})]}),o&&M.jsx(ll,{className:"react-flow__controls-fitview",onClick:X,title:z["controls.fitView.ariaLabel"],"aria-label":z["controls.fitView.ariaLabel"],children:M.jsx(fN,{})}),s&&M.jsx(ll,{className:"react-flow__controls-interactive",onClick:K,title:z["controls.interactive.ariaLabel"],"aria-label":z["controls.interactive.ariaLabel"],children:S?M.jsx(hN,{}):M.jsx(dN,{})}),m]})}l0.displayName="Controls";const mN=q.memo(l0);function gN({id:t,x:r,y:o,width:s,height:l,style:a,color:c,strokeColor:d,strokeWidth:p,className:g,borderRadius:m,shapeRendering:v,selected:y,onClick:w}){const{background:_,backgroundColor:S}=a||{},k=c||_||S;return M.jsx("rect",{className:Ye(["react-flow__minimap-node",{selected:y},g]),x:r,y:o,rx:m,ry:m,width:s,height:l,style:{fill:k,stroke:d,strokeWidth:p},shapeRendering:v,onClick:w?C=>w(C,t):void 0})}const yN=q.memo(gN),vN=t=>t.nodes.map(r=>r.id),ac=t=>t instanceof Function?t:()=>t;function wN({nodeStrokeColor:t,nodeColor:r,nodeClassName:o="",nodeBorderRadius:s=5,nodeStrokeWidth:l,nodeComponent:a=yN,onClick:c}){const d=Te(vN,He),p=ac(r),g=ac(t),m=ac(o),v=typeof window>"u"||window.chrome?"crispEdges":"geometricPrecision";return M.jsx(M.Fragment,{children:d.map(y=>M.jsx(SN,{id:y,nodeColorFunc:p,nodeStrokeColorFunc:g,nodeClassNameFunc:m,nodeBorderRadius:s,nodeStrokeWidth:l,NodeComponent:a,onClick:c,shapeRendering:v},y))})}function xN({id:t,nodeColorFunc:r,nodeStrokeColorFunc:o,nodeClassNameFunc:s,nodeBorderRadius:l,nodeStrokeWidth:a,shapeRendering:c,NodeComponent:d,onClick:p}){const{node:g,x:m,y:v,width:y,height:w}=Te(_=>{const{internals:S}=_.nodeLookup.get(t),k=S.userNode,{x:C,y:z}=S.positionAbsolute,{width:E,height:N}=En(k);return{node:k,x:C,y:z,width:E,height:N}},He);return!g||g.hidden||!lg(g)?null:M.jsx(d,{x:m,y:v,width:y,height:w,style:g.style,selected:!!g.selected,className:s(g),color:r(g),borderRadius:l,strokeColor:o(g),strokeWidth:a,shapeRendering:c,onClick:p,id:g.id})}const SN=q.memo(xN);var EN=q.memo(wN);const _N=200,kN=150,NN=t=>!t.hidden,CN=t=>{const r={x:-t.transform[0]/t.transform[2],y:-t.transform[1]/t.transform[2],width:t.width/t.transform[2],height:t.height/t.transform[2]};return{viewBB:r,boundingRect:t.nodeLookup.size>0?sg(zi(t.nodeLookup,{filter:NN}),r):r,rfId:t.rfId,panZoom:t.panZoom,translateExtent:t.translateExtent,flowWidth:t.width,flowHeight:t.height,ariaLabelConfig:t.ariaLabelConfig}},TN="react-flow__minimap-desc";function u0({style:t,className:r,nodeStrokeColor:o,nodeColor:s,nodeClassName:l="",nodeBorderRadius:a=5,nodeStrokeWidth:c,nodeComponent:d,bgColor:p,maskColor:g,maskStrokeColor:m,maskStrokeWidth:v,position:y="bottom-right",onClick:w,onNodeClick:_,pannable:S=!1,zoomable:k=!1,ariaLabel:C,inversePan:z,zoomStep:E=10,offsetScale:N=5}){const A=$e(),$=q.useRef(null),{boundingRect:F,viewBB:X,rfId:K,panZoom:Z,translateExtent:J,flowWidth:ee,flowHeight:G,ariaLabelConfig:P}=Te(CN,He),W=(t==null?void 0:t.width)??_N,b=(t==null?void 0:t.height)??kN,H=F.width/W,D=F.height/b,I=Math.max(H,D),B=I*W,T=I*b,j=N*I,oe=F.x-(B-F.width)/2-j,ie=F.y-(T-F.height)/2-j,ue=B+j*2,ae=T+j*2,fe=`${TN}-${K}`,re=q.useRef(0),ce=q.useRef();re.current=I,q.useEffect(()=>{if($.current&&Z)return ce.current=BE({domNode:$.current,panZoom:Z,getTransform:()=>A.getState().transform,getViewScale:()=>re.current}),()=>{var Pe;(Pe=ce.current)==null||Pe.destroy()}},[Z]),q.useEffect(()=>{var Pe;(Pe=ce.current)==null||Pe.update({translateExtent:J,width:ee,height:G,inversePan:z,pannable:S,zoomStep:E,zoomable:k})},[S,k,z,E,J,ee,G]);const Se=w?Pe=>{var Be;const[Ne,Ie]=((Be=ce.current)==null?void 0:Be.pointer(Pe))||[0,0];w(Pe,{x:Ne,y:Ie})}:void 0,_e=_?q.useCallback((Pe,Ne)=>{const Ie=A.getState().nodeLookup.get(Ne).internals.userNode;_(Pe,Ie)},[]):void 0,ye=C??P["minimap.ariaLabel"];return M.jsx(ql,{position:y,style:{...t,"--xy-minimap-background-color-props":typeof p=="string"?p:void 0,"--xy-minimap-mask-background-color-props":typeof g=="string"?g:void 0,"--xy-minimap-mask-stroke-color-props":typeof m=="string"?m:void 0,"--xy-minimap-mask-stroke-width-props":typeof v=="number"?v*I:void 0,"--xy-minimap-node-background-color-props":typeof s=="string"?s:void 0,"--xy-minimap-node-stroke-color-props":typeof o=="string"?o:void 0,"--xy-minimap-node-stroke-width-props":typeof c=="number"?c:void 0},className:Ye(["react-flow__minimap",r]),"data-testid":"rf__minimap",children:M.jsxs("svg",{width:W,height:b,viewBox:`${oe} ${ie} ${ue} ${ae}`,className:"react-flow__minimap-svg",role:"img","aria-labelledby":fe,ref:$,onClick:Se,children:[ye&&M.jsx("title",{id:fe,children:ye}),M.jsx(EN,{onClick:_e,nodeColor:s,nodeStrokeColor:o,nodeBorderRadius:a,nodeClassName:l,nodeStrokeWidth:c,nodeComponent:d}),M.jsx("path",{className:"react-flow__minimap-mask",d:`M${oe-j},${ie-j}h${ue+j*2}v${ae+j*2}h${-ue-j*2}z
        M${X.x},${X.y}h${X.width}v${X.height}h${-X.width}z`,fillRule:"evenodd",pointerEvents:"none"})]})})}u0.displayName="MiniMap";const PN=q.memo(u0),RN=t=>r=>t?`${Math.max(1/r.transform[2],1)}`:void 0,MN={[po.Line]:"right",[po.Handle]:"bottom-right"};function AN({nodeId:t,position:r,variant:o=po.Handle,className:s,style:l=void 0,children:a,color:c,minWidth:d=10,minHeight:p=10,maxWidth:g=Number.MAX_VALUE,maxHeight:m=Number.MAX_VALUE,keepAspectRatio:v=!1,resizeDirection:y,autoScale:w=!0,shouldResize:_,onResizeStart:S,onResize:k,onResizeEnd:C}){const z=Fg(),E=typeof t=="string"?t:z,N=$e(),A=q.useRef(null),$=o===po.Handle,F=Te(q.useCallback(RN($&&w),[$,w]),He),X=q.useRef(null),K=r??MN[o];q.useEffect(()=>{if(!(!A.current||!E))return X.current||(X.current=o_({domNode:A.current,nodeId:E,getStoreItems:()=>{const{nodeLookup:J,transform:ee,snapGrid:G,snapToGrid:P,nodeOrigin:W,domNode:b}=N.getState();return{nodeLookup:J,transform:ee,snapGrid:G,snapToGrid:P,nodeOrigin:W,paneDomNode:b}},onChange:(J,ee)=>{const{triggerNodeChanges:G,nodeLookup:P,parentLookup:W,nodeOrigin:b}=N.getState(),H=[],D={x:J.x,y:J.y},I=P.get(E);if(I&&I.expandParent&&I.parentId){const B=I.origin??b,T=J.width??I.measured.width??0,j=J.height??I.measured.height??0,oe={id:I.id,parentId:I.parentId,rect:{width:T,height:j,...ug({x:J.x??I.position.x,y:J.y??I.position.y},{width:T,height:j},I.parentId,P,B)}},ie=Yc([oe],P,W,b);H.push(...ie),D.x=J.x?Math.max(B[0]*T,J.x):void 0,D.y=J.y?Math.max(B[1]*j,J.y):void 0}if(D.x!==void 0&&D.y!==void 0){const B={id:E,type:"position",position:{...D}};H.push(B)}if(J.width!==void 0&&J.height!==void 0){const T={id:E,type:"dimensions",resizing:!0,setAttributes:y?y==="horizontal"?"width":"height":!0,dimensions:{width:J.width,height:J.height}};H.push(T)}for(const B of ee){const T={...B,type:"position"};H.push(T)}G(H)},onEnd:({width:J,height:ee})=>{const G={id:E,type:"dimensions",resizing:!1,dimensions:{width:J,height:ee}};N.getState().triggerNodeChanges([G])}})),X.current.update({controlPosition:K,boundaries:{minWidth:d,minHeight:p,maxWidth:g,maxHeight:m},keepAspectRatio:v,resizeDirection:y,onResizeStart:S,onResize:k,onResizeEnd:C,shouldResize:_}),()=>{var J;(J=X.current)==null||J.destroy()}},[K,d,p,g,m,v,S,k,C,_]);const Z=K.split("-");return M.jsx("div",{className:Ye(["react-flow__resize-control","nodrag",...Z,o,s]),ref:A,style:{...l,scale:F,...c&&{[$?"backgroundColor":"borderColor"]:c}},children:a})}q.memo(AN);let ul;const LN=new Uint8Array(16);function IN(){if(!ul&&(ul=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!ul))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return ul(LN)}const tt=[];for(let t=0;t<256;++t)tt.push((t+256).toString(16).slice(1));function zN(t,r=0){return tt[t[r+0]]+tt[t[r+1]]+tt[t[r+2]]+tt[t[r+3]]+"-"+tt[t[r+4]]+tt[t[r+5]]+"-"+tt[t[r+6]]+tt[t[r+7]]+"-"+tt[t[r+8]]+tt[t[r+9]]+"-"+tt[t[r+10]]+tt[t[r+11]]+tt[t[r+12]]+tt[t[r+13]]+tt[t[r+14]]+tt[t[r+15]]}const ON=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Yp={randomUUID:ON};function qp(t,r,o){if(Yp.randomUUID&&!t)return Yp.randomUUID();t=t||{};const s=t.random||(t.rng||IN)();return s[6]=s[6]&15|64,s[8]=s[8]&63|128,zN(s)}const DN=({data:t,selected:r})=>{const o=t,s=a=>{switch(a){case"http-request":return"🌐";case"set":return"⚙️";case"webhook-trigger":return"🔗";default:return"📦"}},l=a=>{switch(a){case"http-request":return"border-blue-400 bg-blue-50";case"set":return"border-green-400 bg-green-50";case"webhook-trigger":return"border-purple-400 bg-purple-50";default:return"border-gray-400 bg-gray-50"}};return M.jsxs("div",{className:`custom-node ${r?"selected":""} ${l(o.type)}`,children:[o.type!=="webhook-trigger"&&M.jsx(mo,{type:"target",position:ve.Top,className:"react-flow__handle-top"}),M.jsxs("div",{className:"node-header",children:[M.jsx("span",{className:"text-lg",children:s(o.type)}),M.jsxs("div",{children:[M.jsx("div",{className:"node-title",children:o.name||o.type}),M.jsx("div",{className:"node-description text-xs",children:o.type.replace("-"," ").replace(/\b\w/g,a=>a.toUpperCase())})]})]}),o.isDisabled&&M.jsx("div",{className:"text-xs text-red-500 mt-1",children:"Disabled"}),M.jsx(mo,{type:"source",position:ve.Bottom,className:"react-flow__handle-bottom"})]})},$N=({onNodeDragStart:t})=>{const[r,o]=q.useState([]),[s,l]=q.useState([]),[a,c]=q.useState(!0);q.useEffect(()=>{(async()=>{try{const[m,v]=await Promise.all([Hh.getNodes(),Hh.getCategories()]);o(m),l(v)}catch(m){console.error("Failed to fetch nodes:",m)}finally{c(!1)}})()},[]);const d=g=>{switch(g){case"http-request":return"🌐";case"set":return"⚙️";case"webhook-trigger":return"🔗";default:return"📦"}},p=s.reduce((g,m)=>(g[m]=r.filter(v=>v.category===m),g),{});return a?M.jsx("div",{className:"node-sidebar",children:M.jsxs("div",{className:"text-center py-8",children:[M.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"}),M.jsx("p",{className:"mt-2 text-gray-600",children:"Loading nodes..."})]})}):M.jsxs("div",{className:"node-sidebar",children:[M.jsxs("div",{className:"mb-6",children:[M.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Nodes"}),M.jsx("p",{className:"text-sm text-gray-600",children:"Drag nodes to the canvas to build your workflow"})]}),s.map(g=>{var m;return M.jsxs("div",{className:"node-category",children:[M.jsx("h3",{className:"node-category-title",children:g}),(m=p[g])==null?void 0:m.map(v=>M.jsxs("div",{className:"node-item",draggable:!0,onDragStart:y=>t(y,v.nodeType),children:[M.jsx("span",{className:"text-xl",children:d(v.nodeType)}),M.jsxs("div",{className:"flex-1",children:[M.jsx("div",{className:"font-medium text-gray-800",children:v.displayName}),M.jsx("div",{className:"text-xs text-gray-600",children:v.description})]}),v.isTrigger&&M.jsx("span",{className:"text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded",children:"Trigger"})]},v.nodeType))]},g)}),r.length===0&&!a&&M.jsx("div",{className:"text-center py-8",children:M.jsx("p",{className:"text-gray-600",children:"No nodes available"})})]})},jN={custom:DN},FN=({workflowId:t,initialNodes:r=[],initialConnections:o=[],onSave:s})=>{const l=q.useRef(null),[a,c]=q.useState(null),d=N=>N.map(A=>({id:A.id,type:"custom",position:A.position,data:A})),p=N=>N.map(A=>({id:A.id,source:A.sourceNodeId,target:A.targetNodeId,sourceHandle:A.sourceOutput,targetHandle:A.targetInput})),[g,m,v]=nN(d(r)),[y,w,_]=rN(p(o)),S=q.useCallback(N=>w(A=>mg(N,A)),[w]),k=q.useCallback(N=>{N.preventDefault(),N.dataTransfer.dropEffect="move"},[]),C=q.useCallback(N=>{N.preventDefault();const A=N.dataTransfer.getData("application/reactflow");if(!(typeof A>"u"||!A)&&l.current&&a){const $=l.current.getBoundingClientRect(),F=a.screenToFlowPosition({x:N.clientX-$.left,y:N.clientY-$.top}),X={id:qp(),type:"custom",position:F,data:{id:qp(),workflowId:t||"",name:`${A} Node`,type:A,position:F,parameters:{},isDisabled:!1,notes:""}};m(K=>K.concat(X))}},[a,t,m]),z=q.useCallback((N,A)=>{N.dataTransfer.setData("application/reactflow",A),N.dataTransfer.effectAllowed="move"},[]),E=q.useCallback(()=>{if(s){const N=g.map($=>({...$.data,position:$.position})),A=y.map($=>({id:$.id,workflowId:t||"",sourceNodeId:$.source,targetNodeId:$.target,sourceOutput:$.sourceHandle||"main",targetInput:$.targetHandle||"main"}));s(N,A)}},[g,y,t,s]);return M.jsxs("div",{className:"flex h-screen bg-gray-100",children:[M.jsxs("div",{className:"flex-1 relative",children:[M.jsx("div",{ref:l,className:"w-full h-full",children:M.jsxs(tN,{nodes:g,edges:y,onNodesChange:v,onEdgesChange:_,onConnect:S,onInit:c,onDrop:C,onDragOver:k,nodeTypes:jN,fitView:!0,className:"bg-gray-50",children:[M.jsx(mN,{}),M.jsx(PN,{}),M.jsx(uN,{variant:xn.Dots,gap:12,size:1})]})}),M.jsxs("div",{className:"absolute top-4 left-4 bg-white rounded-lg shadow-md p-2 flex gap-2",children:[M.jsx("button",{onClick:E,className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors",children:"Save"}),M.jsx("button",{onClick:()=>m([]),className:"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors",children:"Clear"})]})]}),M.jsx($N,{onNodeDragStart:z})]})},bN=t=>M.jsx(i0,{children:M.jsx(FN,{...t})});function HN(){const[t,r]=q.useState("list"),[o,s]=q.useState(null),l=p=>{s(p),r("editor")},a=async()=>{try{const p={name:"New Workflow",description:"A new workflow",isActive:!1,createdBy:"default",nodes:[],connections:[],settings:{maxRetries:0,saveExecutionProgress:!0,variables:{}}},g=await no.createWorkflow(p);s(g),r("editor")}catch(p){console.error("Failed to create workflow:",p),alert("Failed to create workflow")}},c=async(p,g)=>{if(o)try{const m={...o,nodes:p,connections:g,updatedAt:new Date().toISOString()};await no.updateWorkflow(o.id,m),s(m),alert("Workflow saved successfully!")}catch(m){console.error("Failed to save workflow:",m),alert("Failed to save workflow")}},d=()=>{r("list"),s(null)};return M.jsx("div",{className:"min-h-screen bg-gray-100",children:t==="list"?M.jsx(Pw,{onSelectWorkflow:l,onCreateWorkflow:a}):M.jsxs("div",{className:"h-screen flex flex-col",children:[M.jsxs("div",{className:"bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between",children:[M.jsxs("div",{className:"flex items-center gap-4",children:[M.jsx("button",{onClick:d,className:"text-gray-600 hover:text-gray-800 transition-colors",children:"← Back to Workflows"}),M.jsxs("div",{children:[M.jsx("h1",{className:"text-xl font-semibold text-gray-800",children:(o==null?void 0:o.name)||"Workflow Editor"}),M.jsx("p",{className:"text-sm text-gray-600",children:(o==null?void 0:o.description)||"Edit your workflow"})]})]}),M.jsx("div",{className:"flex items-center gap-2",children:M.jsx("span",{className:`px-3 py-1 text-sm rounded-full ${o!=null&&o.isActive?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700"}`,children:o!=null&&o.isActive?"Active":"Inactive"})})]}),M.jsx("div",{className:"flex-1",children:M.jsx(bN,{workflowId:o==null?void 0:o.id,initialNodes:(o==null?void 0:o.nodes)||[],initialConnections:(o==null?void 0:o.connections)||[],onSave:c})})]})})}Ky.createRoot(document.getElementById("root")).render(M.jsx(q.StrictMode,{children:M.jsx(HN,{})}));

{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|c:\\users\\<USER>\\documents\\augment-projects\\flowcustom\\src\\flowcustom.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|solutionrelative:src\\flowcustom.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|c:\\users\\<USER>\\documents\\augment-projects\\flowcustom\\src\\flowcustom.api\\flowcustom.api.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}", "RelativeMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|solutionrelative:src\\flowcustom.api\\flowcustom.api.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}"}, {"AbsoluteMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|c:\\users\\<USER>\\documents\\augment-projects\\flowcustom\\src\\flowcustom.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|solutionrelative:src\\flowcustom.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\Program.cs", "RelativeDocumentMoniker": "src\\FlowCustom.Api\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\Program.cs", "RelativeToolTip": "src\\FlowCustom.Api\\Program.cs", "ViewState": "AgIAAC0AAAAAAAAAAAArwDoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T13:02:43.043Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "FlowCustom.Api.http", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\FlowCustom.Api.http", "RelativeDocumentMoniker": "src\\FlowCustom.Api\\FlowCustom.Api.http", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\FlowCustom.Api.http", "RelativeToolTip": "src\\FlowCustom.Api\\FlowCustom.Api.http", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003502|", "WhenOpened": "2025-06-21T13:02:35.097Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\appsettings.json", "RelativeDocumentMoniker": "src\\FlowCustom.Api\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\appsettings.json", "RelativeToolTip": "src\\FlowCustom.Api\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-21T13:02:30.69Z"}]}]}]}
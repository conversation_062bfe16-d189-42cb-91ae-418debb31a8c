{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}|src\\FlowCustom.Core\\FlowCustom.Core.csproj|c:\\users\\<USER>\\documents\\augment-projects\\flowcustom\\src\\flowcustom.core\\models\\execution.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5EADAC13-B6F1-4924-ABA4-6F51D5B434CE}|src\\FlowCustom.Core\\FlowCustom.Core.csproj|solutionrelative:src\\flowcustom.core\\models\\execution.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|c:\\users\\<USER>\\documents\\augment-projects\\flowcustom\\src\\flowcustom.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|solutionrelative:src\\flowcustom.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{70FBADCF-A390-4F36-A68C-0394E1C58262}|src\\FlowCustom.Engine\\FlowCustom.Engine.csproj|c:\\users\\<USER>\\documents\\augment-projects\\flowcustom\\src\\flowcustom.engine\\workflowengine.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{70FBADCF-A390-4F36-A68C-0394E1C58262}|src\\FlowCustom.Engine\\FlowCustom.Engine.csproj|solutionrelative:src\\flowcustom.engine\\workflowengine.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|c:\\users\\<USER>\\documents\\augment-projects\\flowcustom\\src\\flowcustom.api\\flowcustom.api.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}", "RelativeMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|solutionrelative:src\\flowcustom.api\\flowcustom.api.http||{5703B403-55E7-4C63-8C88-A8F52C7A45C5}"}, {"AbsoluteMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|c:\\users\\<USER>\\documents\\augment-projects\\flowcustom\\src\\flowcustom.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{01B332F7-0363-4375-BF8A-A372F78ADDD0}|src\\FlowCustom.Api\\FlowCustom.Api.csproj|solutionrelative:src\\flowcustom.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Execution.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Core\\Models\\Execution.cs", "RelativeDocumentMoniker": "src\\FlowCustom.Core\\Models\\Execution.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Core\\Models\\Execution.cs", "RelativeToolTip": "src\\FlowCustom.Core\\Models\\Execution.cs", "ViewState": "AgIAAAsAAAAAAAAAAAA3wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T15:51:13.68Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "WorkflowEngine.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Engine\\WorkflowEngine.cs", "RelativeDocumentMoniker": "src\\FlowCustom.Engine\\WorkflowEngine.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Engine\\WorkflowEngine.cs", "RelativeToolTip": "src\\FlowCustom.Engine\\WorkflowEngine.cs", "ViewState": "AgIAAGUBAAAAAAAAAAAIwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T15:36:20.269Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\Program.cs", "RelativeDocumentMoniker": "src\\FlowCustom.Api\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\Program.cs", "RelativeToolTip": "src\\FlowCustom.Api\\Program.cs", "ViewState": "AgIAAGMAAAAAAAAAAIAwwGwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T13:02:43.043Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "FlowCustom.Api.http", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\FlowCustom.Api.http", "RelativeDocumentMoniker": "src\\FlowCustom.Api\\FlowCustom.Api.http", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\FlowCustom.Api.http", "RelativeToolTip": "src\\FlowCustom.Api\\FlowCustom.Api.http", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003502|", "WhenOpened": "2025-06-21T13:02:35.097Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\appsettings.json", "RelativeDocumentMoniker": "src\\FlowCustom.Api\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\FlowCustom\\src\\FlowCustom.Api\\appsettings.json", "RelativeToolTip": "src\\FlowCustom.Api\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-21T13:02:30.69Z"}]}]}]}
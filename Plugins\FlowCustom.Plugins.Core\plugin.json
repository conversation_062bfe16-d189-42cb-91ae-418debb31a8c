{"id": "FlowCustom.Plugins.Core", "name": "核心插件包", "version": "1.0.0", "author": "FlowCustom Team", "description": "提供基础节点功能的核心插件包，包含触发器、执行器等基础节点", "main": "FlowCustom.Plugins.Core.dll", "dependencies": ["FlowCustom.SDK@^1.0.0"], "supportedFrameworks": ["net8.0"], "nodes": [{"type": "manual-trigger", "class": "FlowCustom.Plugins.Core.ManualTriggerPlugin", "displayName": "手动触发器", "category": "触发器", "description": "手动触发工作流执行"}, {"type": "script-executor", "class": "FlowCustom.Plugins.Core.ScriptExecutorPlugin", "displayName": "脚本执行器", "category": "执行器", "description": "执行Python、JavaScript、PowerShell等脚本"}, {"type": "set", "class": "FlowCustom.Plugins.Core.SetNodePlugin", "displayName": "数据设置", "category": "数据", "description": "设置值和转换数据"}, {"type": "conditional", "class": "FlowCustom.Plugins.Core.ConditionalPlugin", "displayName": "条件判断", "category": "逻辑", "description": "根据条件判断选择执行路径"}], "resources": {"frontend": "wwwroot/", "icons": "icons/", "localization": "locales/"}, "permissions": ["file.read", "file.write", "process.execute", "network.access"], "configuration": {"allowScriptExecution": true, "maxScriptTimeout": 3600, "allowedScriptTypes": ["python", "javascript", "powershell", "bash", "cmd"]}, "metadata": {"homepage": "https://flowcustom.com/plugins/core", "repository": "https://github.com/flowcustom/plugins-core", "license": "MIT", "keywords": ["workflow", "automation", "trigger", "script", "executor"], "screenshots": ["screenshots/manual-trigger.png", "screenshots/script-executor.png"], "documentation": "docs/README.md"}, "compatibility": {"minEngineVersion": "1.0.0", "maxEngineVersion": "2.0.0", "operatingSystems": ["windows", "linux", "macos"]}, "installation": {"preInstallScript": "scripts/pre-install.ps1", "postInstallScript": "scripts/post-install.ps1", "uninstallScript": "scripts/uninstall.ps1"}}
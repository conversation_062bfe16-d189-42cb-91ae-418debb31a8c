using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FlowCustom.SDK;

namespace FlowCustom.Plugins.Http
{
    /// <summary>
    /// HTTP请求插件
    /// 发送HTTP请求并处理响应
    /// </summary>
    public class HttpRequestPlugin : NodePluginBase
    {
        private readonly HttpClient _httpClient;

        public HttpRequestPlugin()
        {
            _httpClient = new HttpClient();
        }

        #region 基本信息

        public override string NodeType => "http-request";
        public override string DisplayName => "HTTP请求";
        public override string Description => "发送HTTP请求并获取响应";
        public override string Category => "网络";
        public override string Author => "FlowCustom Team";
        public override string Version => "1.0.0";
        public override bool IsTrigger => false;
        public override string Icon => "🌐";

        #endregion

        #region 参数定义

        protected override List<ParameterDefinition> GetParameterDefinitions()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "method",
                    DisplayName = "HTTP方法",
                    Description = "HTTP请求方法",
                    Type = ParameterType.Select,
                    Required = true,
                    DefaultValue = "GET",
                    Options = new List<ParameterOption>
                    {
                        new ParameterOption { Value = "GET", Label = "GET", Description = "获取数据" },
                        new ParameterOption { Value = "POST", Label = "POST", Description = "提交数据" },
                        new ParameterOption { Value = "PUT", Label = "PUT", Description = "更新数据" },
                        new ParameterOption { Value = "DELETE", Label = "DELETE", Description = "删除数据" },
                        new ParameterOption { Value = "PATCH", Label = "PATCH", Description = "部分更新" },
                        new ParameterOption { Value = "HEAD", Label = "HEAD", Description = "获取头部" },
                        new ParameterOption { Value = "OPTIONS", Label = "OPTIONS", Description = "获取选项" }
                    }
                },

                new ParameterDefinition
                {
                    Name = "url",
                    DisplayName = "请求URL",
                    Description = "要请求的URL地址",
                    Type = ParameterType.String,
                    Required = true,
                    DefaultValue = "https://api.example.com/data"
                },

                new ParameterDefinition
                {
                    Name = "headers",
                    DisplayName = "请求头",
                    Description = "HTTP请求头 (JSON格式)",
                    Type = ParameterType.Json,
                    Required = false,
                    DefaultValue = "{\"Content-Type\": \"application/json\"}"
                },

                new ParameterDefinition
                {
                    Name = "body",
                    DisplayName = "请求体",
                    Description = "HTTP请求体内容 (JSON格式)",
                    Type = ParameterType.Json,
                    Required = false,
                    DefaultValue = "{}"
                },

                new ParameterDefinition
                {
                    Name = "timeout",
                    DisplayName = "超时时间(秒)",
                    Description = "请求超时时间",
                    Type = ParameterType.Number,
                    Required = false,
                    DefaultValue = 30,
                    Validation = new Dictionary<string, object>
                    {
                        ["min"] = 1,
                        ["max"] = 300
                    }
                },

                new ParameterDefinition
                {
                    Name = "followRedirects",
                    DisplayName = "跟随重定向",
                    Description = "是否自动跟随HTTP重定向",
                    Type = ParameterType.Boolean,
                    Required = false,
                    DefaultValue = true
                },

                new ParameterDefinition
                {
                    Name = "validateSsl",
                    DisplayName = "验证SSL证书",
                    Description = "是否验证SSL证书",
                    Type = ParameterType.Boolean,
                    Required = false,
                    DefaultValue = true
                }
            };
        }

        #endregion

        #region 端点定义

        protected override List<EndpointDefinition> GetOutputDefinitions()
        {
            return new List<EndpointDefinition>
            {
                new EndpointDefinition
                {
                    Id = "success",
                    Name = "成功响应",
                    Description = "HTTP请求成功时的响应",
                    Type = EndpointType.Success,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 30 },
                    DataType = "object"
                },

                new EndpointDefinition
                {
                    Id = "error",
                    Name = "错误响应",
                    Description = "HTTP请求失败时的错误信息",
                    Type = EndpointType.Error,
                    Required = false,
                    Position = new EndpointPosition { Side = "bottom", Offset = 70 },
                    DataType = "error"
                }
            };
        }

        #endregion

        #region 核心逻辑

        protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
        {
            var method = GetParameter<string>(context, "method", "GET");
            var url = GetParameter<string>(context, "url", "");
            var headersJson = GetParameter<string>(context, "headers", "{}");
            var bodyJson = GetParameter<string>(context, "body", "{}");
            var timeout = GetParameter<int>(context, "timeout", 30);
            var followRedirects = GetParameter<bool>(context, "followRedirects", true);
            var validateSsl = GetParameter<bool>(context, "validateSsl", true);

            Log($"发送HTTP请求: {method} {url}");

            if (string.IsNullOrWhiteSpace(url))
            {
                return NodeExecutionResult.Failure("URL不能为空");
            }

            try
            {
                // 配置HttpClient
                var handler = new HttpClientHandler()
                {
                    AllowAutoRedirect = followRedirects
                };

                if (!validateSsl)
                {
                    handler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => true;
                }

                using var httpClient = new HttpClient(handler)
                {
                    Timeout = TimeSpan.FromSeconds(timeout)
                };

                // 创建请求
                using var request = new HttpRequestMessage(new HttpMethod(method), url);

                // 添加请求头
                try
                {
                    var headers = JsonSerializer.Deserialize<Dictionary<string, string>>(headersJson);
                    if (headers != null)
                    {
                        foreach (var header in headers)
                        {
                            if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
                            {
                                continue; // Content-Type 在设置请求体时处理
                            }
                            request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log($"解析请求头失败: {ex.Message}", LogLevel.Warning);
                }

                // 添加请求体
                if (!string.IsNullOrWhiteSpace(bodyJson) && 
                    (method == "POST" || method == "PUT" || method == "PATCH"))
                {
                    try
                    {
                        var contentType = "application/json";
                        var headers = JsonSerializer.Deserialize<Dictionary<string, string>>(headersJson);
                        if (headers?.ContainsKey("Content-Type") == true)
                        {
                            contentType = headers["Content-Type"];
                        }

                        request.Content = new StringContent(bodyJson, Encoding.UTF8, contentType);
                    }
                    catch (Exception ex)
                    {
                        Log($"设置请求体失败: {ex.Message}", LogLevel.Warning);
                    }
                }

                // 发送请求
                var startTime = DateTime.UtcNow;
                var response = await httpClient.SendAsync(request, context.CancellationToken);
                var endTime = DateTime.UtcNow;
                var duration = (endTime - startTime).TotalMilliseconds;

                // 读取响应
                var responseContent = await response.Content.ReadAsStringAsync();
                var responseHeaders = new Dictionary<string, string>();
                
                foreach (var header in response.Headers)
                {
                    responseHeaders[header.Key] = string.Join(", ", header.Value);
                }

                foreach (var header in response.Content.Headers)
                {
                    responseHeaders[header.Key] = string.Join(", ", header.Value);
                }

                var outputData = new Dictionary<string, object>
                {
                    ["statusCode"] = (int)response.StatusCode,
                    ["statusText"] = response.ReasonPhrase ?? "",
                    ["headers"] = responseHeaders,
                    ["body"] = responseContent,
                    ["isSuccess"] = response.IsSuccessStatusCode,
                    ["duration"] = duration,
                    ["url"] = url,
                    ["method"] = method,
                    ["timestamp"] = DateTime.UtcNow
                };

                // 尝试解析JSON响应
                try
                {
                    if (response.Content.Headers.ContentType?.MediaType?.Contains("json") == true)
                    {
                        var jsonData = JsonSerializer.Deserialize<object>(responseContent);
                        outputData["json"] = jsonData;
                    }
                }
                catch (Exception ex)
                {
                    Log($"解析JSON响应失败: {ex.Message}", LogLevel.Warning);
                }

                Log($"HTTP请求完成: {response.StatusCode} ({duration:F0}ms)");

                // 根据状态码选择输出端点
                var nextEndpoint = response.IsSuccessStatusCode ? "success" : "error";
                return NodeExecutionResult.Success(outputData, nextEndpoint);
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                Log($"HTTP请求超时: {timeout}秒", LogLevel.Error);
                return NodeExecutionResult.Success(new Dictionary<string, object>
                {
                    ["error"] = "请求超时",
                    ["timeout"] = timeout,
                    ["url"] = url,
                    ["method"] = method
                }, "error");
            }
            catch (Exception ex)
            {
                Log($"HTTP请求失败: {ex.Message}", LogLevel.Error);
                return NodeExecutionResult.Success(new Dictionary<string, object>
                {
                    ["error"] = ex.Message,
                    ["errorType"] = ex.GetType().Name,
                    ["url"] = url,
                    ["method"] = method
                }, "error");
            }
        }

        #endregion

        #region 验证逻辑

        protected override void OnValidate(NodeConfiguration configuration, 
            List<ValidationError> errors, List<ValidationWarning> warnings)
        {
            // 验证URL
            if (configuration.Parameters.TryGetValue("url", out var urlObj))
            {
                var url = urlObj?.ToString() ?? "";
                if (string.IsNullOrWhiteSpace(url))
                {
                    errors.Add(new ValidationError
                    {
                        Field = "url",
                        Message = "URL不能为空",
                        Code = "EMPTY_URL"
                    });
                }
                else if (!Uri.TryCreate(url, UriKind.Absolute, out var uri))
                {
                    errors.Add(new ValidationError
                    {
                        Field = "url",
                        Message = "URL格式无效",
                        Code = "INVALID_URL"
                    });
                }
                else if (uri.Scheme != "http" && uri.Scheme != "https")
                {
                    errors.Add(new ValidationError
                    {
                        Field = "url",
                        Message = "URL必须使用HTTP或HTTPS协议",
                        Code = "INVALID_PROTOCOL"
                    });
                }
            }

            // 验证请求头JSON格式
            if (configuration.Parameters.TryGetValue("headers", out var headersObj))
            {
                var headersJson = headersObj?.ToString() ?? "{}";
                try
                {
                    JsonSerializer.Deserialize<Dictionary<string, string>>(headersJson);
                }
                catch
                {
                    errors.Add(new ValidationError
                    {
                        Field = "headers",
                        Message = "请求头必须是有效的JSON格式",
                        Code = "INVALID_HEADERS_JSON"
                    });
                }
            }

            // 验证请求体JSON格式
            if (configuration.Parameters.TryGetValue("body", out var bodyObj))
            {
                var bodyJson = bodyObj?.ToString() ?? "{}";
                if (!string.IsNullOrWhiteSpace(bodyJson))
                {
                    try
                    {
                        JsonSerializer.Deserialize<object>(bodyJson);
                    }
                    catch
                    {
                        errors.Add(new ValidationError
                        {
                            Field = "body",
                            Message = "请求体必须是有效的JSON格式",
                            Code = "INVALID_BODY_JSON"
                        });
                    }
                }
            }

            // 验证超时时间
            if (configuration.Parameters.TryGetValue("timeout", out var timeoutObj))
            {
                if (int.TryParse(timeoutObj?.ToString(), out var timeout))
                {
                    if (timeout < 1)
                    {
                        errors.Add(new ValidationError
                        {
                            Field = "timeout",
                            Message = "超时时间必须大于0秒",
                            Code = "INVALID_TIMEOUT"
                        });
                    }
                    else if (timeout > 300)
                    {
                        warnings.Add(new ValidationWarning
                        {
                            Field = "timeout",
                            Message = "超时时间过长，建议不超过5分钟",
                            Code = "LONG_TIMEOUT"
                        });
                    }
                }
            }
        }

        #endregion

        #region 生命周期

        protected override void OnDispose()
        {
            _httpClient?.Dispose();
            Log("HTTP请求插件资源清理完成");
        }

        #endregion
    }
}

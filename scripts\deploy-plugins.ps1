# FlowCustomV1 插件部署脚本
param(
    [string]$Configuration = "Debug",
    [string]$TargetFramework = "net8.0",
    [switch]$Clean = $false,
    [switch]$Verbose = $false
)

Write-Host "🔌 FlowCustomV1 插件部署脚本" -ForegroundColor Green
Write-Host "=" * 50

$ErrorActionPreference = "Stop"

try {
    # 设置详细输出
    if ($Verbose) {
        $VerbosePreference = "Continue"
    }

    # 获取项目根目录
    $ProjectRoot = Split-Path -Parent $PSScriptRoot
    $ApiOutputDir = Join-Path $ProjectRoot "src\FlowCustomV1.Api\bin\$Configuration\$TargetFramework"
    $PluginsDir = Join-Path $ApiOutputDir "plugins"

    Write-Host "📁 项目根目录: $ProjectRoot" -ForegroundColor Cyan
    Write-Host "📁 API输出目录: $ApiOutputDir" -ForegroundColor Cyan
    Write-Host "📁 插件目录: $PluginsDir" -ForegroundColor Cyan

    # 清理插件目录
    if ($Clean -and (Test-Path $PluginsDir)) {
        Write-Host "🧹 清理插件目录..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force $PluginsDir
        Write-Host "✅ 插件目录清理完成" -ForegroundColor Green
    }

    # 创建插件目录
    if (-not (Test-Path $PluginsDir)) {
        Write-Host "📁 创建插件目录..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $PluginsDir -Force | Out-Null
        Write-Host "✅ 插件目录创建完成" -ForegroundColor Green
    }

    # 部署核心插件
    $CorePluginSource = Join-Path $ProjectRoot "plugins\FlowCustomV1.Plugins.Core\bin\$Configuration\$TargetFramework"
    $CorePluginTarget = Join-Path $PluginsDir "FlowCustomV1.Plugins.Core"

    if (Test-Path $CorePluginSource) {
        Write-Host "🔌 部署核心插件..." -ForegroundColor Yellow
        
        # 创建目标目录
        if (-not (Test-Path $CorePluginTarget)) {
            New-Item -ItemType Directory -Path $CorePluginTarget -Force | Out-Null
        }

        # 复制插件文件
        $pluginFiles = @(
            "FlowCustomV1.Plugins.Core.dll",
            "FlowCustomV1.Plugins.Core.pdb",
            "FlowCustomV1.Plugins.Core.xml"
        )

        foreach ($file in $pluginFiles) {
            $sourceFile = Join-Path $CorePluginSource $file
            $targetFile = Join-Path $CorePluginTarget $file
            
            if (Test-Path $sourceFile) {
                Copy-Item $sourceFile $targetFile -Force
                Write-Host "   ✅ 复制: $file" -ForegroundColor Gray
            }
        }

        # 复制插件清单
        $manifestSource = Join-Path $ProjectRoot "plugins\FlowCustomV1.Plugins.Core\plugin.json"
        $manifestTarget = Join-Path $CorePluginTarget "plugin.json"
        
        if (Test-Path $manifestSource) {
            Copy-Item $manifestSource $manifestTarget -Force
            Write-Host "   ✅ 复制: plugin.json" -ForegroundColor Gray
        }

        Write-Host "✅ 核心插件部署完成" -ForegroundColor Green
    } else {
        Write-Host "⚠️  核心插件构建输出不存在: $CorePluginSource" -ForegroundColor Yellow
    }

    # 验证部署
    Write-Host "🔍 验证插件部署..." -ForegroundColor Yellow
    
    $deployedPlugins = Get-ChildItem -Path $PluginsDir -Directory
    Write-Host "📊 已部署插件数量: $($deployedPlugins.Count)" -ForegroundColor Cyan
    
    foreach ($plugin in $deployedPlugins) {
        $pluginManifest = Join-Path $plugin.FullName "plugin.json"
        $pluginDll = Get-ChildItem -Path $plugin.FullName -Filter "*.dll" | Select-Object -First 1
        
        if (Test-Path $pluginManifest) {
            $manifest = Get-Content $pluginManifest | ConvertFrom-Json
            Write-Host "   🔌 $($manifest.name) v$($manifest.version)" -ForegroundColor Gray
            Write-Host "      📁 路径: $($plugin.FullName)" -ForegroundColor DarkGray
            Write-Host "      📄 清单: ✅" -ForegroundColor DarkGray
            Write-Host "      📦 程序集: $(if ($pluginDll) { '✅ ' + $pluginDll.Name } else { '❌ 缺失' })" -ForegroundColor DarkGray
            
            if ($manifest.nodes) {
                Write-Host "      🎯 节点数量: $($manifest.nodes.Count)" -ForegroundColor DarkGray
                foreach ($node in $manifest.nodes) {
                    Write-Host "         - $($node.displayName) ($($node.type))" -ForegroundColor DarkGray
                }
            }
        } else {
            Write-Host "   ❌ $($plugin.Name) - 缺少插件清单" -ForegroundColor Red
        }
    }

    # 创建插件索引文件
    Write-Host "📝 创建插件索引..." -ForegroundColor Yellow
    
    $pluginIndex = @{
        version = "1.0.0"
        generatedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        plugins = @()
    }

    foreach ($plugin in $deployedPlugins) {
        $pluginManifest = Join-Path $plugin.FullName "plugin.json"
        if (Test-Path $pluginManifest) {
            $manifest = Get-Content $pluginManifest | ConvertFrom-Json
            $pluginIndex.plugins += @{
                id = $manifest.id
                name = $manifest.name
                version = $manifest.version
                path = $plugin.Name
                nodeCount = if ($manifest.nodes) { $manifest.nodes.Count } else { 0 }
                isLoaded = $false
            }
        }
    }

    $indexPath = Join-Path $PluginsDir "index.json"
    $pluginIndex | ConvertTo-Json -Depth 10 | Set-Content $indexPath -Encoding UTF8
    Write-Host "✅ 插件索引创建完成: $indexPath" -ForegroundColor Green

    # 显示部署摘要
    Write-Host ""
    Write-Host "📊 部署摘要:" -ForegroundColor Cyan
    Write-Host "   配置: $Configuration" -ForegroundColor Gray
    Write-Host "   目标框架: $TargetFramework" -ForegroundColor Gray
    Write-Host "   插件目录: $PluginsDir" -ForegroundColor Gray
    Write-Host "   已部署插件: $($deployedPlugins.Count)" -ForegroundColor Gray
    Write-Host "   总节点数: $(($pluginIndex.plugins | Measure-Object -Property nodeCount -Sum).Sum)" -ForegroundColor Gray

    Write-Host ""
    Write-Host "🎉 插件部署完成!" -ForegroundColor Green
    Write-Host "💡 提示:"
    Write-Host "   1. 启动API服务器: dotnet run --project src/FlowCustomV1.Api"
    Write-Host "   2. 访问API文档: http://localhost:5000/swagger"
    Write-Host "   3. 查看系统信息: http://localhost:5000/api/system/info"
    Write-Host "   4. 查看插件列表: http://localhost:5000/api/plugins"

} catch {
    Write-Host ""
    Write-Host "❌ 插件部署失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "⏱️  部署完成时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray

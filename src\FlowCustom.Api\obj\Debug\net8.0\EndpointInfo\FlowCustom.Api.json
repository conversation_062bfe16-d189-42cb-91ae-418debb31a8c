{"openapi": "3.0.1", "info": {"title": "FlowCustom.Api", "version": "1.0"}, "paths": {"/api/Nodes": {"get": {"tags": ["Nodes"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NodeDefinition"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NodeDefinition"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NodeDefinition"}}}}}}}}, "/api/Nodes/{nodeType}": {"get": {"tags": ["Nodes"], "parameters": [{"name": "nodeType", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NodeDefinition"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NodeDefinition"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NodeDefinition"}}}}}}}, "/api/Nodes/categories": {"get": {"tags": ["Nodes"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/Workflow": {"get": {"tags": ["Workflow"], "parameters": [{"name": "userId", "in": "query", "style": "form", "schema": {"type": "string", "default": "default"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Workflow"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Workflow"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Workflow"}}}}}}}, "post": {"tags": ["Workflow"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}}}}, "/api/Workflow/{id}": {"get": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}}}, "put": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}}}, "delete": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Workflow/{id}/toggle": {"post": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}, "application/*+json": {"schema": {"type": "boolean"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Workflow/{id}/execute": {"post": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}, "/api/Workflow/{id}/executions": {"get": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 50}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}}, "/api/Workflow/executions/{executionId}": {"get": {"tags": ["Workflow"], "parameters": [{"name": "executionId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}, "/api/Workflow/executions/{executionId}/stop": {"post": {"tags": ["Workflow"], "parameters": [{"name": "executionId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"ExecutionStatus": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "NodeConnection": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "workflowId": {"type": "string", "format": "uuid"}, "sourceNodeId": {"type": "string", "format": "uuid"}, "targetNodeId": {"type": "string", "format": "uuid"}, "sourceOutput": {"type": "string", "nullable": true}, "targetInput": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NodeDefinition": {"type": "object", "properties": {"nodeType": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "isTrigger": {"type": "boolean"}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/NodeParameterDefinition"}, "nullable": true}}, "additionalProperties": false}, "NodeExecution": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "executionId": {"type": "string", "format": "uuid"}, "nodeId": {"type": "string", "format": "uuid"}, "nodeName": {"type": "string", "nullable": true}, "nodeType": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/ExecutionStatus"}, "startedAt": {"type": "string", "format": "date-time"}, "finishedAt": {"type": "string", "format": "date-time", "nullable": true}, "duration": {"$ref": "#/components/schemas/TimeSpan"}, "error": {"type": "string", "nullable": true}, "inputData": {"type": "object", "additionalProperties": {}, "nullable": true}, "outputData": {"type": "object", "additionalProperties": {}, "nullable": true}, "retryCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "NodeParameterDefinition": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/ParameterType"}, "required": {"type": "boolean"}, "defaultValue": {"nullable": true}, "options": {"type": "array", "items": {"type": "string"}, "nullable": true}, "validation": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "NodePosition": {"type": "object", "properties": {"x": {"type": "number", "format": "double"}, "y": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ParameterType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "type": "integer", "format": "int32"}, "TimeSpan": {"type": "object", "properties": {"ticks": {"type": "integer", "format": "int64"}, "days": {"type": "integer", "format": "int32", "readOnly": true}, "hours": {"type": "integer", "format": "int32", "readOnly": true}, "milliseconds": {"type": "integer", "format": "int32", "readOnly": true}, "microseconds": {"type": "integer", "format": "int32", "readOnly": true}, "nanoseconds": {"type": "integer", "format": "int32", "readOnly": true}, "minutes": {"type": "integer", "format": "int32", "readOnly": true}, "seconds": {"type": "integer", "format": "int32", "readOnly": true}, "totalDays": {"type": "number", "format": "double", "readOnly": true}, "totalHours": {"type": "number", "format": "double", "readOnly": true}, "totalMilliseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMicroseconds": {"type": "number", "format": "double", "readOnly": true}, "totalNanoseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMinutes": {"type": "number", "format": "double", "readOnly": true}, "totalSeconds": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "Workflow": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "nodes": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowNode"}, "nullable": true}, "connections": {"type": "array", "items": {"$ref": "#/components/schemas/NodeConnection"}, "nullable": true}, "settings": {"$ref": "#/components/schemas/WorkflowSettings"}}, "additionalProperties": false}, "WorkflowExecution": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "workflowId": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/ExecutionStatus"}, "startedAt": {"type": "string", "format": "date-time"}, "finishedAt": {"type": "string", "format": "date-time", "nullable": true}, "error": {"type": "string", "nullable": true}, "triggeredBy": {"type": "string", "nullable": true}, "inputData": {"type": "object", "additionalProperties": {}, "nullable": true}, "outputData": {"type": "object", "additionalProperties": {}, "nullable": true}, "nodeExecutions": {"type": "array", "items": {"$ref": "#/components/schemas/NodeExecution"}, "nullable": true}}, "additionalProperties": false}, "WorkflowNode": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "workflowId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "position": {"$ref": "#/components/schemas/NodePosition"}, "parameters": {"type": "object", "additionalProperties": {}, "nullable": true}, "isDisabled": {"type": "boolean"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkflowSettings": {"type": "object", "properties": {"timeout": {"$ref": "#/components/schemas/TimeSpan"}, "maxRetries": {"type": "integer", "format": "int32"}, "saveExecutionProgress": {"type": "boolean"}, "variables": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}}}}
{"openapi": "3.0.1", "info": {"title": "FlowCustom.Api", "version": "1.0"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResult"}}}}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResult"}}}}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResult"}}}}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "put": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/api-keys": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateApiKeyRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GenerateApiKeyRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GenerateApiKeyRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiKey"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiKey"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiKey"}}}}}}}, "/Health": {"get": {"tags": ["Health"], "responses": {"200": {"description": "Success"}}}}, "/Health/detailed": {"get": {"tags": ["Health"], "responses": {"200": {"description": "Success"}}}}, "/Health/ready": {"get": {"tags": ["Health"], "responses": {"200": {"description": "Success"}}}}, "/Health/live": {"get": {"tags": ["Health"], "responses": {"200": {"description": "Success"}}}}, "/api/Log": {"get": {"tags": ["Log"], "parameters": [{"name": "workflowId", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "executionId", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "level", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 1000}}, {"name": "skip", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 0}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SystemLogEntry"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SystemLogEntry"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SystemLogEntry"}}}}}}}, "post": {"tags": ["Log"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddLogRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddLogRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddLogRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Log"], "parameters": [{"name": "workflowId", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "level", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Log/executions": {"get": {"tags": ["Log"], "parameters": [{"name": "workflowId", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 100}}, {"name": "skip", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 0}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}}, "/api/Log/executions/{executionId}": {"get": {"tags": ["Log"], "parameters": [{"name": "executionId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}, "/api/Monitoring/executions": {"get": {"tags": ["Monitoring"], "parameters": [{"name": "workflowId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 50}}, {"name": "status", "in": "query", "style": "form", "schema": {"$ref": "#/components/schemas/ExecutionStatus"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}}, "/api/Monitoring/executions/{executionId}": {"get": {"tags": ["Monitoring"], "parameters": [{"name": "executionId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}, "/api/Monitoring/executions/{executionId}/logs": {"get": {"tags": ["Monitoring"], "parameters": [{"name": "executionId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExecutionLog"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExecutionLog"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExecutionLog"}}}}}}}}, "/api/Monitoring/active-executions": {"get": {"tags": ["Monitoring"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}}, "/api/Monitoring/statistics": {"get": {"tags": ["Monitoring"], "parameters": [{"name": "workflowId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "startDate", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExecutionStatistics"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExecutionStatistics"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExecutionStatistics"}}}}}}}, "/api/Monitoring/metrics": {"get": {"tags": ["Monitoring"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SystemMetrics"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SystemMetrics"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SystemMetrics"}}}}}}}, "/api/Monitoring/performance/{workflowId}": {"get": {"tags": ["Monitoring"], "parameters": [{"name": "workflowId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}, {"name": "startDate", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowPerformanceReport"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowPerformanceReport"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowPerformanceReport"}}}}}}}, "/api/Monitoring/cleanup": {"post": {"tags": ["Monitoring"], "parameters": [{"name": "retentionDays", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 30}}], "responses": {"200": {"description": "Success"}}}}, "/api/Nodes": {"get": {"tags": ["Nodes"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NodeDefinition"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NodeDefinition"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NodeDefinition"}}}}}}}}, "/api/Nodes/{nodeType}": {"get": {"tags": ["Nodes"], "parameters": [{"name": "nodeType", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NodeDefinition"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NodeDefinition"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NodeDefinition"}}}}}}}, "/api/Nodes/categories": {"get": {"tags": ["Nodes"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/Trigger/webhook/{path}": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "patch": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Trigger/api/{path}": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "patch": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "path", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Trigger/status": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Trigger/manual/{workflowId}": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "workflowId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Workflow": {"get": {"tags": ["Workflow"], "parameters": [{"name": "userId", "in": "query", "style": "form", "schema": {"type": "string", "default": "default"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Workflow"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Workflow"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Workflow"}}}}}}}, "post": {"tags": ["Workflow"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWorkflowRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateWorkflowRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateWorkflowRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}}}}, "/api/Workflow/{id}": {"get": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}}}, "put": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}}}, "delete": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Workflow/{id}/toggle": {"post": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}, "application/*+json": {"schema": {"type": "boolean"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Workflow/{id}/execute": {"post": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}, "/api/Workflow/{id}/executions": {"get": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 50}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}}, "/api/Workflow/executions/{executionId}": {"get": {"tags": ["Workflow"], "parameters": [{"name": "executionId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}, "/api/Workflow/executions/{executionId}/stop": {"post": {"tags": ["Workflow"], "parameters": [{"name": "executionId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Workflow/{id}/history": {"get": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}, {"name": "limit", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 100}}, {"name": "skip", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 0}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecution"}}}}}}}}, "/api/Workflow/{workflowId}/executions/{executionId}/cancel": {"post": {"tags": ["Workflow"], "parameters": [{"name": "workflowId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}, {"name": "executionId", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"AddLogRequest": {"type": "object", "properties": {"level": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "workflowId": {"type": "string", "format": "uuid", "nullable": true}, "nodeId": {"type": "string", "format": "uuid", "nullable": true}, "executionId": {"type": "string", "format": "uuid", "nullable": true}, "details": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "ApiKey": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "keyHash": {"type": "string", "nullable": true}, "keyPrefix": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "lastUsedAt": {"type": "string", "format": "date-time", "nullable": true}, "scopes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "AuthResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "user": {"$ref": "#/components/schemas/User"}, "error": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChangePasswordRequest": {"type": "object", "properties": {"currentPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateWorkflowRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "nodes": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowNode"}, "nullable": true}, "connections": {"type": "array", "items": {"$ref": "#/components/schemas/NodeConnection"}, "nullable": true}, "settings": {"$ref": "#/components/schemas/WorkflowSettings"}}, "additionalProperties": false}, "ExecutionLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "executionId": {"type": "string", "format": "uuid"}, "nodeId": {"type": "string", "format": "uuid", "nullable": true}, "level": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "details": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "ExecutionStatistics": {"type": "object", "properties": {"totalExecutions": {"type": "integer", "format": "int32"}, "successfulExecutions": {"type": "integer", "format": "int32"}, "failedExecutions": {"type": "integer", "format": "int32"}, "runningExecutions": {"type": "integer", "format": "int32"}, "cancelledExecutions": {"type": "integer", "format": "int32"}, "averageExecutionTime": {"type": "number", "format": "double"}, "lastExecutionTime": {"type": "string", "format": "date-time", "nullable": true}, "successRate": {"type": "number", "format": "double", "readOnly": true}, "failureRate": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "ExecutionStatus": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "GenerateApiKeyRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "scopes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NodeConnection": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "workflowId": {"type": "string", "format": "uuid"}, "sourceNodeId": {"type": "string", "format": "uuid"}, "targetNodeId": {"type": "string", "format": "uuid"}, "sourceOutput": {"type": "string", "nullable": true}, "targetInput": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NodeDefinition": {"type": "object", "properties": {"nodeType": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "isTrigger": {"type": "boolean"}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/NodeParameterDefinition"}, "nullable": true}}, "additionalProperties": false}, "NodeExecution": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "executionId": {"type": "string", "format": "uuid"}, "nodeId": {"type": "string", "format": "uuid"}, "nodeName": {"type": "string", "nullable": true}, "nodeType": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/ExecutionStatus"}, "startedAt": {"type": "string", "format": "date-time"}, "finishedAt": {"type": "string", "format": "date-time", "nullable": true}, "duration": {"$ref": "#/components/schemas/TimeSpan"}, "error": {"type": "string", "nullable": true}, "inputData": {"type": "object", "additionalProperties": {}, "nullable": true}, "outputData": {"type": "object", "additionalProperties": {}, "nullable": true}, "retryCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "NodeParameterDefinition": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/ParameterType"}, "required": {"type": "boolean"}, "defaultValue": {"nullable": true}, "options": {"type": "array", "items": {"type": "string"}, "nullable": true}, "validation": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "NodePerformanceMetrics": {"type": "object", "properties": {"nodeType": {"type": "string", "nullable": true}, "executionCount": {"type": "integer", "format": "int32"}, "successCount": {"type": "integer", "format": "int32"}, "failureCount": {"type": "integer", "format": "int32"}, "totalDuration": {"type": "number", "format": "double"}, "averageDuration": {"type": "number", "format": "double"}, "successRate": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "NodePosition": {"type": "object", "properties": {"x": {"type": "number", "format": "double"}, "y": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ParameterType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "type": "integer", "format": "int32"}, "RefreshTokenRequest": {"type": "object", "properties": {"refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SystemLogEntry": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "timestamp": {"type": "string", "format": "date-time"}, "level": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "workflowId": {"type": "string", "format": "uuid", "nullable": true}, "nodeId": {"type": "string", "format": "uuid", "nullable": true}, "executionId": {"type": "string", "format": "uuid", "nullable": true}, "details": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "SystemMetrics": {"type": "object", "properties": {"activeExecutions": {"type": "integer", "format": "int32"}, "totalExecutionsToday": {"type": "integer", "format": "int32"}, "successRate": {"type": "number", "format": "double"}, "failureRate": {"type": "number", "format": "double"}, "averageExecutionTime": {"type": "number", "format": "double"}, "memoryUsage": {"type": "integer", "format": "int64"}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TimeSpan": {"type": "object", "properties": {"ticks": {"type": "integer", "format": "int64"}, "days": {"type": "integer", "format": "int32", "readOnly": true}, "hours": {"type": "integer", "format": "int32", "readOnly": true}, "milliseconds": {"type": "integer", "format": "int32", "readOnly": true}, "microseconds": {"type": "integer", "format": "int32", "readOnly": true}, "nanoseconds": {"type": "integer", "format": "int32", "readOnly": true}, "minutes": {"type": "integer", "format": "int32", "readOnly": true}, "seconds": {"type": "integer", "format": "int32", "readOnly": true}, "totalDays": {"type": "number", "format": "double", "readOnly": true}, "totalHours": {"type": "number", "format": "double", "readOnly": true}, "totalMilliseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMicroseconds": {"type": "number", "format": "double", "readOnly": true}, "totalNanoseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMinutes": {"type": "number", "format": "double", "readOnly": true}, "totalSeconds": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "UpdateUserRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "settings": {"$ref": "#/components/schemas/UserSettings"}}, "additionalProperties": false}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "passwordHash": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/UserRole"}, "nullable": true}, "settings": {"$ref": "#/components/schemas/UserSettings"}}, "additionalProperties": false}, "UserRole": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "roleName": {"type": "string", "nullable": true}, "assignedAt": {"type": "string", "format": "date-time"}, "assignedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserSettings": {"type": "object", "properties": {"theme": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "timezone": {"type": "string", "nullable": true}, "emailNotifications": {"type": "boolean"}, "workflowNotifications": {"type": "boolean"}, "customSettings": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "Workflow": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "nodes": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowNode"}, "nullable": true}, "connections": {"type": "array", "items": {"$ref": "#/components/schemas/NodeConnection"}, "nullable": true}, "settings": {"$ref": "#/components/schemas/WorkflowSettings"}}, "additionalProperties": false}, "WorkflowExecution": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "workflowId": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/ExecutionStatus"}, "startedAt": {"type": "string", "format": "date-time"}, "finishedAt": {"type": "string", "format": "date-time", "nullable": true}, "error": {"type": "string", "nullable": true}, "triggeredBy": {"type": "string", "nullable": true}, "inputData": {"type": "object", "additionalProperties": {}, "nullable": true}, "outputData": {"type": "object", "additionalProperties": {}, "nullable": true}, "nodeExecutions": {"type": "array", "items": {"$ref": "#/components/schemas/NodeExecution"}, "nullable": true}}, "additionalProperties": false}, "WorkflowNode": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "workflowId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "position": {"$ref": "#/components/schemas/NodePosition"}, "parameters": {"type": "object", "additionalProperties": {}, "nullable": true}, "isDisabled": {"type": "boolean"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkflowPerformanceReport": {"type": "object", "properties": {"workflowId": {"type": "string", "format": "uuid"}, "totalExecutions": {"type": "integer", "format": "int32"}, "successfulExecutions": {"type": "integer", "format": "int32"}, "failedExecutions": {"type": "integer", "format": "int32"}, "averageExecutionTime": {"type": "number", "format": "double"}, "nodePerformance": {"type": "array", "items": {"$ref": "#/components/schemas/NodePerformanceMetrics"}, "nullable": true}, "reportGeneratedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "WorkflowSettings": {"type": "object", "properties": {"timeout": {"$ref": "#/components/schemas/TimeSpan"}, "maxRetries": {"type": "integer", "format": "int32"}, "saveExecutionProgress": {"type": "boolean"}, "variables": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}}}}
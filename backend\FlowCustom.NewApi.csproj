<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\FlowCustom.PluginHost\FlowCustom.PluginHost.csproj" />
    <ProjectReference Include="..\Core\FlowCustom.Engine\FlowCustom.Engine.csproj" />
    <ProjectReference Include="..\PluginSDK\FlowCustom.SDK\FlowCustom.SDK.csproj" />
    <ProjectReference Include="..\src\FlowCustom.Data\FlowCustom.Data.csproj" />
    <ProjectReference Include="..\src\FlowCustom.Core\FlowCustom.Core.csproj" />
  </ItemGroup>

  <!-- 复制插件到输出目录 -->
  <Target Name="CopyPlugins" AfterTargets="Build">
    <ItemGroup>
      <PluginFiles Include="$(SolutionDir)Runtime\plugins\**\*" />
    </ItemGroup>
    
    <MakeDir Directories="$(OutputPath)plugins" />
    
    <Copy SourceFiles="@(PluginFiles)" 
          DestinationFolder="$(OutputPath)plugins\%(RecursiveDir)" 
          SkipUnchangedFiles="true" />
  </Target>

</Project>

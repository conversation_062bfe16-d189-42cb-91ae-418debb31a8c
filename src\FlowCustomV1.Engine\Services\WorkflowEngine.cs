using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using FlowCustomV1.Core.Exceptions;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustomV1.Engine.Services;

/// <summary>
/// 工作流执行引擎
/// 负责工作流的执行、调度和生命周期管理
/// </summary>
public class WorkflowEngine : IWorkflowEngine
{
    private readonly ILogger<WorkflowEngine> _logger;
    private readonly IPluginManager _pluginManager;
    private readonly ConcurrentDictionary<string, CancellationTokenSource> _runningExecutions = new();

    public WorkflowEngine(
        ILogger<WorkflowEngine> logger,
        IPluginManager pluginManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _pluginManager = pluginManager ?? throw new ArgumentNullException(nameof(pluginManager));
    }

    /// <summary>
    /// 执行工作流
    /// </summary>
    public async Task<WorkflowExecutionResult> ExecuteAsync(WorkflowExecutionRequest request, CancellationToken cancellationToken = default)
    {
        var executionId = Guid.NewGuid().ToString();
        var result = new WorkflowExecutionResult
        {
            ExecutionId = executionId,
            WorkflowId = request.WorkflowId,
            Status = WorkflowExecutionStatus.Running,
            StartTime = DateTime.UtcNow,
            InputData = request.InputData,
            TriggeredBy = request.TriggeredBy
        };

        // 创建取消令牌源
        var executionCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        _runningExecutions[executionId] = executionCts;

        try
        {
            _logger.LogInformation("开始执行工作流: {ExecutionId} for workflow: {WorkflowId}", 
                executionId, request.WorkflowId);

            // 验证工作流
            var validationResult = await ValidateWorkflowAsync(request.Workflow);
            if (!validationResult.IsValid)
            {
                result.Status = WorkflowExecutionStatus.Failed;
                result.ErrorMessage = string.Join("; ", validationResult.Errors.Select(e => e.Message));
                result.EndTime = DateTime.UtcNow;
                return result;
            }

            // 构建执行图
            var executionGraph = BuildExecutionGraph(request.Workflow);
            
            // 执行节点
            await ExecuteNodesAsync(executionGraph, request, result, executionCts.Token);

            result.EndTime = DateTime.UtcNow;

            // 确定最终状态
            if (result.NodeResults.Any(r => !r.Success))
            {
                result.Status = WorkflowExecutionStatus.Failed;
            }
            else
            {
                result.Status = WorkflowExecutionStatus.Completed;
            }

            _logger.LogInformation("工作流执行完成: {ExecutionId}, 状态: {Status}, 耗时: {Duration}ms", 
                executionId, result.Status, result.Duration?.TotalMilliseconds);
        }
        catch (OperationCanceledException)
        {
            result.Status = WorkflowExecutionStatus.Cancelled;
            result.ErrorMessage = "执行被取消";
            result.EndTime = DateTime.UtcNow;
            _logger.LogWarning("工作流执行被取消: {ExecutionId}", executionId);
        }
        catch (Exception ex)
        {
            result.Status = WorkflowExecutionStatus.Failed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            result.EndTime = DateTime.UtcNow;
            _logger.LogError(ex, "工作流执行失败: {ExecutionId}", executionId);
        }
        finally
        {
            _runningExecutions.TryRemove(executionId, out _);
            executionCts.Dispose();
        }

        return result;
    }

    /// <summary>
    /// 停止工作流执行
    /// </summary>
    public async Task StopAsync(string executionId, CancellationToken cancellationToken = default)
    {
        if (_runningExecutions.TryGetValue(executionId, out var cts))
        {
            cts.Cancel();
            _logger.LogInformation("停止工作流执行: {ExecutionId}", executionId);
        }
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取执行状态
    /// </summary>
    public async Task<WorkflowExecutionStatus?> GetExecutionStatusAsync(string executionId)
    {
        // TODO: 从存储中获取执行状态
        await Task.CompletedTask;
        return _runningExecutions.ContainsKey(executionId) ? WorkflowExecutionStatus.Running : null;
    }

    /// <summary>
    /// 获取执行结果
    /// </summary>
    public async Task<WorkflowExecutionResult?> GetExecutionResultAsync(string executionId)
    {
        // TODO: 从存储中获取执行结果
        await Task.CompletedTask;
        return null;
    }

    /// <summary>
    /// 获取执行历史
    /// </summary>
    public async Task<PagedResult<WorkflowExecutionResult>> GetExecutionHistoryAsync(string workflowId, int pageIndex = 0, int pageSize = 20)
    {
        // TODO: 从存储中获取执行历史
        await Task.CompletedTask;
        return new PagedResult<WorkflowExecutionResult>
        {
            Items = new List<WorkflowExecutionResult>(),
            TotalCount = 0,
            PageIndex = pageIndex,
            PageSize = pageSize
        };
    }

    /// <summary>
    /// 验证工作流
    /// </summary>
    public async Task<WorkflowValidationResult> ValidateAsync(WorkflowDefinition workflow)
    {
        return await ValidateWorkflowAsync(workflow);
    }

    #region 私有方法

    /// <summary>
    /// 验证工作流
    /// </summary>
    private async Task<WorkflowValidationResult> ValidateWorkflowAsync(WorkflowDefinition workflow)
    {
        var result = new WorkflowValidationResult
        {
            IsValid = true
        };

        if (workflow == null)
        {
            result.Errors.Add(new ValidationError
            {
                Code = "NULL_WORKFLOW",
                Message = "工作流定义不能为空",
                Severity = ValidationSeverity.Critical
            });
            result.IsValid = false;
            return result;
        }

        // 验证节点
        foreach (var node in workflow.Nodes)
        {
            var plugin = _pluginManager.GetPlugin(node.Type);
            if (plugin == null)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "PLUGIN_NOT_FOUND",
                    Message = $"节点类型 '{node.Type}' 没有对应的插件",
                    Field = node.Id,
                    Severity = ValidationSeverity.Error
                });
                continue;
            }

            try
            {
                var nodeConfig = new NodeConfiguration
                {
                    Parameters = node.Parameters,
                    IsDisabled = node.IsDisabled,
                    Notes = node.Notes,
                    CustomInputs = node.CustomInputs,
                    CustomOutputs = node.CustomOutputs
                };

                var nodeValidation = await plugin.ValidateAsync(nodeConfig);
                if (!nodeValidation.IsValid)
                {
                    result.Errors.AddRange(nodeValidation.Errors);
                    result.Warnings.AddRange(nodeValidation.Warnings);
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "VALIDATION_EXCEPTION",
                    Message = $"验证节点 '{node.Name}' 时发生异常: {ex.Message}",
                    Field = node.Id,
                    Severity = ValidationSeverity.Error
                });
            }
        }

        // 验证连接
        foreach (var connection in workflow.Connections)
        {
            var sourceNode = workflow.Nodes.FirstOrDefault(n => n.Id == connection.SourceNodeId);
            var targetNode = workflow.Nodes.FirstOrDefault(n => n.Id == connection.TargetNodeId);

            if (sourceNode == null)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "INVALID_SOURCE_NODE",
                    Message = $"连接的源节点不存在: {connection.SourceNodeId}",
                    Field = connection.Id,
                    Severity = ValidationSeverity.Error
                });
            }

            if (targetNode == null)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "INVALID_TARGET_NODE",
                    Message = $"连接的目标节点不存在: {connection.TargetNodeId}",
                    Field = connection.Id,
                    Severity = ValidationSeverity.Error
                });
            }
        }

        // 检查是否有触发器节点
        var triggerNodes = workflow.Nodes.Where(n => 
        {
            var plugin = _pluginManager.GetPlugin(n.Type);
            return plugin?.IsTrigger == true;
        }).ToList();

        if (!triggerNodes.Any())
        {
            result.Warnings.Add(new ValidationWarning
            {
                Code = "NO_TRIGGER_NODES",
                Message = "工作流中没有触发器节点"
            });
        }

        result.ValidatedNodes = workflow.Nodes.Count;
        result.ValidatedConnections = workflow.Connections.Count;
        result.IsValid = result.Errors.Count == 0;

        return result;
    }

    /// <summary>
    /// 构建执行图
    /// </summary>
    private ExecutionGraph BuildExecutionGraph(WorkflowDefinition workflow)
    {
        var graph = new ExecutionGraph();

        // 添加节点
        foreach (var node in workflow.Nodes.Where(n => !n.IsDisabled))
        {
            var plugin = _pluginManager.GetPlugin(node.Type);
            if (plugin != null)
            {
                graph.Nodes[node.Id] = new ExecutionNode
                {
                    Id = node.Id,
                    Name = node.Name,
                    Type = node.Type,
                    Plugin = plugin,
                    Configuration = new NodeConfiguration
                    {
                        Parameters = node.Parameters,
                        IsDisabled = node.IsDisabled,
                        Notes = node.Notes,
                        CustomInputs = node.CustomInputs,
                        CustomOutputs = node.CustomOutputs
                    },
                    Dependencies = new List<string>(),
                    Dependents = new List<string>()
                };
            }
        }

        // 添加依赖关系
        foreach (var connection in workflow.Connections)
        {
            if (graph.Nodes.TryGetValue(connection.SourceNodeId, out var sourceNode) &&
                graph.Nodes.TryGetValue(connection.TargetNodeId, out var targetNode))
            {
                targetNode.Dependencies.Add(connection.SourceNodeId);
                sourceNode.Dependents.Add(connection.TargetNodeId);
                
                graph.Connections.Add(new ExecutionConnection
                {
                    SourceNodeId = connection.SourceNodeId,
                    TargetNodeId = connection.TargetNodeId,
                    SourceOutput = connection.SourceOutput,
                    TargetInput = connection.TargetInput
                });
            }
        }

        return graph;
    }

    /// <summary>
    /// 执行节点
    /// </summary>
    private async Task ExecuteNodesAsync(
        ExecutionGraph graph,
        WorkflowExecutionRequest request,
        WorkflowExecutionResult result,
        CancellationToken cancellationToken)
    {
        var executedNodes = new HashSet<string>();
        var nodeData = new Dictionary<string, Dictionary<string, object>>();

        // 找到起始节点（没有依赖的节点或触发器节点）
        var startNodes = graph.Nodes.Values
            .Where(n => !n.Dependencies.Any() || n.Plugin?.IsTrigger == true)
            .ToList();

        if (!startNodes.Any())
        {
            throw new WorkflowExecutionException(result.ExecutionId, request.WorkflowId, "工作流中没有找到起始节点");
        }

        // 层级执行
        var currentLevel = startNodes;
        
        while (currentLevel.Any())
        {
            cancellationToken.ThrowIfCancellationRequested();

            _logger.LogDebug("执行第{Level}层，包含{Count}个节点", 
                executedNodes.Count / startNodes.Count + 1, currentLevel.Count);

            // 并行执行当前层级的节点
            var tasks = currentLevel.Select(node =>
                ExecuteNodeAsync(node, graph, request, result, nodeData, cancellationToken)).ToArray();
            
            await Task.WhenAll(tasks);

            // 标记已执行的节点
            foreach (var node in currentLevel)
            {
                executedNodes.Add(node.Id);
            }

            // 查找下一层级的节点
            var nextLevel = graph.Nodes.Values
                .Where(n => !executedNodes.Contains(n.Id) && 
                           n.Dependencies.All(dep => executedNodes.Contains(dep)))
                .ToList();

            currentLevel = nextLevel;
        }
    }

    /// <summary>
    /// 执行单个节点
    /// </summary>
    private async Task ExecuteNodeAsync(
        ExecutionNode node,
        ExecutionGraph graph,
        WorkflowExecutionRequest request,
        WorkflowExecutionResult result,
        Dictionary<string, Dictionary<string, object>> nodeData,
        CancellationToken cancellationToken)
    {
        var nodeResult = new NodeExecutionResult
        {
            NodeId = node.Id,
            NodeName = node.Name,
            NodeType = node.Type,
            Status = NodeExecutionStatus.Running,
            StartTime = DateTime.UtcNow
        };

        result.NodeResults.Add(nodeResult);

        try
        {
            _logger.LogDebug("开始执行节点: {NodeId} ({NodeType})", node.Id, node.Type);

            // 准备输入数据
            var inputData = PrepareNodeInputData(node, graph, nodeData, request);

            // 创建执行上下文
            var context = new NodeExecutionContext
            {
                ExecutionId = result.ExecutionId,
                WorkflowId = request.WorkflowId,
                NodeId = node.Id,
                Configuration = node.Configuration,
                InputData = inputData,
                Variables = request.Variables ?? new(),
                CancellationToken = cancellationToken
            };

            // 执行节点
            var executionResult = await node.Plugin.ExecuteAsync(context, cancellationToken);

            // 更新结果
            nodeResult.Success = executionResult.Success;
            nodeResult.Status = executionResult.Success ? NodeExecutionStatus.Completed : NodeExecutionStatus.Failed;
            nodeResult.OutputData = executionResult.OutputData;
            nodeResult.NextEndpoint = executionResult.NextEndpoint;
            nodeResult.ErrorMessage = executionResult.ErrorMessage;
            nodeResult.Exception = executionResult.Exception;
            nodeResult.EndTime = DateTime.UtcNow;

            // 保存节点输出数据
            nodeData[node.Id] = executionResult.OutputData;

            _logger.LogDebug("节点执行完成: {NodeId}, 成功: {Success}, 耗时: {Duration}ms", 
                node.Id, nodeResult.Success, nodeResult.Duration?.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            nodeResult.Success = false;
            nodeResult.Status = NodeExecutionStatus.Failed;
            nodeResult.ErrorMessage = ex.Message;
            nodeResult.Exception = ex;
            nodeResult.EndTime = DateTime.UtcNow;

            _logger.LogError(ex, "节点执行失败: {NodeId}", node.Id);
        }
    }

    /// <summary>
    /// 准备节点输入数据
    /// </summary>
    private Dictionary<string, object> PrepareNodeInputData(
        ExecutionNode node,
        ExecutionGraph graph,
        Dictionary<string, Dictionary<string, object>> nodeData,
        WorkflowExecutionRequest request)
    {
        var inputData = new Dictionary<string, object>();

        // 添加全局输入数据
        foreach (var kvp in request.InputData)
        {
            inputData[kvp.Key] = kvp.Value;
        }

        // 添加触发数据
        if (request.TriggerData != null)
        {
            foreach (var kvp in request.TriggerData)
            {
                inputData[kvp.Key] = kvp.Value;
            }
        }

        // 添加前置节点的输出数据
        var incomingConnections = graph.Connections
            .Where(c => c.TargetNodeId == node.Id)
            .ToList();

        foreach (var connection in incomingConnections)
        {
            if (nodeData.TryGetValue(connection.SourceNodeId, out var sourceData))
            {
                var inputKey = string.IsNullOrEmpty(connection.TargetInput) ? "main" : connection.TargetInput;
                
                if (!string.IsNullOrEmpty(connection.SourceOutput) && 
                    sourceData.TryGetValue(connection.SourceOutput, out var outputValue))
                {
                    inputData[inputKey] = outputValue;
                }
                else
                {
                    inputData[inputKey] = sourceData;
                }
            }
        }

        return inputData;
    }

    #endregion
}

using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Nodes;

/// <summary>
/// 文件监控触发器节点
/// </summary>
public class FileWatcherTriggerNode : ITriggerNode
{
    private readonly ILogger<FileWatcherTriggerNode> _logger;
    private static readonly ConcurrentDictionary<Guid, FileSystemWatcher> _activeWatchers = new();

    public FileWatcherTriggerNode(ILogger<FileWatcherTriggerNode> logger)
    {
        _logger = logger;
    }

    public string NodeType => "file-watcher-trigger";
    public string DisplayName => "File Watcher Trigger";
    public string Description => "Trigger workflow when files are created, modified, or deleted";
    public string Category => "Trigger";
    public string Icon => "folder";

    public event EventHandler<TriggerEventArgs>? Triggered;

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "watchPath",
                DisplayName = "Watch Path",
                Description = "Directory path to monitor for file changes",
                Type = ParameterType.String,
                Required = true
            },
            new NodeParameterDefinition
            {
                Name = "fileFilter",
                DisplayName = "File Filter",
                Description = "File filter pattern (e.g., *.txt, *.json)",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = "*.*"
            },
            new NodeParameterDefinition
            {
                Name = "watchEvents",
                DisplayName = "Watch Events",
                Description = "Events to monitor",
                Type = ParameterType.MultiSelect,
                Required = false,
                DefaultValue = new[] { "Created", "Changed" },
                Options = new[] { "Created", "Changed", "Deleted", "Renamed" }
            },
            new NodeParameterDefinition
            {
                Name = "includeSubdirectories",
                DisplayName = "Include Subdirectories",
                Description = "Monitor subdirectories as well",
                Type = ParameterType.Boolean,
                Required = false,
                DefaultValue = false
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node, 
        FlowCustom.Core.Models.ExecutionContext context, 
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        return NodeExecutionResult.CreateSuccess(new Dictionary<string, object>
        {
            ["message"] = "File watcher trigger is active",
            ["watchPath"] = GetParameter<string>(node, "watchPath", ""),
            ["fileFilter"] = GetParameter<string>(node, "fileFilter", "*.*")
        });
    }

    public async Task StartAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        var watchPath = GetParameter<string>(node, "watchPath");
        var fileFilter = GetParameter<string>(node, "fileFilter", "*.*");
        var includeSubdirectories = GetParameter<bool>(node, "includeSubdirectories", false);

        if (string.IsNullOrEmpty(watchPath))
        {
            throw new ArgumentException("Watch path is required");
        }

        if (!Directory.Exists(watchPath))
        {
            throw new DirectoryNotFoundException($"Watch path does not exist: {watchPath}");
        }

        try
        {
            var watcher = new FileSystemWatcher(watchPath, fileFilter)
            {
                IncludeSubdirectories = includeSubdirectories,
                EnableRaisingEvents = true
            };

            // 订阅事件
            watcher.Created += (sender, e) => OnFileEvent(node, "Created", e);
            watcher.Changed += (sender, e) => OnFileEvent(node, "Changed", e);
            watcher.Deleted += (sender, e) => OnFileEvent(node, "Deleted", e);
            watcher.Renamed += (sender, e) => OnFileEvent(node, "Renamed", e);

            _activeWatchers[node.Id] = watcher;
            
            _logger.LogInformation("Started file watcher: {WatchPath} with filter: {FileFilter} for node: {NodeId}", 
                watchPath, fileFilter, node.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start file watcher for node: {NodeId}", node.Id);
            throw;
        }

        await Task.CompletedTask;
    }

    public async Task StopAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        var nodeId = node.Id;
        
        if (_activeWatchers.TryRemove(nodeId, out var watcher))
        {
            watcher.EnableRaisingEvents = false;
            watcher.Dispose();
            _logger.LogInformation("Stopped file watcher for node: {NodeId}", node.Id);
        }

        await Task.CompletedTask;
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.ContainsKey("watchPath") || 
            string.IsNullOrEmpty(parameters["watchPath"]?.ToString()))
        {
            errors.Add("Watch path is required");
        }

        return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
    }

    private void OnFileEvent(WorkflowNode node, string eventType, FileSystemEventArgs e)
    {
        try
        {
            var triggerArgs = new TriggerEventArgs
            {
                WorkflowId = node.WorkflowId,
                NodeId = node.Id,
                Data = new Dictionary<string, object>
                {
                    ["eventType"] = eventType,
                    ["filePath"] = e.FullPath,
                    ["fileName"] = e.Name ?? "",
                    ["timestamp"] = DateTime.UtcNow,
                    ["changeType"] = e.ChangeType.ToString()
                },
                Source = "file-watcher"
            };

            // 如果是重命名事件，添加旧文件名
            if (e is RenamedEventArgs renamedArgs)
            {
                triggerArgs.Data["oldFilePath"] = renamedArgs.OldFullPath;
                triggerArgs.Data["oldFileName"] = renamedArgs.OldName ?? "";
            }

            Triggered?.Invoke(this, triggerArgs);
            _logger.LogInformation("File watcher triggered: {EventType} for file: {FilePath}", 
                eventType, e.FullPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing file event for node: {NodeId}", node.Id);
        }
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

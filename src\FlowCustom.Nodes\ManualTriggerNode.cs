using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;

namespace FlowCustom.Nodes;

/// <summary>
/// 手动触发器节点
/// </summary>
public class ManualTriggerNode : ITriggerNode
{
    private readonly ILogger<ManualTriggerNode> _logger;

    public ManualTriggerNode(ILogger<ManualTriggerNode> logger)
    {
        _logger = logger;
    }

    public string NodeType => "manual-trigger";

    public string DisplayName => "手动触发器";

    public string Description => "通过手动点击按钮触发工作流执行";

    public string Category => "触发器";

    public string Icon => "hand-pointer";

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "buttonText",
                DisplayName = "按钮文本",
                Description = "触发按钮显示的文本",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = "立即执行"
            },
            new NodeParameterDefinition
            {
                Name = "confirmMessage",
                DisplayName = "确认消息",
                Description = "点击按钮时显示的确认消息（留空则不显示确认对话框）",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = ""
            },
            new NodeParameterDefinition
            {
                Name = "enabled",
                DisplayName = "启用状态",
                Description = "是否启用此触发器",
                Type = ParameterType.Boolean,
                Required = true,
                DefaultValue = true
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node,
        FlowCustom.Core.Models.ExecutionContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var enabled = GetParameter<bool>(node, "enabled", true);
            if (!enabled)
            {
                _logger.LogInformation("手动触发器已禁用，跳过执行");
                return NodeExecutionResult.CreateSuccess(new Dictionary<string, object>
                {
                    ["triggered"] = false,
                    ["reason"] = "disabled",
                    ["timestamp"] = DateTime.UtcNow
                });
            }

            var buttonText = GetParameter<string>(node, "buttonText", "立即执行");
            var confirmMessage = GetParameter<string>(node, "confirmMessage", "");

            _logger.LogInformation("手动触发器执行: {ButtonText}", buttonText);

            // 手动触发器的执行结果
            var result = new Dictionary<string, object>
            {
                ["triggered"] = true,
                ["buttonText"] = buttonText,
                ["confirmMessage"] = confirmMessage,
                ["timestamp"] = DateTime.UtcNow,
                ["triggerType"] = "manual",
                ["nodeId"] = node.Id,
                ["workflowId"] = node.WorkflowId
            };

            _logger.LogInformation("手动触发器执行成功");
            return NodeExecutionResult.CreateSuccess(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动触发器执行失败");
            return NodeExecutionResult.CreateError($"手动触发器执行失败: {ex.Message}");
        }
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        // 手动触发器的参数都是可选的，所以总是返回成功
        return ValidationResult.Success();
    }

    // ITriggerNode 接口实现
    public event EventHandler<TriggerEventArgs>? Triggered;

    public async Task StartAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        // 手动触发器不需要启动监听，因为它是通过 API 调用触发的
        _logger.LogInformation("手动触发器已准备就绪: {NodeId}", node.Id);
        await Task.CompletedTask;
    }

    public async Task StopAsync(WorkflowNode node, CancellationToken cancellationToken = default)
    {
        // 手动触发器不需要停止监听
        _logger.LogInformation("手动触发器已停止: {NodeId}", node.Id);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 手动触发工作流（由 API 调用）
    /// </summary>
    public void TriggerManually(WorkflowNode node, Dictionary<string, object> data)
    {
        _logger.LogInformation("手动触发器被触发: {NodeId}", node.Id);

        var eventArgs = new TriggerEventArgs
        {
            WorkflowId = node.WorkflowId,
            NodeId = node.Id,
            Data = data,
            Source = "manual"
        };

        Triggered?.Invoke(this, eventArgs);
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法转换参数 {ParameterName} 的值，使用默认值", parameterName);
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

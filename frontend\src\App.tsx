import { useState } from 'react';
import WorkflowList from './components/WorkflowList';
import WorkflowEditor from './components/WorkflowEditor';
import ExecutionHistory from './components/ExecutionHistory';
import LogViewer from './components/LogViewer';
import ErrorBoundary from './components/ErrorBoundary';
import type { Workflow, WorkflowNode, NodeConnection } from './types/workflow';
import { workflowApi } from './services/api';

type AppView = 'list' | 'editor' | 'history' | 'logs';

function App() {
  const [currentView, setCurrentView] = useState<AppView>('list');
  const [currentWorkflow, setCurrentWorkflow] = useState<Workflow | null>(null);

  /**
   * 处理选择工作流的逻辑
   *
   * 重要修复：不直接使用列表中的workflow对象，而是重新从后端获取完整数据
   * 这确保了connections等数据的完整性，解决连线不显示的问题
   */
  const handleSelectWorkflow = async (workflow: Workflow) => {
    try {
      console.log('选择工作流，重新加载完整数据:', workflow.id);

      // 重新从后端获取完整的工作流数据，确保包含所有connections
      const fullWorkflow = await workflowApi.getWorkflow(workflow.id);

      console.log('加载的完整工作流数据:', {
        id: fullWorkflow.id,
        name: fullWorkflow.name,
        nodeCount: fullWorkflow.nodes?.length || 0,
        connectionCount: fullWorkflow.connections?.length || 0,
        connections: fullWorkflow.connections
      });

      setCurrentWorkflow(fullWorkflow);
      setCurrentView('editor');
    } catch (error) {
      console.error('加载工作流详情失败:', error);
      // 如果加载失败，仍然使用列表中的数据，但给出警告
      console.warn('使用列表中的工作流数据，可能缺少连线信息');
      setCurrentWorkflow(workflow);
      setCurrentView('editor');
    }
  };

  const handleCreateWorkflow = async () => {
    try {
      const newWorkflow: Partial<Workflow> = {
        name: '新建工作流',
        description: '一个新的工作流',
        isActive: false,
        createdBy: 'default',
        nodes: [],
        connections: [],
        settings: {
          maxRetries: 0,
          saveExecutionProgress: true,
          variables: {},
        },
      };

      const createdWorkflow = await workflowApi.createWorkflow(newWorkflow);
      setCurrentWorkflow(createdWorkflow);
      setCurrentView('editor');
    } catch (error) {
      console.error('Failed to create workflow:', error);
      alert('创建工作流失败');
    }
  };

  const handleSaveWorkflow = async (nodes: WorkflowNode[], connections: NodeConnection[]) => {
    if (!currentWorkflow) return;

    try {
      // 确保发送正确格式的工作流数据
      const updatedWorkflow: Workflow = {
        id: currentWorkflow.id,
        name: currentWorkflow.name,
        description: currentWorkflow.description,
        isActive: currentWorkflow.isActive,
        createdAt: currentWorkflow.createdAt,
        updatedAt: new Date().toISOString(),
        createdBy: currentWorkflow.createdBy || 'system',
        nodes,
        connections,
        settings: currentWorkflow.settings || {
          maxRetries: 0,
          saveExecutionProgress: true,
          variables: {},
        },
      };

      const result = await workflowApi.updateWorkflow(currentWorkflow.id, updatedWorkflow);
      setCurrentWorkflow(result);
      alert('工作流保存成功！');
    } catch (error) {
      console.error('Failed to save workflow:', error);
      alert('工作流保存失败');
    }
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setCurrentWorkflow(null);
  };

  const handleViewHistory = (workflow: Workflow) => {
    setCurrentWorkflow(workflow);
    setCurrentView('history');
  };

  const handleViewLogs = () => {
    setCurrentView('logs');
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-100">
        {currentView === 'list' ? (
        <WorkflowList
          key={Date.now()} // 强制重新渲染以刷新数据
          onSelectWorkflow={handleSelectWorkflow}
          onCreateWorkflow={handleCreateWorkflow}
          onViewHistory={handleViewHistory}
          onViewLogs={handleViewLogs}
        />
      ) : currentView === 'history' && currentWorkflow ? (
        <div className="h-full flex flex-col">
          <div className="p-4 border-b border-gray-200 bg-white">
            <button
              onClick={handleBackToList}
              className="text-gray-600 hover:text-gray-800 transition-colors mb-2"
            >
              ← 返回工作流列表
            </button>
            <h1 className="text-xl font-semibold text-gray-800">
              执行历史 - {currentWorkflow.name}
            </h1>
          </div>
          <div className="flex-1">
            <ExecutionHistory workflowId={currentWorkflow.id} />
          </div>
        </div>
      ) : currentView === 'logs' ? (
        <div className="h-full flex flex-col">
          <div className="p-4 border-b border-gray-200 bg-white">
            <button
              onClick={handleBackToList}
              className="text-gray-600 hover:text-gray-800 transition-colors mb-2"
            >
              ← 返回工作流列表
            </button>
            <h1 className="text-xl font-semibold text-gray-800">
              系统日志
            </h1>
          </div>
          <div className="flex-1">
            <LogViewer />
          </div>
        </div>
      ) : (
        <WorkflowEditor
          workflowId={currentWorkflow?.id}
          initialNodes={currentWorkflow?.nodes || []}
          initialConnections={currentWorkflow?.connections || []}
          onSave={handleSaveWorkflow}
          onBack={handleBackToList}
        />
        )}
      </div>
    </ErrorBoundary>
  );
}

export default App;

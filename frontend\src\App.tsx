import { useState } from 'react';
import WorkflowList from './components/WorkflowList';
import WorkflowEditor from './components/WorkflowEditor';
import type { Workflow, WorkflowNode, NodeConnection } from './types/workflow';
import { workflowApi } from './services/api';

type AppView = 'list' | 'editor';

function App() {
  const [currentView, setCurrentView] = useState<AppView>('list');
  const [currentWorkflow, setCurrentWorkflow] = useState<Workflow | null>(null);

  const handleSelectWorkflow = (workflow: Workflow) => {
    setCurrentWorkflow(workflow);
    setCurrentView('editor');
  };

  const handleCreateWorkflow = async () => {
    try {
      const newWorkflow: Partial<Workflow> = {
        name: 'New Workflow',
        description: 'A new workflow',
        isActive: false,
        createdBy: 'default',
        nodes: [],
        connections: [],
        settings: {
          maxRetries: 0,
          saveExecutionProgress: true,
          variables: {},
        },
      };

      const createdWorkflow = await workflowApi.createWorkflow(newWorkflow);
      setCurrentWorkflow(createdWorkflow);
      setCurrentView('editor');
    } catch (error) {
      console.error('Failed to create workflow:', error);
      alert('Failed to create workflow');
    }
  };

  const handleSaveWorkflow = async (nodes: WorkflowNode[], connections: NodeConnection[]) => {
    if (!currentWorkflow) return;

    try {
      // 确保发送正确格式的工作流数据
      const updatedWorkflow: Workflow = {
        id: currentWorkflow.id,
        name: currentWorkflow.name,
        description: currentWorkflow.description,
        isActive: currentWorkflow.isActive,
        createdAt: currentWorkflow.createdAt,
        updatedAt: new Date().toISOString(),
        createdBy: currentWorkflow.createdBy || 'system',
        nodes,
        connections,
        settings: currentWorkflow.settings || {
          maxRetries: 0,
          saveExecutionProgress: true,
          variables: {},
        },
      };

      const result = await workflowApi.updateWorkflow(currentWorkflow.id, updatedWorkflow);
      setCurrentWorkflow(result);
      alert('Workflow saved successfully!');
    } catch (error) {
      console.error('Failed to save workflow:', error);
      alert('Failed to save workflow');
    }
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setCurrentWorkflow(null);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {currentView === 'list' ? (
        <WorkflowList
          key={Date.now()} // 强制重新渲染以刷新数据
          onSelectWorkflow={handleSelectWorkflow}
          onCreateWorkflow={handleCreateWorkflow}
        />
      ) : (
        <div className="h-screen flex flex-col">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={handleBackToList}
                className="text-gray-600 hover:text-gray-800 transition-colors"
              >
                ← Back to Workflows
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-800">
                  {currentWorkflow?.name || 'Workflow Editor'}
                </h1>
                <p className="text-sm text-gray-600">
                  {currentWorkflow?.description || 'Edit your workflow'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span
                className={`px-3 py-1 text-sm rounded-full ${
                  currentWorkflow?.isActive
                    ? 'bg-green-100 text-green-700'
                    : 'bg-gray-100 text-gray-700'
                }`}
              >
                {currentWorkflow?.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>

          {/* Editor */}
          <div className="flex-1">
            <WorkflowEditor
              workflowId={currentWorkflow?.id}
              initialNodes={currentWorkflow?.nodes || []}
              initialConnections={currentWorkflow?.connections || []}
              onSave={handleSaveWorkflow}
            />
          </div>
        </div>
      )}
    </div>
  );
}

export default App;

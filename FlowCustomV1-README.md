# 🚀 FlowCustomV1 - 下一代插件化工作流自动化平台

[![.NET](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/flowcustom/flowcustomv1)

FlowCustomV1 是一个现代化的插件化工作流自动化平台，采用 .NET 8 构建，提供强大的工作流编排和执行能力。

## ✨ 特性

- 🔌 **完全插件化** - 所有节点都是独立的插件，支持动态加载
- ⚡ **高性能执行** - 异步并行执行，支持复杂工作流
- 🛡️ **企业级安全** - JWT认证、权限控制、审计日志
- 📊 **实时监控** - 执行状态监控、性能指标、错误追踪
- 🎨 **现代化API** - RESTful API设计，Swagger文档集成
- 🔧 **开发友好** - 丰富的SDK、模板和开发工具

## 🏗️ 架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   Web API       │    │   执行引擎      │
│   (React)       │◄──►│  (ASP.NET Core) │◄──►│  (WorkflowEngine)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   插件管理器    │    │   数据访问层    │
                       │ (PluginManager) │    │ (Entity Framework)│
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   插件生态系统  │
                       │  (Plugin DLLs)  │
                       └─────────────────┘
```

## 🚀 快速开始

### 前置要求

- [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [Git](https://git-scm.com/)

### 安装和运行

1. **克隆项目**
   ```bash
   git clone https://github.com/flowcustom/flowcustomv1.git
   cd flowcustomv1
   ```

2. **还原依赖**
   ```bash
   dotnet restore
   ```

3. **构建项目**
   ```bash
   dotnet build
   ```

4. **启动API服务**
   ```bash
   dotnet run --project src/FlowCustomV1.Api
   ```

5. **访问API文档**
   ```
   http://localhost:5279/swagger
   ```

### 验证安装

```bash
# 检查健康状态
curl http://localhost:5279/health

# 查看系统信息
curl http://localhost:5279/api/system/info

# 查看插件列表
curl http://localhost:5279/api/plugins
```

## 📖 API 文档

### 核心端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/api/system/info` | GET | 系统信息 |
| `/api/plugins` | GET | 插件列表 |
| `/api/plugins/{nodeType}` | GET | 插件详情 |
| `/api/workflow/{workflowId}/execute` | POST | 执行工作流 |
| `/api/workflow/validate` | POST | 验证工作流 |

### 示例请求

**执行工作流**
```bash
curl -X POST "http://localhost:5279/api/workflow/test-workflow/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "workflow": {
      "id": "test-workflow",
      "name": "测试工作流",
      "nodes": [...],
      "connections": [...]
    },
    "inputData": {
      "message": "Hello, FlowCustomV1!"
    }
  }'
```

## 🔌 插件开发

### 创建插件

1. **继承基类**
   ```csharp
   public class MyPlugin : NodePluginBase
   {
       public override string NodeType => "my-plugin";
       public override string DisplayName => "我的插件";
       // ...
   }
   ```

2. **实现执行逻辑**
   ```csharp
   protected override async Task<NodeExecutionResult> OnExecuteAsync(
       NodeExecutionContext context, 
       CancellationToken cancellationToken)
   {
       // 插件逻辑
       return NodeExecutionResult.Success(outputData);
   }
   ```

3. **定义参数**
   ```csharp
   protected override IEnumerable<ParameterDefinition> GetParameterDefinitionsCore()
   {
       return new[]
       {
           new ParameterDefinition
           {
               Name = "message",
               DisplayName = "消息",
               Type = ParameterType.String,
               Required = true
           }
       };
   }
   ```

## 📁 项目结构

```
FlowCustomV1/
├── src/
│   ├── FlowCustomV1.Core/          # 核心接口和模型
│   ├── FlowCustomV1.SDK/           # 插件开发SDK
│   ├── FlowCustomV1.PluginHost/    # 插件宿主服务
│   ├── FlowCustomV1.Engine/        # 工作流执行引擎
│   └── FlowCustomV1.Api/           # Web API服务
├── plugins/
│   └── FlowCustomV1.Plugins.Core/  # 核心插件
├── scripts/                        # 构建和部署脚本
└── docs/                          # 文档
```

## 🎉 项目完成状态

✅ **已完成的核心功能**:
- 完整的插件化架构
- 工作流执行引擎
- Web API服务
- 核心插件实现
- 构建和部署脚本

🚧 **待开发功能**:
- 数据访问层 (Entity Framework)
- 前端应用 (React)
- 更多插件 (HTTP、条件判断等)
- 单元测试和集成测试

---

**FlowCustomV1** - 让工作流自动化变得简单而强大 🚀

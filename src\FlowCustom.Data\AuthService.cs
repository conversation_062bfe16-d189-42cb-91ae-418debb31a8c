using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.IdentityModel.Tokens;

namespace FlowCustom.Data;

/// <summary>
/// 认证服务实现
/// </summary>
public class AuthService : IAuthService
{
    private readonly IUserService _userService;
    private readonly ILogger<AuthService> _logger;
    private readonly ConcurrentDictionary<string, DateTime> _tokenBlacklist = new();
    private readonly ConcurrentDictionary<Guid, ApiKey> _apiKeys = new();
    private readonly string _jwtSecret = "FlowCustomJwtSecretKey123456789"; // 在实际项目中应该从配置读取
    private readonly TimeSpan _tokenExpiry = TimeSpan.FromHours(24);

    public AuthService(IUserService userService, ILogger<AuthService> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    public async Task<AuthResult> LoginAsync(string username, string password)
    {
        try
        {
            var user = await _userService.GetUserByUsernameAsync(username);
            if (user == null)
            {
                user = await _userService.GetUserByEmailAsync(username);
            }

            if (user == null || !user.IsActive)
            {
                return new AuthResult { Success = false, Error = "Invalid credentials" };
            }

            var isValidPassword = await _userService.ValidatePasswordAsync(user.Id, password);
            if (!isValidPassword)
            {
                return new AuthResult { Success = false, Error = "Invalid credentials" };
            }

            // 更新最后登录时间
            user.LastLoginAt = DateTime.UtcNow;
            await _userService.UpdateUserAsync(user);

            // 生成 JWT 令牌
            var accessToken = GenerateJwtToken(user);
            var refreshToken = GenerateRefreshToken();

            _logger.LogInformation("User logged in: {Username} ({UserId})", user.Username, user.Id);

            return new AuthResult
            {
                Success = true,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresAt = DateTime.UtcNow.Add(_tokenExpiry),
                User = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for username: {Username}", username);
            return new AuthResult { Success = false, Error = "Login failed" };
        }
    }

    public async Task<AuthResult> RefreshTokenAsync(string refreshToken)
    {
        // 简化实现 - 在实际项目中应该验证 refresh token
        await Task.CompletedTask;
        return new AuthResult { Success = false, Error = "Refresh token not implemented" };
    }

    public async Task LogoutAsync(string token)
    {
        // 将令牌加入黑名单
        _tokenBlacklist[token] = DateTime.UtcNow.Add(_tokenExpiry);
        
        _logger.LogInformation("User logged out, token blacklisted");
        await Task.CompletedTask;
    }

    public async Task<TokenValidationResult> ValidateTokenAsync(string token)
    {
        try
        {
            // 检查令牌是否在黑名单中
            if (_tokenBlacklist.ContainsKey(token))
            {
                return new TokenValidationResult { IsValid = false, Error = "Token is blacklisted" };
            }

            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSecret);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
            
            var userIdClaim = principal.FindFirst("userId")?.Value;
            var usernameClaim = principal.FindFirst("username")?.Value;
            var rolesClaim = principal.FindAll("role").Select(c => c.Value).ToList();

            if (Guid.TryParse(userIdClaim, out var userId))
            {
                return new TokenValidationResult
                {
                    IsValid = true,
                    UserId = userId,
                    Username = usernameClaim,
                    Roles = rolesClaim
                };
            }

            return new TokenValidationResult { IsValid = false, Error = "Invalid token claims" };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Token validation failed");
            return new TokenValidationResult { IsValid = false, Error = "Invalid token" };
        }
    }

    public async Task<ApiKey> GenerateApiKeyAsync(Guid userId, string name, List<string> scopes, DateTime? expiresAt = null)
    {
        var keyValue = GenerateApiKeyValue();
        var apiKey = new ApiKey
        {
            UserId = userId,
            Name = name,
            KeyHash = HashApiKey(keyValue),
            KeyPrefix = keyValue.Substring(0, 8),
            Scopes = scopes,
            ExpiresAt = expiresAt
        };

        _apiKeys[apiKey.Id] = apiKey;
        
        _logger.LogInformation("Generated API key: {Name} for user: {UserId}", name, userId);
        
        await Task.CompletedTask;
        return apiKey;
    }

    public async Task<ApiKeyValidationResult> ValidateApiKeyAsync(string apiKey)
    {
        try
        {
            var keyHash = HashApiKey(apiKey);
            var matchingKey = _apiKeys.Values.FirstOrDefault(k => k.KeyHash == keyHash && k.IsActive);

            if (matchingKey == null)
            {
                return new ApiKeyValidationResult { IsValid = false, Error = "Invalid API key" };
            }

            if (matchingKey.ExpiresAt.HasValue && matchingKey.ExpiresAt.Value < DateTime.UtcNow)
            {
                return new ApiKeyValidationResult { IsValid = false, Error = "API key expired" };
            }

            // 更新最后使用时间
            matchingKey.LastUsedAt = DateTime.UtcNow;

            return new ApiKeyValidationResult
            {
                IsValid = true,
                UserId = matchingKey.UserId,
                Scopes = matchingKey.Scopes
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "API key validation failed");
            return new ApiKeyValidationResult { IsValid = false, Error = "Validation failed" };
        }
    }

    private string GenerateJwtToken(User user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_jwtSecret);

        var claims = new List<Claim>
        {
            new("userId", user.Id.ToString()),
            new("username", user.Username),
            new("email", user.Email)
        };

        // 添加角色声明
        foreach (var role in user.Roles)
        {
            claims.Add(new Claim("role", role.RoleName));
        }

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.Add(_tokenExpiry),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    private string GenerateRefreshToken()
    {
        var randomBytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        return Convert.ToBase64String(randomBytes);
    }

    private string GenerateApiKeyValue()
    {
        var randomBytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        return "fc_" + Convert.ToBase64String(randomBytes).Replace("+", "").Replace("/", "").Replace("=", "")[..40];
    }

    private string HashApiKey(string apiKey)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(apiKey));
        return Convert.ToBase64String(hashedBytes);
    }
}

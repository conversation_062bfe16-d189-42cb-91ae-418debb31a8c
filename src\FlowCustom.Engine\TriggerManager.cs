using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Engine;

/// <summary>
/// 触发器管理器
/// </summary>
public class TriggerManager : ITriggerManager
{
    private readonly IWorkflowService _workflowService;
    private readonly INodeRegistry _nodeRegistry;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly ILogger<TriggerManager> _logger;
    private readonly ConcurrentDictionary<Guid, List<ITriggerNode>> _activeTriggers = new();
    private readonly ConcurrentDictionary<Guid, TriggerStatus> _triggerStatus = new();

    public TriggerManager(
        IWorkflowService workflowService,
        INodeRegistry nodeRegistry,
        IWorkflowEngine workflowEngine,
        ILogger<TriggerManager> logger)
    {
        _workflowService = workflowService;
        _nodeRegistry = nodeRegistry;
        _workflowEngine = workflowEngine;
        _logger = logger;
    }

    public async Task StartTriggersAsync(Guid workflowId)
    {
        var workflow = await _workflowService.GetWorkflowAsync(workflowId);
        if (workflow == null || !workflow.IsActive)
        {
            _logger.LogWarning("Workflow not found or inactive: {WorkflowId}", workflowId);
            return;
        }

        var triggerNodes = workflow.Nodes.Where(n => !n.IsDisabled).ToList();
        var triggers = new List<ITriggerNode>();

        foreach (var workflowNode in triggerNodes)
        {
            var node = _nodeRegistry.GetNode(workflowNode.Type);
            if (node is ITriggerNode triggerNode)
            {
                try
                {
                    // 订阅触发器事件
                    triggerNode.Triggered += async (sender, args) =>
                    {
                        await OnTriggerFired(args);
                    };

                    await triggerNode.StartAsync(workflowNode);
                    triggers.Add(triggerNode);

                    var status = new TriggerStatus
                    {
                        WorkflowId = workflowId,
                        NodeId = workflowNode.Id,
                        NodeType = workflowNode.Type,
                        IsActive = true,
                        StartedAt = DateTime.UtcNow
                    };
                    _triggerStatus[workflowNode.Id] = status;

                    _logger.LogInformation("Started trigger: {NodeId} ({NodeType}) for workflow: {WorkflowId}",
                        workflowNode.Id, workflowNode.Type, workflowId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to start trigger: {NodeId} ({NodeType})",
                        workflowNode.Id, workflowNode.Type);

                    var status = new TriggerStatus
                    {
                        WorkflowId = workflowId,
                        NodeId = workflowNode.Id,
                        NodeType = workflowNode.Type,
                        IsActive = false,
                        StartedAt = DateTime.UtcNow,
                        LastError = ex.Message
                    };
                    _triggerStatus[workflowNode.Id] = status;
                }
            }
        }

        if (triggers.Any())
        {
            _activeTriggers[workflowId] = triggers;
            _logger.LogInformation("Started {Count} triggers for workflow: {WorkflowId}",
                triggers.Count, workflowId);
        }
    }

    public async Task StopTriggersAsync(Guid workflowId)
    {
        if (!_activeTriggers.TryRemove(workflowId, out var triggers))
        {
            return;
        }

        foreach (var trigger in triggers)
        {
            try
            {
                // TODO: 获取对应的 WorkflowNode
                await trigger.StopAsync(new WorkflowNode());
                _logger.LogInformation("Stopped trigger for workflow: {WorkflowId}", workflowId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to stop trigger for workflow: {WorkflowId}", workflowId);
            }
        }

        // 清理状态
        var statusesToRemove = _triggerStatus.Where(kvp => kvp.Value.WorkflowId == workflowId)
            .Select(kvp => kvp.Key).ToList();
        
        foreach (var nodeId in statusesToRemove)
        {
            _triggerStatus.TryRemove(nodeId, out _);
        }

        _logger.LogInformation("Stopped all triggers for workflow: {WorkflowId}", workflowId);
    }

    public async Task ReloadTriggersAsync(Guid workflowId)
    {
        await StopTriggersAsync(workflowId);
        await StartTriggersAsync(workflowId);
        _logger.LogInformation("Reloaded triggers for workflow: {WorkflowId}", workflowId);
    }

    public async Task<IEnumerable<TriggerStatus>> GetActiveTriggerStatusAsync()
    {
        await Task.CompletedTask;
        return _triggerStatus.Values.ToList();
    }

    private async Task OnTriggerFired(TriggerEventArgs args)
    {
        try
        {
            _logger.LogInformation("Trigger fired for workflow: {WorkflowId}, node: {NodeId}",
                args.WorkflowId, args.NodeId);

            await _workflowEngine.ExecuteAsync(
                args.WorkflowId,
                args.Data,
                $"Trigger:{args.NodeId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute workflow triggered by: {NodeId}", args.NodeId);
            
            // 更新触发器状态
            if (_triggerStatus.TryGetValue(args.NodeId, out var status))
            {
                status.LastError = ex.Message;
            }
        }
    }
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多分支并行执行测试脚本
"""

import requests
import json
import uuid
import time

# API 基础URL
BASE_URL = "http://localhost:5053/api"

def create_parallel_workflow():
    """创建多分支并行执行的工作流"""
    print("🔧 创建多分支并行执行工作流...")
    
    workflow_data = {
        "name": "多分支并行执行测试",
        "description": "测试多个分支同时并行执行，包含条件判断、循环、数据转换等"
    }
    
    response = requests.post(f"{BASE_URL}/workflow", json=workflow_data)
    
    if response.status_code == 201:
        workflow = response.json()
        workflow_id = workflow["id"]
        print(f"✅ 工作流创建成功，ID: {workflow_id}")
        return workflow_id
    else:
        print(f"❌ 工作流创建失败: {response.status_code}")
        return None

def setup_parallel_workflow(workflow_id):
    """设置复杂的并行工作流"""
    print(f"🚀 设置并行工作流: {workflow_id}")
    
    # 创建节点ID
    manual_trigger_id = str(uuid.uuid4())
    condition1_id = str(uuid.uuid4())
    condition2_id = str(uuid.uuid4())
    
    # 分支1：数据处理分支
    script1_id = str(uuid.uuid4())
    loop1_id = str(uuid.uuid4())
    transform1_id = str(uuid.uuid4())
    
    # 分支2：计算分支
    script2_id = str(uuid.uuid4())
    script3_id = str(uuid.uuid4())
    
    # 分支3：条件分支
    script4_id = str(uuid.uuid4())
    script5_id = str(uuid.uuid4())
    
    # 汇聚节点
    final_script_id = str(uuid.uuid4())
    
    # 手动触发器
    manual_trigger = {
        "id": manual_trigger_id,
        "workflowId": workflow_id,
        "name": "开始并行处理",
        "type": "manual-trigger",
        "position": {"x": 100, "y": 200},
        "parameters": {
            "buttonText": "启动并行流程",
            "enabled": True
        },
        "isDisabled": False,
        "notes": "触发多分支并行执行"
    }
    
    # 条件判断1 - 数据量判断
    condition1 = {
        "id": condition1_id,
        "workflowId": workflow_id,
        "name": "数据量判断",
        "type": "conditional",
        "position": {"x": 300, "y": 100},
        "parameters": {
            "condition": "data.count > 10",
            "inputData": {"count": 15, "type": "large_dataset"},
            "trueOutput": {"branch": "large_data", "processing": "batch"},
            "falseOutput": {"branch": "small_data", "processing": "single"}
        },
        "isDisabled": False,
        "notes": "判断数据量大小"
    }
    
    # 条件判断2 - 优先级判断
    condition2 = {
        "id": condition2_id,
        "workflowId": workflow_id,
        "name": "优先级判断",
        "type": "conditional",
        "position": {"x": 300, "y": 300},
        "parameters": {
            "condition": "data.priority == \"high\"",
            "inputData": {"priority": "high", "urgency": "critical"},
            "trueOutput": {"branch": "high_priority", "action": "immediate"},
            "falseOutput": {"branch": "normal_priority", "action": "queue"}
        },
        "isDisabled": False,
        "notes": "判断处理优先级"
    }
    
    # 分支1：数据处理分支
    script1 = {
        "id": script1_id,
        "workflowId": workflow_id,
        "name": "数据预处理",
        "type": "script-executor",
        "position": {"x": 500, "y": 50},
        "parameters": {
            "scriptType": "python",
            "script": """
print("=== 数据预处理开始 ===")
import time
import json

# 模拟数据预处理
data = {
    "records": [{"id": i, "value": i * 2} for i in range(1, 11)],
    "processed_at": time.strftime("%Y-%m-%d %H:%M:%S"),
    "status": "preprocessed"
}

print(f"处理了 {len(data['records'])} 条记录")
print(f"预处理完成时间: {data['processed_at']}")
print("=== 数据预处理完成 ===")
""",
            "timeout": 30
        },
        "isDisabled": False,
        "notes": "数据预处理脚本"
    }
    
    loop1 = {
        "id": loop1_id,
        "workflowId": workflow_id,
        "name": "批量处理循环",
        "type": "loop",
        "position": {"x": 700, "y": 50},
        "parameters": {
            "loopType": "array",
            "arrayData": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            "maxIterations": 15,
            "batchSize": 3
        },
        "isDisabled": False,
        "notes": "批量处理数据"
    }
    
    transform1 = {
        "id": transform1_id,
        "workflowId": workflow_id,
        "name": "数据格式转换",
        "type": "data-transform",
        "position": {"x": 900, "y": 50},
        "parameters": {
            "transformType": "map",
            "inputData": [
                {"name": "用户A", "score": 85, "level": "中级"},
                {"name": "用户B", "score": 92, "level": "高级"},
                {"name": "用户C", "score": 78, "level": "初级"}
            ],
            "transformExpression": "item.name + ' [' + item.level + '] 分数:' + item.score"
        },
        "isDisabled": False,
        "notes": "转换数据格式"
    }
    
    # 分支2：计算分支
    script2 = {
        "id": script2_id,
        "workflowId": workflow_id,
        "name": "数学计算",
        "type": "script-executor",
        "position": {"x": 500, "y": 200},
        "parameters": {
            "scriptType": "python",
            "script": """
print("=== 数学计算开始 ===")
import math
import time

# 复杂数学计算
numbers = list(range(1, 1001))
result = {
    "sum": sum(numbers),
    "average": sum(numbers) / len(numbers),
    "sqrt_sum": math.sqrt(sum(numbers)),
    "factorial_10": math.factorial(10),
    "calculated_at": time.strftime("%Y-%m-%d %H:%M:%S")
}

print(f"计算结果: 总和={result['sum']}, 平均值={result['average']:.2f}")
print(f"平方根={result['sqrt_sum']:.2f}, 10的阶乘={result['factorial_10']}")
print("=== 数学计算完成 ===")
""",
            "timeout": 30
        },
        "isDisabled": False,
        "notes": "执行数学计算"
    }
    
    script3 = {
        "id": script3_id,
        "workflowId": workflow_id,
        "name": "统计分析",
        "type": "script-executor",
        "position": {"x": 700, "y": 200},
        "parameters": {
            "scriptType": "javascript",
            "script": """
console.log("=== 统计分析开始 ===");

// 生成随机数据进行统计
const data = Array.from({length: 100}, () => Math.floor(Math.random() * 100));
const stats = {
    count: data.length,
    min: Math.min(...data),
    max: Math.max(...data),
    mean: data.reduce((a, b) => a + b, 0) / data.length,
    median: data.sort((a, b) => a - b)[Math.floor(data.length / 2)],
    analyzed_at: new Date().toISOString()
};

console.log(`统计结果: 数量=${stats.count}, 最小值=${stats.min}, 最大值=${stats.max}`);
console.log(`平均值=${stats.mean.toFixed(2)}, 中位数=${stats.median}`);
console.log("=== 统计分析完成 ===");
""",
            "timeout": 30
        },
        "isDisabled": False,
        "notes": "统计分析脚本"
    }
    
    # 分支3：条件分支
    script4 = {
        "id": script4_id,
        "workflowId": workflow_id,
        "name": "高优先级处理",
        "type": "script-executor",
        "position": {"x": 500, "y": 350},
        "parameters": {
            "scriptType": "python",
            "script": """
print("=== 高优先级处理开始 ===")
import time

# 高优先级任务处理
task = {
    "priority": "HIGH",
    "status": "processing",
    "start_time": time.strftime("%Y-%m-%d %H:%M:%S"),
    "estimated_completion": "2 minutes",
    "resources_allocated": "maximum"
}

print(f"高优先级任务启动: {task['start_time']}")
print(f"预计完成时间: {task['estimated_completion']}")
print("=== 高优先级处理完成 ===")
""",
            "timeout": 30
        },
        "isDisabled": False,
        "notes": "高优先级任务处理"
    }
    
    script5 = {
        "id": script5_id,
        "workflowId": workflow_id,
        "name": "普通优先级处理",
        "type": "script-executor",
        "position": {"x": 500, "y": 450},
        "parameters": {
            "scriptType": "python",
            "script": """
print("=== 普通优先级处理开始 ===")
import time

# 普通优先级任务处理
task = {
    "priority": "NORMAL",
    "status": "queued",
    "start_time": time.strftime("%Y-%m-%d %H:%M:%S"),
    "estimated_completion": "5 minutes",
    "resources_allocated": "standard"
}

print(f"普通优先级任务启动: {task['start_time']}")
print(f"预计完成时间: {task['estimated_completion']}")
print("=== 普通优先级处理完成 ===")
""",
            "timeout": 30
        },
        "isDisabled": False,
        "notes": "普通优先级任务处理"
    }
    
    # 汇聚节点
    final_script = {
        "id": final_script_id,
        "workflowId": workflow_id,
        "name": "结果汇总",
        "type": "script-executor",
        "position": {"x": 1100, "y": 250},
        "parameters": {
            "scriptType": "python",
            "script": """
print("=== 并行处理结果汇总 ===")
import time

# 汇总所有分支的处理结果
summary = {
    "workflow_name": "多分支并行执行测试",
    "total_branches": 3,
    "completion_time": time.strftime("%Y-%m-%d %H:%M:%S"),
    "status": "SUCCESS",
    "branches_completed": [
        "数据处理分支",
        "计算分支", 
        "条件分支"
    ]
}

print(f"工作流: {summary['workflow_name']}")
print(f"总分支数: {summary['total_branches']}")
print(f"完成时间: {summary['completion_time']}")
print(f"状态: {summary['status']}")
print("所有分支处理完成！")
print("=== 汇总完成 ===")
""",
            "timeout": 30
        },
        "isDisabled": False,
        "notes": "汇总所有分支结果"
    }
    
    # 创建连接
    connections = [
        # 触发器到条件判断
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": manual_trigger_id, "targetNodeId": condition1_id, "sourceOutput": "main", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": manual_trigger_id, "targetNodeId": condition2_id, "sourceOutput": "main", "targetInput": "main"},
        
        # 分支1：数据处理分支
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": condition1_id, "targetNodeId": script1_id, "sourceOutput": "true", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": script1_id, "targetNodeId": loop1_id, "sourceOutput": "main", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": loop1_id, "targetNodeId": transform1_id, "sourceOutput": "main", "targetInput": "main"},
        
        # 分支2：计算分支
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": condition1_id, "targetNodeId": script2_id, "sourceOutput": "false", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": script2_id, "targetNodeId": script3_id, "sourceOutput": "main", "targetInput": "main"},
        
        # 分支3：条件分支
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": condition2_id, "targetNodeId": script4_id, "sourceOutput": "true", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": condition2_id, "targetNodeId": script5_id, "sourceOutput": "false", "targetInput": "main"},
        
        # 汇聚到最终节点
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": transform1_id, "targetNodeId": final_script_id, "sourceOutput": "main", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": script3_id, "targetNodeId": final_script_id, "sourceOutput": "main", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": script4_id, "targetNodeId": final_script_id, "sourceOutput": "main", "targetInput": "main"},
        {"id": str(uuid.uuid4()), "workflowId": workflow_id, "sourceNodeId": script5_id, "targetNodeId": final_script_id, "sourceOutput": "main", "targetInput": "main"},
    ]
    
    # 获取当前工作流
    response = requests.get(f"{BASE_URL}/workflow/{workflow_id}")
    if response.status_code != 200:
        print(f"❌ 获取工作流失败: {response.status_code}")
        return False
    
    workflow = response.json()
    
    # 更新工作流
    workflow["nodes"] = [
        manual_trigger, condition1, condition2,
        script1, loop1, transform1,
        script2, script3,
        script4, script5,
        final_script
    ]
    workflow["connections"] = connections
    workflow["isActive"] = True
    
    print("更新工作流，添加并行节点和连接...")
    update_response = requests.put(f"{BASE_URL}/workflow/{workflow_id}", json=workflow)
    
    if update_response.status_code == 200:
        print("✅ 并行工作流设置成功")
        print(f"   节点数量: {len(workflow['nodes'])}")
        print(f"   连接数量: {len(workflow['connections'])}")
        print(f"   分支数量: 3个并行分支")
        return True
    else:
        print(f"❌ 工作流更新失败: {update_response.status_code}")
        print(f"   响应: {update_response.text}")
        return False

def test_parallel_execution(workflow_id):
    """测试并行执行"""
    print(f"\n🚀 测试并行执行: {workflow_id}")
    
    response = requests.post(f"{BASE_URL}/trigger/manual/{workflow_id}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 并行工作流触发成功")
        execution_id = result.get("executionId")
        print(f"   执行ID: {execution_id}")
        
        # 等待执行完成
        print("等待并行执行完成...")
        time.sleep(15)  # 并行执行可能需要更长时间
        
        # 获取执行结果
        history_response = requests.get(f"{BASE_URL}/workflow/{workflow_id}/history")
        if history_response.status_code == 200:
            executions = history_response.json()
            if executions:
                latest_execution = executions[0]
                print("✅ 并行执行结果:")
                print(f"   状态: {latest_execution.get('status', 'N/A')}")
                print(f"   开始时间: {latest_execution.get('startedAt', 'N/A')}")
                print(f"   结束时间: {latest_execution.get('finishedAt', 'N/A')}")
                
                # 显示节点执行结果
                node_executions = latest_execution.get("nodeExecutions", [])
                if node_executions:
                    print(f"\n📊 节点执行详情 ({len(node_executions)}个节点):")
                    for node_exec in node_executions:
                        print(f"   节点: {node_exec.get('nodeName', 'N/A')}")
                        print(f"     类型: {node_exec.get('nodeType', 'N/A')}")
                        print(f"     状态: {node_exec.get('status', 'N/A')}")
                        print(f"     耗时: {node_exec.get('duration', 'N/A')}")
                return True
        
        return False
    else:
        print(f"❌ 并行工作流触发失败: {response.status_code}")
        return False

def main():
    print("🚀 开始多分支并行执行测试...")
    print("="*60)
    
    try:
        # 1. 创建并行工作流
        workflow_id = create_parallel_workflow()
        if not workflow_id:
            return
        
        # 2. 设置并行工作流
        if setup_parallel_workflow(workflow_id):
            # 3. 测试并行执行
            test_parallel_execution(workflow_id)
        
        print("\n" + "="*60)
        print("✅ 多分支并行执行测试完成！")
        print(f"📝 测试工作流ID: {workflow_id}")
        print(f"🔗 前端地址: http://localhost:5173")
        print("🎯 特性测试:")
        print("   ✅ 多分支并行执行")
        print("   ✅ 条件判断分支")
        print("   ✅ 循环和数据转换")
        print("   ✅ 多种脚本类型")
        print("   ✅ 结果汇聚")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

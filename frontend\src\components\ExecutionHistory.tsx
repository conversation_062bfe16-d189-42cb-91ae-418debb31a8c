import React, { useState, useEffect } from 'react';
import <PERSON>g<PERSON>iewer from './LogViewer';

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'Running' | 'Success' | 'Error' | 'Cancelled' | 'Timeout';
  startTime: string;
  endTime?: string;
  duration?: number;
  triggeredBy: string;
  inputData: Record<string, unknown>;
  outputData?: Record<string, unknown>;
  errorMessage?: string;
  nodeExecutions: NodeExecution[];
}

export interface NodeExecution {
  id: string;
  nodeId: string;
  nodeName: string;
  nodeType: string;
  status: 'Running' | 'Success' | 'Error' | 'Skipped';
  startTime: string;
  endTime?: string;
  duration?: number;
  inputData: Record<string, unknown>;
  outputData?: Record<string, unknown>;
  errorMessage?: string;
}

interface ExecutionHistoryProps {
  workflowId: string;
}

const ExecutionHistory: React.FC<ExecutionHistoryProps> = ({ workflowId }) => {
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [selectedExecution, setSelectedExecution] = useState<WorkflowExecution | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showLogs, setShowLogs] = useState(false);

  const fetchExecutions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/workflow/${workflowId}/history`);
      if (!response.ok) {
        throw new Error(`获取执行历史失败: ${response.statusText}`);
      }
      
      const data = await response.json();
      setExecutions(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取执行历史失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExecutions();
  }, [workflowId]);

  useEffect(() => {
    // 每5秒自动刷新执行历史
    const interval = setInterval(fetchExecutions, 5000);
    return () => clearInterval(interval);
  }, [workflowId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Running': return 'text-blue-600 bg-blue-100';
      case 'Success': return 'text-green-600 bg-green-100';
      case 'Error': return 'text-red-600 bg-red-100';
      case 'Cancelled': return 'text-gray-600 bg-gray-100';
      case 'Timeout': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Running': return '🔄';
      case 'Success': return '✅';
      case 'Error': return '❌';
      case 'Cancelled': return '⏹️';
      case 'Timeout': return '⏰';
      default: return '❓';
    }
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return '-';
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  if (showLogs && selectedExecution) {
    return (
      <div className="h-full flex flex-col">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-800">
              执行日志 - {selectedExecution.id.substring(0, 8)}...
            </h3>
            <button
              onClick={() => setShowLogs(false)}
              className="px-4 py-2 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600"
            >
              返回执行历史
            </button>
          </div>
        </div>
        <div className="flex-1">
          <LogViewer
            workflowId={workflowId}
            executionId={selectedExecution.id}
            autoRefresh={selectedExecution.status === 'Running'}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800 flex items-center">
            <span className="mr-2">📊</span>
            执行历史
          </h3>
          <button
            onClick={fetchExecutions}
            disabled={loading}
            className="px-4 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? '刷新中...' : '刷新'}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border-b border-red-200">
          <p className="text-red-600 text-sm">❌ {error}</p>
        </div>
      )}

      {/* Execution List */}
      <div className="flex-1 overflow-y-auto">
        {loading && executions.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            加载执行历史中...
          </div>
        ) : executions.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <div className="text-4xl mb-4">📋</div>
            <p>暂无执行历史</p>
            <p className="text-sm text-gray-400 mt-2">运行工作流后将显示执行记录</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {executions.map((execution) => (
              <div
                key={execution.id}
                className="p-4 hover:bg-gray-50 cursor-pointer"
                onClick={() => setSelectedExecution(execution)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{getStatusIcon(execution.status)}</span>
                    <div>
                      <div className="font-medium text-gray-900">
                        执行 {execution.id.substring(0, 8)}...
                      </div>
                      <div className="text-sm text-gray-500">
                        触发方式: {execution.triggeredBy}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`px-2 py-1 text-xs rounded-full font-medium ${getStatusColor(execution.status)}`}>
                      {execution.status}
                    </span>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatDuration(execution.duration)}
                    </div>
                  </div>
                </div>

                <div className="text-sm text-gray-600 mb-2">
                  开始时间: {formatTimestamp(execution.startTime)}
                  {execution.endTime && (
                    <span className="ml-4">
                      结束时间: {formatTimestamp(execution.endTime)}
                    </span>
                  )}
                </div>

                {execution.errorMessage && (
                  <div className="text-sm text-red-600 bg-red-50 p-2 rounded mb-2">
                    错误: {execution.errorMessage}
                  </div>
                )}

                {/* Node Executions */}
                {execution.nodeExecutions.length > 0 && (
                  <div className="mt-3">
                    <div className="text-sm font-medium text-gray-700 mb-2">节点执行情况:</div>
                    <div className="space-y-1">
                      {execution.nodeExecutions.map((nodeExec) => (
                        <div
                          key={nodeExec.id}
                          className="flex items-center justify-between text-sm bg-gray-50 p-2 rounded"
                        >
                          <div className="flex items-center space-x-2">
                            <span>{getStatusIcon(nodeExec.status)}</span>
                            <span className="font-medium">{nodeExec.nodeName}</span>
                            <span className="text-gray-500">({nodeExec.nodeType})</span>
                          </div>
                          <div className="text-xs text-gray-500">
                            {formatDuration(nodeExec.duration)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="mt-3 flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedExecution(execution);
                      setShowLogs(true);
                    }}
                    className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    查看日志
                  </button>
                  {execution.status === 'Running' && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: 实现取消执行功能
                      }}
                      className="px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
                    >
                      取消执行
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ExecutionHistory;

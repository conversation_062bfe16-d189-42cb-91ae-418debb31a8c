using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Engine;

/// <summary>
/// 工作流执行引擎
/// </summary>
public class WorkflowEngine : IWorkflowEngine
{
    private readonly IWorkflowService _workflowService;
    private readonly INodeRegistry _nodeRegistry;
    private readonly ILogger<WorkflowEngine> _logger;
    private readonly ConcurrentDictionary<Guid, CancellationTokenSource> _runningExecutions = new();

    public WorkflowEngine(
        IWorkflowService workflowService,
        INodeRegistry nodeRegistry,
        ILogger<WorkflowEngine> logger)
    {
        _workflowService = workflowService;
        _nodeRegistry = nodeRegistry;
        _logger = logger;
    }

    public async Task<WorkflowExecution> ExecuteAsync(
        Guid workflowId, 
        Dictionary<string, object>? inputData = null,
        string? triggeredBy = null,
        CancellationToken cancellationToken = default)
    {
        var workflow = await _workflowService.GetWorkflowAsync(workflowId);
        if (workflow == null)
        {
            throw new ArgumentException($"Workflow not found: {workflowId}");
        }

        var execution = new WorkflowExecution
        {
            WorkflowId = workflowId,
            InputData = inputData ?? new Dictionary<string, object>(),
            TriggeredBy = triggeredBy,
            Status = ExecutionStatus.Running
        };

        var executionCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        _runningExecutions[execution.Id] = executionCts;

        try
        {
            _logger.LogInformation("Starting workflow execution: {ExecutionId} for workflow: {WorkflowId}", 
                execution.Id, workflowId);

            var context = new ExecutionContext
            {
                ExecutionId = execution.Id,
                WorkflowId = workflowId,
                Variables = new Dictionary<string, object>(workflow.Settings.Variables),
                GlobalData = new Dictionary<string, object>(execution.InputData),
                CancellationToken = executionCts.Token
            };

            // 查找起始节点（没有输入连接的节点）
            var startNodes = FindStartNodes(workflow);
            if (!startNodes.Any())
            {
                throw new InvalidOperationException("No start nodes found in workflow");
            }

            // 执行工作流
            await ExecuteNodesAsync(workflow, startNodes, context, execution);

            execution.Status = ExecutionStatus.Success;
            execution.FinishedAt = DateTime.UtcNow;

            _logger.LogInformation("Workflow execution completed successfully: {ExecutionId}", execution.Id);
        }
        catch (OperationCanceledException)
        {
            execution.Status = ExecutionStatus.Cancelled;
            execution.FinishedAt = DateTime.UtcNow;
            _logger.LogInformation("Workflow execution cancelled: {ExecutionId}", execution.Id);
        }
        catch (Exception ex)
        {
            execution.Status = ExecutionStatus.Error;
            execution.Error = ex.Message;
            execution.FinishedAt = DateTime.UtcNow;
            _logger.LogError(ex, "Workflow execution failed: {ExecutionId}", execution.Id);
        }
        finally
        {
            _runningExecutions.TryRemove(execution.Id, out _);
            executionCts.Dispose();
        }

        return execution;
    }

    public async Task StopExecutionAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        if (_runningExecutions.TryGetValue(executionId, out var cts))
        {
            cts.Cancel();
            _logger.LogInformation("Stopping workflow execution: {ExecutionId}", executionId);
        }
        
        await Task.CompletedTask;
    }

    public async Task<WorkflowExecution?> GetExecutionAsync(Guid executionId)
    {
        // TODO: 从数据库获取执行记录
        await Task.CompletedTask;
        return null;
    }

    public async Task<IEnumerable<WorkflowExecution>> GetExecutionHistoryAsync(
        Guid workflowId, 
        int page = 1, 
        int pageSize = 50)
    {
        // TODO: 从数据库获取执行历史
        await Task.CompletedTask;
        return Enumerable.Empty<WorkflowExecution>();
    }

    private List<WorkflowNode> FindStartNodes(Workflow workflow)
    {
        var nodesWithInputs = workflow.Connections.Select(c => c.TargetNodeId).ToHashSet();
        return workflow.Nodes.Where(n => !nodesWithInputs.Contains(n.Id) && !n.IsDisabled).ToList();
    }

    private async Task ExecuteNodesAsync(
        Workflow workflow, 
        IEnumerable<WorkflowNode> nodes, 
        Models.ExecutionContext context,
        WorkflowExecution execution)
    {
        var nodesToExecute = new Queue<WorkflowNode>(nodes);
        var executedNodes = new HashSet<Guid>();

        while (nodesToExecute.Count > 0)
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            var node = nodesToExecute.Dequeue();
            
            if (executedNodes.Contains(node.Id))
                continue;

            await ExecuteNodeAsync(workflow, node, context, execution);
            executedNodes.Add(node.Id);

            // 查找下一个要执行的节点
            var nextNodes = GetNextNodes(workflow, node.Id);
            foreach (var nextNode in nextNodes)
            {
                if (!executedNodes.Contains(nextNode.Id) && !nextNode.IsDisabled)
                {
                    nodesToExecute.Enqueue(nextNode);
                }
            }
        }
    }

    private async Task ExecuteNodeAsync(
        Workflow workflow,
        WorkflowNode workflowNode,
        Models.ExecutionContext context,
        WorkflowExecution execution)
    {
        var nodeExecution = new NodeExecution
        {
            ExecutionId = execution.Id,
            NodeId = workflowNode.Id,
            NodeName = workflowNode.Name,
            NodeType = workflowNode.Type,
            Status = ExecutionStatus.Running
        };

        execution.NodeExecutions.Add(nodeExecution);

        try
        {
            var node = _nodeRegistry.GetNode(workflowNode.Type);
            if (node == null)
            {
                throw new InvalidOperationException($"Node type not found: {workflowNode.Type}");
            }

            _logger.LogDebug("Executing node: {NodeId} ({NodeType})", workflowNode.Id, workflowNode.Type);

            var result = await node.ExecuteAsync(workflowNode, context, context.CancellationToken);

            nodeExecution.Status = result.Success ? ExecutionStatus.Success : ExecutionStatus.Error;
            nodeExecution.OutputData = result.OutputData;
            nodeExecution.Error = result.Error;
            nodeExecution.FinishedAt = DateTime.UtcNow;

            // 保存节点输出数据供后续节点使用
            context.PreviousNodeOutputs[workflowNode.Id] = result.OutputData;

            if (!result.Success)
            {
                throw new Exception($"Node execution failed: {result.Error}");
            }
        }
        catch (Exception ex)
        {
            nodeExecution.Status = ExecutionStatus.Error;
            nodeExecution.Error = ex.Message;
            nodeExecution.FinishedAt = DateTime.UtcNow;
            throw;
        }
    }

    private List<WorkflowNode> GetNextNodes(Workflow workflow, Guid nodeId)
    {
        var nextNodeIds = workflow.Connections
            .Where(c => c.SourceNodeId == nodeId)
            .Select(c => c.TargetNodeId)
            .ToList();

        return workflow.Nodes
            .Where(n => nextNodeIds.Contains(n.Id))
            .ToList();
    }
}

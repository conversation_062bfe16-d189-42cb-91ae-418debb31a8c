using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using FlowCustom.Data;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Engine;

/// <summary>
/// 工作流执行引擎 - FlowCustom系统的核心执行组件
///
/// 主要职责：
/// 1. 工作流的并行执行调度 - 采用层级并行执行策略，同层节点并行，不同层按依赖顺序执行
/// 2. 节点依赖关系分析 - 自动分析节点间的连接关系，确定正确的执行顺序
/// 3. 执行状态监控 - 实时监控每个节点的执行状态，提供详细的执行日志
/// 4. 错误处理和恢复 - 完善的异常处理机制，支持执行取消和错误恢复
/// 5. 数据传递管理 - 管理节点间的数据流转和全局上下文
///
/// 执行流程：
/// 1. 查找起始节点（没有输入连接的节点）
/// 2. 按层级并行执行：同一层级的节点使用Task.WhenAll并行执行
/// 3. 每执行完一层，分析下一层可执行的节点（前置节点都已完成）
/// 4. 重复直到所有节点执行完成
///
/// 并行执行策略：
/// - 第一层：起始节点（通常是触发器节点）
/// - 第二层：直接连接到起始节点的节点
/// - 第N层：所有前置节点都已完成的节点
/// 这样既保证了执行顺序的正确性，又最大化了并行执行的效率
/// </summary>
public class WorkflowEngine : IWorkflowEngine
{
    private readonly IWorkflowService _workflowService;          // 工作流数据访问服务
    private readonly INodeRegistry _nodeRegistry;               // 节点注册表，管理所有可用的节点类型
    private readonly ILogger<WorkflowEngine> _logger;           // 日志记录器
    private readonly MonitoringService? _monitoringService;     // 性能监控服务（可选）
    private readonly ExecutionHistoryService _executionHistoryService;  // 执行历史记录服务

    // 正在运行的执行实例管理 - 用于支持执行取消功能
    // Key: ExecutionId, Value: CancellationTokenSource
    private readonly ConcurrentDictionary<Guid, CancellationTokenSource> _runningExecutions = new();

    public WorkflowEngine(
        IWorkflowService workflowService,
        INodeRegistry nodeRegistry,
        ILogger<WorkflowEngine> logger,
        ExecutionHistoryService executionHistoryService,
        MonitoringService? monitoringService = null)
    {
        _workflowService = workflowService;
        _nodeRegistry = nodeRegistry;
        _logger = logger;
        _executionHistoryService = executionHistoryService;
        _monitoringService = monitoringService;
    }

    /// <summary>
    /// 执行工作流的主入口方法
    ///
    /// 执行步骤：
    /// 1. 加载工作流定义和验证
    /// 2. 创建执行实例和上下文
    /// 3. 查找起始节点（没有输入连接的节点，通常是触发器）
    /// 4. 按层级并行执行所有节点
    /// 5. 记录执行结果和性能指标
    ///
    /// 并行执行原理：
    /// - 使用层级遍历算法，确保节点按正确的依赖顺序执行
    /// - 同一层级的节点使用Task.WhenAll实现真正的并行执行
    /// - 每层执行完成后，分析下一层可执行的节点
    /// </summary>
    /// <param name="workflowId">要执行的工作流ID</param>
    /// <param name="inputData">输入数据，将作为全局上下文传递给所有节点</param>
    /// <param name="triggeredBy">触发者标识，用于审计和日志记录</param>
    /// <param name="cancellationToken">取消令牌，支持外部取消执行</param>
    /// <returns>工作流执行结果，包含所有节点的执行状态和输出数据</returns>
    public async Task<WorkflowExecution> ExecuteAsync(
        Guid workflowId,
        Dictionary<string, object>? inputData = null,
        string? triggeredBy = null,
        CancellationToken cancellationToken = default)
    {
        // 1. 加载工作流定义
        var workflow = await _workflowService.GetWorkflowAsync(workflowId);
        if (workflow == null)
        {
            throw new ArgumentException($"Workflow not found: {workflowId}");
        }

        // 2. 创建执行实例 - 记录执行的基本信息
        var execution = new WorkflowExecution
        {
            WorkflowId = workflowId,
            InputData = inputData ?? new Dictionary<string, object>(),
            TriggeredBy = triggeredBy,
            Status = ExecutionStatus.Running  // 初始状态为运行中
        };

        // 3. 创建取消令牌源，支持执行过程中的取消操作
        var executionCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        _runningExecutions[execution.Id] = executionCts;  // 注册到运行中的执行列表

        try
        {
            _logger.LogInformation("开始执行工作流: {ExecutionId} for workflow: {WorkflowId}",
                execution.Id, workflowId);

            // 记录系统日志
            await _executionHistoryService.AddSystemLogAsync(
                "Info",
                $"开始执行工作流: {workflow.Name}",
                "WorkflowEngine",
                workflowId,
                null,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["workflowName"] = workflow.Name,
                    ["triggeredBy"] = triggeredBy ?? "unknown",
                    ["inputDataCount"] = execution.InputData.Count
                });

            // 开始监控
            if (_monitoringService != null)
            {
                await _monitoringService.StartMonitoringAsync(execution);
            }

            // 4. 创建执行上下文 - 包含执行过程中需要的所有数据和状态
            var context = new FlowCustom.Core.Models.ExecutionContext
            {
                ExecutionId = execution.Id,
                WorkflowId = workflowId,
                Variables = new Dictionary<string, object>(workflow.Settings.Variables),  // 工作流级别的变量
                GlobalData = new Dictionary<string, object>(execution.InputData),         // 全局数据，所有节点都可以访问
                CancellationToken = executionCts.Token  // 取消令牌，用于响应取消请求
            };

            // 5. 查找起始节点（没有输入连接的节点）
            // 起始节点通常是触发器节点，它们没有前置依赖，可以直接开始执行
            var startNodes = FindStartNodes(workflow);
            if (!startNodes.Any())
            {
                throw new InvalidOperationException("No start nodes found in workflow");
            }

            // 6. 开始并行执行工作流 - 这是核心的执行逻辑
            await ExecuteNodesAsync(workflow, startNodes, context, execution);

            execution.Status = ExecutionStatus.Success;
            execution.FinishedAt = DateTime.UtcNow;

            _logger.LogInformation("工作流执行成功完成: {ExecutionId}", execution.Id);

            // 记录成功日志
            await _executionHistoryService.AddSystemLogAsync(
                "Info",
                $"工作流执行成功: {workflow.Name}",
                "WorkflowEngine",
                workflowId,
                null,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["duration"] = (execution.FinishedAt - execution.StartedAt)?.TotalMilliseconds ?? 0,
                    ["nodeCount"] = execution.NodeExecutions.Count,
                    ["status"] = "Success"
                });

            // 完成监控
            if (_monitoringService != null)
            {
                await _monitoringService.CompleteMonitoringAsync(execution);
            }
        }
        catch (OperationCanceledException)
        {
            execution.Status = ExecutionStatus.Cancelled;
            execution.FinishedAt = DateTime.UtcNow;
            _logger.LogInformation("工作流执行已取消: {ExecutionId}", execution.Id);

            // 记录取消日志
            await _executionHistoryService.AddSystemLogAsync(
                "Warning",
                $"工作流执行已取消: {workflow.Name}",
                "WorkflowEngine",
                workflowId,
                null,
                execution.Id);
        }
        catch (Exception ex)
        {
            execution.Status = ExecutionStatus.Error;
            execution.Error = ex.Message;
            execution.FinishedAt = DateTime.UtcNow;
            _logger.LogError(ex, "工作流执行失败: {ExecutionId}", execution.Id);

            // 记录错误日志
            await _executionHistoryService.AddSystemLogAsync(
                "Error",
                $"工作流执行失败: {workflow.Name} - {ex.Message}",
                "WorkflowEngine",
                workflowId,
                null,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["exception"] = ex.GetType().Name,
                    ["stackTrace"] = ex.StackTrace ?? "",
                    ["innerException"] = ex.InnerException?.Message ?? ""
                });
        }
        finally
        {
            _runningExecutions.TryRemove(execution.Id, out _);
            executionCts.Dispose();
        }

        return execution;
    }

    public async Task StopExecutionAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        if (_runningExecutions.TryGetValue(executionId, out var cts))
        {
            cts.Cancel();
            _logger.LogInformation("停止工作流执行: {ExecutionId}", executionId);

            // 记录停止日志
            await _executionHistoryService.AddSystemLogAsync(
                "Warning",
                $"手动停止工作流执行: {executionId}",
                "WorkflowEngine",
                null,
                null,
                executionId);
        }

        await Task.CompletedTask;
    }

    public async Task CancelExecutionAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        await StopExecutionAsync(executionId, cancellationToken);
    }

    public async Task<WorkflowExecution?> GetExecutionAsync(Guid executionId)
    {
        // TODO: 从数据库获取执行记录
        await Task.CompletedTask;
        return null;
    }

    public async Task<IEnumerable<WorkflowExecution>> GetExecutionHistoryAsync(
        Guid workflowId, 
        int page = 1, 
        int pageSize = 50)
    {
        // TODO: 从数据库获取执行历史
        await Task.CompletedTask;
        return Enumerable.Empty<WorkflowExecution>();
    }

    private List<WorkflowNode> FindStartNodes(Workflow workflow)
    {
        var nodesWithInputs = workflow.Connections.Select(c => c.TargetNodeId).ToHashSet();
        return workflow.Nodes.Where(n => !nodesWithInputs.Contains(n.Id) && !n.IsDisabled).ToList();
    }

    /// <summary>
    /// 并行执行节点的核心方法 - 实现层级并行执行策略
    ///
    /// 算法原理：
    /// 1. 层级遍历：将工作流看作有向无环图(DAG)，按层级遍历执行
    /// 2. 并行执行：同一层级的节点没有相互依赖，可以并行执行
    /// 3. 依赖检查：每层执行完成后，检查哪些节点的前置依赖已满足
    ///
    /// 执行流程：
    /// 第1层：起始节点（触发器）
    /// 第2层：直接连接到起始节点的节点
    /// 第3层：前置节点都在第1、2层且已完成的节点
    /// ...依此类推
    ///
    /// 并行优势：
    /// - 最大化利用系统资源
    /// - 减少总执行时间
    /// - 保证执行顺序的正确性
    /// </summary>
    /// <param name="workflow">工作流定义，包含所有节点和连接信息</param>
    /// <param name="nodes">起始节点列表</param>
    /// <param name="context">执行上下文，包含全局数据和状态</param>
    /// <param name="execution">执行实例，用于记录执行结果</param>
    private async Task ExecuteNodesAsync(
        Workflow workflow,
        IEnumerable<WorkflowNode> nodes,
        FlowCustom.Core.Models.ExecutionContext context,
        WorkflowExecution execution)
    {
        var executedNodes = new HashSet<Guid>();        // 已执行完成的节点ID集合
        var currentLevel = new List<WorkflowNode>(nodes); // 当前层级要执行的节点

        // 层级循环：每次循环执行一个层级的所有节点
        while (currentLevel.Any())
        {
            // 检查是否收到取消请求
            context.CancellationToken.ThrowIfCancellationRequested();

            _logger.LogDebug("开始执行第{Level}层，包含{Count}个节点",
                executedNodes.Count + 1, currentLevel.Count);

            // 并行执行当前层级的所有节点 - 这是并行执行的核心
            // Task.WhenAll确保所有节点同时开始执行，并等待全部完成
            var tasks = currentLevel.Select(node =>
                ExecuteNodeAsync(workflow, node, context, execution)).ToArray();
            await Task.WhenAll(tasks);

            // 标记已执行的节点
            foreach (var node in currentLevel)
            {
                executedNodes.Add(node.Id);
            }

            // 查找下一层级的节点（所有前置节点都已执行的节点）
            var nextLevel = new List<WorkflowNode>();
            var remainingNodes = workflow.Nodes
                .Where(n => !executedNodes.Contains(n.Id) && !n.IsDisabled)
                .ToList();

            foreach (var node in remainingNodes)
            {
                // 检查该节点的所有前置节点是否都已执行
                var predecessors = GetPredecessorNodes(workflow, node.Id);
                if (predecessors.All(p => executedNodes.Contains(p.Id)))
                {
                    nextLevel.Add(node);
                }
            }

            currentLevel = nextLevel;

            _logger.LogDebug("第{Level}层执行完成，下一层包含{Count}个节点",
                executedNodes.Count, nextLevel.Count);
        }
    }

    /// <summary>
    /// 执行单个节点的核心方法 - 工作流执行的最小单元
    ///
    /// 节点执行的完整生命周期：
    /// 1. 创建执行记录：初始化NodeExecution对象，记录开始时间和基本信息
    /// 2. 节点查找：从节点注册表中获取对应类型的节点实现
    /// 3. 执行准备：记录开始执行的系统日志，包含节点参数等详细信息
    /// 4. 实际执行：调用节点的ExecuteAsync方法，传入工作流节点定义和执行上下文
    /// 5. 结果处理：根据执行结果更新节点状态，保存输出数据
    /// 6. 数据传递：将节点输出保存到上下文中，供后续节点使用
    /// 7. 日志记录：记录执行成功/失败的详细日志
    /// 8. 异常处理：捕获并记录所有可能的异常情况
    ///
    /// 错误处理策略：
    /// - 节点类型不存在：抛出InvalidOperationException
    /// - 节点执行失败：记录错误信息，抛出Exception停止工作流
    /// - 系统异常：记录完整的异常信息和堆栈跟踪
    ///
    /// 数据流设计：
    /// - 输入数据：从context.PreviousNodeOutputs获取前置节点的输出
    /// - 输出数据：保存到context.PreviousNodeOutputs[nodeId]供后续节点使用
    /// - 全局数据：context.GlobalData在整个工作流中共享
    ///
    /// 性能监控：
    /// - 记录节点执行开始和结束时间
    /// - 计算执行耗时（毫秒级精度）
    /// - 统计输出数据的键数量
    /// </summary>
    /// <param name="workflow">工作流定义，包含所有节点和连接信息</param>
    /// <param name="workflowNode">要执行的节点定义，包含类型、参数等</param>
    /// <param name="context">执行上下文，包含全局数据和前置节点输出</param>
    /// <param name="execution">工作流执行实例，用于记录执行历史</param>
    private async Task ExecuteNodeAsync(
        Workflow workflow,
        WorkflowNode workflowNode,
        FlowCustom.Core.Models.ExecutionContext context,
        WorkflowExecution execution)
    {
        var nodeExecution = new NodeExecution
        {
            ExecutionId = execution.Id,
            NodeId = workflowNode.Id,
            NodeName = workflowNode.Name,
            NodeType = workflowNode.Type,
            Status = ExecutionStatus.Running
        };

        execution.NodeExecutions.Add(nodeExecution);

        try
        {
            var node = _nodeRegistry.GetNode(workflowNode.Type);
            if (node == null)
            {
                throw new InvalidOperationException($"Node type not found: {workflowNode.Type}");
            }

            _logger.LogDebug("开始执行节点: {NodeId} ({NodeType})", workflowNode.Id, workflowNode.Type);

            // 记录节点开始执行日志
            await _executionHistoryService.AddSystemLogAsync(
                "Info",
                $"开始执行节点: {workflowNode.Name} ({workflowNode.Type})",
                "NodeExecution",
                context.WorkflowId,
                workflowNode.Id,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["nodeName"] = workflowNode.Name,
                    ["nodeType"] = workflowNode.Type,
                    ["parameters"] = workflowNode.Parameters
                });

            var result = await node.ExecuteAsync(workflowNode, context, context.CancellationToken);

            nodeExecution.Status = result.Success ? ExecutionStatus.Success : ExecutionStatus.Error;
            nodeExecution.OutputData = result.OutputData;
            nodeExecution.Error = result.Error;
            nodeExecution.FinishedAt = DateTime.UtcNow;

            // 保存节点输出数据供后续节点使用
            context.PreviousNodeOutputs[workflowNode.Id] = result.OutputData;

            if (result.Success)
            {
                // 记录节点成功日志
                await _executionHistoryService.AddSystemLogAsync(
                    "Info",
                    $"节点执行成功: {workflowNode.Name}",
                    "NodeExecution",
                    context.WorkflowId,
                    workflowNode.Id,
                    execution.Id,
                    new Dictionary<string, object>
                    {
                        ["duration"] = (nodeExecution.FinishedAt - nodeExecution.StartedAt)?.TotalMilliseconds ?? 0,
                        ["outputDataKeys"] = result.OutputData.Keys.ToArray()
                    });
            }
            else
            {
                // 记录节点失败日志
                await _executionHistoryService.AddSystemLogAsync(
                    "Error",
                    $"节点执行失败: {workflowNode.Name} - {result.Error}",
                    "NodeExecution",
                    context.WorkflowId,
                    workflowNode.Id,
                    execution.Id,
                    new Dictionary<string, object>
                    {
                        ["error"] = result.Error ?? "",
                        ["duration"] = (nodeExecution.FinishedAt - nodeExecution.StartedAt)?.TotalMilliseconds ?? 0
                    });

                throw new Exception($"Node execution failed: {result.Error}");
            }
        }
        catch (Exception ex)
        {
            nodeExecution.Status = ExecutionStatus.Error;
            nodeExecution.Error = ex.Message;
            nodeExecution.FinishedAt = DateTime.UtcNow;

            // 记录节点异常日志
            await _executionHistoryService.AddSystemLogAsync(
                "Error",
                $"节点执行异常: {workflowNode.Name} - {ex.Message}",
                "NodeExecution",
                context.WorkflowId,
                workflowNode.Id,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["exception"] = ex.GetType().Name,
                    ["stackTrace"] = ex.StackTrace ?? "",
                    ["duration"] = (nodeExecution.FinishedAt - nodeExecution.StartedAt)?.TotalMilliseconds ?? 0
                });

            throw;
        }
    }

    private List<WorkflowNode> GetNextNodes(Workflow workflow, Guid nodeId)
    {
        var nextNodeIds = workflow.Connections
            .Where(c => c.SourceNodeId == nodeId)
            .Select(c => c.TargetNodeId)
            .ToList();

        return workflow.Nodes
            .Where(n => nextNodeIds.Contains(n.Id))
            .ToList();
    }

    private List<WorkflowNode> GetPredecessorNodes(Workflow workflow, Guid nodeId)
    {
        var predecessorNodeIds = workflow.Connections
            .Where(c => c.TargetNodeId == nodeId)
            .Select(c => c.SourceNodeId)
            .ToList();

        return workflow.Nodes
            .Where(n => predecessorNodeIds.Contains(n.Id))
            .ToList();
    }
}

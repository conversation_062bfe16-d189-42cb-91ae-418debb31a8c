using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using FlowCustom.Data;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustom.Engine;

/// <summary>
/// 工作流执行引擎
/// </summary>
public class WorkflowEngine : IWorkflowEngine
{
    private readonly IWorkflowService _workflowService;
    private readonly INodeRegistry _nodeRegistry;
    private readonly ILogger<WorkflowEngine> _logger;
    private readonly MonitoringService? _monitoringService;
    private readonly ExecutionHistoryService _executionHistoryService;
    private readonly ConcurrentDictionary<Guid, CancellationTokenSource> _runningExecutions = new();

    public WorkflowEngine(
        IWorkflowService workflowService,
        INodeRegistry nodeRegistry,
        ILogger<WorkflowEngine> logger,
        ExecutionHistoryService executionHistoryService,
        MonitoringService? monitoringService = null)
    {
        _workflowService = workflowService;
        _nodeRegistry = nodeRegistry;
        _logger = logger;
        _executionHistoryService = executionHistoryService;
        _monitoringService = monitoringService;
    }

    public async Task<WorkflowExecution> ExecuteAsync(
        Guid workflowId, 
        Dictionary<string, object>? inputData = null,
        string? triggeredBy = null,
        CancellationToken cancellationToken = default)
    {
        var workflow = await _workflowService.GetWorkflowAsync(workflowId);
        if (workflow == null)
        {
            throw new ArgumentException($"Workflow not found: {workflowId}");
        }

        var execution = new WorkflowExecution
        {
            WorkflowId = workflowId,
            InputData = inputData ?? new Dictionary<string, object>(),
            TriggeredBy = triggeredBy,
            Status = ExecutionStatus.Running
        };

        var executionCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        _runningExecutions[execution.Id] = executionCts;

        try
        {
            _logger.LogInformation("开始执行工作流: {ExecutionId} for workflow: {WorkflowId}",
                execution.Id, workflowId);

            // 记录系统日志
            await _executionHistoryService.AddSystemLogAsync(
                "Info",
                $"开始执行工作流: {workflow.Name}",
                "WorkflowEngine",
                workflowId,
                null,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["workflowName"] = workflow.Name,
                    ["triggeredBy"] = triggeredBy ?? "unknown",
                    ["inputDataCount"] = execution.InputData.Count
                });

            // 开始监控
            if (_monitoringService != null)
            {
                await _monitoringService.StartMonitoringAsync(execution);
            }

            var context = new FlowCustom.Core.Models.ExecutionContext
            {
                ExecutionId = execution.Id,
                WorkflowId = workflowId,
                Variables = new Dictionary<string, object>(workflow.Settings.Variables),
                GlobalData = new Dictionary<string, object>(execution.InputData),
                CancellationToken = executionCts.Token
            };

            // 查找起始节点（没有输入连接的节点）
            var startNodes = FindStartNodes(workflow);
            if (!startNodes.Any())
            {
                throw new InvalidOperationException("No start nodes found in workflow");
            }

            // 执行工作流
            await ExecuteNodesAsync(workflow, startNodes, context, execution);

            execution.Status = ExecutionStatus.Success;
            execution.FinishedAt = DateTime.UtcNow;

            _logger.LogInformation("工作流执行成功完成: {ExecutionId}", execution.Id);

            // 记录成功日志
            await _executionHistoryService.AddSystemLogAsync(
                "Info",
                $"工作流执行成功: {workflow.Name}",
                "WorkflowEngine",
                workflowId,
                null,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["duration"] = (execution.FinishedAt - execution.StartedAt)?.TotalMilliseconds ?? 0,
                    ["nodeCount"] = execution.NodeExecutions.Count,
                    ["status"] = "Success"
                });

            // 完成监控
            if (_monitoringService != null)
            {
                await _monitoringService.CompleteMonitoringAsync(execution);
            }
        }
        catch (OperationCanceledException)
        {
            execution.Status = ExecutionStatus.Cancelled;
            execution.FinishedAt = DateTime.UtcNow;
            _logger.LogInformation("工作流执行已取消: {ExecutionId}", execution.Id);

            // 记录取消日志
            await _executionHistoryService.AddSystemLogAsync(
                "Warning",
                $"工作流执行已取消: {workflow.Name}",
                "WorkflowEngine",
                workflowId,
                null,
                execution.Id);
        }
        catch (Exception ex)
        {
            execution.Status = ExecutionStatus.Error;
            execution.Error = ex.Message;
            execution.FinishedAt = DateTime.UtcNow;
            _logger.LogError(ex, "工作流执行失败: {ExecutionId}", execution.Id);

            // 记录错误日志
            await _executionHistoryService.AddSystemLogAsync(
                "Error",
                $"工作流执行失败: {workflow.Name} - {ex.Message}",
                "WorkflowEngine",
                workflowId,
                null,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["exception"] = ex.GetType().Name,
                    ["stackTrace"] = ex.StackTrace ?? "",
                    ["innerException"] = ex.InnerException?.Message ?? ""
                });
        }
        finally
        {
            _runningExecutions.TryRemove(execution.Id, out _);
            executionCts.Dispose();
        }

        return execution;
    }

    public async Task StopExecutionAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        if (_runningExecutions.TryGetValue(executionId, out var cts))
        {
            cts.Cancel();
            _logger.LogInformation("停止工作流执行: {ExecutionId}", executionId);

            // 记录停止日志
            await _executionHistoryService.AddSystemLogAsync(
                "Warning",
                $"手动停止工作流执行: {executionId}",
                "WorkflowEngine",
                null,
                null,
                executionId);
        }

        await Task.CompletedTask;
    }

    public async Task CancelExecutionAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        await StopExecutionAsync(executionId, cancellationToken);
    }

    public async Task<WorkflowExecution?> GetExecutionAsync(Guid executionId)
    {
        // TODO: 从数据库获取执行记录
        await Task.CompletedTask;
        return null;
    }

    public async Task<IEnumerable<WorkflowExecution>> GetExecutionHistoryAsync(
        Guid workflowId, 
        int page = 1, 
        int pageSize = 50)
    {
        // TODO: 从数据库获取执行历史
        await Task.CompletedTask;
        return Enumerable.Empty<WorkflowExecution>();
    }

    private List<WorkflowNode> FindStartNodes(Workflow workflow)
    {
        var nodesWithInputs = workflow.Connections.Select(c => c.TargetNodeId).ToHashSet();
        return workflow.Nodes.Where(n => !nodesWithInputs.Contains(n.Id) && !n.IsDisabled).ToList();
    }

    private async Task ExecuteNodesAsync(
        Workflow workflow, 
        IEnumerable<WorkflowNode> nodes, 
        FlowCustom.Core.Models.ExecutionContext context,
        WorkflowExecution execution)
    {
        var nodesToExecute = new Queue<WorkflowNode>(nodes);
        var executedNodes = new HashSet<Guid>();

        while (nodesToExecute.Count > 0)
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            var node = nodesToExecute.Dequeue();
            
            if (executedNodes.Contains(node.Id))
                continue;

            await ExecuteNodeAsync(workflow, node, context, execution);
            executedNodes.Add(node.Id);

            // 查找下一个要执行的节点
            var nextNodes = GetNextNodes(workflow, node.Id);
            foreach (var nextNode in nextNodes)
            {
                if (!executedNodes.Contains(nextNode.Id) && !nextNode.IsDisabled)
                {
                    nodesToExecute.Enqueue(nextNode);
                }
            }
        }
    }

    private async Task ExecuteNodeAsync(
        Workflow workflow,
        WorkflowNode workflowNode,
        FlowCustom.Core.Models.ExecutionContext context,
        WorkflowExecution execution)
    {
        var nodeExecution = new NodeExecution
        {
            ExecutionId = execution.Id,
            NodeId = workflowNode.Id,
            NodeName = workflowNode.Name,
            NodeType = workflowNode.Type,
            Status = ExecutionStatus.Running
        };

        execution.NodeExecutions.Add(nodeExecution);

        try
        {
            var node = _nodeRegistry.GetNode(workflowNode.Type);
            if (node == null)
            {
                throw new InvalidOperationException($"Node type not found: {workflowNode.Type}");
            }

            _logger.LogDebug("开始执行节点: {NodeId} ({NodeType})", workflowNode.Id, workflowNode.Type);

            // 记录节点开始执行日志
            await _executionHistoryService.AddSystemLogAsync(
                "Info",
                $"开始执行节点: {workflowNode.Name} ({workflowNode.Type})",
                "NodeExecution",
                context.WorkflowId,
                workflowNode.Id,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["nodeName"] = workflowNode.Name,
                    ["nodeType"] = workflowNode.Type,
                    ["parameters"] = workflowNode.Parameters
                });

            var result = await node.ExecuteAsync(workflowNode, context, context.CancellationToken);

            nodeExecution.Status = result.Success ? ExecutionStatus.Success : ExecutionStatus.Error;
            nodeExecution.OutputData = result.OutputData;
            nodeExecution.Error = result.Error;
            nodeExecution.FinishedAt = DateTime.UtcNow;

            // 保存节点输出数据供后续节点使用
            context.PreviousNodeOutputs[workflowNode.Id] = result.OutputData;

            if (result.Success)
            {
                // 记录节点成功日志
                await _executionHistoryService.AddSystemLogAsync(
                    "Info",
                    $"节点执行成功: {workflowNode.Name}",
                    "NodeExecution",
                    context.WorkflowId,
                    workflowNode.Id,
                    execution.Id,
                    new Dictionary<string, object>
                    {
                        ["duration"] = (nodeExecution.FinishedAt - nodeExecution.StartedAt)?.TotalMilliseconds ?? 0,
                        ["outputDataKeys"] = result.OutputData.Keys.ToArray()
                    });
            }
            else
            {
                // 记录节点失败日志
                await _executionHistoryService.AddSystemLogAsync(
                    "Error",
                    $"节点执行失败: {workflowNode.Name} - {result.Error}",
                    "NodeExecution",
                    context.WorkflowId,
                    workflowNode.Id,
                    execution.Id,
                    new Dictionary<string, object>
                    {
                        ["error"] = result.Error ?? "",
                        ["duration"] = (nodeExecution.FinishedAt - nodeExecution.StartedAt)?.TotalMilliseconds ?? 0
                    });

                throw new Exception($"Node execution failed: {result.Error}");
            }
        }
        catch (Exception ex)
        {
            nodeExecution.Status = ExecutionStatus.Error;
            nodeExecution.Error = ex.Message;
            nodeExecution.FinishedAt = DateTime.UtcNow;

            // 记录节点异常日志
            await _executionHistoryService.AddSystemLogAsync(
                "Error",
                $"节点执行异常: {workflowNode.Name} - {ex.Message}",
                "NodeExecution",
                context.WorkflowId,
                workflowNode.Id,
                execution.Id,
                new Dictionary<string, object>
                {
                    ["exception"] = ex.GetType().Name,
                    ["stackTrace"] = ex.StackTrace ?? "",
                    ["duration"] = (nodeExecution.FinishedAt - nodeExecution.StartedAt)?.TotalMilliseconds ?? 0
                });

            throw;
        }
    }

    private List<WorkflowNode> GetNextNodes(Workflow workflow, Guid nodeId)
    {
        var nextNodeIds = workflow.Connections
            .Where(c => c.SourceNodeId == nodeId)
            .Select(c => c.TargetNodeId)
            .ToList();

        return workflow.Nodes
            .Where(n => nextNodeIds.Contains(n.Id))
            .ToList();
    }
}

using FlowCustom.Data;
using FlowCustom.PluginHost;
using FlowCustom.Engine;
using FlowCustom.Core.Interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;

// 设置控制台编码为UTF-8
Console.OutputEncoding = Encoding.UTF8;
Console.InputEncoding = Encoding.UTF8;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        options.JsonSerializerOptions.WriteIndented = true;
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    });

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { 
        Title = "FlowCustom API", 
        Version = "v1",
        Description = "插件化工作流自动化平台 API"
    });
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 注册插件管理器
var pluginDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plugins");
builder.Services.AddSingleton<IPluginManager>(serviceProvider =>
{
    var logger = serviceProvider.GetRequiredService<ILogger<PluginManager>>();
    return new PluginManager(logger, serviceProvider, pluginDirectory);
});

// 注册新的执行引擎
builder.Services.AddSingleton<IWorkflowEngine, PluginBasedWorkflowEngine>();

// 注册传统服务（向后兼容）
builder.Services.AddSingleton<IWorkflowService, WorkflowRepository>();
builder.Services.AddSingleton<ExecutionHistoryService>();
builder.Services.AddSingleton<IExecutionHistoryService, ExecutionHistoryService>();
builder.Services.AddSingleton<MonitoringService>();
builder.Services.AddSingleton<IUserService, UserService>();
builder.Services.AddSingleton<IAuthService, AuthService>();

// Register HTTP client
builder.Services.AddHttpClient();

// Add JWT Authentication
var jwtSecret = builder.Configuration["JWT:Secret"] ?? "FlowCustomJwtSecretKey123456789";
var key = Encoding.ASCII.GetBytes(jwtSecret);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddAuthorization();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "FlowCustom API v1");
        c.RoutePrefix = "swagger";
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// 初始化插件系统
var pluginManager = app.Services.GetRequiredService<IPluginManager>();
Task.Run(async () =>
{
    try
    {
        var logger = app.Services.GetRequiredService<ILogger<Program>>();
        logger.LogInformation("开始加载插件...");
        
        var loadedCount = await pluginManager.LoadPluginsAsync();
        logger.LogInformation($"插件加载完成，共加载 {loadedCount} 个插件");
        
        // 记录加载的插件
        var plugins = pluginManager.GetAllPlugins();
        foreach (var plugin in plugins)
        {
            logger.LogInformation($"已加载插件: {plugin.DisplayName} ({plugin.NodeType}) v{plugin.Version}");
        }
        
        // 添加系统日志
        var executionHistoryService = app.Services.GetRequiredService<ExecutionHistoryService>();
        await executionHistoryService.AddSystemLogAsync("Info", "插件化系统启动完成", "PluginSystem");
        await executionHistoryService.AddSystemLogAsync("Info", $"成功加载 {loadedCount} 个插件", "PluginManager");
        
        foreach (var plugin in plugins)
        {
            await executionHistoryService.AddSystemLogAsync("Info", 
                $"插件已加载: {plugin.DisplayName} ({plugin.NodeType}) v{plugin.Version}", 
                "PluginLoader");
        }
    }
    catch (Exception ex)
    {
        var logger = app.Services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "插件加载失败");
        
        var executionHistoryService = app.Services.GetRequiredService<ExecutionHistoryService>();
        await executionHistoryService.AddSystemLogAsync("Error", $"插件加载失败: {ex.Message}", "PluginSystem");
    }
});

// 健康检查端点
app.MapGet("/health", () => new
{
    Status = "Healthy",
    Timestamp = DateTime.UtcNow,
    Version = "2.0.0-plugin",
    Architecture = "Plugin-based"
});

// 插件信息端点
app.MapGet("/api/system/info", (IPluginManager pluginManager) =>
{
    var plugins = pluginManager.GetAllPlugins();
    return new
    {
        System = new
        {
            Name = "FlowCustom",
            Version = "2.0.0",
            Architecture = "Plugin-based",
            StartTime = DateTime.UtcNow
        },
        Plugins = plugins.Select(p => new
        {
            p.NodeType,
            p.DisplayName,
            p.Description,
            p.Category,
            p.Version,
            p.Author,
            p.IsTrigger
        }).ToList()
    };
});

Console.WriteLine("🚀 FlowCustom 插件化工作流平台启动中...");
Console.WriteLine($"📁 插件目录: {pluginDirectory}");
Console.WriteLine("🌐 API地址: http://localhost:5053");
Console.WriteLine("📚 API文档: http://localhost:5053/swagger");

app.Run();

import React, { useState, useEffect } from 'react';
import type { WorkflowNode, NodeDefinition, NodeParameterDefinition } from '../types/workflow';
import { nodesApi } from '../services/api';

interface NodeConfigPanelProps {
  selectedNode: WorkflowNode | null;
  onNodeUpdate: (nodeId: string, updates: Partial<WorkflowNode>) => void;
  onClose: () => void;
}

const NodeConfigPanel: React.FC<NodeConfigPanelProps> = ({
  selectedNode,
  onNodeUpdate,
  onClose,
}) => {
  const [nodeDefinition, setNodeDefinition] = useState<NodeDefinition | null>(null);
  const [parameters, setParameters] = useState<Record<string, unknown>>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (selectedNode) {
      setParameters(selectedNode.parameters || {});
      fetchNodeDefinition(selectedNode.type);
    }
  }, [selectedNode]);

  const fetchNodeDefinition = async (nodeType: string) => {
    try {
      setLoading(true);
      const nodes = await nodesApi.getNodes();
      const definition = nodes.find(node => node.nodeType === nodeType);
      setNodeDefinition(definition || null);
    } catch (error) {
      console.error('获取节点定义失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleParameterChange = (paramName: string, value: unknown) => {
    const newParameters = { ...parameters, [paramName]: value };
    setParameters(newParameters);
    
    if (selectedNode) {
      onNodeUpdate(selectedNode.id, { parameters: newParameters });
    }
  };

  const handleNameChange = (name: string) => {
    if (selectedNode) {
      onNodeUpdate(selectedNode.id, { name });
    }
  };

  const handleNotesChange = (notes: string) => {
    if (selectedNode) {
      onNodeUpdate(selectedNode.id, { notes });
    }
  };

  const handleDisabledToggle = () => {
    if (selectedNode) {
      onNodeUpdate(selectedNode.id, { isDisabled: !selectedNode.isDisabled });
    }
  };

  const renderParameterInput = (param: NodeParameterDefinition) => {
    const value = parameters[param.name] || param.defaultValue || '';

    switch (param.type) {
      case 'String':
      case 'Expression':
        return (
          <input
            type="text"
            value={value as string}
            onChange={(e) => handleParameterChange(param.name, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={param.description}
          />
        );

      case 'Number':
        return (
          <input
            type="number"
            value={value as number}
            onChange={(e) => handleParameterChange(param.name, parseFloat(e.target.value) || 0)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={param.description}
          />
        );

      case 'Boolean':
        return (
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={value as boolean}
              onChange={(e) => handleParameterChange(param.name, e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-600">{param.description}</span>
          </label>
        );

      case 'Select':
        return (
          <select
            value={value as string}
            onChange={(e) => handleParameterChange(param.name, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">请选择...</option>
            {param.options?.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        );

      case 'Object':
      case 'Array':
        return (
          <textarea
            value={typeof value === 'string' ? value : JSON.stringify(value, null, 2)}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleParameterChange(param.name, parsed);
              } catch {
                handleParameterChange(param.name, e.target.value);
              }
            }}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={`输入 JSON 格式的${param.type === 'Array' ? '数组' : '对象'}`}
          />
        );

      default:
        return (
          <input
            type="text"
            value={value as string}
            onChange={(e) => handleParameterChange(param.name, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={param.description}
          />
        );
    }
  };

  if (!selectedNode) {
    return (
      <div className="w-80 coze-card border-0 rounded-none shadow-lg">
        <div className="coze-card-body text-center py-16">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
            <svg className="w-8 h-8" style={{ color: 'var(--coze-text-muted)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--coze-text-primary)' }}>
            节点配置
          </h3>
          <p className="text-sm mb-1" style={{ color: 'var(--coze-text-secondary)' }}>
            选择一个节点来配置参数
          </p>
          <p className="text-xs" style={{ color: 'var(--coze-text-muted)' }}>
            点击画布中的节点开始配置
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 coze-card border-0 rounded-none flex flex-col h-full shadow-lg">
      {/* Header */}
      <div className="coze-card-header flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-lg flex items-center justify-center text-lg"
               style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
            ⚙️
          </div>
          <h3 className="text-lg font-semibold" style={{ color: 'var(--coze-text-primary)' }}>
            节点配置
          </h3>
        </div>
        <button
          onClick={onClose}
          className="coze-btn coze-btn-secondary p-2"
          title="关闭配置面板"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Basic Info */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <span className="text-blue-500 mr-2">ℹ️</span>
            基本信息
          </h4>
          
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                节点名称
              </label>
              <input
                type="text"
                value={selectedNode.name}
                onChange={(e) => handleNameChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入节点名称"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                节点类型
              </label>
              <input
                type="text"
                value={selectedNode.type}
                disabled
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
              />
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedNode.isDisabled}
                  onChange={handleDisabledToggle}
                  className="mr-2"
                />
                <span className="text-sm text-gray-600">禁用此节点</span>
              </label>
            </div>
          </div>
        </div>

        {/* Parameters */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">加载参数中...</p>
          </div>
        ) : nodeDefinition?.parameters && nodeDefinition.parameters.length > 0 ? (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <span className="text-blue-500 mr-2">⚙️</span>
              参数配置
            </h4>
            <div className="space-y-4">
              {nodeDefinition.parameters.map(param => (
                <div key={param.name}>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    {param.displayName}
                    {param.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  {renderParameterInput(param)}
                  {param.description && (
                    <p className="text-xs text-gray-500 mt-1">{param.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-sm text-gray-500">此节点无需配置参数</p>
          </div>
        )}

        {/* Notes */}
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <span className="text-yellow-500 mr-2">📝</span>
            备注
          </h4>
          <textarea
            value={selectedNode.notes}
            onChange={(e) => handleNotesChange(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="添加节点备注..."
          />
        </div>
      </div>
    </div>
  );
};

export default NodeConfigPanel;

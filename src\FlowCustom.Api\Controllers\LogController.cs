using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustom.Api.Controllers;

/// <summary>
/// 日志管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class LogController : ControllerBase
{
    private readonly IExecutionHistoryService _executionHistoryService;
    private readonly ILogger<LogController> _logger;

    public LogController(
        IExecutionHistoryService executionHistoryService,
        ILogger<LogController> logger)
    {
        _executionHistoryService = executionHistoryService;
        _logger = logger;
    }

    /// <summary>
    /// 获取系统日志
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<SystemLogEntry>>> GetLogs(
        [FromQuery] string? workflowId = null,
        [FromQuery] string? executionId = null,
        [FromQuery] string? level = null,
        [FromQuery] int limit = 1000,
        [FromQuery] int skip = 0)
    {
        try
        {
            var logs = await _executionHistoryService.GetLogsAsync(
                workflowId != null ? Guid.Parse(workflowId) : null,
                executionId != null ? Guid.Parse(executionId) : null,
                level,
                limit,
                skip);

            return Ok(logs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日志失败");
            return StatusCode(500, "获取日志失败");
        }
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    [HttpGet("executions")]
    public async Task<ActionResult<IEnumerable<WorkflowExecution>>> GetExecutions(
        [FromQuery] string? workflowId = null,
        [FromQuery] int limit = 100,
        [FromQuery] int skip = 0)
    {
        try
        {
            var executions = await _executionHistoryService.GetExecutionsAsync(
                workflowId != null ? Guid.Parse(workflowId) : null,
                limit,
                skip);

            return Ok(executions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取执行历史失败");
            return StatusCode(500, "获取执行历史失败");
        }
    }

    /// <summary>
    /// 获取特定执行的详细信息
    /// </summary>
    [HttpGet("executions/{executionId}")]
    public async Task<ActionResult<WorkflowExecution>> GetExecution(Guid executionId)
    {
        try
        {
            var execution = await _executionHistoryService.GetExecutionAsync(executionId);
            if (execution == null)
            {
                return NotFound("执行记录不存在");
            }

            return Ok(execution);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取执行详情失败: {ExecutionId}", executionId);
            return StatusCode(500, "获取执行详情失败");
        }
    }

    /// <summary>
    /// 添加日志
    /// </summary>
    [HttpPost]
    public async Task<ActionResult> AddLog([FromBody] AddLogRequest request)
    {
        try
        {
            await _executionHistoryService.AddSystemLogAsync(
                request.Level,
                request.Message,
                request.Source,
                request.WorkflowId,
                request.NodeId,
                request.ExecutionId,
                request.Details);

            return Ok("日志已添加");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加日志失败");
            return StatusCode(500, "添加日志失败");
        }
    }

    /// <summary>
    /// 清空日志
    /// </summary>
    [HttpDelete]
    public async Task<ActionResult> ClearLogs(
        [FromQuery] string? workflowId = null,
        [FromQuery] string? level = null)
    {
        try
        {
            await _executionHistoryService.ClearLogsAsync(
                workflowId != null ? Guid.Parse(workflowId) : null,
                level);

            _logger.LogInformation("日志已清空: WorkflowId={WorkflowId}, Level={Level}", workflowId, level);
            return Ok("日志已清空");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空日志失败");
            return StatusCode(500, "清空日志失败");
        }
    }
}

/// <summary>
/// 添加日志请求
/// </summary>
public class AddLogRequest
{
    public string Level { get; set; } = "Info";
    public string Message { get; set; } = string.Empty;
    public string? Source { get; set; }
    public Guid? WorkflowId { get; set; }
    public Guid? NodeId { get; set; }
    public Guid? ExecutionId { get; set; }
    public Dictionary<string, object>? Details { get; set; }
}



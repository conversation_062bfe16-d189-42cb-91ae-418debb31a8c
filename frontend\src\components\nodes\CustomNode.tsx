import React from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import type { NodeProps } from '@xyflow/react';
import type { WorkflowNode } from '../../types/workflow';

const getNodeDisplayName = (nodeType: string): string => {
  const displayNames: Record<string, string> = {
    'http-request': 'HTTP请求',
    'set': '数据设置',
    'webhook-trigger': 'Webhook触发器',
    'cron-trigger': '定时触发器',
    'api-trigger': 'API触发器',
    'file-watcher-trigger': '文件监控触发器',
  };
  return displayNames[nodeType] || nodeType;
};

const CustomNode: React.FC<NodeProps> = ({ data, selected }) => {
  const nodeData = data as WorkflowNode & {
    onDelete?: () => void;
    onEdit?: () => void;
  };
  const getNodeIcon = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return '🌐';
      case 'set':
        return '⚙️';
      case 'webhook-trigger':
        return '🔗';
      case 'cron-trigger':
        return '⏰';
      case 'api-trigger':
        return '🔌';
      case 'file-watcher-trigger':
        return '📁';
      default:
        return '📦';
    }
  };

  const getNodeTheme = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return { bg: '#dbeafe', border: '#3b82f6', icon: '#1d4ed8' };
      case 'set':
        return { bg: '#dcfce7', border: '#10b981', icon: '#047857' };
      case 'webhook-trigger':
      case 'cron-trigger':
      case 'api-trigger':
      case 'file-watcher-trigger':
        return { bg: '#fdf4ff', border: '#a855f7', icon: '#7c3aed' };
      default:
        return { bg: '#f8fafc', border: '#64748b', icon: '#475569' };
    }
  };

  const theme = getNodeTheme(nodeData.type);
  const isTrigger = ['webhook-trigger', 'cron-trigger', 'api-trigger', 'file-watcher-trigger'].includes(nodeData.type);

  return (
    <div
      className={`group relative transition-all duration-200 ${
        selected
          ? 'ring-2 ring-blue-500 shadow-lg scale-105'
          : 'hover:shadow-md'
      }`}
      style={{
        minWidth: '180px',
        backgroundColor: nodeData.isDisabled ? '#f1f5f9' : theme.bg,
        border: `2px solid ${nodeData.isDisabled ? '#cbd5e1' : theme.border}`,
        borderRadius: 'var(--coze-radius-lg)',
        opacity: nodeData.isDisabled ? 0.6 : 1,
        boxShadow: selected ? `0 0 0 4px ${theme.border}20` : 'var(--coze-shadow-sm)'
      }}
    >
      {/* Node Actions - 悬停时显示 */}
      <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1 z-10">
        {nodeData.onEdit && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              nodeData.onEdit?.();
            }}
            className="w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center hover:bg-blue-600 transition-colors shadow-md"
            title="编辑节点"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>
        )}
        {nodeData.onDelete && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (window.confirm('确定要删除这个节点吗？')) {
                nodeData.onDelete?.();
              }
            }}
            className="w-6 h-6 rounded-full bg-red-500 text-white flex items-center justify-center hover:bg-red-600 transition-colors shadow-md"
            title="删除节点"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        )}
      </div>
      {/* Input Handle */}
      {!isTrigger && (
        <Handle
          type="target"
          position={Position.Top}
          style={{
            width: '12px',
            height: '12px',
            backgroundColor: theme.border,
            border: '2px solid white',
            top: '-6px'
          }}
        />
      )}

      {/* Node Content */}
      <div className="p-4">
        <div className="flex items-center space-x-3">
          <div
            className="w-10 h-10 rounded-lg flex items-center justify-center text-xl"
            style={{
              backgroundColor: 'white',
              color: theme.icon,
              border: `1px solid ${theme.border}30`
            }}
          >
            {getNodeIcon(nodeData.type)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-semibold text-sm truncate" style={{ color: 'var(--coze-text-primary)' }}>
              {nodeData.name || getNodeDisplayName(nodeData.type)}
            </div>
            <div className="text-xs truncate mt-1" style={{ color: 'var(--coze-text-secondary)' }}>
              {getNodeDisplayName(nodeData.type)}
            </div>
          </div>
        </div>

        {/* Node Status */}
        <div className="flex items-center justify-between mt-3">
          <div className="flex items-center space-x-2">
            {isTrigger && (
              <span className="coze-badge coze-badge-primary text-xs">
                触发器
              </span>
            )}
            {nodeData.isDisabled && (
              <span className="coze-badge text-xs">
                已禁用
              </span>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <div
              className="w-2 h-2 rounded-full"
              style={{
                backgroundColor: nodeData.isDisabled
                  ? '#94a3b8'
                  : isTrigger
                    ? '#a855f7'
                    : '#10b981'
              }}
            ></div>
            <span className="text-xs" style={{ color: 'var(--coze-text-muted)' }}>
              {nodeData.isDisabled ? '已禁用' : '就绪'}
            </span>
          </div>
        </div>
      </div>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        style={{
          width: '12px',
          height: '12px',
          backgroundColor: theme.border,
          border: '2px solid white',
          bottom: '-6px'
        }}
      />
    </div>
  );
};

export default CustomNode;

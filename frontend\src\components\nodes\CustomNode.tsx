/**
 * CustomNode 组件 - FlowCustom工作流编辑器的自定义节点组件
 *
 * 这是ReactFlow中显示的每个工作流节点的可视化组件，负责：
 * 1. 节点的视觉呈现：图标、颜色主题、边框样式
 * 2. 执行状态显示：运行中、成功、失败的视觉反馈
 * 3. 交互功能：编辑、删除按钮
 * 4. 连接点管理：输入和输出Handle的位置和样式
 * 5. 执行结果展示：执行时间、错误信息、输出数据摘要
 *
 * 设计原则：
 * - 遵循Coze设计系统的视觉规范
 * - 提供清晰的执行状态反馈
 * - 支持不同类型节点的差异化显示
 * - 响应式设计，适配不同屏幕尺寸
 */

import React from 'react';
import { Handle, Position } from '@xyflow/react';
import type { NodeProps } from '@xyflow/react';
import type { WorkflowNode, NodeEndpoint } from '../../types/workflow';
import { getNodeEndpoints, isTriggerNode } from '../../config/nodeEndpoints';

/**
 * 获取节点类型的中文显示名称
 *
 * 这个映射表将英文的节点类型转换为用户友好的中文名称
 * 支持的节点类型包括：
 * - 触发器类：手动、Webhook、定时、API、文件监控
 * - 执行器类：HTTP请求、脚本执行、数据设置
 * - 逻辑控制类：条件判断、循环、数据转换
 *
 * @param nodeType 节点类型的英文标识符
 * @returns 对应的中文显示名称
 */
const getNodeDisplayName = (nodeType: string): string => {
  const displayNames: Record<string, string> = {
    'http-request': 'HTTP请求',
    'set': '数据设置',
    'webhook-trigger': 'Webhook触发器',
    'cron-trigger': '定时触发器',
    'api-trigger': 'API触发器',
    'file-watcher-trigger': '文件监控触发器',
    'manual-trigger': '手动触发器',
    'script-executor': '脚本执行器',
    'conditional': '条件判断',
    'loop': '循环',
    'data-transform': '数据转换',
  };
  return displayNames[nodeType] || nodeType;
};

/**
 * 将端点位置配置转换为ReactFlow Position
 */
const getReactFlowPosition = (side: string): Position => {
  switch (side) {
    case 'top': return Position.Top;
    case 'bottom': return Position.Bottom;
    case 'left': return Position.Left;
    case 'right': return Position.Right;
    default: return Position.Top;
  }
};

/**
 * 计算Handle的样式位置
 */
const getHandleStyle = (endpoint: NodeEndpoint, nodeWidth = 200, nodeHeight = 120) => {
  const { side, offset } = endpoint.position;
  const handleSize = 12;
  const handleOffset = handleSize / 2;

  switch (side) {
    case 'top':
      return {
        left: `${offset}%`,
        top: `-${handleOffset}px`,
        transform: 'translateX(-50%)'
      };
    case 'bottom':
      return {
        left: `${offset}%`,
        bottom: `-${handleOffset}px`,
        transform: 'translateX(-50%)'
      };
    case 'left':
      return {
        left: `-${handleOffset}px`,
        top: `${offset}%`,
        transform: 'translateY(-50%)'
      };
    case 'right':
      return {
        right: `-${handleOffset}px`,
        top: `${offset}%`,
        transform: 'translateY(-50%)'
      };
    default:
      return {};
  }
};

/**
 * 获取端点类型对应的颜色
 */
const getEndpointColor = (endpointType: string): string => {
  const colors: Record<string, string> = {
    'trigger': '#a855f7',  // 紫色 - 触发器
    'data': '#3b82f6',     // 蓝色 - 数据
    'error': '#ef4444',    // 红色 - 错误
    'success': '#10b981',  // 绿色 - 成功
    'condition': '#f59e0b' // 橙色 - 条件
  };

  return colors[endpointType] || '#6b7280'; // 默认灰色
};

/**
 * CustomNode 主组件
 *
 * 接收ReactFlow传入的节点数据和选中状态，渲染完整的节点UI
 *
 * Props说明：
 * - data: 节点数据，包含WorkflowNode的所有属性以及额外的UI状态
 * - selected: 节点是否被选中，影响边框和阴影样式
 *
 * 扩展属性：
 * - onDelete/onEdit: 节点操作回调函数
 * - executionStatus: 执行状态（idle/running/success/error）
 * - executionResult: 执行结果详情
 * - isExecuting: 是否正在执行（用于动画控制）
 */
const CustomNode: React.FC<NodeProps> = ({ data, selected }) => {
  // 类型断言：将通用的data转换为具体的节点数据类型
  const nodeData = data as WorkflowNode & {
    onDelete?: () => void;
    onEdit?: () => void;
    executionStatus?: 'idle' | 'running' | 'success' | 'error';
    executionResult?: {
      status: string;
      duration?: string;
      error?: string;
      outputData?: any;
      startedAt?: string;
      finishedAt?: string;
    };
    isExecuting?: boolean;
  };

  /**
   * 获取节点类型对应的图标
   *
   * 使用Emoji图标提供直观的视觉识别：
   * - 🌐 网络相关（HTTP请求）
   * - ⚙️ 配置相关（数据设置）
   * - 🔗 连接相关（Webhook）
   * - ⏰ 时间相关（定时触发）
   * - 👆 手动相关（手动触发）
   * - 💻 代码相关（脚本执行）
   * - ❓ 逻辑相关（条件判断）
   * - 🔁 循环相关（循环处理）
   * - 🔄 转换相关（数据转换）
   *
   * @param nodeType 节点类型标识符
   * @returns 对应的Emoji图标
   */
  const getNodeIcon = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return '🌐';
      case 'set':
        return '⚙️';
      case 'webhook-trigger':
        return '🔗';
      case 'cron-trigger':
        return '⏰';
      case 'api-trigger':
        return '🔌';
      case 'file-watcher-trigger':
        return '📁';
      case 'manual-trigger':
        return '👆';
      case 'script-executor':
        return '💻';
      case 'conditional':
        return '❓';
      case 'loop':
        return '🔁';
      case 'data-transform':
        return '🔄';
      default:
        return '📦';
    }
  };

  const getNodeTheme = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return { bg: '#dbeafe', border: '#3b82f6', icon: '#1d4ed8' };
      case 'set':
      case 'data-transform':
        return { bg: '#dcfce7', border: '#10b981', icon: '#047857' };
      case 'script-executor':
        return { bg: '#fef3c7', border: '#f59e0b', icon: '#d97706' };
      case 'conditional':
      case 'loop':
        return { bg: '#fef2f2', border: '#ef4444', icon: '#dc2626' };
      case 'webhook-trigger':
      case 'cron-trigger':
      case 'api-trigger':
      case 'file-watcher-trigger':
      case 'manual-trigger':
        return { bg: '#fdf4ff', border: '#a855f7', icon: '#7c3aed' };
      default:
        return { bg: '#f8fafc', border: '#64748b', icon: '#475569' };
    }
  };

  const theme = getNodeTheme(nodeData.type);
  const isTrigger = isTriggerNode(nodeData.type);

  // 获取节点的端点配置 - 优先使用自定义配置
  const defaultEndpoints = getNodeEndpoints(nodeData.type);
  const endpoints = {
    inputs: nodeData.customInputs || defaultEndpoints.inputs,
    outputs: nodeData.customOutputs || defaultEndpoints.outputs
  };

  // 端点配置日志（开发模式）
  if (process.env.NODE_ENV === 'development') {
    console.log(`节点 ${nodeData.id} (${nodeData.type}) 端点配置:`, {
      inputs: endpoints.inputs.length,
      outputs: endpoints.outputs.length,
      hasCustomInputs: !!nodeData.customInputs,
      hasCustomOutputs: !!nodeData.customOutputs,
      isTrigger
    });
  }

  // 获取执行状态样式
  const getExecutionStatusStyle = () => {
    switch (nodeData.executionStatus) {
      case 'running':
        return {
          border: '2px solid #f59e0b',
          boxShadow: '0 0 10px rgba(245, 158, 11, 0.3)',
          animation: 'pulse 2s infinite'
        };
      case 'success':
        return {
          border: '2px solid #10b981',
          boxShadow: '0 0 10px rgba(16, 185, 129, 0.3)'
        };
      case 'error':
        return {
          border: '2px solid #ef4444',
          boxShadow: '0 0 10px rgba(239, 68, 68, 0.3)'
        };
      default:
        return {};
    }
  };

  // 格式化执行时间
  const formatDuration = (duration?: string) => {
    if (!duration) return '';
    const match = duration.match(/(\d+):(\d+):(\d+)\.(\d+)/);
    if (match) {
      const [, hours, minutes, seconds, milliseconds] = match;
      const totalMs = parseInt(hours) * 3600000 + parseInt(minutes) * 60000 + parseInt(seconds) * 1000 + parseInt(milliseconds.substring(0, 3));
      if (totalMs < 1000) {
        return `${totalMs}ms`;
      } else if (totalMs < 60000) {
        return `${(totalMs / 1000).toFixed(1)}s`;
      } else {
        return `${Math.floor(totalMs / 60000)}m ${Math.floor((totalMs % 60000) / 1000)}s`;
      }
    }
    return duration;
  };

  const executionStyle = getExecutionStatusStyle();

  return (
    <div
      className={`group relative transition-all duration-200 ${
        selected
          ? 'ring-2 ring-blue-500 shadow-lg scale-105'
          : 'hover:shadow-md'
      }`}
      style={{
        minWidth: '200px',
        backgroundColor: nodeData.isDisabled ? '#f1f5f9' : theme.bg,
        border: executionStyle.border || `2px solid ${nodeData.isDisabled ? '#cbd5e1' : theme.border}`,
        borderRadius: 'var(--coze-radius-lg)',
        opacity: nodeData.isDisabled ? 0.6 : 1,
        boxShadow: executionStyle.boxShadow || (selected ? `0 0 0 4px ${theme.border}20` : 'var(--coze-shadow-sm)'),
        animation: executionStyle.animation
      }}
    >
      {/* Node Actions - 悬停时显示 */}
      <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1 z-10">
        {nodeData.onEdit && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              nodeData.onEdit?.();
            }}
            className="w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center hover:bg-blue-600 transition-colors shadow-md"
            title="编辑节点"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>
        )}
        {nodeData.onDelete && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (window.confirm('确定要删除这个节点吗？')) {
                nodeData.onDelete?.();
              }
            }}
            className="w-6 h-6 rounded-full bg-red-500 text-white flex items-center justify-center hover:bg-red-600 transition-colors shadow-md"
            title="删除节点"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        )}
      </div>
      {/* Input Handles - 多输入端点 */}
      {endpoints.inputs.map((endpoint) => (
        <Handle
          key={`input-${endpoint.id}`}
          id={endpoint.id}
          type="target"
          position={getReactFlowPosition(endpoint.position.side)}
          style={{
            width: '12px',
            height: '12px',
            backgroundColor: getEndpointColor(endpoint.type),
            border: '2px solid white',
            ...getHandleStyle(endpoint)
          }}
          title={`${endpoint.name}: ${endpoint.description || ''}`}
        />
      ))}

      {/* Node Content */}
      <div className="p-4">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div
              className="w-10 h-10 rounded-lg flex items-center justify-center text-xl"
              style={{
                backgroundColor: 'white',
                color: theme.icon,
                border: `1px solid ${theme.border}30`
              }}
            >
              {getNodeIcon(nodeData.type)}
            </div>
            {/* 执行状态指示器 */}
            {nodeData.executionStatus === 'running' && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center animate-spin">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            )}
            {nodeData.executionStatus === 'success' && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
            {nodeData.executionStatus === 'error' && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-semibold text-sm truncate" style={{ color: 'var(--coze-text-primary)' }}>
              {nodeData.name || getNodeDisplayName(nodeData.type)}
            </div>
            <div className="text-xs truncate mt-1" style={{ color: 'var(--coze-text-secondary)' }}>
              {getNodeDisplayName(nodeData.type)}
            </div>
          </div>
        </div>

        {/* Node Status */}
        <div className="flex items-center justify-between mt-3">
          <div className="flex items-center space-x-2">
            {isTrigger && (
              <span className="coze-badge coze-badge-primary text-xs">
                触发器
              </span>
            )}
            {nodeData.isDisabled && (
              <span className="coze-badge text-xs">
                已禁用
              </span>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <div
              className="w-2 h-2 rounded-full"
              style={{
                backgroundColor: nodeData.isDisabled
                  ? '#94a3b8'
                  : nodeData.executionStatus === 'running'
                    ? '#f59e0b'
                    : nodeData.executionStatus === 'success'
                      ? '#10b981'
                      : nodeData.executionStatus === 'error'
                        ? '#ef4444'
                        : isTrigger
                          ? '#a855f7'
                          : '#10b981'
              }}
            ></div>
            <span className="text-xs" style={{ color: 'var(--coze-text-muted)' }}>
              {nodeData.isDisabled
                ? '已禁用'
                : nodeData.executionStatus === 'running'
                  ? '执行中'
                  : nodeData.executionStatus === 'success'
                    ? '成功'
                    : nodeData.executionStatus === 'error'
                      ? '失败'
                      : '就绪'}
            </span>
          </div>
        </div>

        {/* 执行结果显示 */}
        {nodeData.executionResult && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between text-xs">
              <span style={{ color: 'var(--coze-text-secondary)' }}>
                {nodeData.executionResult.status === 'success' ? '✅ 执行成功' :
                 nodeData.executionResult.status === 'error' ? '❌ 执行失败' :
                 '⏳ 执行中'}
              </span>
              {nodeData.executionResult.duration && (
                <span style={{ color: 'var(--coze-text-muted)' }}>
                  {formatDuration(nodeData.executionResult.duration)}
                </span>
              )}
            </div>
            {nodeData.executionResult.error && (
              <div className="mt-1 text-xs text-red-600 truncate" title={nodeData.executionResult.error}>
                {nodeData.executionResult.error}
              </div>
            )}
            {nodeData.executionResult.outputData && !nodeData.executionResult.error && (
              <div className="mt-1 text-xs" style={{ color: 'var(--coze-text-muted)' }}>
                {typeof nodeData.executionResult.outputData === 'object'
                  ? `输出: ${Object.keys(nodeData.executionResult.outputData).length} 项`
                  : `输出: ${String(nodeData.executionResult.outputData).substring(0, 30)}...`}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Output Handles - 多输出端点 */}
      {endpoints.outputs.map((endpoint) => (
        <Handle
          key={`output-${endpoint.id}`}
          id={endpoint.id}
          type="source"
          position={getReactFlowPosition(endpoint.position.side)}
          style={{
            width: '12px',
            height: '12px',
            backgroundColor: getEndpointColor(endpoint.type),
            border: '2px solid white',
            ...getHandleStyle(endpoint)
          }}
          title={`${endpoint.name}: ${endpoint.description || ''}`}
        />
      ))}
    </div>
  );
};

export default CustomNode;

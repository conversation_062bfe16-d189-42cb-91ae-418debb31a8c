import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import { WorkflowNode } from '../../types/workflow';

interface CustomNodeData extends WorkflowNode {
  selected?: boolean;
}

const CustomNode: React.FC<NodeProps<CustomNodeData>> = ({ data, selected }) => {
  const getNodeIcon = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return '🌐';
      case 'set':
        return '⚙️';
      case 'webhook-trigger':
        return '🔗';
      default:
        return '📦';
    }
  };

  const getNodeColor = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return 'border-blue-400 bg-blue-50';
      case 'set':
        return 'border-green-400 bg-green-50';
      case 'webhook-trigger':
        return 'border-purple-400 bg-purple-50';
      default:
        return 'border-gray-400 bg-gray-50';
    }
  };

  return (
    <div className={`custom-node ${selected ? 'selected' : ''} ${getNodeColor(data.type)}`}>
      {/* Input Handle */}
      {data.type !== 'webhook-trigger' && (
        <Handle
          type="target"
          position={Position.Top}
          className="react-flow__handle-top"
        />
      )}
      
      {/* Node Content */}
      <div className="node-header">
        <span className="text-lg">{getNodeIcon(data.type)}</span>
        <div>
          <div className="node-title">{data.name || data.type}</div>
          <div className="node-description text-xs">
            {data.type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </div>
        </div>
      </div>

      {/* Node Status */}
      {data.isDisabled && (
        <div className="text-xs text-red-500 mt-1">Disabled</div>
      )}

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="react-flow__handle-bottom"
      />
    </div>
  );
};

export default CustomNode;

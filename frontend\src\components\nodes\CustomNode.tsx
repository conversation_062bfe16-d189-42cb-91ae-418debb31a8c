import React from 'react';
import { Handle, Position } from '@xyflow/react';
import type { NodeProps } from '@xyflow/react';
import type { WorkflowNode } from '../../types/workflow';

const getNodeDisplayName = (nodeType: string): string => {
  const displayNames: Record<string, string> = {
    'http-request': 'HTTP请求',
    'set': '数据设置',
    'webhook-trigger': 'Webhook触发器',
    'cron-trigger': '定时触发器',
    'api-trigger': 'API触发器',
    'file-watcher-trigger': '文件监控触发器',
  };
  return displayNames[nodeType] || nodeType;
};

const CustomNode: React.FC<NodeProps> = ({ data, selected }) => {
  const nodeData = data as WorkflowNode;
  const getNodeIcon = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return '🌐';
      case 'set':
        return '⚙️';
      case 'webhook-trigger':
        return '🔗';
      default:
        return '📦';
    }
  };

  const getNodeColor = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return 'border-blue-400 bg-blue-50';
      case 'set':
        return 'border-green-400 bg-green-50';
      case 'webhook-trigger':
        return 'border-purple-400 bg-purple-50';
      default:
        return 'border-gray-400 bg-gray-50';
    }
  };

  return (
    <div className={`custom-node ${selected ? 'selected' : ''} ${getNodeColor(nodeData.type)}`}>
      {/* Input Handle */}
      {nodeData.type !== 'webhook-trigger' && (
        <Handle
          type="target"
          position={Position.Top}
          className="react-flow__handle-top"
        />
      )}
      
      {/* Node Content */}
      <div className="node-header">
        <span className="text-lg">{getNodeIcon(nodeData.type)}</span>
        <div>
          <div className="node-title">{nodeData.name || getNodeDisplayName(nodeData.type)}</div>
          <div className="node-description text-xs">
            {getNodeDisplayName(nodeData.type)}
          </div>
        </div>
      </div>

      {/* Node Status */}
      {nodeData.isDisabled && (
        <div className="text-xs text-red-500 mt-1">Disabled</div>
      )}

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="react-flow__handle-bottom"
      />
    </div>
  );
};

export default CustomNode;

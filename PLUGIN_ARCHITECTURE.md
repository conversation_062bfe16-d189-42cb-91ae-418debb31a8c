# 插件化节点系统架构设计

## 概述

将工作流系统重构为插件化架构，实现框架与节点的完全分离。每个节点作为独立的DLL插件，支持动态加载和热插拔。

## 架构设计

### 1. 目录结构

```
FlowCustom/
├── Core/                           # 核心框架
│   ├── FlowCustom.Core/           # 核心库
│   ├── FlowCustom.Engine/         # 执行引擎
│   ├── FlowCustom.Api/            # Web API
│   └── FlowCustom.PluginHost/     # 插件宿主
├── Plugins/                        # 插件目录
│   ├── FlowCustom.Plugins.Core/   # 核心插件（基础节点）
│   ├── FlowCustom.Plugins.Http/   # HTTP插件
│   ├── FlowCustom.Plugins.Database/ # 数据库插件
│   ├── FlowCustom.Plugins.Script/ # 脚本插件
│   └── FlowCustom.Plugins.Email/  # 邮件插件
├── PluginSDK/                      # 插件开发SDK
│   ├── FlowCustom.SDK/            # 插件开发接口
│   ├── Templates/                 # 插件模板
│   └── Documentation/             # 开发文档
└── Runtime/                        # 运行时目录
    ├── plugins/                   # 已安装插件
    ├── configs/                   # 插件配置
    └── cache/                     # 插件缓存
```

### 2. 插件接口设计

#### 核心接口

```csharp
// 节点插件接口
public interface INodePlugin
{
    string NodeType { get; }
    string DisplayName { get; }
    string Description { get; }
    string Category { get; }
    string Version { get; }
    string Author { get; }
    bool IsTrigger { get; }
    
    NodeDefinition GetDefinition();
    Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context);
    Task<bool> ValidateAsync(NodeConfiguration config);
    void Initialize(IServiceProvider serviceProvider);
    void Dispose();
}

// 插件元数据
public interface IPluginMetadata
{
    string Id { get; }
    string Name { get; }
    string Version { get; }
    string Author { get; }
    string Description { get; }
    string[] Dependencies { get; }
    string[] SupportedFrameworks { get; }
}
```

### 3. 插件加载机制

#### 插件发现
- 扫描 `Runtime/plugins/` 目录
- 读取插件清单文件 `plugin.json`
- 验证插件依赖和兼容性
- 加载插件程序集

#### 插件生命周期
1. **发现阶段**: 扫描插件目录，读取元数据
2. **验证阶段**: 检查依赖、版本兼容性
3. **加载阶段**: 加载程序集，创建插件实例
4. **初始化阶段**: 调用插件初始化方法
5. **运行阶段**: 插件可用于工作流执行
6. **卸载阶段**: 释放插件资源

### 4. 插件配置

#### 插件清单 (plugin.json)
```json
{
  "id": "FlowCustom.Plugins.Http",
  "name": "HTTP 请求插件",
  "version": "1.0.0",
  "author": "FlowCustom Team",
  "description": "提供HTTP请求功能的插件",
  "main": "FlowCustom.Plugins.Http.dll",
  "dependencies": [
    "FlowCustom.SDK@^1.0.0"
  ],
  "supportedFrameworks": ["net8.0"],
  "nodes": [
    {
      "type": "http-request",
      "class": "FlowCustom.Plugins.Http.HttpRequestNode"
    },
    {
      "type": "webhook-trigger",
      "class": "FlowCustom.Plugins.Http.WebhookTriggerNode"
    }
  ],
  "resources": {
    "frontend": "wwwroot/",
    "icons": "icons/",
    "localization": "locales/"
  }
}
```

### 5. 前端插件支持

#### 前端插件结构
```
plugin-frontend/
├── components/           # React组件
│   ├── HttpRequestConfig.tsx
│   └── WebhookTriggerConfig.tsx
├── icons/               # 节点图标
├── locales/            # 国际化文件
└── index.js            # 插件入口
```

#### 前端插件接口
```typescript
interface IFrontendPlugin {
  id: string;
  name: string;
  version: string;
  
  // 节点配置组件
  getConfigComponent(nodeType: string): React.ComponentType;
  
  // 节点图标
  getIcon(nodeType: string): string;
  
  // 国际化资源
  getLocaleResources(locale: string): Record<string, string>;
}
```

### 6. 插件安全机制

#### 沙箱隔离
- 每个插件在独立的AppDomain中运行
- 限制插件访问系统资源
- 提供安全的API接口

#### 权限控制
- 插件需要声明所需权限
- 管理员可以控制插件权限
- 运行时权限检查

### 7. 插件开发流程

#### 开发步骤
1. 使用SDK创建插件项目
2. 实现INodePlugin接口
3. 创建前端配置组件
4. 编写插件清单文件
5. 编译生成插件包
6. 部署到插件目录

#### 开发工具
- 插件项目模板
- 代码生成器
- 调试工具
- 打包工具

### 8. 插件管理

#### 管理功能
- 插件安装/卸载
- 插件启用/禁用
- 插件更新
- 依赖管理
- 配置管理

#### 管理界面
- 插件市场
- 插件列表
- 插件详情
- 插件配置

### 9. 性能优化

#### 延迟加载
- 按需加载插件
- 插件缓存机制
- 预编译优化

#### 资源管理
- 插件资源隔离
- 内存管理
- 垃圾回收

### 10. 扩展性

#### 插件类型扩展
- 节点插件
- 触发器插件
- 连接器插件
- 工具插件

#### 平台扩展
- 支持多种开发语言
- 跨平台兼容
- 云端插件

## 实施计划

### 阶段1: 核心架构
- 创建插件接口和SDK
- 实现插件加载器
- 重构执行引擎

### 阶段2: 基础插件
- 将现有节点转换为插件
- 创建核心插件包
- 实现前端插件支持

### 阶段3: 管理工具
- 插件管理界面
- 开发工具链
- 文档和示例

### 阶段4: 高级功能
- 插件市场
- 安全机制
- 性能优化

## 优势

1. **模块化**: 功能完全解耦，易于维护
2. **可扩展**: 支持第三方插件开发
3. **热插拔**: 无需重启即可加载新插件
4. **版本管理**: 独立的插件版本控制
5. **性能**: 按需加载，减少资源消耗
6. **安全**: 插件沙箱隔离
7. **生态**: 构建插件生态系统

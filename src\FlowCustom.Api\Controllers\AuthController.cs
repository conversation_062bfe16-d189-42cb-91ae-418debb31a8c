using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustom.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly IUserService _userService;

    public AuthController(IAuthService authService, IUserService userService)
    {
        _authService = authService;
        _userService = userService;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    [HttpPost("login")]
    public async Task<ActionResult<AuthResult>> Login([FromBody] LoginRequest request)
    {
        if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
        {
            return BadRequest(new { message = "Username and password are required" });
        }

        var result = await _authService.LoginAsync(request.Username, request.Password);
        
        if (result.Success)
        {
            return Ok(result);
        }
        
        return Unauthorized(new { message = result.Error });
    }

    /// <summary>
    /// 用户注册
    /// </summary>
    [HttpPost("register")]
    public async Task<ActionResult<AuthResult>> Register([FromBody] RegisterRequest request)
    {
        if (string.IsNullOrEmpty(request.Username) || 
            string.IsNullOrEmpty(request.Email) || 
            string.IsNullOrEmpty(request.Password))
        {
            return BadRequest(new { message = "Username, email, and password are required" });
        }

        try
        {
            var user = new User
            {
                Username = request.Username,
                Email = request.Email,
                FirstName = request.FirstName ?? "",
                LastName = request.LastName ?? ""
            };

            await _userService.CreateUserAsync(user, request.Password);
            
            // 自动登录新用户
            var loginResult = await _authService.LoginAsync(request.Username, request.Password);
            return Ok(loginResult);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception)
        {
            return StatusCode(500, new { message = "Registration failed" });
        }
    }

    /// <summary>
    /// 用户登出
    /// </summary>
    [HttpPost("logout")]
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        await _authService.LogoutAsync(token);
        return Ok(new { message = "Logged out successfully" });
    }

    /// <summary>
    /// 刷新令牌
    /// </summary>
    [HttpPost("refresh")]
    public async Task<ActionResult<AuthResult>> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        if (string.IsNullOrEmpty(request.RefreshToken))
        {
            return BadRequest(new { message = "Refresh token is required" });
        }

        var result = await _authService.RefreshTokenAsync(request.RefreshToken);
        
        if (result.Success)
        {
            return Ok(result);
        }
        
        return Unauthorized(new { message = result.Error });
    }

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    [HttpGet("me")]
    [Authorize]
    public async Task<ActionResult<User>> GetCurrentUser()
    {
        var userIdClaim = User.FindFirst("userId")?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            var user = await _userService.GetUserAsync(userId);
            if (user != null)
            {
                // 不返回密码哈希
                user.PasswordHash = "";
                return Ok(user);
            }
        }
        
        return NotFound(new { message = "User not found" });
    }

    /// <summary>
    /// 更新用户信息
    /// </summary>
    [HttpPut("me")]
    [Authorize]
    public async Task<ActionResult<User>> UpdateCurrentUser([FromBody] UpdateUserRequest request)
    {
        var userIdClaim = User.FindFirst("userId")?.Value;
        if (!Guid.TryParse(userIdClaim, out var userId))
        {
            return Unauthorized();
        }

        var user = await _userService.GetUserAsync(userId);
        if (user == null)
        {
            return NotFound(new { message = "User not found" });
        }

        // 更新用户信息
        if (!string.IsNullOrEmpty(request.FirstName))
            user.FirstName = request.FirstName;
        
        if (!string.IsNullOrEmpty(request.LastName))
            user.LastName = request.LastName;
        
        if (!string.IsNullOrEmpty(request.Email))
            user.Email = request.Email;

        if (request.Settings != null)
            user.Settings = request.Settings;

        var updatedUser = await _userService.UpdateUserAsync(user);
        updatedUser.PasswordHash = ""; // 不返回密码哈希
        
        return Ok(updatedUser);
    }

    /// <summary>
    /// 更改密码
    /// </summary>
    [HttpPost("change-password")]
    [Authorize]
    public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        var userIdClaim = User.FindFirst("userId")?.Value;
        if (!Guid.TryParse(userIdClaim, out var userId))
        {
            return Unauthorized();
        }

        if (string.IsNullOrEmpty(request.CurrentPassword) || string.IsNullOrEmpty(request.NewPassword))
        {
            return BadRequest(new { message = "Current password and new password are required" });
        }

        // 验证当前密码
        var isValidPassword = await _userService.ValidatePasswordAsync(userId, request.CurrentPassword);
        if (!isValidPassword)
        {
            return BadRequest(new { message = "Current password is incorrect" });
        }

        await _userService.UpdatePasswordAsync(userId, request.NewPassword);
        return Ok(new { message = "Password updated successfully" });
    }

    /// <summary>
    /// 生成 API 密钥
    /// </summary>
    [HttpPost("api-keys")]
    [Authorize]
    public async Task<ActionResult<ApiKey>> GenerateApiKey([FromBody] GenerateApiKeyRequest request)
    {
        var userIdClaim = User.FindFirst("userId")?.Value;
        if (!Guid.TryParse(userIdClaim, out var userId))
        {
            return Unauthorized();
        }

        if (string.IsNullOrEmpty(request.Name))
        {
            return BadRequest(new { message = "API key name is required" });
        }

        var apiKey = await _authService.GenerateApiKeyAsync(
            userId, 
            request.Name, 
            request.Scopes ?? new List<string>(), 
            request.ExpiresAt);

        return Ok(apiKey);
    }
}

// 请求模型
public class LoginRequest
{
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}

public class RegisterRequest
{
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
}

public class RefreshTokenRequest
{
    public string RefreshToken { get; set; } = string.Empty;
}

public class UpdateUserRequest
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Email { get; set; }
    public UserSettings? Settings { get; set; }
}

public class ChangePasswordRequest
{
    public string CurrentPassword { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
}

public class GenerateApiKeyRequest
{
    public string Name { get; set; } = string.Empty;
    public List<string>? Scopes { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

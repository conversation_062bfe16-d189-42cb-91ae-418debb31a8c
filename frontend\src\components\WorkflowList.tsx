import React, { useState, useEffect } from 'react';
import type { Workflow } from '../types/workflow';
import { workflowApi } from '../services/api';

interface WorkflowListProps {
  onSelectWorkflow: (workflow: Workflow) => void;
  onCreateWorkflow: () => void;
  onViewHistory?: (workflow: Workflow) => void;
  onViewLogs?: () => void;
}

const WorkflowList: React.FC<WorkflowListProps> = ({
  onSelectWorkflow,
  onCreateWorkflow,
  onViewHistory,
  onViewLogs
}) => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWorkflows();
  }, []);

  const fetchWorkflows = async () => {
    try {
      setLoading(true);
      const data = await workflowApi.getWorkflows();
      setWorkflows(data);
      setError(null);
    } catch (err) {
      setError('获取工作流失败');
      console.error('Error fetching workflows:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (workflow: Workflow) => {
    try {
      await workflowApi.toggleWorkflow(workflow.id, !workflow.isActive);
      await fetchWorkflows(); // Refresh the list
    } catch (err) {
      console.error('Error toggling workflow:', err);
    }
  };

  const handleDeleteWorkflow = async (workflow: Workflow) => {
    if (window.confirm(`确定要删除工作流"${workflow.name}"吗？`)) {
      try {
        await workflowApi.deleteWorkflow(workflow.id);
        await fetchWorkflows(); // Refresh the list
      } catch (err) {
        console.error('Error deleting workflow:', err);
      }
    }
  };

  const handleExecuteWorkflow = async (workflow: Workflow) => {
    try {
      await workflowApi.executeWorkflow(workflow.id);
      alert('工作流执行成功！');
    } catch (err) {
      console.error('Error executing workflow:', err);
      alert('工作流执行失败');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">加载工作流中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error}</p>
        <button
          onClick={fetchWorkflows}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--coze-bg-secondary)' }}>
      {/* Header */}
      <div className="coze-card border-0 rounded-none shadow-sm">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div>
              <h1 className="text-3xl font-bold" style={{ color: 'var(--coze-text-primary)' }}>
                工作流管理
              </h1>
              <p className="mt-2 text-base" style={{ color: 'var(--coze-text-secondary)' }}>
                创建和管理您的自动化工作流
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={fetchWorkflows}
                disabled={loading}
                className="coze-btn coze-btn-secondary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {loading ? '刷新中...' : '刷新'}
              </button>
              <button
                onClick={onCreateWorkflow}
                className="coze-btn coze-btn-primary"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                创建工作流
              </button>
              {onViewLogs && (
                <button
                  onClick={onViewLogs}
                  className="coze-btn coze-btn-success"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  系统日志
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-6 lg:px-8 py-8">

        {workflows.length === 0 ? (
          <div className="text-center py-20">
            <div className="w-24 h-24 mx-auto mb-6 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
              <svg className="w-12 h-12" style={{ color: 'var(--coze-text-muted)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3" style={{ color: 'var(--coze-text-primary)' }}>
              暂无工作流
            </h3>
            <p className="text-base mb-8 max-w-md mx-auto" style={{ color: 'var(--coze-text-secondary)' }}>
              创建您的第一个工作流，开始自动化您的业务流程
            </p>
            <button
              onClick={onCreateWorkflow}
              className="coze-btn coze-btn-primary px-8 py-3"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              创建第一个工作流
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {workflows.map((workflow) => (
              <div
                key={workflow.id}
                className="coze-card group cursor-pointer"
              >
                <div className="coze-card-header">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--coze-text-primary)' }}>
                        {workflow.name}
                      </h3>
                      <p className="text-sm line-clamp-2" style={{ color: 'var(--coze-text-secondary)' }}>
                        {workflow.description || '暂无描述'}
                      </p>
                    </div>
                    <div className="ml-4">
                      <span
                        className={`coze-badge ${
                          workflow.isActive
                            ? 'coze-badge-success'
                            : 'coze-badge'
                        }`}
                      >
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          workflow.isActive ? 'bg-green-500' : 'bg-gray-400'
                        }`}></div>
                        {workflow.isActive ? '运行中' : '已停止'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="coze-card-body">
                  <div className="flex items-center justify-between text-sm mb-6" style={{ color: 'var(--coze-text-muted)' }}>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                      <span>{workflow.nodes.length} 个节点</span>
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>{new Date(workflow.updatedAt).toLocaleDateString('zh-CN')}</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => onSelectWorkflow(workflow)}
                      className="coze-btn coze-btn-primary flex-1 min-w-0"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      编辑
                    </button>
                    <button
                      onClick={() => handleExecuteWorkflow(workflow)}
                      className={`coze-btn ${workflow.isActive ? 'coze-btn-success' : 'coze-btn-secondary'}`}
                      disabled={!workflow.isActive}
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M5 12h14l-1 7H6l-1-7z" />
                      </svg>
                      运行
                    </button>
                  </div>

                  <div className="flex flex-wrap gap-2 mt-3">
                    {onViewHistory && (
                      <button
                        onClick={() => onViewHistory(workflow)}
                        className="coze-btn coze-btn-secondary text-xs px-3 py-1"
                      >
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        历史
                      </button>
                    )}
                    <button
                      onClick={() => handleToggleActive(workflow)}
                      className={`coze-btn text-xs px-3 py-1 ${
                        workflow.isActive ? 'coze-btn-warning' : 'coze-btn-secondary'
                      }`}
                    >
                      {workflow.isActive ? '暂停' : '启动'}
                    </button>
                    <button
                      onClick={() => handleDeleteWorkflow(workflow)}
                      className="coze-btn coze-btn-danger text-xs px-3 py-1"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkflowList;

import React, { useState, useEffect } from 'react';
import type { Workflow } from '../types/workflow';
import { workflowApi } from '../services/api';

interface WorkflowListProps {
  onSelectWorkflow: (workflow: Workflow) => void;
  onCreateWorkflow: () => void;
}

const WorkflowList: React.FC<WorkflowListProps> = ({ onSelectWorkflow, onCreateWorkflow }) => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWorkflows();
  }, []);

  const fetchWorkflows = async () => {
    try {
      setLoading(true);
      const data = await workflowApi.getWorkflows();
      setWorkflows(data);
      setError(null);
    } catch (err) {
      setError('获取工作流失败');
      console.error('Error fetching workflows:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (workflow: Workflow) => {
    try {
      await workflowApi.toggleWorkflow(workflow.id, !workflow.isActive);
      await fetchWorkflows(); // Refresh the list
    } catch (err) {
      console.error('Error toggling workflow:', err);
    }
  };

  const handleDeleteWorkflow = async (workflow: Workflow) => {
    if (window.confirm(`确定要删除工作流"${workflow.name}"吗？`)) {
      try {
        await workflowApi.deleteWorkflow(workflow.id);
        await fetchWorkflows(); // Refresh the list
      } catch (err) {
        console.error('Error deleting workflow:', err);
      }
    }
  };

  const handleExecuteWorkflow = async (workflow: Workflow) => {
    try {
      await workflowApi.executeWorkflow(workflow.id);
      alert('工作流执行成功！');
    } catch (err) {
      console.error('Error executing workflow:', err);
      alert('工作流执行失败');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">加载工作流中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error}</p>
        <button
          onClick={fetchWorkflows}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">工作流管理</h1>
        <div className="flex gap-2">
          <button
            onClick={fetchWorkflows}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
            disabled={loading}
          >
            {loading ? '刷新中...' : '刷新'}
          </button>
          <button
            onClick={onCreateWorkflow}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            创建工作流
          </button>
        </div>
      </div>

      {workflows.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔧</div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">暂无工作流</h3>
          <p className="text-gray-600 mb-4">创建您的第一个工作流开始使用</p>
          <button
            onClick={onCreateWorkflow}
            className="px-6 py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            创建第一个工作流
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {workflows.map((workflow) => (
            <div
              key={workflow.id}
              className="bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">
                      {workflow.name}
                    </h3>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {workflow.description || '暂无描述'}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        workflow.isActive
                          ? 'bg-green-100 text-green-700'
                          : 'bg-gray-100 text-gray-700'
                      }`}
                    >
                      {workflow.isActive ? '运行中' : '已停止'}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <span>{workflow.nodes.length} 个节点</span>
                  <span>更新于 {new Date(workflow.updatedAt).toLocaleDateString('zh-CN')}</span>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => onSelectWorkflow(workflow)}
                    className="flex-1 px-3 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors"
                  >
                    编辑
                  </button>
                  <button
                    onClick={() => handleExecuteWorkflow(workflow)}
                    className="px-3 py-2 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors"
                    disabled={!workflow.isActive}
                  >
                    运行
                  </button>
                  <button
                    onClick={() => handleToggleActive(workflow)}
                    className={`px-3 py-2 text-sm rounded-md transition-colors ${
                      workflow.isActive
                        ? 'bg-yellow-500 text-white hover:bg-yellow-600'
                        : 'bg-gray-500 text-white hover:bg-gray-600'
                    }`}
                  >
                    {workflow.isActive ? '暂停' : '启动'}
                  </button>
                  <button
                    onClick={() => handleDeleteWorkflow(workflow)}
                    className="px-3 py-2 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default WorkflowList;

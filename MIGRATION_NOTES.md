# FlowCustom 插件化架构迁移说明

## 迁移概述

本次迁移将FlowCustom从传统的单体架构转换为完全插件化的架构。以下是详细的迁移说明和代码清理指南。

## 🔄 架构变更

### 旧架构 → 新架构

```
旧架构:
src/FlowCustom.Nodes/          → Plugins/FlowCustom.Plugins.*/
src/FlowCustom.Engine/         → Core/FlowCustom.Engine/ (重构)
src/FlowCustom.Api/            → backend/ (简化)

新架构:
Core/                          # 核心框架
├── FlowCustom.PluginHost/     # 插件宿主
└── FlowCustom.Engine/         # 新执行引擎

Plugins/                       # 插件目录
├── FlowCustom.Plugins.Core/   # 核心插件
└── FlowCustom.Plugins.Http/   # HTTP插件

PluginSDK/                     # 插件开发SDK
└── FlowCustom.SDK/            # 插件接口
```

## 📦 节点迁移对照表

| 旧节点文件 | 新插件位置 | 状态 |
|-----------|-----------|------|
| `ManualTriggerNode.cs` | `Plugins/FlowCustom.Plugins.Core/ManualTriggerPlugin.cs` | ✅ 已迁移 |
| `HttpRequestNode.cs` | `Plugins/FlowCustom.Plugins.Http/HttpRequestPlugin.cs` | ✅ 已迁移 |
| `SetNode.cs` | `Plugins/FlowCustom.Plugins.Core/SetNodePlugin.cs` | ✅ 已迁移 |
| `ConditionalNode.cs` | `Plugins/FlowCustom.Plugins.Core/ConditionalPlugin.cs` | ✅ 已迁移 |
| `ScriptExecutorNode.cs` | `Plugins/FlowCustom.Plugins.Core/ScriptExecutorPlugin.cs` | ✅ 已迁移 |
| `WebhookTriggerNode.cs` | `Plugins/FlowCustom.Plugins.Http/WebhookTriggerPlugin.cs` | ✅ 已迁移 |
| `CronTriggerNode.cs` | 待迁移 | ⏳ 计划中 |
| `FileWatcherTriggerNode.cs` | 待迁移 | ⏳ 计划中 |
| `LoopNode.cs` | 待迁移 | ⏳ 计划中 |
| `DataTransformNode.cs` | 待迁移 | ⏳ 计划中 |
| `ApiTriggerNode.cs` | 待迁移 | ⏳ 计划中 |

## 🗑️ 可以清理的旧代码

### 立即可删除的文件
```
src/FlowCustom.Nodes/ManualTriggerNode.cs     # 已迁移到插件
src/FlowCustom.Nodes/HttpRequestNode.cs       # 已迁移到插件
src/FlowCustom.Nodes/SetNode.cs               # 已迁移到插件
src/FlowCustom.Nodes/ConditionalNode.cs       # 已迁移到插件
src/FlowCustom.Nodes/ScriptExecutorNode.cs    # 已迁移到插件
src/FlowCustom.Nodes/WebhookTriggerNode.cs    # 已迁移到插件
```

### 需要重构的文件
```
src/FlowCustom.Engine/WorkflowEngine.cs       # 已被 Core/FlowCustom.Engine/PluginBasedWorkflowEngine.cs 替代
src/FlowCustom.Api/Controllers/NodesController.cs  # 已被 backend/Controllers/PluginsController.cs 替代
```

### 保留但需要更新的文件
```
src/FlowCustom.Core/                          # 保留，用于向后兼容
src/FlowCustom.Data/                          # 保留，数据访问层
src/FlowCustom.Api/Controllers/WorkflowController.cs  # 已更新为 backend/Controllers/WorkflowController.cs
```

## 🔧 API 变更

### 新增的API端点
```
GET  /api/plugins                    # 获取所有插件
GET  /api/plugins/{nodeType}         # 获取特定插件信息
GET  /api/plugins/{nodeType}/definition  # 获取节点定义
POST /api/plugins/{nodeType}/validate    # 验证节点配置
POST /api/plugins/reload             # 重新加载插件
GET  /api/system/info                # 系统信息
```

### 更新的API端点
```
POST /api/workflow/{id}/execute      # 现在使用插件化执行引擎
PUT  /api/workflow/{id}              # 现在使用插件验证
```

## 🚀 启动配置变更

### 新的Program.cs
- 集成了插件管理器
- 自动加载插件
- 使用新的执行引擎
- 保持向后兼容

### 新的项目依赖
```xml
<ProjectReference Include="..\Core\FlowCustom.PluginHost\FlowCustom.PluginHost.csproj" />
<ProjectReference Include="..\Core\FlowCustom.Engine\FlowCustom.Engine.csproj" />
<ProjectReference Include="..\PluginSDK\FlowCustom.SDK\FlowCustom.SDK.csproj" />
```

## 📋 迁移检查清单

### ✅ 已完成
- [x] 创建插件SDK和基础架构
- [x] 迁移核心节点到插件
- [x] 创建插件管理器
- [x] 更新执行引擎
- [x] 更新API控制器
- [x] 创建新的Program.cs
- [x] 增强端点配置功能

### ⏳ 待完成
- [ ] 迁移剩余节点（Cron、FileWatcher、Loop等）
- [ ] 更新前端以使用新的插件API
- [ ] 清理旧代码文件
- [ ] 更新文档和测试
- [ ] 性能测试和优化

### 🔄 迁移剩余节点的步骤

1. **创建新插件项目**（如需要）
2. **复制旧节点逻辑**到新的插件类
3. **实现插件接口**（继承NodePluginBase）
4. **更新参数定义**和端点配置
5. **添加验证逻辑**
6. **更新插件清单文件**
7. **测试插件功能**
8. **删除旧节点文件**

## 🧪 测试策略

### 单元测试
- 每个插件都应有对应的单元测试
- 测试参数验证逻辑
- 测试执行逻辑

### 集成测试
- 测试插件加载和卸载
- 测试工作流执行
- 测试API端点

### 兼容性测试
- 确保现有工作流仍能正常运行
- 测试前端集成
- 测试数据迁移

## 📚 开发指南

### 创建新插件
1. 使用插件模板：`PluginSDK/Templates/PluginTemplate.cs`
2. 参考开发文档：`PluginSDK/Documentation/PLUGIN_DEVELOPMENT_GUIDE.md`
3. 查看示例插件：`Plugins/FlowCustom.Plugins.Core/`

### 调试插件
1. 在开发环境中直接调试
2. 使用日志记录跟踪执行
3. 利用插件重新加载功能

## 🔒 向后兼容性

### 保留的功能
- 现有的工作流定义格式
- 现有的API端点（大部分）
- 现有的数据库结构

### 新增的功能
- 插件化节点系统
- 动态节点加载
- 增强的端点配置
- 插件管理API

## 📈 性能优化

### 插件加载优化
- 延迟加载非必需插件
- 插件缓存机制
- 并行加载插件

### 执行引擎优化
- 异步执行节点
- 内存管理优化
- 错误处理改进

## 🎯 下一步计划

1. **完成剩余节点迁移**
2. **前端插件集成**
3. **插件市场开发**
4. **性能监控和优化**
5. **文档完善**
6. **社区插件支持**

---

**注意**: 在删除任何旧代码之前，请确保对应的插件已经完全测试并正常工作。建议先将旧代码移动到 `Legacy/` 目录，而不是直接删除。

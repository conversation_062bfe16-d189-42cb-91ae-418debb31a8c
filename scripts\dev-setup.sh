#!/bin/bash

# FlowCustom 开发环境设置脚本
set -e

echo "🛠️  Setting up FlowCustom development environment..."

# 检查必要的工具
echo "🔍 Checking prerequisites..."

# 检查 .NET SDK
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET SDK is not installed. Please install .NET 8 SDK first."
    echo "   Download from: https://dotnet.microsoft.com/download"
    exit 1
else
    echo "✅ .NET SDK found: $(dotnet --version)"
fi

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Download from: https://nodejs.org/"
    exit 1
else
    echo "✅ Node.js found: $(node --version)"
fi

# 检查 npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed."
    exit 1
else
    echo "✅ npm found: $(npm --version)"
fi

# 还原 .NET 依赖项
echo "📦 Restoring .NET dependencies..."
dotnet restore

# 构建 .NET 项目
echo "🔨 Building .NET projects..."
dotnet build

# 安装前端依赖项
if [ -d "frontend" ]; then
    echo "📦 Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
else
    echo "⚠️  Frontend directory not found"
fi

# 创建开发用的目录
echo "📁 Creating development directories..."
mkdir -p data/logs
mkdir -p data/uploads
mkdir -p data/temp

# 设置权限
chmod 755 data/logs
chmod 755 data/uploads
chmod 755 data/temp

# 创建开发配置文件
echo "⚙️  Creating development configuration..."
cat > src/FlowCustom.Api/appsettings.Development.json << EOF
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Information",
      "FlowCustom": "Debug"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=FlowCustom_Dev;Trusted_Connection=true;TrustServerCertificate=true;",
    "Redis": "localhost:6379"
  },
  "FlowCustom": {
    "EnableMetrics": true,
    "EnableAuditLogging": true,
    "DataRetentionDays": 7,
    "Security": {
      "RequireHttps": false,
      "EnableCors": true,
      "AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"],
      "EnableRateLimiting": false
    },
    "Features": {
      "EnableUserRegistration": true,
      "EnableApiKeys": true,
      "EnableTeams": true,
      "EnableWorkflowSharing": true,
      "EnableExecutionHistory": true,
      "EnableRealTimeMonitoring": true
    }
  }
}
EOF

# 创建启动脚本
echo "📝 Creating startup scripts..."

# 后端启动脚本
cat > scripts/start-backend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting FlowCustom API..."
cd src/FlowCustom.Api
dotnet run
EOF

# 前端启动脚本
cat > scripts/start-frontend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting FlowCustom Frontend..."
cd frontend
npm run dev
EOF

# 同时启动脚本
cat > scripts/start-dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting FlowCustom development environment..."

# 启动后端（后台）
echo "Starting backend..."
cd src/FlowCustom.Api
dotnet run &
BACKEND_PID=$!

# 等待后端启动
sleep 10

# 启动前端
echo "Starting frontend..."
cd ../../frontend
npm run dev &
FRONTEND_PID=$!

echo "✅ Development environment started!"
echo "   Backend: http://localhost:5053"
echo "   Frontend: http://localhost:5173"
echo "   API Docs: http://localhost:5053/swagger"
echo ""
echo "Press Ctrl+C to stop all services"

# 等待中断信号
trap 'echo "Stopping services..."; kill $BACKEND_PID $FRONTEND_PID; exit' INT
wait
EOF

# 设置脚本权限
chmod +x scripts/start-backend.sh
chmod +x scripts/start-frontend.sh
chmod +x scripts/start-dev.sh

# 运行测试
echo "🧪 Running tests..."
dotnet test --verbosity minimal

echo ""
echo "🎉 Development environment setup completed!"
echo ""
echo "🚀 Quick start commands:"
echo "   Start backend only: ./scripts/start-backend.sh"
echo "   Start frontend only: ./scripts/start-frontend.sh"
echo "   Start both: ./scripts/start-dev.sh"
echo ""
echo "📍 Development URLs:"
echo "   Backend API: http://localhost:5053"
echo "   Frontend: http://localhost:5173"
echo "   Swagger UI: http://localhost:5053/swagger"
echo "   Health Check: http://localhost:5053/health"
echo ""
echo "📚 Default credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "🔧 Useful commands:"
echo "   Build: dotnet build"
echo "   Test: dotnet test"
echo "   Clean: dotnet clean"
echo "   Watch (auto-reload): dotnet watch run --project src/FlowCustom.Api"
echo ""

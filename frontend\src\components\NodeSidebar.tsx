import React, { useState, useEffect } from 'react';
import type { NodeDefinition } from '../types/workflow';
import { nodesApi } from '../services/api';

interface NodeSidebarProps {
  onNodeDragStart: (event: React.DragEvent, nodeType: string) => void;
}

const NodeSidebar: React.FC<NodeSidebarProps> = ({ onNodeDragStart }) => {
  const [nodes, setNodes] = useState<NodeDefinition[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNodes = async () => {
      try {
        const [nodesData, categoriesData] = await Promise.all([
          nodesApi.getNodes(),
          nodesApi.getCategories()
        ]);
        setNodes(nodesData);
        setCategories(categoriesData);
      } catch (error) {
        console.error('Failed to fetch nodes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchNodes();
  }, []);

  const getNodeIcon = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return '🌐';
      case 'set':
        return '⚙️';
      case 'webhook-trigger':
        return '🔗';
      default:
        return '📦';
    }
  };

  const nodesByCategory = categories.reduce((acc, category) => {
    acc[category] = nodes.filter(node => node.category === category);
    return acc;
  }, {} as Record<string, NodeDefinition[]>);

  if (loading) {
    return (
      <div className="node-sidebar">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-600">加载节点中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="node-sidebar">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-2">节点库</h2>
        <p className="text-sm text-gray-600">拖拽节点到画布来构建您的工作流</p>
      </div>

      {categories.map(category => (
        <div key={category} className="node-category">
          <h3 className="node-category-title">{category}</h3>
          {nodesByCategory[category]?.map(node => (
            <div
              key={node.nodeType}
              className="node-item"
              draggable
              onDragStart={(event) => onNodeDragStart(event, node.nodeType)}
            >
              <span className="text-xl">{getNodeIcon(node.nodeType)}</span>
              <div className="flex-1">
                <div className="font-medium text-gray-800">{node.displayName}</div>
                <div className="text-xs text-gray-600">{node.description}</div>
              </div>
              {node.isTrigger && (
                <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                  触发器
                </span>
              )}
            </div>
          ))}
        </div>
      ))}

      {nodes.length === 0 && !loading && (
        <div className="text-center py-8">
          <p className="text-gray-600">暂无可用节点</p>
        </div>
      )}
    </div>
  );
};

export default NodeSidebar;

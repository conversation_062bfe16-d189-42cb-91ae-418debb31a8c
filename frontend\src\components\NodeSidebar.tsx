import React, { useState, useEffect } from 'react';
import type { NodeDefinition } from '../types/workflow';
import { nodesApi } from '../services/api';

interface NodeSidebarProps {
  onNodeDragStart: (event: React.DragEvent, nodeType: string) => void;
}

const NodeSidebar: React.FC<NodeSidebarProps> = ({ onNodeDragStart }) => {
  const [nodes, setNodes] = useState<NodeDefinition[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNodes = async () => {
      try {
        const [nodesData, categoriesData] = await Promise.all([
          nodesApi.getNodes(),
          nodesApi.getCategories()
        ]);
        setNodes(nodesData);
        setCategories(categoriesData);
      } catch (error) {
        console.error('Failed to fetch nodes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchNodes();
  }, []);

  const getNodeIcon = (nodeType: string) => {
    switch (nodeType) {
      case 'http-request':
        return '🌐';
      case 'set':
        return '⚙️';
      case 'webhook-trigger':
        return '🔗';
      default:
        return '📦';
    }
  };

  const nodesByCategory = categories.reduce((acc, category) => {
    acc[category] = nodes.filter(node => node.category === category);
    return acc;
  }, {} as Record<string, NodeDefinition[]>);

  if (loading) {
    return (
      <div className="node-sidebar">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-600">加载节点中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 coze-card border-0 rounded-none shadow-sm overflow-y-auto">
      <div className="coze-card-header">
        <h2 className="text-lg font-semibold" style={{ color: 'var(--coze-text-primary)' }}>
          节点库
        </h2>
        <p className="text-sm mt-1" style={{ color: 'var(--coze-text-secondary)' }}>
          拖拽节点到画布来构建您的工作流
        </p>
      </div>

      <div className="coze-card-body space-y-6">
        {categories.map(category => (
          <div key={category} className="space-y-3">
            <h3 className="text-sm font-medium" style={{ color: 'var(--coze-text-primary)' }}>
              {category}
            </h3>
            <div className="space-y-2">
              {nodesByCategory[category]?.map(node => (
                <div
                  key={node.nodeType}
                  className="coze-card group cursor-grab active:cursor-grabbing hover:shadow-md transition-all duration-200"
                  draggable
                  onDragStart={(event) => onNodeDragStart(event, node.nodeType)}
                  style={{ padding: '12px' }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-lg flex items-center justify-center text-lg"
                         style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
                      {getNodeIcon(node.nodeType)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm" style={{ color: 'var(--coze-text-primary)' }}>
                        {node.displayName}
                      </div>
                      <div className="text-xs mt-1 line-clamp-2" style={{ color: 'var(--coze-text-secondary)' }}>
                        {node.description}
                      </div>
                    </div>
                    {node.isTrigger && (
                      <span className="coze-badge coze-badge-primary text-xs">
                        触发器
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {nodes.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--coze-bg-tertiary)' }}>
            <svg className="w-8 h-8" style={{ color: 'var(--coze-text-muted)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
            </svg>
          </div>
          <p className="text-sm" style={{ color: 'var(--coze-text-secondary)' }}>
            暂无可用节点
          </p>
        </div>
      )}
    </div>
  );
};

export default NodeSidebar;

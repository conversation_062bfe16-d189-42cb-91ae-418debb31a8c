using FlowCustomV1.Core.Models;
using FlowCustomV1.SDK.Attributes;
using FlowCustomV1.SDK.Base;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace FlowCustomV1.Plugins.Core;

/// <summary>
/// 数据设置插件
/// 用于设置和转换数据，支持表达式和变量替换
/// </summary>
[NodePlugin("set",
    DisplayName = "数据设置",
    Description = "设置值和转换数据，支持表达式计算和变量替换",
    Category = "数据",
    Icon = "📝",
    Version = "1.0.0",
    Author = "FlowCustom Team")]
public class SetNodePlugin : NodePluginBase
{
    public override string NodeType => "set";
    public override string DisplayName => "数据设置";
    public override string Description => "设置值和转换数据，支持表达式计算和变量替换";
    public override string Category => "数据";
    public override string Icon => "📝";

    public SetNodePlugin() : base()
    {
    }

    public SetNodePlugin(ILogger<SetNodePlugin> logger) : base(logger)
    {
    }

    #region 参数定义

    protected override IEnumerable<ParameterDefinition> GetParameterDefinitionsCore()
    {
        return new[]
        {
            new ParameterDefinition
            {
                Name = "values",
                DisplayName = "设置值",
                Description = "要设置的键值对",
                Type = ParameterType.Json,
                Required = true,
                DefaultValue = "{}",
                Group = "数据设置",
                Order = 1,
                HelpText = "JSON格式的键值对，支持表达式 {{variable}} 和函数调用",
                Placeholder = "{\n  \"key1\": \"value1\",\n  \"key2\": \"{{input.data}}\"\n}"
            },

            new ParameterDefinition
            {
                Name = "keepOnlySet",
                DisplayName = "仅保留设置的值",
                Description = "如果为true，只有设置的值会传递给下一个节点",
                Type = ParameterType.Boolean,
                Required = false,
                DefaultValue = false,
                Group = "数据设置",
                Order = 2,
                HelpText = "启用后，输出将只包含新设置的值，不包含输入数据"
            },

            new ParameterDefinition
            {
                Name = "mergeStrategy",
                DisplayName = "合并策略",
                Description = "当键冲突时的处理策略",
                Type = ParameterType.Select,
                Required = false,
                DefaultValue = "overwrite",
                Options = new List<ParameterOption>
                {
                    new ParameterOption { Value = "overwrite", Label = "覆盖", Description = "新值覆盖旧值" },
                    new ParameterOption { Value = "merge", Label = "合并", Description = "深度合并对象" },
                    new ParameterOption { Value = "skip", Label = "跳过", Description = "保留原值，跳过新值" },
                    new ParameterOption { Value = "array", Label = "数组", Description = "将冲突值组成数组" }
                },
                Group = "高级设置",
                Order = 3
            },

            new ParameterDefinition
            {
                Name = "enableExpressions",
                DisplayName = "启用表达式",
                Description = "是否启用表达式计算",
                Type = ParameterType.Boolean,
                Required = false,
                DefaultValue = true,
                Group = "高级设置",
                Order = 4,
                HelpText = "启用后，支持 {{variable}} 语法和简单的表达式计算"
            },

            new ParameterDefinition
            {
                Name = "outputFormat",
                DisplayName = "输出格式",
                Description = "输出数据的格式",
                Type = ParameterType.Select,
                Required = false,
                DefaultValue = "object",
                Options = new List<ParameterOption>
                {
                    new ParameterOption { Value = "object", Label = "对象", Description = "标准JSON对象" },
                    new ParameterOption { Value = "flat", Label = "扁平化", Description = "扁平化的键值对" },
                    new ParameterOption { Value = "nested", Label = "嵌套", Description = "支持点号分隔的嵌套对象" }
                },
                Group = "高级设置",
                Order = 5
            }
        };
    }

    #endregion

    #region 端点定义

    protected override IEnumerable<EndpointDefinition> GetInputDefinitionsCore()
    {
        return new[]
        {
            new EndpointDefinition
            {
                Id = "main",
                Name = "数据输入",
                Description = "要处理的输入数据",
                Type = EndpointType.Data,
                DataType = "object",
                Required = false,
                Position = new EndpointPosition { Side = "top", Offset = 50 }
            }
        };
    }

    protected override IEnumerable<EndpointDefinition> GetOutputDefinitionsCore()
    {
        return new[]
        {
            new EndpointDefinition
            {
                Id = "main",
                Name = "数据输出",
                Description = "处理后的输出数据",
                Type = EndpointType.Data,
                DataType = "object",
                Required = false,
                Position = new EndpointPosition { Side = "bottom", Offset = 50 }
            }
        };
    }

    #endregion

    #region 验证逻辑

    protected override Task OnValidateAsync(NodeConfiguration configuration,
        List<ValidationError> errors, List<ValidationWarning> warnings,
        CancellationToken cancellationToken = default)
    {
        // 验证values参数
        if (configuration.Parameters.TryGetValue("values", out var valuesObj))
        {
            var valuesJson = valuesObj?.ToString();
            if (string.IsNullOrWhiteSpace(valuesJson))
            {
                errors.Add(new ValidationError
                {
                    Code = "EMPTY_VALUES",
                    Message = "设置值不能为空",
                    Field = "values",
                    Severity = ValidationSeverity.Error
                });
            }
            else
            {
                try
                {
                    JsonDocument.Parse(valuesJson);
                }
                catch (JsonException ex)
                {
                    errors.Add(new ValidationError
                    {
                        Code = "INVALID_JSON",
                        Message = $"设置值必须是有效的JSON格式: {ex.Message}",
                        Field = "values",
                        Severity = ValidationSeverity.Error
                    });
                }
            }
        }

        return Task.CompletedTask;
    }

    #endregion

    #region 执行逻辑

    protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken = default)
    {
        // 获取参数
        var valuesJson = GetParameter<string>(context, "values", "{}");
        var keepOnlySet = GetParameter<bool>(context, "keepOnlySet", false);
        var mergeStrategy = GetParameter<string>(context, "mergeStrategy", "overwrite");
        var enableExpressions = GetParameter<bool>(context, "enableExpressions", true);
        var outputFormat = GetParameter<string>(context, "outputFormat", "object");

        Log($"开始执行数据设置: keepOnlySet={keepOnlySet}, mergeStrategy={mergeStrategy}");

        try
        {
            // 解析设置值
            var setValues = JsonSerializer.Deserialize<Dictionary<string, object>>(valuesJson);
            if (setValues == null || !setValues.Any())
            {
                return NodeExecutionResult.Error("设置值为空或无效");
            }

            // 获取输入数据
            var inputData = context.InputData.ContainsKey("main")
                ? context.InputData["main"] as Dictionary<string, object> ?? new()
                : new Dictionary<string, object>();

            // 准备输出数据
            var outputData = keepOnlySet ? new Dictionary<string, object>() : new Dictionary<string, object>(inputData);

            // 处理设置值
            foreach (var kvp in setValues)
            {
                var processedValue = enableExpressions 
                    ? await ProcessValueAsync(kvp.Value, context, cancellationToken)
                    : kvp.Value;

                // 根据合并策略处理键冲突
                if (outputData.ContainsKey(kvp.Key))
                {
                    outputData[kvp.Key] = MergeValues(outputData[kvp.Key], processedValue, mergeStrategy);
                }
                else
                {
                    outputData[kvp.Key] = processedValue;
                }
            }

            // 根据输出格式处理数据
            var finalOutput = FormatOutput(outputData, outputFormat);

            // 添加元数据
            finalOutput["_metadata"] = new Dictionary<string, object>
            {
                ["processedAt"] = DateTime.UtcNow,
                ["nodeId"] = context.NodeId,
                ["setKeysCount"] = setValues.Count,
                ["outputKeysCount"] = finalOutput.Count - 1, // 减去metadata
                ["mergeStrategy"] = mergeStrategy,
                ["outputFormat"] = outputFormat
            };

            Log($"数据设置完成: 设置了 {setValues.Count} 个键，输出 {finalOutput.Count - 1} 个键");
            return NodeExecutionResult.Success(finalOutput, "main");
        }
        catch (JsonException ex)
        {
            Log($"JSON解析失败: {ex.Message}", LogLevel.Error);
            return NodeExecutionResult.Error($"JSON解析失败: {ex.Message}");
        }
        catch (Exception ex)
        {
            Log($"数据设置失败: {ex.Message}", LogLevel.Error);
            return NodeExecutionResult.Error($"数据设置失败: {ex.Message}");
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 处理值，支持表达式和变量替换
    /// </summary>
    private async Task<object> ProcessValueAsync(object value, NodeExecutionContext context, CancellationToken cancellationToken)
    {
        if (value is not string stringValue)
            return value;

        // 处理表达式 {{variable}}
        var expressionPattern = @"\{\{([^}]+)\}\}";
        var matches = Regex.Matches(stringValue, expressionPattern);

        if (!matches.Any())
            return value;

        var result = stringValue;
        foreach (Match match in matches)
        {
            var expression = match.Groups[1].Value.Trim();
            var evaluatedValue = await EvaluateExpressionAsync(expression, context, cancellationToken);
            result = result.Replace(match.Value, evaluatedValue?.ToString() ?? "");
        }

        // 尝试转换为适当的类型
        return ConvertToAppropriateType(result);
    }

    /// <summary>
    /// 评估表达式
    /// </summary>
    private async Task<object?> EvaluateExpressionAsync(string expression, NodeExecutionContext context, CancellationToken cancellationToken)
    {
        await Task.CompletedTask; // 占位符，实际可以实现更复杂的表达式计算

        // 简单的变量替换
        if (expression.StartsWith("input."))
        {
            var path = expression.Substring(6);
            return GetNestedValue(context.InputData.GetValueOrDefault("main"), path);
        }

        if (expression.StartsWith("variables."))
        {
            var path = expression.Substring(10);
            return GetNestedValue(context.Variables, path);
        }

        // 内置函数
        return expression switch
        {
            "now" => DateTime.UtcNow,
            "today" => DateTime.UtcNow.Date,
            "guid" => Guid.NewGuid().ToString(),
            "timestamp" => DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            _ => expression
        };
    }

    /// <summary>
    /// 获取嵌套值
    /// </summary>
    private object? GetNestedValue(object? obj, string path)
    {
        if (obj == null) return null;

        var parts = path.Split('.');
        var current = obj;

        foreach (var part in parts)
        {
            if (current is Dictionary<string, object> dict)
            {
                current = dict.GetValueOrDefault(part);
            }
            else
            {
                return null;
            }
        }

        return current;
    }

    /// <summary>
    /// 合并值
    /// </summary>
    private object MergeValues(object existingValue, object newValue, string strategy)
    {
        return strategy switch
        {
            "overwrite" => newValue,
            "skip" => existingValue,
            "array" => new object[] { existingValue, newValue },
            "merge" when existingValue is Dictionary<string, object> existingDict && 
                        newValue is Dictionary<string, object> newDict => MergeDictionaries(existingDict, newDict),
            _ => newValue
        };
    }

    /// <summary>
    /// 合并字典
    /// </summary>
    private Dictionary<string, object> MergeDictionaries(Dictionary<string, object> existing, Dictionary<string, object> newDict)
    {
        var result = new Dictionary<string, object>(existing);
        foreach (var kvp in newDict)
        {
            result[kvp.Key] = kvp.Value;
        }
        return result;
    }

    /// <summary>
    /// 格式化输出
    /// </summary>
    private Dictionary<string, object> FormatOutput(Dictionary<string, object> data, string format)
    {
        return format switch
        {
            "flat" => FlattenDictionary(data),
            "nested" => CreateNestedDictionary(data),
            _ => data
        };
    }

    /// <summary>
    /// 扁平化字典
    /// </summary>
    private Dictionary<string, object> FlattenDictionary(Dictionary<string, object> data, string prefix = "")
    {
        var result = new Dictionary<string, object>();
        foreach (var kvp in data)
        {
            var key = string.IsNullOrEmpty(prefix) ? kvp.Key : $"{prefix}.{kvp.Key}";
            if (kvp.Value is Dictionary<string, object> nestedDict)
            {
                var flattened = FlattenDictionary(nestedDict, key);
                foreach (var flatKvp in flattened)
                {
                    result[flatKvp.Key] = flatKvp.Value;
                }
            }
            else
            {
                result[key] = kvp.Value;
            }
        }
        return result;
    }

    /// <summary>
    /// 创建嵌套字典
    /// </summary>
    private Dictionary<string, object> CreateNestedDictionary(Dictionary<string, object> data)
    {
        var result = new Dictionary<string, object>();
        foreach (var kvp in data)
        {
            SetNestedValue(result, kvp.Key, kvp.Value);
        }
        return result;
    }

    /// <summary>
    /// 设置嵌套值
    /// </summary>
    private void SetNestedValue(Dictionary<string, object> dict, string path, object value)
    {
        var parts = path.Split('.');
        var current = dict;

        for (int i = 0; i < parts.Length - 1; i++)
        {
            if (!current.ContainsKey(parts[i]))
            {
                current[parts[i]] = new Dictionary<string, object>();
            }
            current = (Dictionary<string, object>)current[parts[i]];
        }

        current[parts[^1]] = value;
    }

    /// <summary>
    /// 转换为适当的类型
    /// </summary>
    private object ConvertToAppropriateType(string value)
    {
        if (bool.TryParse(value, out var boolValue))
            return boolValue;

        if (int.TryParse(value, out var intValue))
            return intValue;

        if (double.TryParse(value, out var doubleValue))
            return doubleValue;

        if (DateTime.TryParse(value, out var dateValue))
            return dateValue;

        return value;
    }

    #endregion
}

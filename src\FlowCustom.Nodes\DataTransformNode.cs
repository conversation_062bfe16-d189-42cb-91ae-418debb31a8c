using FlowCustom.Core.Interfaces;
using FlowCustom.Core.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace FlowCustom.Nodes;

/// <summary>
/// 数据转换节点
/// </summary>
public class DataTransformNode : INode
{
    private readonly ILogger<DataTransformNode> _logger;

    public DataTransformNode(ILogger<DataTransformNode> logger)
    {
        _logger = logger;
    }

    public string NodeType => "data-transform";

    public string DisplayName => "数据转换";

    public string Description => "对数据进行各种转换操作";

    public string Category => "数据";

    public string Icon => "transform";

    public NodeParameterDefinition[] GetParameterDefinitions()
    {
        return new[]
        {
            new NodeParameterDefinition
            {
                Name = "transformType",
                DisplayName = "转换类型",
                Description = "选择数据转换类型",
                Type = ParameterType.Select,
                Required = true,
                DefaultValue = "map",
                Options = new[] { "map", "filter", "sort", "group", "aggregate", "format" }
            },
            new NodeParameterDefinition
            {
                Name = "inputData",
                DisplayName = "输入数据",
                Description = "要转换的数据（JSON格式）",
                Type = ParameterType.Object,
                Required = true,
                DefaultValue = new object[] { 
                    new { name = "张三", age = 25, score = 85 },
                    new { name = "李四", age = 30, score = 92 },
                    new { name = "王五", age = 28, score = 78 }
                }
            },
            new NodeParameterDefinition
            {
                Name = "transformExpression",
                DisplayName = "转换表达式",
                Description = "转换逻辑表达式或映射规则",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = "item.name + ' (' + item.age + '岁)'"
            },
            new NodeParameterDefinition
            {
                Name = "filterCondition",
                DisplayName = "过滤条件",
                Description = "过滤条件表达式（仅filter类型使用）",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = "item.age >= 25"
            },
            new NodeParameterDefinition
            {
                Name = "sortField",
                DisplayName = "排序字段",
                Description = "排序的字段名（仅sort类型使用）",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = "age"
            },
            new NodeParameterDefinition
            {
                Name = "sortOrder",
                DisplayName = "排序顺序",
                Description = "排序顺序",
                Type = ParameterType.Select,
                Required = false,
                DefaultValue = "asc",
                Options = new[] { "asc", "desc" }
            },
            new NodeParameterDefinition
            {
                Name = "groupByField",
                DisplayName = "分组字段",
                Description = "分组的字段名（仅group类型使用）",
                Type = ParameterType.String,
                Required = false,
                DefaultValue = "category"
            }
        };
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        WorkflowNode node,
        FlowCustom.Core.Models.ExecutionContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var transformType = GetParameter<string>(node, "transformType", "map");
            var inputData = GetParameter<object>(node, "inputData", new object[0]);
            var transformExpression = GetParameter<string>(node, "transformExpression", "");
            var filterCondition = GetParameter<string>(node, "filterCondition", "");
            var sortField = GetParameter<string>(node, "sortField", "");
            var sortOrder = GetParameter<string>(node, "sortOrder", "asc");
            var groupByField = GetParameter<string>(node, "groupByField", "");

            _logger.LogInformation("开始执行数据转换节点: {NodeId}, 类型: {TransformType}", node.Id, transformType);

            object result = transformType switch
            {
                "map" => await TransformMap(inputData, transformExpression),
                "filter" => await TransformFilter(inputData, filterCondition),
                "sort" => await TransformSort(inputData, sortField, sortOrder),
                "group" => await TransformGroup(inputData, groupByField),
                "aggregate" => await TransformAggregate(inputData),
                "format" => await TransformFormat(inputData, transformExpression),
                _ => throw new ArgumentException($"不支持的转换类型: {transformType}")
            };

            _logger.LogInformation("数据转换节点执行完成: {NodeId}, 类型: {TransformType}", node.Id, transformType);

            var outputData = new Dictionary<string, object>
            {
                ["result"] = result,
                ["transformType"] = transformType,
                ["inputCount"] = GetDataCount(inputData),
                ["outputCount"] = GetDataCount(result),
                ["transformedAt"] = DateTime.UtcNow
            };

            return NodeExecutionResult.CreateSuccess(outputData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据转换节点执行失败: {NodeId}", node.Id);
            return NodeExecutionResult.CreateError($"数据转换节点执行失败: {ex.Message}");
        }
    }

    public ValidationResult ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        if (!parameters.TryGetValue("transformType", out var transformTypeObj) || 
            string.IsNullOrWhiteSpace(transformTypeObj?.ToString()))
        {
            errors.Add("转换类型不能为空");
        }
        else
        {
            var transformType = transformTypeObj.ToString();
            if (!new[] { "map", "filter", "sort", "group", "aggregate", "format" }.Contains(transformType))
            {
                errors.Add("不支持的转换类型");
            }
        }

        if (!parameters.TryGetValue("inputData", out var inputDataObj) || inputDataObj == null)
        {
            errors.Add("输入数据不能为空");
        }

        return errors.Count == 0 ? ValidationResult.Success() : ValidationResult.Failure(errors.ToArray());
    }

    private async Task<object> TransformMap(object inputData, string expression)
    {
        await Task.CompletedTask;

        if (inputData is not IEnumerable<object> enumerable)
        {
            return new[] { ApplyExpression(inputData, expression) };
        }

        return enumerable.Select(item => ApplyExpression(item, expression)).ToArray();
    }

    private async Task<object> TransformFilter(object inputData, string condition)
    {
        await Task.CompletedTask;

        if (inputData is not IEnumerable<object> enumerable)
        {
            return EvaluateCondition(inputData, condition) ? new[] { inputData } : new object[0];
        }

        return enumerable.Where(item => EvaluateCondition(item, condition)).ToArray();
    }

    private async Task<object> TransformSort(object inputData, string sortField, string sortOrder)
    {
        await Task.CompletedTask;

        if (inputData is not IEnumerable<object> enumerable)
        {
            return new[] { inputData };
        }

        var items = enumerable.ToArray();
        
        if (string.IsNullOrWhiteSpace(sortField))
        {
            return items;
        }

        try
        {
            var sorted = items.OrderBy(item => GetFieldValue(item, sortField));
            return sortOrder == "desc" ? sorted.Reverse().ToArray() : sorted.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "排序失败，返回原始数据");
            return items;
        }
    }

    private async Task<object> TransformGroup(object inputData, string groupByField)
    {
        await Task.CompletedTask;

        if (inputData is not IEnumerable<object> enumerable)
        {
            return new Dictionary<string, object[]> { ["default"] = new[] { inputData } };
        }

        if (string.IsNullOrWhiteSpace(groupByField))
        {
            return new Dictionary<string, object[]> { ["all"] = enumerable.ToArray() };
        }

        try
        {
            var groups = enumerable
                .GroupBy(item => GetFieldValue(item, groupByField)?.ToString() ?? "null")
                .ToDictionary(g => g.Key, g => g.ToArray());

            return groups;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "分组失败，返回原始数据");
            return new Dictionary<string, object[]> { ["error"] = enumerable.ToArray() };
        }
    }

    private async Task<object> TransformAggregate(object inputData)
    {
        await Task.CompletedTask;

        if (inputData is not IEnumerable<object> enumerable)
        {
            return new { count = 1, items = new[] { inputData } };
        }

        var items = enumerable.ToArray();
        var numericValues = new List<double>();

        foreach (var item in items)
        {
            if (item is double d) numericValues.Add(d);
            else if (item is int i) numericValues.Add(i);
            else if (item is float f) numericValues.Add(f);
            else if (double.TryParse(item?.ToString(), out var parsed)) numericValues.Add(parsed);
        }

        return new
        {
            count = items.Length,
            numericCount = numericValues.Count,
            sum = numericValues.Sum(),
            average = numericValues.Count > 0 ? numericValues.Average() : 0,
            min = numericValues.Count > 0 ? numericValues.Min() : 0,
            max = numericValues.Count > 0 ? numericValues.Max() : 0,
            items = items
        };
    }

    private async Task<object> TransformFormat(object inputData, string formatExpression)
    {
        await Task.CompletedTask;

        if (inputData is not IEnumerable<object> enumerable)
        {
            return FormatItem(inputData, formatExpression);
        }

        return enumerable.Select(item => FormatItem(item, formatExpression)).ToArray();
    }

    private object ApplyExpression(object item, string expression)
    {
        if (string.IsNullOrWhiteSpace(expression))
        {
            return item;
        }

        try
        {
            // 简单的表达式处理
            var result = expression;
            
            if (item != null)
            {
                var properties = GetObjectProperties(item);
                foreach (var prop in properties)
                {
                    var placeholder = $"item.{prop.Key}";
                    if (result.Contains(placeholder))
                    {
                        result = result.Replace(placeholder, prop.Value?.ToString() ?? "");
                    }
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "表达式应用失败: {Expression}", expression);
            return item;
        }
    }

    private bool EvaluateCondition(object item, string condition)
    {
        if (string.IsNullOrWhiteSpace(condition))
        {
            return true;
        }

        try
        {
            // 简单的条件评估
            var evaluatedCondition = condition;
            
            if (item != null)
            {
                var properties = GetObjectProperties(item);
                foreach (var prop in properties)
                {
                    var placeholder = $"item.{prop.Key}";
                    if (evaluatedCondition.Contains(placeholder))
                    {
                        var value = prop.Value?.ToString() ?? "null";
                        if (prop.Value is string)
                        {
                            value = $"\"{value}\"";
                        }
                        evaluatedCondition = evaluatedCondition.Replace(placeholder, value);
                    }
                }
            }

            // 简单的比较操作
            if (evaluatedCondition.Contains(">="))
            {
                var parts = evaluatedCondition.Split(">=");
                if (parts.Length == 2 && 
                    double.TryParse(parts[0].Trim(), out var left) && 
                    double.TryParse(parts[1].Trim(), out var right))
                {
                    return left >= right;
                }
            }
            // 可以添加更多条件操作...

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "条件评估失败: {Condition}", condition);
            return true;
        }
    }

    private object? GetFieldValue(object item, string fieldName)
    {
        if (item == null || string.IsNullOrWhiteSpace(fieldName))
        {
            return null;
        }

        var properties = GetObjectProperties(item);
        return properties.TryGetValue(fieldName, out var value) ? value : null;
    }

    private object FormatItem(object item, string formatExpression)
    {
        if (string.IsNullOrWhiteSpace(formatExpression))
        {
            return item;
        }

        return ApplyExpression(item, formatExpression);
    }

    private Dictionary<string, object?> GetObjectProperties(object obj)
    {
        var properties = new Dictionary<string, object?>();

        if (obj == null)
        {
            return properties;
        }

        try
        {
            if (obj is JsonElement jsonElement)
            {
                foreach (var property in jsonElement.EnumerateObject())
                {
                    properties[property.Name] = property.Value.ToString();
                }
            }
            else
            {
                var type = obj.GetType();
                foreach (var prop in type.GetProperties())
                {
                    try
                    {
                        properties[prop.Name] = prop.GetValue(obj);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "无法获取属性值: {PropertyName}", prop.Name);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取对象属性失败");
        }

        return properties;
    }

    private int GetDataCount(object data)
    {
        if (data is IEnumerable<object> enumerable)
        {
            return enumerable.Count();
        }
        return data != null ? 1 : 0;
    }

    private T GetParameter<T>(WorkflowNode node, string parameterName, T defaultValue = default!)
    {
        if (node.Parameters.TryGetValue(parameterName, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                if (typeof(T) == typeof(string))
                {
                    return (T)(object)(value?.ToString() ?? string.Empty);
                }

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法转换参数 {ParameterName} 的值 '{Value}'，使用默认值", parameterName, value);
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

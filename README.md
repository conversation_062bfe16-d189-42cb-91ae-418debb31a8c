# FlowCustom - 插件化工作流自动化平台

一个基于 .NET 和 React 的现代化工作流自动化平台，采用完全插件化架构，支持可视化流程设计、动态节点加载和自动化执行。

## 🚀 核心特性

### 插件化架构
- 🔌 **完全插件化** - 框架与节点完全分离，支持动态加载
- 🔥 **热插拔** - 运行时加载新插件，无需重启系统
- 🛡️ **沙箱隔离** - 插件在独立环境中运行，确保系统稳定性
- 🎯 **标准化接口** - 统一的插件开发接口和规范

### 可视化设计
- 🎨 **拖拽式设计器** - 直观的可视化工作流设计界面
- 🔗 **灵活端点配置** - 支持自定义输入输出端点，灵活连线
- 🎛️ **实时配置** - 双击节点即可配置参数和端点
- 🌈 **智能连线** - 根据端点类型自动设置连线颜色

### 执行引擎
- ⚡ **高性能执行** - 基于插件的异步执行引擎
- 📊 **实时监控** - 完整的执行历史记录和日志
- 🔄 **错误处理** - 完善的错误处理和重试机制
- ⏹️ **取消支持** - 支持工作流执行取消

### 开发体验
- 📚 **完整文档** - 详细的插件开发指南和API文档
- 🛠️ **开发工具** - 插件模板、示例代码和调试工具
- 🧪 **测试支持** - 单元测试和集成测试框架
- 🌐 **现代化界面** - 基于 React 的响应式用户界面

## 🏗️ 技术栈

### 后端
- **.NET 8.0** - 现代化的 .NET 平台
- **ASP.NET Core Web API** - RESTful API 服务
- **插件系统** - 基于 AssemblyLoadContext 的动态加载
- **Entity Framework Core** - ORM 数据访问
- **SQLite** - 轻量级数据库

### 前端
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的构建工具
- **ReactFlow** - 专业的流程图组件
- **Tailwind CSS** - 实用优先的 CSS 框架

## 📁 项目结构

```
FlowCustom/
├── Core/                           # 🏗️ 核心框架
│   ├── FlowCustom.Core/           # 核心库
│   ├── FlowCustom.Engine/         # 执行引擎
│   ├── FlowCustom.Api/            # Web API
│   └── FlowCustom.PluginHost/     # 插件宿主
├── Plugins/                        # 🔌 插件目录
│   ├── FlowCustom.Plugins.Core/   # 核心插件（触发器、执行器等）
│   ├── FlowCustom.Plugins.Http/   # HTTP 插件
│   ├── FlowCustom.Plugins.Database/ # 数据库插件
│   └── FlowCustom.Plugins.Email/  # 邮件插件
├── PluginSDK/                      # 🛠️ 插件开发SDK
│   ├── FlowCustom.SDK/            # 插件开发接口
│   ├── Templates/                 # 插件模板
│   └── Documentation/             # 开发文档
├── Runtime/                        # 🚀 运行时目录
│   ├── plugins/                   # 已安装插件
│   ├── configs/                   # 插件配置
│   └── cache/                     # 插件缓存
├── backend/                        # 🔙 后端服务（兼容性保留）
├── frontend/                       # 🎨 前端应用
└── docs/                          # 📚 项目文档
```

## 🚀 快速开始

### 环境要求

- **.NET 8.0 SDK** - 用于后端开发
- **Node.js 18+** - 用于前端开发
- **Visual Studio 2022** 或 **VS Code** - 推荐的开发环境

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd FlowCustom
   ```

2. **启动后端**
   ```bash
   cd backend
   dotnet restore
   dotnet run
   ```
   后端将在 http://localhost:5053 启动

3. **启动前端**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```
   前端将在 http://localhost:5173 启动

4. **访问应用**
   打开浏览器访问 http://localhost:5173

## 🔌 插件开发

### 快速创建插件

1. **创建插件项目**
   ```bash
   dotnet new classlib -n MyCompany.MyPlugin
   cd MyCompany.MyPlugin
   dotnet add package FlowCustom.SDK
   ```

2. **实现插件类**
   ```csharp
   using FlowCustom.SDK;

   public class MyAwesomePlugin : NodePluginBase
   {
       public override string NodeType => "my-awesome-node";
       public override string DisplayName => "我的超棒节点";
       public override string Description => "这是一个超棒的自定义节点";
       public override string Category => "自定义";
       
       protected override async Task<NodeExecutionResult> OnExecuteAsync(NodeExecutionContext context)
       {
           // 实现您的业务逻辑
           var message = GetParameter<string>(context, "message", "Hello");
           
           Log($"处理消息: {message}");
           
           return NodeExecutionResult.Success(new Dictionary<string, object>
           {
               ["result"] = $"处理完成: {message}",
               ["timestamp"] = DateTime.UtcNow
           });
       }
       
       protected override List<ParameterDefinition> GetParameterDefinitions()
       {
           return new List<ParameterDefinition>
           {
               new ParameterDefinition
               {
                   Name = "message",
                   DisplayName = "消息内容",
                   Description = "要处理的消息",
                   Type = ParameterType.String,
                   Required = true,
                   DefaultValue = "Hello, World!"
               }
           };
       }
   }
   ```

3. **创建插件清单**
   ```json
   {
     "id": "MyCompany.MyPlugin",
     "name": "我的插件",
     "version": "1.0.0",
     "author": "我的公司",
     "description": "插件描述",
     "main": "MyCompany.MyPlugin.dll",
     "dependencies": ["FlowCustom.SDK@^1.0.0"],
     "supportedFrameworks": ["net8.0"],
     "nodes": [
       {
         "type": "my-awesome-node",
         "class": "MyCompany.MyPlugin.MyAwesomePlugin"
       }
     ]
   }
   ```

4. **部署插件**
   ```bash
   # 编译插件
   dotnet build -c Release
   
   # 创建插件目录
   mkdir -p Runtime/plugins/MyCompany.MyPlugin
   
   # 复制文件
   cp bin/Release/net8.0/MyCompany.MyPlugin.dll Runtime/plugins/MyCompany.MyPlugin/
   cp plugin.json Runtime/plugins/MyCompany.MyPlugin/
   
   # 重新加载插件
   curl -X POST http://localhost:5053/api/plugins/reload
   ```

### 插件开发指南

详细的插件开发指南请参考：[插件开发文档](PluginSDK/Documentation/PLUGIN_DEVELOPMENT_GUIDE.md)

## 🎯 下一步开发流程

### 添加新节点的标准流程

1. **需求分析**
   - 确定节点功能和用途
   - 设计输入输出参数
   - 规划端点配置

2. **插件开发**
   - 使用插件模板创建项目
   - 实现节点业务逻辑
   - 定义参数和端点
   - 编写验证逻辑

3. **前端组件**（可选）
   - 创建自定义配置组件
   - 实现参数输入界面
   - 添加图标和样式

4. **测试验证**
   - 编写单元测试
   - 集成测试验证
   - 性能测试

5. **部署发布**
   - 编译生成插件包
   - 部署到插件目录
   - 重新加载插件

### 系统扩展方向

- 🌐 **插件市场** - 构建插件生态系统
- 🔐 **权限管理** - 细粒度的权限控制
- 📈 **性能监控** - 实时性能指标和告警
- 🌍 **多语言支持** - 国际化和本地化
- ☁️ **云端部署** - 容器化和云原生支持

## 🛠️ 开发工具

### 插件开发工具
- **插件模板** - 快速创建插件项目
- **代码生成器** - 自动生成样板代码
- **调试工具** - 插件调试和测试工具
- **打包工具** - 插件打包和发布工具

### API 文档
- **Swagger UI** - http://localhost:5053/swagger
- **插件API** - http://localhost:5053/api/plugins
- **工作流API** - http://localhost:5053/api/workflow

## 🤝 贡献指南

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送到分支** (`git push origin feature/AmazingFeature`)
5. **创建 Pull Request**

### 插件贡献
- 欢迎贡献新的插件
- 请遵循插件开发规范
- 提供完整的文档和测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**FlowCustom** - 让工作流自动化变得简单而强大 🚀

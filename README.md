# FlowCustom - 企业级工作流自动化平台

FlowCustom 是一个功能强大的工作流自动化平台，类似于 n8n，专为企业级应用而设计。它提供了可视化的工作流编辑器、丰富的节点库、实时监控和完整的用户管理系统。

## 🚀 核心特性

### ✅ 已完成功能

1. **可视化工作流编辑器**
   - 基于 React Flow 的拖拽式编辑器
   - 节点侧边栏，支持拖拽添加节点
   - 可视化节点连接和数据流
   - 缩放、平移、小地图等交互功能

2. **丰富的节点系统**
   - HTTP Request 节点 - 发送 HTTP 请求
   - Set 节点 - 数据设置和转换
   - Webhook Trigger 节点 - Webhook 触发器
   - Cron Trigger 节点 - 定时任务触发器
   - File Watcher Trigger 节点 - 文件监控触发器
   - API Trigger 节点 - API 调用触发器

3. **强大的工作流引擎**
   - 异步执行引擎
   - 错误处理和重试机制
   - 条件分支和循环支持
   - 数据传递和转换

4. **触发器和调度系统**
   - 多种触发器类型
   - Cron 表达式支持
   - 文件系统监控
   - API 端点触发

5. **执行监控和调试**
   - 实时执行监控
   - 详细的执行日志
   - 性能指标收集
   - 错误追踪和调试

6. **用户管理和权限控制**
   - JWT 身份认证
   - 基于角色的权限控制
   - API 密钥管理
   - 审计日志记录

7. **API 和部署支持**
   - RESTful API
   - Docker 容器化
   - Docker Compose 部署
   - Nginx 反向代理
   - 健康检查端点

## 🏗️ 技术架构

### 后端技术栈
- **.NET 8** - 现代化的 C# 开发平台
- **ASP.NET Core** - Web API 框架
- **Entity Framework Core** - ORM 数据访问
- **JWT** - 身份认证
- **Swagger/OpenAPI** - API 文档

### 前端技术栈
- **React 18** - 现代化的前端框架
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS** - 实用优先的 CSS 框架
- **React Flow** - 流程图和节点编辑器
- **Axios** - HTTP 客户端

### 部署技术栈
- **Docker** - 容器化部署
- **Docker Compose** - 多容器编排
- **Nginx** - 反向代理和静态文件服务
- **PostgreSQL** - 关系型数据库
- **Redis** - 缓存和会话存储

## 📁 项目结构

```
FlowCustom/
├── src/
│   ├── FlowCustom.Api/          # Web API 项目
│   ├── FlowCustom.Core/         # 核心模型和接口
│   ├── FlowCustom.Engine/       # 工作流执行引擎
│   ├── FlowCustom.Data/         # 数据访问层
│   └── FlowCustom.Nodes/        # 节点实现
├── frontend/                    # React 前端应用
├── tests/                       # 单元测试
├── scripts/                     # 部署和开发脚本
├── nginx/                       # Nginx 配置
├── docker-compose.yml           # Docker Compose 配置
├── Dockerfile                   # Docker 镜像构建
└── README.md                    # 项目文档
```

## 🚀 快速开始

### 开发环境设置

1. **安装依赖**
   ```bash
   # 运行开发环境设置脚本
   chmod +x scripts/dev-setup.sh
   ./scripts/dev-setup.sh
   ```

2. **启动开发服务器**
   ```bash
   # 启动后端和前端
   ./scripts/start-dev.sh

   # 或者分别启动
   ./scripts/start-backend.sh    # 后端: http://localhost:5053
   ./scripts/start-frontend.sh   # 前端: http://localhost:5173
   ```

### 生产环境部署

1. **使用 Docker Compose 部署**
   ```bash
   # 运行部署脚本
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh
   ```

2. **手动部署**
   ```bash
   # 构建前端
   cd frontend
   npm install && npm run build

   # 启动服务
   docker-compose up -d
   ```

## 📍 访问地址

- **前端应用**: http://localhost (生产) / http://localhost:5173 (开发)
- **API 接口**: http://localhost/api (生产) / http://localhost:5053/api (开发)
- **API 文档**: http://localhost/swagger (生产) / http://localhost:5053/swagger (开发)
- **健康检查**: http://localhost/health

## 🔐 默认凭据

- **用户名**: admin
- **密码**: admin123

## 📖 开发指南

### 核心概念

1. **工作流 (Workflow)** - 由多个节点组成的自动化流程
2. **节点 (Node)** - 执行特定功能的最小单元
3. **连接 (Connection)** - 节点间的数据流连接
4. **触发器 (Trigger)** - 启动工作流执行的事件
5. **执行 (Execution)** - 工作流的一次运行实例

### 添加自定义节点

参考 `src/FlowCustom.Nodes` 项目中的示例节点实现。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

下一步建议：
好的，我看到 Id 和 WorkflowId 都是 Guid 类型。问题是我在使用 Guid.Parse() 时传入了 Guid 而不是字符串。让我修复这些错误：

现在修复 FileWatcherTriggerNode 中的相同错误：

现在重新构建项目：

我需要修复 TokenValidationResult 的命名冲突。让我使用完全限定名：

现在重新构建项目：

我需要添加 Process 的 using 语句：

现在再次构建项目：

看起来有一个正在运行的 FlowCustom.Api 进程锁定了文件。我需要先停止它：

现在重新构建项目：

太好了！构建成功了。现在让我创建一个最终的总结文档：

现在让我更新 README 文件以反映完成的功能：

现在让我更新项目结构和快速开始部分：

现在更新快速开始部分：


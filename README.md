# FlowCustom - .NET 工作流自动化平台

FlowCustom 是一个类似 n8n 的开源工作流自动化平台，使用 .NET 技术栈构建。

## 🚀 功能特性

- **可视化工作流编辑器** - 基于 React 的拖拽式流程设计器
- **丰富的节点系统** - 支持 HTTP 请求、数据库操作、文件处理等
- **多种触发方式** - Webhook、定时任务、文件监控、API 调用
- **实时执行监控** - 完整的执行历史和调试信息
- **可扩展架构** - 插件化节点系统，支持自定义节点
- **企业级安全** - 用户认证、权限控制、数据加密
- **容器化部署** - Docker 支持，易于部署和扩展

## 🏗️ 架构设计

### 核心模块

```
FlowCustom/
├── src/
│   ├── FlowCustom.Core/          # 核心模型和接口
│   ├── FlowCustom.Engine/        # 工作流执行引擎
│   ├── FlowCustom.Nodes/         # 节点系统实现
│   ├── FlowCustom.Data/          # 数据访问层
│   └── FlowCustom.Api/           # Web API 服务
├── frontend/                     # React 前端应用
├── tests/                        # 单元测试
└── docs/                         # 文档
```

### 技术栈

- **后端**: .NET 8, ASP.NET Core, Entity Framework Core
- **前端**: React 18, TypeScript, React Flow
- **数据库**: PostgreSQL / SQL Server
- **缓存**: Redis
- **消息队列**: RabbitMQ
- **容器化**: Docker, Docker Compose

## 🔧 快速开始

### 环境要求

- .NET 8 SDK
- Node.js 18+
- PostgreSQL 或 SQL Server
- Redis (可选)

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd FlowCustom
```

2. 启动后端服务
```bash
dotnet restore
dotnet run --project src/FlowCustom.Api
```

3. 启动前端开发服务器
```bash
cd frontend
npm install
npm start
```

4. 访问应用
- 前端: http://localhost:3000
- API: http://localhost:5000

## 📖 开发指南

### 核心概念

1. **工作流 (Workflow)** - 由多个节点组成的自动化流程
2. **节点 (Node)** - 执行特定功能的最小单元
3. **连接 (Connection)** - 节点间的数据流连接
4. **触发器 (Trigger)** - 启动工作流执行的事件
5. **执行 (Execution)** - 工作流的一次运行实例

### 添加自定义节点

参考 `src/FlowCustom.Nodes` 项目中的示例节点实现。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

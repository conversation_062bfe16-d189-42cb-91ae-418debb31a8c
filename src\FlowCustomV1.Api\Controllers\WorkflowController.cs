using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustomV1.Api.Controllers;

/// <summary>
/// 工作流管理控制器
/// 提供工作流的CRUD操作和执行功能
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class WorkflowController : ControllerBase
{
    private readonly IWorkflowEngine _workflowEngine;
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<WorkflowController> _logger;

    public WorkflowController(
        IWorkflowEngine workflowEngine,
        IPluginManager pluginManager,
        ILogger<WorkflowController> logger)
    {
        _workflowEngine = workflowEngine ?? throw new ArgumentNullException(nameof(workflowEngine));
        _pluginManager = pluginManager ?? throw new ArgumentNullException(nameof(pluginManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 执行工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="request">执行请求</param>
    /// <returns>执行结果</returns>
    [HttpPost("{workflowId}/execute")]
    public async Task<ActionResult<object>> ExecuteWorkflow(
        string workflowId,
        [FromBody] WorkflowExecutionRequest request)
    {
        try
        {
            _logger.LogInformation("开始执行工作流: {WorkflowId}", workflowId);

            // 设置工作流ID
            request.WorkflowId = workflowId;

            // 执行工作流
            var result = await _workflowEngine.ExecuteAsync(request);

            var response = new
            {
                executionId = result.ExecutionId,
                workflowId = result.WorkflowId,
                status = result.Status.ToString(),
                startTime = result.StartTime,
                endTime = result.EndTime,
                duration = result.Duration?.TotalMilliseconds,
                success = result.Status == WorkflowExecutionStatus.Completed,
                errorMessage = result.ErrorMessage,
                inputData = result.InputData,
                triggeredBy = result.TriggeredBy,
                nodeResults = result.NodeResults.Select(nr => new
                {
                    nodeId = nr.NodeId,
                    nodeName = nr.NodeName,
                    nodeType = nr.NodeType,
                    status = nr.Status.ToString(),
                    success = nr.Success,
                    startTime = nr.StartTime,
                    endTime = nr.EndTime,
                    duration = nr.Duration?.TotalMilliseconds,
                    outputData = nr.OutputData,
                    nextEndpoint = nr.NextEndpoint,
                    errorMessage = nr.ErrorMessage,
                    logs = nr.Logs.Select(log => new
                    {
                        level = log.Level.ToString(),
                        message = log.Message,
                        timestamp = log.Timestamp,
                        source = log.Source,
                        data = log.Data
                    }).ToList()
                }).ToList()
            };

            _logger.LogInformation("工作流执行完成: {WorkflowId}, 状态: {Status}, 耗时: {Duration}ms",
                workflowId, result.Status, result.Duration?.TotalMilliseconds);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行工作流失败: {WorkflowId}", workflowId);
            return StatusCode(500, new
            {
                success = false,
                message = "执行工作流失败",
                error = ex.Message,
                workflowId = workflowId,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 停止工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <returns>停止结果</returns>
    [HttpPost("executions/{executionId}/stop")]
    public async Task<ActionResult<object>> StopExecution(string executionId)
    {
        try
        {
            _logger.LogInformation("停止工作流执行: {ExecutionId}", executionId);

            await _workflowEngine.StopAsync(executionId);

            var response = new
            {
                success = true,
                message = "工作流执行已停止",
                executionId = executionId,
                timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("工作流执行已停止: {ExecutionId}", executionId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止工作流执行失败: {ExecutionId}", executionId);
            return StatusCode(500, new
            {
                success = false,
                message = "停止工作流执行失败",
                error = ex.Message,
                executionId = executionId,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 获取执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <returns>执行状态</returns>
    [HttpGet("executions/{executionId}/status")]
    public async Task<ActionResult<object>> GetExecutionStatus(string executionId)
    {
        try
        {
            var status = await _workflowEngine.GetExecutionStatusAsync(executionId);
            
            if (status == null)
            {
                return NotFound(new { message = $"执行记录 '{executionId}' 不存在" });
            }

            var response = new
            {
                executionId = executionId,
                status = status.ToString(),
                timestamp = DateTime.UtcNow
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取执行状态失败: {ExecutionId}", executionId);
            return StatusCode(500, new
            {
                message = "获取执行状态失败",
                error = ex.Message,
                executionId = executionId,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 获取执行结果
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <returns>执行结果</returns>
    [HttpGet("executions/{executionId}")]
    public async Task<ActionResult<object>> GetExecutionResult(string executionId)
    {
        try
        {
            var result = await _workflowEngine.GetExecutionResultAsync(executionId);
            
            if (result == null)
            {
                return NotFound(new { message = $"执行记录 '{executionId}' 不存在" });
            }

            var response = new
            {
                executionId = result.ExecutionId,
                workflowId = result.WorkflowId,
                status = result.Status.ToString(),
                startTime = result.StartTime,
                endTime = result.EndTime,
                duration = result.Duration?.TotalMilliseconds,
                success = result.Status == WorkflowExecutionStatus.Completed,
                errorMessage = result.ErrorMessage,
                inputData = result.InputData,
                triggeredBy = result.TriggeredBy,
                nodeResults = result.NodeResults.Select(nr => new
                {
                    nodeId = nr.NodeId,
                    nodeName = nr.NodeName,
                    nodeType = nr.NodeType,
                    status = nr.Status.ToString(),
                    success = nr.Success,
                    startTime = nr.StartTime,
                    endTime = nr.EndTime,
                    duration = nr.Duration?.TotalMilliseconds,
                    outputData = nr.OutputData,
                    nextEndpoint = nr.NextEndpoint,
                    errorMessage = nr.ErrorMessage
                }).ToList()
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取执行结果失败: {ExecutionId}", executionId);
            return StatusCode(500, new
            {
                message = "获取执行结果失败",
                error = ex.Message,
                executionId = executionId,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 获取执行历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>执行历史</returns>
    [HttpGet("{workflowId}/executions")]
    public async Task<ActionResult<object>> GetExecutionHistory(
        string workflowId,
        [FromQuery] int pageIndex = 0,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var result = await _workflowEngine.GetExecutionHistoryAsync(workflowId, pageIndex, pageSize);

            var response = new
            {
                workflowId = workflowId,
                totalCount = result.TotalCount,
                pageIndex = result.PageIndex,
                pageSize = result.PageSize,
                totalPages = (int)Math.Ceiling((double)result.TotalCount / pageSize),
                items = result.Items.Select(item => new
                {
                    executionId = item.ExecutionId,
                    workflowId = item.WorkflowId,
                    status = item.Status.ToString(),
                    startTime = item.StartTime,
                    endTime = item.EndTime,
                    duration = item.Duration?.TotalMilliseconds,
                    success = item.Status == WorkflowExecutionStatus.Completed,
                    errorMessage = item.ErrorMessage,
                    triggeredBy = item.TriggeredBy,
                    nodeCount = item.NodeResults.Count,
                    successfulNodes = item.NodeResults.Count(nr => nr.Success),
                    failedNodes = item.NodeResults.Count(nr => !nr.Success)
                }).ToList()
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取执行历史失败: {WorkflowId}", workflowId);
            return StatusCode(500, new
            {
                message = "获取执行历史失败",
                error = ex.Message,
                workflowId = workflowId,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 验证工作流
    /// </summary>
    /// <param name="workflow">工作流定义</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate")]
    public async Task<ActionResult<object>> ValidateWorkflow([FromBody] WorkflowDefinition workflow)
    {
        try
        {
            _logger.LogInformation("验证工作流: {WorkflowName}", workflow.Name);

            var validationResult = await _workflowEngine.ValidateAsync(workflow);

            var response = new
            {
                isValid = validationResult.IsValid,
                workflowName = workflow.Name,
                validatedNodes = validationResult.ValidatedNodes,
                validatedConnections = validationResult.ValidatedConnections,
                errors = validationResult.Errors.Select(e => new
                {
                    code = e.Code,
                    message = e.Message,
                    field = e.Field,
                    value = e.Value,
                    severity = e.Severity.ToString()
                }).ToList(),
                warnings = validationResult.Warnings.Select(w => new
                {
                    code = w.Code,
                    message = w.Message,
                    field = w.Field,
                    value = w.Value
                }).ToList(),
                timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("工作流验证完成: {WorkflowName}, 有效: {IsValid}, 错误: {ErrorCount}, 警告: {WarningCount}",
                workflow.Name, validationResult.IsValid, validationResult.Errors.Count, validationResult.Warnings.Count);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证工作流失败: {WorkflowName}", workflow.Name);
            return StatusCode(500, new
            {
                isValid = false,
                message = "验证工作流失败",
                error = ex.Message,
                workflowName = workflow.Name,
                timestamp = DateTime.UtcNow
            });
        }
    }
}
